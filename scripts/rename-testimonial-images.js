/**
 * Rename Testimonial Images
 * 
 * This script renames testimonial images to use a standardized naming convention.
 * It replaces spaces with hyphens and ensures consistent casing.
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';

// Configuration
const sourceDir = 'public/images/testimonials';
const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp'];

// Mapping of old names to new names
const nameMapping = {};

// Find all images in the source directory
async function findImages() {
  const pattern = `${sourceDir}/**/*.{${imageExtensions.join(',')}}`;
  return await glob(pattern);
}

// Rename an image file
function renameImage(imagePath) {
  try {
    // Get the directory and filename
    const dir = path.dirname(imagePath);
    const filename = path.basename(imagePath);
    
    // Skip if the filename doesn't contain spaces
    if (!filename.includes(' ')) {
      console.log(`Skipping ${filename} (already standardized)`);
      return false;
    }
    
    // Create the new filename
    const newFilename = filename
      .replace(/\s+/g, '-') // Replace spaces with hyphens
      .toLowerCase(); // Convert to lowercase
    
    // Create the new path
    const newPath = path.join(dir, newFilename);
    
    // Skip if the new file already exists
    if (fs.existsSync(newPath)) {
      console.log(`Skipping ${filename} (target file already exists)`);
      return false;
    }
    
    // Rename the file
    fs.renameSync(imagePath, newPath);
    
    // Store the mapping
    nameMapping[filename] = newFilename;
    
    console.log(`Renamed ${filename} to ${newFilename}`);
    return true;
  } catch (error) {
    console.error(`Error renaming ${imagePath}: ${error.message}`);
    return false;
  }
}

// Save the mapping to a file
function saveMapping() {
  const mappingPath = path.join(process.cwd(), 'testimonial-name-mapping.json');
  fs.writeFileSync(mappingPath, JSON.stringify(nameMapping, null, 2));
  console.log(`Mapping saved to ${mappingPath}`);
}

// Update references in the codebase
async function updateReferences() {
  // Find all files that might reference the images
  const files = await glob('src/**/*.{js,jsx,ts,tsx,astro}');
  
  let modifiedCount = 0;
  
  for (const file of files) {
    try {
      let content = fs.readFileSync(file, 'utf8');
      let modified = false;
      
      // Replace references to the old filenames
      for (const [oldName, newName] of Object.entries(nameMapping)) {
        if (content.includes(oldName)) {
          content = content.replace(new RegExp(oldName.replace(/\./g, '\\.'), 'g'), newName);
          modified = true;
        }
      }
      
      // Save the file if modified
      if (modified) {
        fs.writeFileSync(file, content);
        console.log(`Updated references in ${file}`);
        modifiedCount++;
      }
    } catch (error) {
      console.error(`Error updating references in ${file}: ${error.message}`);
    }
  }
  
  console.log(`Updated references in ${modifiedCount} files`);
}

// Main function
async function main() {
  console.log('Starting image renaming...');
  
  // Find all images
  const images = await findImages();
  console.log(`Found ${images.length} images to process`);
  
  // Rename images
  let renamedCount = 0;
  for (const imagePath of images) {
    const renamed = renameImage(imagePath);
    if (renamed) renamedCount++;
  }
  
  console.log(`\nRenaming complete!`);
  console.log(`Total images: ${images.length}`);
  console.log(`Renamed images: ${renamedCount}`);
  
  // Save the mapping
  if (renamedCount > 0) {
    saveMapping();
    
    // Update references in the codebase
    console.log('\nUpdating references in the codebase...');
    await updateReferences();
  }
}

// Run the script
main().catch(console.error);
