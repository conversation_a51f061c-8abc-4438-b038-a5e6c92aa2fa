import{r as e,j as o}from"./vendor/react-bq1VYqFi.js";import{D as t}from"./app/design-system-lJ5ci6hi.js";import"./vendor/ui-components-_FzKGR3X.js";import"./vendor/utils-hwhCH1Yb.js";const n=({deal:n})=>{const[a,i]=e.useState(!1);e.useEffect((()=>{if("undefined"!=typeof window){const e=new URLSearchParams(window.location.search),o=e.get("dealId"),t=e.get("showPopup");o&&"true"===t&&o===n.id.toString()&&i(!0)}}),[n.id]);return a?o.jsx(t,{deal:n,onClose:()=>{if(i(!1),"undefined"!=typeof window){const e=new URL(window.location.href);e.searchParams.delete("dealId"),e.searchParams.delete("showPopup"),e.searchParams.delete("view"),window.history.replaceState({},"",e.toString())}},onVote:(e,o)=>{if("undefined"!=typeof window&&"function"==typeof navigator.sendBeacon)try{navigator.sendBeacon("/api/track-click",JSON.stringify({dealId:e.toString(),action:o?"vote_up":"vote_down",timestamp:Date.now()}))}catch(t){}}}):null};export{n as CouponPagePopup};
