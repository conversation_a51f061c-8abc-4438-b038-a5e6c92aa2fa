import{g as e,f as t,r as s,h as r,j as i}from"./vendor/react-bq1VYqFi.js";import{_ as n}from"./app/deals-3jyzwLVb.js";import{c as a,s as o,u as l,b as c,m as h}from"./proxy-BAs1uqPX.js";import"./vendor/ui-components-_FzKGR3X.js";function u(e,t){[...t].reverse().forEach((s=>{const r=e.getVariant(s);r&&o(e,r),e.variantChildren&&e.variantChildren.forEach((e=>{u(e,t)}))}))}function d(){const e=new Set,t={subscribe:t=>(e.add(t),()=>{e.delete(t)}),start(t,s){const r=[];return e.forEach((e=>{r.push(a(e,t,{transitionOverride:s}))})),Promise.all(r)},set:t=>e.forEach((e=>{!function(e,t){Array.isArray(t)?u(e,t):"string"==typeof t?u(e,[t]):o(e,t)}(e,t)})),stop(){e.forEach((e=>{!function(e){e.values.forEach((e=>e.stop()))}(e)}))},mount:()=>()=>{t.stop()}};return t}const f=function(){const e=l(d);return c(e.mount,[]),e};class p extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class g extends p{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class m extends p{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class y extends p{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var v;!function(e){e.Any="any",e.ApNortheast1="ap-northeast-1",e.ApNortheast2="ap-northeast-2",e.ApSouth1="ap-south-1",e.ApSoutheast1="ap-southeast-1",e.ApSoutheast2="ap-southeast-2",e.CaCentral1="ca-central-1",e.EuCentral1="eu-central-1",e.EuWest1="eu-west-1",e.EuWest2="eu-west-2",e.EuWest3="eu-west-3",e.SaEast1="sa-east-1",e.UsEast1="us-east-1",e.UsWest1="us-west-1",e.UsWest2="us-west-2"}(v||(v={}));var w=function(e,t,s,r){return new(s||(s=Promise))((function(i,n){function a(e){try{l(r.next(e))}catch(t){n(t)}}function o(e){try{l(r.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};class _{constructor(e,{headers:t={},customFetch:s,region:r=v.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>n((async()=>{const{default:e}=await Promise.resolve().then((()=>C));return{default:e}}),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)})(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s;return w(this,void 0,void 0,(function*(){try{const{headers:r,method:i,body:n}=t;let a,o={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(o["x-region"]=l),n&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!=typeof Blob&&n instanceof Blob||n instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",a=n):"string"==typeof n?(o["Content-Type"]="text/plain",a=n):"undefined"!=typeof FormData&&n instanceof FormData?a=n:(o["Content-Type"]="application/json",a=JSON.stringify(n)));const c=yield this.fetch(`${this.url}/${e}`,{method:i||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),r),body:a}).catch((e=>{throw new g(e)})),h=c.headers.get("x-relay-error");if(h&&"true"===h)throw new m(c);if(!c.ok)throw new y(c);let u,d=(null!==(s=c.headers.get("Content-Type"))&&void 0!==s?s:"text/plain").split(";")[0].trim();return u="application/json"===d?yield c.json():"application/octet-stream"===d?yield c.blob():"text/event-stream"===d?c:"multipart/form-data"===d?yield c.formData():yield c.text(),{data:u,error:null}}catch(r){return{data:null,error:r}}}))}}var b={},k={},S={},T={},j={},E={},x=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const O=x.fetch,P=x.fetch.bind(x),$=x.Headers,A=x.Request,I=x.Response,C=Object.freeze(Object.defineProperty({__proto__:null,Headers:$,Request:A,Response:I,default:P,fetch:O},Symbol.toStringTag,{value:"Module"})),R=e(C);var U,L,N,D,B,q={};function M(){if(U)return q;U=1,Object.defineProperty(q,"__esModule",{value:!0});class e extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}}return q.default=e,q}function z(){if(L)return E;L=1;var e=E&&E.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(E,"__esModule",{value:!0});const t=e(R),s=e(M());return E.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=t.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let r=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then((async e=>{var t,r,i;let n=null,a=null,o=null,l=e.status,c=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(a="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const s=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),i=null===(r=e.headers.get("content-range"))||void 0===r?void 0:r.split("/");s&&i&&i.length>1&&(o=parseInt(i[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(a)&&(a.length>1?(n={code:"PGRST116",details:`Results contain ${a.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},a=null,o=null,l=406,c="Not Acceptable"):a=1===a.length?a[0]:null)}else{const t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(a=[],n=null,l=200,c="OK")}catch(h){404===e.status&&""===t?(l=204,c="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null===(i=null==n?void 0:n.details)||void 0===i?void 0:i.includes("0 rows"))&&(n=null,l=200,c="OK"),n&&this.shouldThrowOnError)throw new s.default(n)}return{error:n,data:a,count:o,status:l,statusText:c}}));return this.shouldThrowOnError||(r=r.catch((e=>{var t,s,r;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(s=null==e?void 0:e.stack)&&void 0!==s?s:""}`,hint:"",code:`${null!==(r=null==e?void 0:e.code)&&void 0!==r?r:""}`},data:null,count:null,status:0,statusText:""}}))),r.then(e,t)}returns(){return this}overrideTypes(){return this}},E}function F(){if(N)return j;N=1;var e=j&&j.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(j,"__esModule",{value:!0});const t=e(z());class s extends t.default{select(e){let t=!1;const s=(null!=e?e:"*").split("").map((e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e))).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:i=r}={}){const n=i?`${i}.order`:"order",a=this.url.searchParams.get(n);return this.url.searchParams.set(n,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){const r=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:r=s}={}){const i=void 0===r?"offset":`${r}.offset`,n=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(i,`${e}`),this.url.searchParams.set(n,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:i=!1,format:n="text"}={}){var a;const o=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,i?"wal":null].filter(Boolean).join("|"),l=null!==(a=this.headers.Accept)&&void 0!==a?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${n}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}}return j.default=s,j}function J(){if(D)return T;D=1;var e=T&&T.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(T,"__esModule",{value:!0});const t=e(F());class s extends t.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const s=Array.from(new Set(t)).map((e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`)).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:r}={}){let i="";"plain"===r?i="pl":"phrase"===r?i="ph":"websearch"===r&&(i="w");const n=void 0===s?"":`(${s})`;return this.url.searchParams.append(e,`${i}fts${n}.${t}`),this}match(e){return Object.entries(e).forEach((([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)})),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){const r=s?`${s}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}}return T.default=s,T}function K(){if(B)return S;B=1;var e=S&&S.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(S,"__esModule",{value:!0});const t=e(J());return S.default=class{constructor(e,{headers:t={},schema:s,fetch:r}){this.url=e,this.headers=t,this.schema=s,this.fetch=r}select(e,{head:s=!1,count:r}={}){const i=s?"HEAD":"GET";let n=!1;const a=(null!=e?e:"*").split("").map((e=>/\s/.test(e)&&!n?"":('"'===e&&(n=!n),e))).join("");return this.url.searchParams.set("select",a),r&&(this.headers.Prefer=`count=${r}`),new t.default({method:i,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:s,defaultToNull:r=!0}={}){const i=[];if(this.headers.Prefer&&i.push(this.headers.Prefer),s&&i.push(`count=${s}`),r||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>`"${e}"`));this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:s,ignoreDuplicates:r=!1,count:i,defaultToNull:n=!0}={}){const a=[`resolution=${r?"ignore":"merge"}-duplicates`];if(void 0!==s&&this.url.searchParams.set("on_conflict",s),this.headers.Prefer&&a.push(this.headers.Prefer),i&&a.push(`count=${i}`),n||a.push("missing=default"),this.headers.Prefer=a.join(","),Array.isArray(e)){const t=e.reduce(((e,t)=>e.concat(Object.keys(t))),[]);if(t.length>0){const e=[...new Set(t)].map((e=>`"${e}"`));this.url.searchParams.set("columns",e.join(","))}}return new t.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:s}={}){const r=[];return this.headers.Prefer&&r.push(this.headers.Prefer),s&&r.push(`count=${s}`),this.headers.Prefer=r.join(","),new t.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const s=[];return e&&s.push(`count=${e}`),this.headers.Prefer&&s.unshift(this.headers.Prefer),this.headers.Prefer=s.join(","),new t.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}},S}var W,H,G,V,Y={},X={};function Z(){if(H)return Y;H=1,Object.defineProperty(Y,"__esModule",{value:!0}),Y.DEFAULT_HEADERS=void 0;const e=(W||(W=1,Object.defineProperty(X,"__esModule",{value:!0}),X.version=void 0,X.version="0.0.0-automated"),X);return Y.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${e.version}`},Y}var Q=function(){if(V)return b;V=1;var e=b&&b.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(b,"__esModule",{value:!0}),b.PostgrestError=b.PostgrestBuilder=b.PostgrestTransformBuilder=b.PostgrestFilterBuilder=b.PostgrestQueryBuilder=b.PostgrestClient=void 0;const t=e(function(){if(G)return k;G=1;var e=k&&k.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(k,"__esModule",{value:!0});const t=e(K()),s=e(J()),r=Z();class i{constructor(e,{headers:t={},schema:s,fetch:i}={}){this.url=e,this.headers=Object.assign(Object.assign({},r.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=i}from(e){const s=new URL(`${this.url}/${e}`);return new t.default(s,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(e){return new i(this.url,{headers:this.headers,schema:e,fetch:this.fetch})}rpc(e,t={},{head:r=!1,get:i=!1,count:n}={}){let a;const o=new URL(`${this.url}/rpc/${e}`);let l;r||i?(a=r?"HEAD":"GET",Object.entries(t).filter((([e,t])=>void 0!==t)).map((([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`])).forEach((([e,t])=>{o.searchParams.append(e,t)}))):(a="POST",l=t);const c=Object.assign({},this.headers);return n&&(c.Prefer=`count=${n}`),new s.default({method:a,url:o,headers:c,schema:this.schemaName,body:l,fetch:this.fetch,allowEmpty:!1})}}return k.default=i,k}());b.PostgrestClient=t.default;const s=e(K());b.PostgrestQueryBuilder=s.default;const r=e(J());b.PostgrestFilterBuilder=r.default;const i=e(F());b.PostgrestTransformBuilder=i.default;const n=e(z());b.PostgrestBuilder=n.default;const a=e(M());return b.PostgrestError=a.default,b.default={PostgrestClient:t.default,PostgrestQueryBuilder:s.default,PostgrestFilterBuilder:r.default,PostgrestTransformBuilder:i.default,PostgrestBuilder:n.default,PostgrestError:a.default},b}();const ee=t(Q),{PostgrestClient:te,PostgrestQueryBuilder:se,PostgrestFilterBuilder:re,PostgrestTransformBuilder:ie,PostgrestBuilder:ne,PostgrestError:ae}=ee;let oe;oe="undefined"==typeof window?require("ws"):window.WebSocket;const le={"X-Client-Info":"realtime-js/2.11.10"};var ce,he,ue,de,fe,pe;!function(e){e[e.connecting=0]="connecting",e[e.open=1]="open",e[e.closing=2]="closing",e[e.closed=3]="closed"}(ce||(ce={})),function(e){e.closed="closed",e.errored="errored",e.joined="joined",e.joining="joining",e.leaving="leaving"}(he||(he={})),function(e){e.close="phx_close",e.error="phx_error",e.join="phx_join",e.reply="phx_reply",e.leave="phx_leave",e.access_token="access_token"}(ue||(ue={})),function(e){e.websocket="websocket"}(de||(de={})),function(e){e.Connecting="connecting",e.Open="open",e.Closing="closing",e.Closed="closed"}(fe||(fe={}));class ge{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){const r=t.getUint8(1),i=t.getUint8(2);let n=this.HEADER_LENGTH+2;const a=s.decode(e.slice(n,n+r));n+=r;const o=s.decode(e.slice(n,n+i));n+=i;return{ref:null,topic:a,event:o,payload:JSON.parse(s.decode(e.slice(n,e.byteLength)))}}}class me{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout((()=>{this.tries=this.tries+1,this.callback()}),this.timerCalc(this.tries+1))}}!function(e){e.abstime="abstime",e.bool="bool",e.date="date",e.daterange="daterange",e.float4="float4",e.float8="float8",e.int2="int2",e.int4="int4",e.int4range="int4range",e.int8="int8",e.int8range="int8range",e.json="json",e.jsonb="jsonb",e.money="money",e.numeric="numeric",e.oid="oid",e.reltime="reltime",e.text="text",e.time="time",e.timestamp="timestamp",e.timestamptz="timestamptz",e.timetz="timetz",e.tsrange="tsrange",e.tstzrange="tstzrange"}(pe||(pe={}));const ye=(e,t,s={})=>{var r;const i=null!==(r=s.skipTypes)&&void 0!==r?r:[];return Object.keys(t).reduce(((s,r)=>(s[r]=ve(r,e,t,i),s)),{})},ve=(e,t,s,r)=>{const i=t.find((t=>t.name===e)),n=null==i?void 0:i.type,a=s[e];return n&&!r.includes(n)?we(n,a):_e(a)},we=(e,t)=>{if("_"===e.charAt(0)){const s=e.slice(1,e.length);return Te(t,s)}switch(e){case pe.bool:return be(t);case pe.float4:case pe.float8:case pe.int2:case pe.int4:case pe.int8:case pe.numeric:case pe.oid:return ke(t);case pe.json:case pe.jsonb:return Se(t);case pe.timestamp:return je(t);case pe.abstime:case pe.date:case pe.daterange:case pe.int4range:case pe.int8range:case pe.money:case pe.reltime:case pe.text:case pe.time:case pe.timestamptz:case pe.timetz:case pe.tsrange:case pe.tstzrange:default:return _e(t)}},_e=e=>e,be=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},ke=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Se=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return e}return e},Te=(e,t)=>{if("string"!=typeof e)return e;const s=e.length-1,r=e[s];if("{"===e[0]&&"}"===r){let r;const n=e.slice(1,s);try{r=JSON.parse("["+n+"]")}catch(i){r=n?n.split(","):[]}return r.map((e=>we(t,e)))}return e},je=e=>"string"==typeof e?e.replace(" ","T"):e,Ee=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class xe{constructor(e,t,s={},r=1e4){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null===(s=this.receivedResp)||void 0===s?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},(e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)})),this.timeoutTimer=setTimeout((()=>{this.trigger("timeout",{})}),this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter((t=>t.status===e)).forEach((e=>e.callback(t)))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Oe,Pe,$e,Ae;!function(e){e.SYNC="sync",e.JOIN="join",e.LEAVE="leave"}(Oe||(Oe={}));class Ie{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},(e=>{const{onJoin:t,onLeave:s,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Ie.syncState(this.state,e,t,s),this.pendingDiffs.forEach((e=>{this.state=Ie.syncDiff(this.state,e,t,s)})),this.pendingDiffs=[],r()})),this.channel._on(s.diff,{},(e=>{const{onJoin:t,onLeave:s,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=Ie.syncDiff(this.state,e,t,s),r())})),this.onJoin(((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})})),this.onLeave(((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})})),this.onSync((()=>{this.channel._trigger("presence",{event:"sync"})}))}static syncState(e,t,s,r){const i=this.cloneDeep(e),n=this.transformState(t),a={},o={};return this.map(i,((e,t)=>{n[e]||(o[e]=t)})),this.map(n,((e,t)=>{const s=i[e];if(s){const r=t.map((e=>e.presence_ref)),i=s.map((e=>e.presence_ref)),n=t.filter((e=>i.indexOf(e.presence_ref)<0)),l=s.filter((e=>r.indexOf(e.presence_ref)<0));n.length>0&&(a[e]=n),l.length>0&&(o[e]=l)}else a[e]=t})),this.syncDiff(i,{joins:a,leaves:o},s,r)}static syncDiff(e,t,s,r){const{joins:i,leaves:n}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(i,((t,r)=>{var i;const n=null!==(i=e[t])&&void 0!==i?i:[];if(e[t]=this.cloneDeep(r),n.length>0){const s=e[t].map((e=>e.presence_ref)),r=n.filter((e=>s.indexOf(e.presence_ref)<0));e[t].unshift(...r)}s(t,n,r)})),this.map(n,((t,s)=>{let i=e[t];if(!i)return;const n=s.map((e=>e.presence_ref));i=i.filter((e=>n.indexOf(e.presence_ref)<0)),e[t]=i,r(t,i,s),0===i.length&&delete e[t]})),e}static map(e,t){return Object.getOwnPropertyNames(e).map((s=>t(s,e[s])))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce(((t,s)=>{const r=e[s];return t[s]="metas"in r?r.metas.map((e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e))):r,t}),{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}!function(e){e.ALL="*",e.INSERT="INSERT",e.UPDATE="UPDATE",e.DELETE="DELETE"}(Pe||(Pe={})),function(e){e.BROADCAST="broadcast",e.PRESENCE="presence",e.POSTGRES_CHANGES="postgres_changes",e.SYSTEM="system"}($e||($e={})),function(e){e.SUBSCRIBED="SUBSCRIBED",e.TIMED_OUT="TIMED_OUT",e.CLOSED="CLOSED",e.CHANNEL_ERROR="CHANNEL_ERROR"}(Ae||(Ae={}));class Ce{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=he.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new xe(this,ue.join,this.params,this.timeout),this.rejoinTimer=new me((()=>this._rejoinUntilConnected()),this.socket.reconnectAfterMs),this.joinPush.receive("ok",(()=>{this.state=he.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach((e=>e.send())),this.pushBuffer=[]})),this._onClose((()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=he.closed,this.socket._remove(this)})),this._onError((e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=he.errored,this.rejoinTimer.scheduleTimeout())})),this.joinPush.receive("timeout",(()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=he.errored,this.rejoinTimer.scheduleTimeout())})),this._on(ue.reply,{},((e,t)=>{this._trigger(this._replyEventName(t),e)})),this.presence=new Ie(this),this.broadcastEndpointURL=Ee(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.joinedOnce)throw"tried to subscribe multiple times. 'subscribe' can only be called a single time per channel instance";{const{config:{broadcast:i,presence:n,private:a}}=this.params;this._onError((t=>null==e?void 0:e(Ae.CHANNEL_ERROR,t))),this._onClose((()=>null==e?void 0:e(Ae.CLOSED)));const o={},l={broadcast:i,presence:n,postgres_changes:null!==(r=null===(s=this.bindings.postgres_changes)||void 0===s?void 0:s.map((e=>e.filter)))&&void 0!==r?r:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",(async({postgres_changes:t})=>{var s;if(this.socket.setAuth(),void 0!==t){const r=this.bindings.postgres_changes,i=null!==(s=null==r?void 0:r.length)&&void 0!==s?s:0,n=[];for(let s=0;s<i;s++){const i=r[s],{filter:{event:a,schema:o,table:l,filter:c}}=i,h=t&&t[s];if(!h||h.event!==a||h.schema!==o||h.table!==l||h.filter!==c)return this.unsubscribe(),this.state=he.errored,void(null==e||e(Ae.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));n.push(Object.assign(Object.assign({},i),{id:h.id}))}return this.bindings.postgres_changes=n,void(e&&e(Ae.SUBSCRIBED))}null==e||e(Ae.SUBSCRIBED)})).receive("error",(t=>{this.state=he.errored,null==e||e(Ae.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))})).receive("timeout",(()=>{null==e||e(Ae.TIMED_OUT)}))}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,r;if(this._canPush()||"broadcast"!==e.type)return new Promise((s=>{var r,i,n;const a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(n=null===(i=null===(r=this.params)||void 0===r?void 0:r.config)||void 0===i?void 0:i.broadcast)||void 0===n?void 0:n.ack)||s("ok"),a.receive("ok",(()=>s("ok"))),a.receive("error",(()=>s("error"))),a.receive("timeout",(()=>s("timed out")))}));{const{event:n,payload:a}=e,o={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:n,payload:a,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,o,null!==(s=t.timeout)&&void 0!==s?s:this.timeout);return await(null===(r=e.body)||void 0===r?void 0:r.cancel()),e.ok?"ok":"error"}catch(i){return"AbortError"===i.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=he.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(ue.close,"leave",this._joinRef())};return this.joinPush.destroy(),new Promise((s=>{const r=new xe(this,ue.leave,{},e);r.receive("ok",(()=>{t(),s("ok")})).receive("timeout",(()=>{t(),s("timed out")})).receive("error",(()=>{s("error")})),r.send(),this._canPush()||r.trigger("ok",{})}))}teardown(){this.pushBuffer.forEach((e=>e.destroy())),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,s){const r=new AbortController,i=setTimeout((()=>r.abort()),s),n=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(i),n}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new xe(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,i;const n=e.toLocaleLowerCase(),{close:a,error:o,leave:l,join:c}=ue;if(s&&[a,o,l,c].indexOf(n)>=0&&s!==this._joinRef())return;let h=this._onMessage(n,t,s);if(t&&!h)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(n)?null===(r=this.bindings.postgres_changes)||void 0===r||r.filter((e=>{var t,s,r;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(r=null===(s=e.filter)||void 0===s?void 0:s.event)||void 0===r?void 0:r.toLocaleLowerCase())===n})).map((e=>e.callback(h,s))):null===(i=this.bindings[n])||void 0===i||i.filter((e=>{var s,r,i,a,o,l;if(["broadcast","presence","postgres_changes"].includes(n)){if("id"in e){const n=e.id,a=null===(s=e.filter)||void 0===s?void 0:s.event;return n&&(null===(r=t.ids)||void 0===r?void 0:r.includes(n))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null===(i=t.data)||void 0===i?void 0:i.type.toLocaleLowerCase()))}{const s=null===(o=null===(a=null==e?void 0:e.filter)||void 0===a?void 0:a.event)||void 0===o?void 0:o.toLocaleLowerCase();return"*"===s||s===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===n})).map((e=>{if("object"==typeof h&&"ids"in h){const e=h.data,{schema:t,table:s,commit_timestamp:r,type:i,errors:n}=e,a={schema:t,table:s,commit_timestamp:r,eventType:i,new:{},old:{},errors:n};h=Object.assign(Object.assign({},a),this._getPayloadRecords(e))}e.callback(h,s)}))}_isClosed(){return this.state===he.closed}_isJoined(){return this.state===he.joined}_isJoining(){return this.state===he.joining}_isLeaving(){return this.state===he.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){const r=e.toLocaleLowerCase(),i={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(i):this.bindings[r]=[i],this}_off(e,t){const s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter((e=>{var r;return!((null===(r=e.type)||void 0===r?void 0:r.toLocaleLowerCase())===s&&Ce.isEqual(e.filter,t))})),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(ue.close,{},e)}_onError(e){this._on(ue.error,{},(t=>e(t)))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=he.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=ye(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=ye(e.columns,e.old_record)),t}}const Re=()=>{};class Ue{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers=le,this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Re,this.ref=0,this.logger=Re,this.conn=null,this.sendBuffer=[],this.serializer=new ge,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>n((async()=>{const{default:e}=await Promise.resolve().then((()=>C));return{default:e}}),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${de.websocket}`,this.httpEndpoint=Ee(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.headers)&&(this.headers=Object.assign(Object.assign({},this.headers),t.headers)),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const r=null===(s=null==t?void 0:t.params)||void 0===s?void 0:s.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new me((async()=>{this.disconnect(),this.connect()}),this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=oe),this.transport){const e="undefined"!=typeof window&&this.transport===window.WebSocket;return this.conn=e?new this.transport(this.endpointURL()):new this.transport(this.endpointURL(),void 0,{headers:this.headers}),void this.setupConnection()}this.conn=new Le(this.endpointURL(),void 0,{close:()=>{this.conn=null}})}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach((e=>e.teardown())))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return this.channels=this.channels.filter((t=>t._joinRef!==e._joinRef)),0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map((e=>e.unsubscribe())));return this.channels=[],this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case ce.connecting:return fe.Connecting;case ce.open:return fe.Open;case ce.closing:return fe.Closing;default:return fe.Closed}}isConnected(){return this.connectionState()===fe.Open}channel(e,t={config:{}}){const s=`realtime:${e}`,r=this.getChannels().find((e=>e.topic===s));if(r)return r;{const s=new Ce(`realtime:${e}`,t,this);return this.channels.push(s),s}}push(e){const{topic:t,event:s,payload:r,ref:i}=e,n=()=>{this.encode(e,(e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)}))};this.log("push",`${t} ${s} (${i})`,r),this.isConnected()?n():this.sendBuffer.push(n)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach((e=>{t&&e.updateJoinPayload({access_token:t,version:this.headers&&this.headers["X-Client-Info"]}),e.joinedOnce&&e._isJoined()&&e._push(ue.access_token,{access_token:t})})))}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach((e=>e())),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find((t=>t.topic===e&&(t._isJoined()||t._isJoining())));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter((t=>t.topic!==e.topic))}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,(e=>{let{topic:t,event:s,payload:r,ref:i}=e;"phoenix"===t&&"phx_reply"===s&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),i&&i===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${s} ${i&&"("+i+")"||""}`,r),Array.from(this.channels).filter((e=>e._isMember(t))).forEach((e=>e._trigger(s,r,i))),this.stateChangeCallbacks.message.forEach((t=>t(e)))}))}_onConnOpen(){if(this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}else this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval((()=>this.sendHeartbeat()),this.heartbeatIntervalMs);this.stateChangeCallbacks.open.forEach((e=>e()))}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach((t=>t(e)))}_onConnError(e){this.log("transport",e.message),this._triggerChanError(),this.stateChangeCallbacks.error.forEach((t=>t(e)))}_triggerChanError(){this.channels.forEach((e=>e._trigger(ue.error)))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const s=e.match(/\?/)?"&":"?";return`${e}${s}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class Le{constructor(e,t,s){this.binaryType="arraybuffer",this.onclose=()=>{},this.onerror=()=>{},this.onmessage=()=>{},this.onopen=()=>{},this.readyState=ce.connecting,this.send=()=>{},this.url=null,this.url=e,this.close=s.close}}class Ne extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function De(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class Be extends Ne{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class qe extends Ne{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var Me=function(e,t,s,r){return new(s||(s=Promise))((function(i,n){function a(e){try{l(r.next(e))}catch(t){n(t)}}function o(e){try{l(r.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const ze=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>n((async()=>{const{default:e}=await Promise.resolve().then((()=>C));return{default:e}}),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},Fe=e=>{if(Array.isArray(e))return e.map((e=>Fe(e)));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach((([e,s])=>{const r=e.replace(/([-_][a-z])/gi,(e=>e.toUpperCase().replace(/[-_]/g,"")));t[r]=Fe(s)})),t};var Je=function(e,t,s,r){return new(s||(s=Promise))((function(i,n){function a(e){try{l(r.next(e))}catch(t){n(t)}}function o(e){try{l(r.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const Ke=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),We=(e,t,s)=>Je(void 0,void 0,void 0,(function*(){const r=yield Me(void 0,void 0,void 0,(function*(){return"undefined"==typeof Response?(yield n((()=>Promise.resolve().then((()=>C))),void 0)).Response:Response}));e instanceof r&&!(null==s?void 0:s.noResolveJson)?e.json().then((s=>{t(new Be(Ke(s),e.status||500))})).catch((e=>{t(new qe(Ke(e),e))})):t(new qe(Ke(e),e))}));function He(e,t,s,r,i,n){return Je(this,void 0,void 0,(function*(){return new Promise(((a,o)=>{e(s,((e,t,s,r)=>{const i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),r&&(i.body=JSON.stringify(r)),Object.assign(Object.assign({},i),s))})(t,r,i,n)).then((e=>{if(!e.ok)throw e;return(null==r?void 0:r.noResolveJson)?e:e.json()})).then((e=>a(e))).catch((e=>We(e,o,r)))}))}))}function Ge(e,t,s,r){return Je(this,void 0,void 0,(function*(){return He(e,"GET",t,s,r)}))}function Ve(e,t,s,r,i){return Je(this,void 0,void 0,(function*(){return He(e,"POST",t,r,i,s)}))}function Ye(e,t,s,r,i){return Je(this,void 0,void 0,(function*(){return He(e,"DELETE",t,r,i,s)}))}var Xe=function(e,t,s,r){return new(s||(s=Promise))((function(i,n){function a(e){try{l(r.next(e))}catch(t){n(t)}}function o(e){try{l(r.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const Ze={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},Qe={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class et{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=ze(r)}uploadOrUpdate(e,t,s,r){return Xe(this,void 0,void 0,(function*(){try{let i;const n=Object.assign(Object.assign({},Qe),r);let a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(n.upsert)});const o=n.metadata;"undefined"!=typeof Blob&&s instanceof Blob?(i=new FormData,i.append("cacheControl",n.cacheControl),o&&i.append("metadata",this.encodeMetadata(o)),i.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(i=s,i.append("cacheControl",n.cacheControl),o&&i.append("metadata",this.encodeMetadata(o))):(i=s,a["cache-control"]=`max-age=${n.cacheControl}`,a["content-type"]=n.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==r?void 0:r.headers)&&(a=Object.assign(Object.assign({},a),r.headers));const l=this._removeEmptyFolders(t),c=this._getFinalPath(l),h=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:i,headers:a},(null==n?void 0:n.duplex)?{duplex:n.duplex}:{})),u=yield h.json();if(h.ok)return{data:{path:l,id:u.Id,fullPath:u.Key},error:null};return{data:null,error:u}}catch(i){if(De(i))return{data:null,error:i};throw i}}))}upload(e,t,s){return Xe(this,void 0,void 0,(function*(){return this.uploadOrUpdate("POST",e,t,s)}))}uploadToSignedUrl(e,t,s,r){return Xe(this,void 0,void 0,(function*(){const i=this._removeEmptyFolders(e),n=this._getFinalPath(i),a=new URL(this.url+`/object/upload/sign/${n}`);a.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:Qe.upsert},r),n=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&s instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(e=s,e.append("cacheControl",t.cacheControl)):(e=s,n["cache-control"]=`max-age=${t.cacheControl}`,n["content-type"]=t.contentType);const o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:n}),l=yield o.json();if(o.ok)return{data:{path:i,fullPath:l.Key},error:null};return{data:null,error:l}}catch(o){if(De(o))return{data:null,error:o};throw o}}))}createSignedUploadUrl(e,t){return Xe(this,void 0,void 0,(function*(){try{let s=this._getFinalPath(e);const r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");const i=yield Ve(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),n=new URL(this.url+i.url),a=n.searchParams.get("token");if(!a)throw new Ne("No token returned by API");return{data:{signedUrl:n.toString(),path:e,token:a},error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}}))}update(e,t,s){return Xe(this,void 0,void 0,(function*(){return this.uploadOrUpdate("PUT",e,t,s)}))}move(e,t,s){return Xe(this,void 0,void 0,(function*(){try{return{data:yield Ve(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}}))}copy(e,t,s){return Xe(this,void 0,void 0,(function*(){try{return{data:{path:(yield Ve(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}}))}createSignedUrl(e,t,s){return Xe(this,void 0,void 0,(function*(){try{let r=this._getFinalPath(e),i=yield Ve(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers});const n=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return i={signedUrl:encodeURI(`${this.url}${i.signedURL}${n}`)},{data:i,error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}}))}createSignedUrls(e,t,s){return Xe(this,void 0,void 0,(function*(){try{const r=yield Ve(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),i=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:r.map((e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${i}`):null}))),error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}}))}download(e,t){return Xe(this,void 0,void 0,(function*(){const s=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),i=r?`?${r}`:"";try{const t=this._getFinalPath(e),r=yield Ge(this.fetch,`${this.url}/${s}/${t}${i}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(n){if(De(n))return{data:null,error:n};throw n}}))}info(e){return Xe(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{const e=yield Ge(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:Fe(e),error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}}))}exists(e){return Xe(this,void 0,void 0,(function*(){const t=this._getFinalPath(e);try{return yield function(e,t,s,r){return Je(this,void 0,void 0,(function*(){return He(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),r)}))}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(De(s)&&s instanceof qe){const e=s.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:s}}throw s}}))}getPublicUrl(e,t){const s=this._getFinalPath(e),r=[],i=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==i&&r.push(i);const n=void 0!==(null==t?void 0:t.transform)?"render/image":"object",a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&r.push(a);let o=r.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${n}/public/${s}${o}`)}}}remove(e){return Xe(this,void 0,void 0,(function*(){try{return{data:yield Ye(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(De(t))return{data:null,error:t};throw t}}))}list(e,t,s){return Xe(this,void 0,void 0,(function*(){try{const r=Object.assign(Object.assign(Object.assign({},Ze),t),{prefix:e||""});return{data:yield Ve(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(r){if(De(r))return{data:null,error:r};throw r}}))}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const tt={"X-Client-Info":"storage-js/2.7.1"};var st=function(e,t,s,r){return new(s||(s=Promise))((function(i,n){function a(e){try{l(r.next(e))}catch(t){n(t)}}function o(e){try{l(r.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};class rt{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},tt),t),this.fetch=ze(s)}listBuckets(){return st(this,void 0,void 0,(function*(){try{return{data:yield Ge(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(De(e))return{data:null,error:e};throw e}}))}getBucket(e){return st(this,void 0,void 0,(function*(){try{return{data:yield Ge(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(De(t))return{data:null,error:t};throw t}}))}createBucket(e,t={public:!1}){return st(this,void 0,void 0,(function*(){try{return{data:yield Ve(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}}))}updateBucket(e,t){return st(this,void 0,void 0,(function*(){try{const s=yield function(e,t,s,r,i){return Je(this,void 0,void 0,(function*(){return He(e,"PUT",t,r,i,s)}))}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:s,error:null}}catch(s){if(De(s))return{data:null,error:s};throw s}}))}emptyBucket(e){return st(this,void 0,void 0,(function*(){try{return{data:yield Ve(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(De(t))return{data:null,error:t};throw t}}))}deleteBucket(e){return st(this,void 0,void 0,(function*(){try{return{data:yield Ye(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(De(t))return{data:null,error:t};throw t}}))}}class it extends rt{constructor(e,t={},s){super(e,t,s)}from(e){return new et(this.url,this.headers,e,this.fetch)}}let nt="";nt="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const at={headers:{"X-Client-Info":`supabase-js-${nt}/2.50.0`}},ot={schema:"public"},lt={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},ct={};var ht=function(e,t,s,r){return new(s||(s=Promise))((function(i,n){function a(e){try{l(r.next(e))}catch(t){n(t)}}function o(e){try{l(r.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const ut=e=>{let t;return t=e||("undefined"==typeof fetch?P:fetch),(...e)=>t(...e)},dt=(e,t,s)=>{const r=ut(s),i="undefined"==typeof Headers?$:Headers;return(s,n)=>ht(void 0,void 0,void 0,(function*(){var a;const o=null!==(a=yield t())&&void 0!==a?a:e;let l=new i(null==n?void 0:n.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),r(s,Object.assign(Object.assign({},n),{headers:l}))}))};var ft=function(e,t,s,r){return new(s||(s=Promise))((function(i,n){function a(e){try{l(r.next(e))}catch(t){n(t)}}function o(e){try{l(r.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};const pt="2.70.0",gt=3e4,mt=9e4,yt={"X-Client-Info":`gotrue-js/${pt}`},vt="X-Supabase-Api-Version",wt={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},_t=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class bt extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function kt(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class St extends bt{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}class Tt extends bt{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class jt extends bt{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class Et extends jt{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class xt extends jt{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class Ot extends jt{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Pt extends jt{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class $t extends jt{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class At extends jt{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function It(e){return kt(e)&&"AuthRetryableFetchError"===e.name}class Ct extends jt{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class Rt extends jt{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Ut="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Lt=" \t\n\r=".split(""),Nt=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Lt.length;t+=1)e[Lt[t].charCodeAt(0)]=-2;for(let t=0;t<Ut.length;t+=1)e[Ut[t].charCodeAt(0)]=t;return e})();function Dt(e,t,s){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;s(Ut[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;s(Ut[e]),t.queuedBits-=6}}function Bt(e,t,s){const r=Nt[e];if(!(r>-1)){if(-2===r)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function qt(e){const t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},i={queue:0,queuedBits:0},n=e=>{!function(e,t,s){if(0===t.utf8seq){if(e<=127)return void s(e);for(let s=1;s<6;s+=1)if(!(e>>7-s&1)){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}(e,r,s)};for(let a=0;a<e.length;a+=1)Bt(e.charCodeAt(a),i,n);return t.join("")}function Mt(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function zt(e){const t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let i=0;i<e.length;i+=1)Bt(e.charCodeAt(i),s,r);return new Uint8Array(t)}function Ft(e){const t=[];return function(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){const t=1024*(r-55296)&65535;r=65536+(e.charCodeAt(s+1)-56320&65535|t),s+=1}Mt(r,t)}}(e,(e=>t.push(e))),new Uint8Array(t)}function Jt(e){const t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};return e.forEach((e=>Dt(e,s,r))),Dt(null,s,r),t.join("")}const Kt=()=>"undefined"!=typeof window&&"undefined"!=typeof document,Wt={tested:!1,writable:!1},Ht=()=>{if(!Kt())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(Wt.tested)return Wt.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),Wt.tested=!0,Wt.writable=!0}catch(t){Wt.tested=!0,Wt.writable=!1}return Wt.writable};const Gt=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>n((async()=>{const{default:e}=await Promise.resolve().then((()=>C));return{default:e}}),void 0).then((({default:t})=>t(...e))):fetch),(...e)=>t(...e)},Vt=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},Yt=async(e,t)=>{const s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch(r){return s}},Xt=async(e,t)=>{await e.removeItem(t)};class Zt{constructor(){this.promise=new Zt.promiseConstructor(((e,t)=>{this.resolve=e,this.reject=t}))}}function Qt(e){const t=e.split(".");if(3!==t.length)throw new Rt("Invalid JWT structure");for(let s=0;s<t.length;s++)if(!_t.test(t[s]))throw new Rt("JWT not in base64url format");return{header:JSON.parse(qt(t[0])),payload:JSON.parse(qt(t[1])),signature:zt(t[2]),raw:{header:t[0],payload:t[1]}}}function es(e){return("0"+e.toString(16)).substr(-2)}async function ts(e){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return e;const t=await async function(e){const t=(new TextEncoder).encode(e),s=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(s);return Array.from(r).map((e=>String.fromCharCode(e))).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function ss(e,t,s=!1){const r=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let s="";for(let r=0;r<56;r++)s+=e.charAt(Math.floor(Math.random()*t));return s}return crypto.getRandomValues(e),Array.from(e,es).join("")}();let i=r;s&&(i+="/PASSWORD_RECOVERY"),await Vt(e,`${t}-code-verifier`,i);const n=await ts(r);return[n,r===n?"plain":"s256"]}Zt.promiseConstructor=Promise;const rs=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const is=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function ns(e){if(!is.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}var as=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]])}return s};const os=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),ls=[502,503,504];async function cs(e){var t,s;if(!("object"==typeof(s=e)&&null!==s&&"status"in s&&"ok"in s&&"json"in s&&"function"==typeof s.json))throw new At(os(e),0);if(ls.includes(e.status))throw new At(os(e),e.status);let r,i;try{r=await e.json()}catch(a){throw new Tt(os(a),a)}const n=function(e){const t=e.headers.get(vt);if(!t)return null;if(!t.match(rs))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(a){return null}}(e);if(n&&n.getTime()>=wt.timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?i=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(i=r.error_code),i){if("weak_password"===i)throw new Ct(os(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===i)throw new Et}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce(((e,t)=>e&&"string"==typeof t),!0))throw new Ct(os(r),e.status,r.weak_password.reasons);throw new St(os(r),e.status||500,i)}async function hs(e,t,s,r){var i;const n=Object.assign({},null==r?void 0:r.headers);n[vt]||(n[vt]=wt.name),(null==r?void 0:r.jwt)&&(n.Authorization=`Bearer ${r.jwt}`);const a=null!==(i=null==r?void 0:r.query)&&void 0!==i?i:{};(null==r?void 0:r.redirectTo)&&(a.redirect_to=r.redirectTo);const o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await async function(e,t,s,r,i,n){const a=((e,t,s,r)=>{const i={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?i:(i.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),i.body=JSON.stringify(r),Object.assign(Object.assign({},i),s))})(t,r,i,n);let o;try{o=await e(s,Object.assign({},a))}catch(l){throw new At(os(l),0)}o.ok||await cs(o);if(null==r?void 0:r.noResolveJson)return o;try{return await o.json()}catch(l){await cs(l)}}(e,t,s+o,{headers:n,noResolveJson:null==r?void 0:r.noResolveJson},{},null==r?void 0:r.body);return(null==r?void 0:r.xform)?null==r?void 0:r.xform(l):{data:Object.assign({},l),error:null}}function us(e){var t;let s=null;var r;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(s=Object.assign({},e),e.expires_at||(s.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:s,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function ds(e){const t=us(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce(((e,t)=>e&&"string"==typeof t),!0)&&(t.data.weak_password=e.weak_password),t}function fs(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function ps(e){return{data:e,error:null}}function gs(e){const{action_link:t,email_otp:s,hashed_token:r,redirect_to:i,verification_type:n}=e,a=as(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:s,hashed_token:r,redirect_to:i,verification_type:n},user:Object.assign({},a)},error:null}}function ms(e){return e}const ys=["global","local","others"];var vs=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var i=0;for(r=Object.getOwnPropertySymbols(e);i<r.length;i++)t.indexOf(r[i])<0&&Object.prototype.propertyIsEnumerable.call(e,r[i])&&(s[r[i]]=e[r[i]])}return s};class ws{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=Gt(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=ys[0]){if(ys.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${ys.join(", ")}`);try{return await hs(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(s){if(kt(s))return{data:null,error:s};throw s}}async inviteUserByEmail(e,t={}){try{return await hs(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:fs})}catch(s){if(kt(s))return{data:{user:null},error:s};throw s}}async generateLink(e){try{const{options:t}=e,s=vs(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=null==s?void 0:s.newEmail,delete r.newEmail),await hs(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:gs,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(kt(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await hs(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:fs})}catch(t){if(kt(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,s,r,i,n,a,o;try{const l={nextPage:null,lastPage:0,total:0},c=await hs(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(s=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==s?s:"",per_page:null!==(i=null===(r=null==e?void 0:e.perPage)||void 0===r?void 0:r.toString())&&void 0!==i?i:""},xform:ms});if(c.error)throw c.error;const h=await c.json(),u=null!==(n=c.headers.get("x-total-count"))&&void 0!==n?n:0,d=null!==(o=null===(a=c.headers.get("link"))||void 0===a?void 0:a.split(","))&&void 0!==o?o:[];return d.length>0&&(d.forEach((e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(e.split(";")[1].split("=")[1]);l[`${s}Page`]=t})),l.total=parseInt(u)),{data:Object.assign(Object.assign({},h),l),error:null}}catch(l){if(kt(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){ns(e);try{return await hs(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:fs})}catch(t){if(kt(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){ns(e);try{return await hs(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:fs})}catch(s){if(kt(s))return{data:{user:null},error:s};throw s}}async deleteUser(e,t=!1){ns(e);try{return await hs(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:fs})}catch(s){if(kt(s))return{data:{user:null},error:s};throw s}}async _listFactors(e){ns(e.userId);try{const{data:t,error:s}=await hs(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:s}}catch(t){if(kt(t))return{data:null,error:t};throw t}}async _deleteFactor(e){ns(e.userId),ns(e.id);try{return{data:await hs(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(kt(t))return{data:null,error:t};throw t}}}const _s={getItem:e=>Ht()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{Ht()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{Ht()&&globalThis.localStorage.removeItem(e)}};function bs(e={}){return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}const ks=!!(globalThis&&Ht()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class Ss extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class Ts extends Ss{}async function js(e,t,s){const r=new globalThis.AbortController;return t>0&&setTimeout((()=>{r.abort()}),t),await Promise.resolve().then((()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},(async r=>{if(!r){if(0===t)throw new Ts(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(ks)try{await globalThis.navigator.locks.query()}catch(i){}return await s()}try{return await s()}finally{}}))))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const Es={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:yt,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function xs(e,t,s){return await s()}class Os{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Os.nextInstanceID,Os.nextInstanceID+=1,this.instanceID>0&&Kt();const r=Object.assign(Object.assign({},Es),e);if(this.logDebugMessages=!!r.debug,"function"==typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new ws({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=Gt(r.fetch),this.lock=r.lock||xs,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:Kt()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=js:this.lock=xs,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:Ht()?this.storage=_s:(this.memoryStorage={},this.storage=bs(this.memoryStorage)):(this.memoryStorage={},this.storage=bs(this.memoryStorage)),Kt()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(i){}null===(s=this.broadcastChannel)||void 0===s||s.addEventListener("message",(async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)}))}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${pt}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,(async()=>await this._initialize())))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},s=new URL(e);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach(((e,s)=>{t[s]=e}))}catch(r){}return s.searchParams.forEach(((e,s)=>{t[s]=e})),t}(window.location.href);let s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),Kt()&&this.detectSessionInUrl&&"none"!==s){const{data:r,error:i}=await this._getSessionFromURL(t,s);if(i){if(this._debug("#_initialize()","error detecting session from URL",i),function(e){return kt(e)&&"AuthImplicitGrantRedirectError"===e.name}(i)){const t=null===(e=i.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:i}}return await this._removeSession(),{error:i}}const{session:n,redirectType:a}=r;return this._debug("#_initialize()","detected session in URL",n,"redirect type",a),await this._saveSession(n),setTimeout((async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",n):await this._notifyAllSubscribers("SIGNED_IN",n)}),0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return kt(t)?{error:t}:{error:new Tt("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,r;try{const i=await hs(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(s=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==s?s:{},gotrue_meta_security:{captcha_token:null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken}},xform:us}),{data:n,error:a}=i;if(a||!n)return{data:{user:null,session:null},error:a};const o=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(i){if(kt(i))return{data:{user:null,session:null},error:i};throw i}}async signUp(e){var t,s,r;try{let i;if("email"in e){const{email:s,password:r,options:n}=e;let a=null,o=null;"pkce"===this.flowType&&([a,o]=await ss(this.storage,this.storageKey)),i=await hs(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==n?void 0:n.emailRedirectTo,body:{email:s,password:r,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:a,code_challenge_method:o},xform:us})}else{if(!("phone"in e))throw new Ot("You must provide either an email or phone number and a password");{const{phone:t,password:n,options:a}=e;i=await hs(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:n,data:null!==(s=null==a?void 0:a.data)&&void 0!==s?s:{},channel:null!==(r=null==a?void 0:a.channel)&&void 0!==r?r:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:us})}}const{data:n,error:a}=i;if(a||!n)return{data:{user:null,session:null},error:a};const o=n.session,l=n.user;return n.session&&(await this._saveSession(n.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(i){if(kt(i))return{data:{user:null,session:null},error:i};throw i}}async signInWithPassword(e){try{let t;if("email"in e){const{email:s,password:r,options:i}=e;t=await hs(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:ds})}else{if(!("phone"in e))throw new Ot("You must provide either an email or phone number and a password");{const{phone:s,password:r,options:i}=e;t=await hs(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},xform:ds})}}const{data:s,error:r}=t;return r?{data:{user:null,session:null},error:r}:s&&s.session&&s.user?(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r}):{data:{user:null,session:null},error:new xt}}catch(t){if(kt(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,s,r,i;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:null===(i=e.options)||void 0===i?void 0:i.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,(async()=>this._exchangeCodeForSession(e)))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,s,r,i,n,a,o,l,c,h,u,d;let f,p;if("message"in e)f=e.message,p=e.signature;else{const{chain:u,wallet:d,statement:g,options:m}=e;let y;if(Kt())if("object"==typeof d)y=d;else{const e=window;if(!("solana"in e)||"object"!=typeof e.solana||!("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");y=e.solana}else{if("object"!=typeof d||!(null==m?void 0:m.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");y=d}const v=new URL(null!==(t=null==m?void 0:m.url)&&void 0!==t?t:window.location.href);if("signIn"in y&&y.signIn){const e=await y.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null==m?void 0:m.signInWithSolana),{version:"1",domain:v.host,uri:v.href}),g?{statement:g}:null));let t;if(Array.isArray(e)&&e[0]&&"object"==typeof e[0])t=e[0];else{if(!(e&&"object"==typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"==typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"==typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in y&&"function"==typeof y.signMessage&&"publicKey"in y&&"object"==typeof y&&y.publicKey&&"toBase58"in y.publicKey&&"function"==typeof y.publicKey.toBase58))throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${v.host} wants you to sign in with your Solana account:`,y.publicKey.toBase58(),...g?["",g,""]:[""],"Version: 1",`URI: ${v.href}`,`Issued At: ${null!==(r=null===(s=null==m?void 0:m.signInWithSolana)||void 0===s?void 0:s.issuedAt)&&void 0!==r?r:(new Date).toISOString()}`,...(null===(i=null==m?void 0:m.signInWithSolana)||void 0===i?void 0:i.notBefore)?[`Not Before: ${m.signInWithSolana.notBefore}`]:[],...(null===(n=null==m?void 0:m.signInWithSolana)||void 0===n?void 0:n.expirationTime)?[`Expiration Time: ${m.signInWithSolana.expirationTime}`]:[],...(null===(a=null==m?void 0:m.signInWithSolana)||void 0===a?void 0:a.chainId)?[`Chain ID: ${m.signInWithSolana.chainId}`]:[],...(null===(o=null==m?void 0:m.signInWithSolana)||void 0===o?void 0:o.nonce)?[`Nonce: ${m.signInWithSolana.nonce}`]:[],...(null===(l=null==m?void 0:m.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${m.signInWithSolana.requestId}`]:[],...(null===(h=null===(c=null==m?void 0:m.signInWithSolana)||void 0===c?void 0:c.resources)||void 0===h?void 0:h.length)?["Resources",...m.signInWithSolana.resources.map((e=>`- ${e}`))]:[]].join("\n");const e=await y.signMessage((new TextEncoder).encode(f),"utf8");if(!(e&&e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:s}=await hs(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:Jt(p)},(null===(u=e.options)||void 0===u?void 0:u.captchaToken)?{gotrue_meta_security:{captcha_token:null===(d=e.options)||void 0===d?void 0:d.captchaToken}}:null),xform:us});if(s)throw s;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:s}):{data:{user:null,session:null},error:new xt}}catch(g){if(kt(g))return{data:{user:null,session:null},error:g};throw g}}async _exchangeCodeForSession(e){const t=await Yt(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(null!=t?t:"").split("/");try{const{data:t,error:i}=await hs(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:us});if(await Xt(this.storage,`${this.storageKey}-code-verifier`),i)throw i;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=r?r:null}),error:i}):{data:{user:null,session:null,redirectType:null},error:new xt}}catch(i){if(kt(i))return{data:{user:null,session:null,redirectType:null},error:i};throw i}}async signInWithIdToken(e){try{const{options:t,provider:s,token:r,access_token:i,nonce:n}=e,a=await hs(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:i,nonce:n,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:us}),{data:o,error:l}=a;return l?{data:{user:null,session:null},error:l}:o&&o.session&&o.user?(o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:o,error:l}):{data:{user:null,session:null},error:new xt}}catch(t){if(kt(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,s,r,i,n;try{if("email"in e){const{email:r,options:i}=e;let n=null,a=null;"pkce"===this.flowType&&([n,a]=await ss(this.storage,this.storageKey));const{error:o}=await hs(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!==(t=null==i?void 0:i.data)&&void 0!==t?t:{},create_user:null===(s=null==i?void 0:i.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:n,code_challenge_method:a},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:t,options:s}=e,{data:a,error:o}=await hs(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:{},create_user:null===(i=null==s?void 0:s.shouldCreateUser)||void 0===i||i,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},channel:null!==(n=null==s?void 0:s.channel)&&void 0!==n?n:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new Ot("You must provide either an email or phone number.")}catch(a){if(kt(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(e){var t,s;try{let r,i;"options"in e&&(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo,i=null===(s=e.options)||void 0===s?void 0:s.captchaToken);const{data:n,error:a}=await hs(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:i}}),redirectTo:r,xform:us});if(a)throw a;if(!n)throw new Error("An error occurred on token verification.");const o=n.session,l=n.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(r){if(kt(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,s,r;try{let i=null,n=null;return"pkce"===this.flowType&&([i,n]=await ss(this.storage,this.storageKey)),await hs(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==s?s:void 0}),(null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:i,code_challenge_method:n}),headers:this.headers,xform:ps})}catch(i){if(kt(i))return{data:null,error:i};throw i}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._reauthenticate()))}async _reauthenticate(){try{return await this._useSession((async e=>{const{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new Et;const{error:r}=await hs(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}}))}catch(e){if(kt(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:s,type:r,options:i}=e,{error:n}=await hs(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}},redirectTo:null==i?void 0:i.emailRedirectTo});return{data:{user:null,session:null},error:n}}if("phone"in e){const{phone:s,type:r,options:i}=e,{data:n,error:a}=await hs(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken}}});return{data:{user:null,session:null,messageId:null==n?void 0:n.message_id},error:a}}throw new Ot("You must provide either an email or phone number and a type")}catch(t){if(kt(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,(async()=>this._useSession((async e=>e))))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch(e){}})()),s}return await this.lock(`lock:${this.storageKey}`,e,(async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}}))}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await Yt(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const s=!!e.expires_at&&1e3*e.expires_at-Date.now()<mt;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,s,r)=>(t||"user"!==s||(t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,s,r))})}return{data:{session:e},error:null}}const{session:r,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{session:null},error:i}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,(async()=>await this._getUser()))}async _getUser(e){try{return e?await hs(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:fs}):await this._useSession((async e=>{var t,s,r;const{data:i,error:n}=e;if(n)throw n;return(null===(t=i.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await hs(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(r=null===(s=i.session)||void 0===s?void 0:s.access_token)&&void 0!==r?r:void 0,xform:fs}):{data:{user:null},error:new Et}}))}catch(t){if(kt(t))return function(e){return kt(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await Xt(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._updateUser(e,t)))}async _updateUser(e,t={}){try{return await this._useSession((async s=>{const{data:r,error:i}=s;if(i)throw i;if(!r.session)throw new Et;const n=r.session;let a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await ss(this.storage,this.storageKey));const{data:l,error:c}=await hs(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:n.access_token,xform:fs});if(c)throw c;return n.user=l.user,await this._saveSession(n),await this._notifyAllSubscribers("USER_UPDATED",n),{data:{user:n.user},error:null}}))}catch(s){if(kt(s))return{data:{user:null},error:s};throw s}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._setSession(e)))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new Et;const t=Date.now()/1e3;let s=t,r=!0,i=null;const{payload:n}=Qt(e.access_token);if(n.exp&&(s=n.exp,r=s<=t),r){const{session:t,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!t)return{data:{user:null,session:null},error:null};i=t}else{const{data:r,error:n}=await this._getUser(e.access_token);if(n)throw n;i={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(i),await this._notifyAllSubscribers("SIGNED_IN",i)}return{data:{user:i.user,session:i},error:null}}catch(t){if(kt(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._refreshSession(e)))}async _refreshSession(e){try{return await this._useSession((async t=>{var s;if(!e){const{data:r,error:i}=t;if(i)throw i;e=null!==(s=r.session)&&void 0!==s?s:void 0}if(!(null==e?void 0:e.refresh_token))throw new Et;const{session:r,error:i}=await this._callRefreshToken(e.refresh_token);return i?{data:{user:null,session:null},error:i}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}}))}catch(t){if(kt(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!Kt())throw new Pt("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Pt(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new $t("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new Pt("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new $t("No code detected.");const{data:t,error:s}=await this._exchangeCodeForSession(e.code);if(s)throw s;const r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:r,access_token:i,refresh_token:n,expires_in:a,expires_at:o,token_type:l}=e;if(!(i&&a&&n&&l))throw new Pt("No session defined in URL");const c=Math.round(Date.now()/1e3),h=parseInt(a);let u=c+h;o&&(u=parseInt(o));const{data:d,error:f}=await this._getUser(i);if(f)throw f;const p={provider_token:s,provider_refresh_token:r,access_token:i,expires_in:h,expires_at:u,refresh_token:n,token_type:l,user:d.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:p,redirectType:e.type},error:null}}catch(s){if(kt(s))return{data:{session:null,redirectType:null},error:s};throw s}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await Yt(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,(async()=>await this._signOut(e)))}async _signOut({scope:e}={scope:"global"}){return await this._useSession((async t=>{var s;const{data:r,error:i}=t;if(i)return{error:i};const n=null===(s=r.session)||void 0===s?void 0:s.access_token;if(n){const{error:t}=await this.admin.signOut(n,e);if(t&&(!function(e){return kt(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await Xt(this.storage,`${this.storageKey}-code-verifier`)),{error:null}}))}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)})),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,(async()=>{this._emitInitialSession(t)}))})(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession((async t=>{var s,r;try{const{data:{session:r},error:i}=t;if(i)throw i;await(null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(i){await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",i)}}))}async resetPasswordForEmail(e,t={}){let s=null,r=null;"pkce"===this.flowType&&([s,r]=await ss(this.storage,this.storageKey,!0));try{return await hs(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(i){if(kt(i))return{data:null,error:i};throw i}}async getUserIdentities(){var e;try{const{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(kt(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:s,error:r}=await this._useSession((async t=>{var s,r,i,n,a;const{data:o,error:l}=t;if(l)throw l;const c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(s=e.options)||void 0===s?void 0:s.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(i=e.options)||void 0===i?void 0:i.queryParams,skipBrowserRedirect:!0});return await hs(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(a=null===(n=o.session)||void 0===n?void 0:n.access_token)&&void 0!==a?a:void 0})}));if(r)throw r;return Kt()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==s?void 0:s.url),{data:{provider:e.provider,url:null==s?void 0:s.url},error:null}}catch(s){if(kt(s))return{data:{provider:e.provider,url:null},error:s};throw s}}async unlinkIdentity(e){try{return await this._useSession((async t=>{var s,r;const{data:i,error:n}=t;if(n)throw n;return await hs(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(r=null===(s=i.session)||void 0===s?void 0:s.access_token)&&void 0!==r?r:void 0})}))}catch(t){if(kt(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const i=Date.now();return await(s=async s=>(s>0&&await async function(e){return await new Promise((t=>{setTimeout((()=>t(null)),e)}))}(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await hs(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:us})),r=(e,t)=>{const s=200*Math.pow(2,e);return t&&It(t)&&Date.now()+s-i<gt},new Promise(((e,t)=>{(async()=>{for(let n=0;n<1/0;n++)try{const t=await s(n);if(!r(n,null,t))return void e(t)}catch(i){if(!r(n,i))return void t(i)}})()})))}catch(i){if(this._debug(t,"error",i),kt(i))return{data:{session:null,user:null},error:i};throw i}finally{this._debug(t,"end")}var s,r}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const s=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),Kt()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const s=await Yt(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s))return this._debug(t,"session is not valid"),void(null!==s&&await this._removeSession());const r=1e3*(null!==(e=s.expires_at)&&void 0!==e?e:1/0)-Date.now()<mt;if(this._debug(t,`session has${r?"":" not"} expired with margin of 90000s`),r){if(this.autoRefreshToken&&s.refresh_token){const{error:e}=await this._callRefreshToken(s.refresh_token);e&&(It(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){return void this._debug(t,"error",s)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new Et;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new Zt;const{data:t,error:s}=await this._refreshAccessToken(e);if(s)throw s;if(!t.session)throw new Et;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(i){if(this._debug(r,"error",i),kt(i)){const e={session:null,error:i};return It(i)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(s=this.refreshingDeferred)||void 0===s||s.reject(i),i}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,s=!0){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});const r=[],i=Array.from(this.stateChangeEmitters.values()).map((async s=>{try{await s.callback(e,t)}catch(i){r.push(i)}}));if(await Promise.all(i),r.length>0){for(let e=0;e<r.length;e+=1);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await Vt(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await Xt(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&Kt()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval((()=>this._autoRefreshTokenTick()),gt);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout((async()=>{await this.initializePromise,await this._autoRefreshTokenTick()}),0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,(async()=>{try{const t=Date.now();try{return await this._useSession((async e=>{const{data:{session:s}}=e;if(!s||!s.refresh_token||!s.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const r=Math.floor((1e3*s.expires_at-t)/gt);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),r<=3&&await this._callRefreshToken(s.refresh_token)}))}catch(e){}}finally{this._debug("#_autoRefreshTokenTick()","end")}}))}catch(e){if(!(e.isAcquireTimeout||e instanceof Ss))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!Kt()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,(async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")})))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){const r=[`provider=${encodeURIComponent(t)}`];if((null==s?void 0:s.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){const[e,t]=await ss(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(s.toString())}if(null==s?void 0:s.queryParams){const e=new URLSearchParams(s.queryParams);r.push(e.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession((async t=>{var s;const{data:r,error:i}=t;return i?{data:null,error:i}:await hs(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token})}))}catch(t){if(kt(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession((async t=>{var s,r;const{data:i,error:n}=t;if(n)return{data:null,error:n};const a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await hs(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null===(s=null==i?void 0:i.session)||void 0===s?void 0:s.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(r=null==o?void 0:o.totp)||void 0===r?void 0:r.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})}))}catch(t){if(kt(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async t=>{var s;const{data:r,error:i}=t;if(i)return{data:null,error:i};const{data:n,error:a}=await hs(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+n.expires_in},n)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",n),{data:n,error:a})}))}catch(t){if(kt(t))return{data:null,error:t};throw t}}))}async _challenge(e){return this._acquireLock(-1,(async()=>{try{return await this._useSession((async t=>{var s;const{data:r,error:i}=t;return i?{data:null,error:i}:await hs(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token})}))}catch(t){if(kt(t))return{data:null,error:t};throw t}}))}async _challengeAndVerify(e){const{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const s=(null==e?void 0:e.factors)||[],r=s.filter((e=>"totp"===e.factor_type&&"verified"===e.status)),i=s.filter((e=>"phone"===e.factor_type&&"verified"===e.status));return{data:{all:s,totp:r,phone:i},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,(async()=>await this._useSession((async e=>{var t,s;const{data:{session:r},error:i}=e;if(i)return{data:null,error:i};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:n}=Qt(r.access_token);let a=null;n.aal&&(a=n.aal);let o=a;(null!==(s=null===(t=r.user.factors)||void 0===t?void 0:t.filter((e=>"verified"===e.status)))&&void 0!==s?s:[]).length>0&&(o="aal2");return{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:n.amr||[]},error:null}}))))}async fetchJwk(e,t={keys:[]}){let s=t.keys.find((t=>t.kid===e));if(s)return s;if(s=this.jwks.keys.find((t=>t.kid===e)),s&&this.jwks_cached_at+6e5>Date.now())return s;const{data:r,error:i}=await hs(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(i)throw i;if(!r.keys||0===r.keys.length)throw new Rt("JWKS is empty");if(this.jwks=r,this.jwks_cached_at=Date.now(),s=r.keys.find((t=>t.kid===e)),!s)throw new Rt("No matching signing key found in JWKS");return s}async getClaims(e,t={keys:[]}){try{let s=e;if(!s){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};s=e.session.access_token}const{header:r,payload:i,signature:n,raw:{header:a,payload:o}}=Qt(s);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(i.exp),!r.kid||"HS256"===r.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(s);if(e)throw e;return{data:{claims:i,header:r,signature:n},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(r.alg),c=await this.fetchJwk(r.kid,t),h=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(await crypto.subtle.verify(l,h,n,Ft(`${a}.${o}`))))throw new Rt("Invalid JWT signature");return{data:{claims:i,header:r,signature:n},error:null}}catch(s){if(kt(s))return{data:null,error:s};throw s}}}Os.nextInstanceID=0;const Ps=Os;class $s extends Ps{constructor(e){super(e)}}var As=function(e,t,s,r){return new(s||(s=Promise))((function(i,n){function a(e){try{l(r.next(e))}catch(t){n(t)}}function o(e){try{l(r.throw(e))}catch(t){n(t)}}function l(e){var t;e.done?i(e.value):(t=e.value,t instanceof s?t:new s((function(e){e(t)}))).then(a,o)}l((r=r.apply(e,t||[])).next())}))};class Is{constructor(e,t,s){var r,i,n;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const a=(o=e).endsWith("/")?o:o+"/";var o;const l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,h=function(e,t){var s,r;const{db:i,auth:n,realtime:a,global:o}=e,{db:l,auth:c,realtime:h,global:u}=t,d={db:Object.assign(Object.assign({},l),i),auth:Object.assign(Object.assign({},c),n),realtime:Object.assign(Object.assign({},h),a),global:Object.assign(Object.assign(Object.assign({},u),o),{headers:Object.assign(Object.assign({},null!==(s=null==u?void 0:u.headers)&&void 0!==s?s:{}),null!==(r=null==o?void 0:o.headers)&&void 0!==r?r:{})}),accessToken:()=>ft(this,void 0,void 0,(function*(){return""}))};return e.accessToken?d.accessToken=e.accessToken:delete d.accessToken,d}(null!=s?s:{},{db:ot,realtime:ct,auth:Object.assign(Object.assign({},lt),{storageKey:c}),global:at});this.storageKey=null!==(r=h.auth.storageKey)&&void 0!==r?r:"",this.headers=null!==(i=h.global.headers)&&void 0!==i?i:{},h.accessToken?(this.accessToken=h.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(n=h.auth)&&void 0!==n?n:{},this.headers,h.global.fetch),this.fetch=dt(t,this._getAccessToken.bind(this),h.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},h.realtime)),this.rest=new te(new URL("rest/v1",l).href,{headers:this.headers,schema:h.db.schema,fetch:this.fetch}),h.accessToken||this._listenForAuthEvents()}get functions(){return new _(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new it(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return As(this,void 0,void 0,(function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return null!==(t=null===(e=s.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null}))}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:i,flowType:n,lock:a,debug:o},l,c){const h={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new $s({url:this.authUrl.href,headers:Object.assign(Object.assign({},h),l),storageKey:i,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:n,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new Ue(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange(((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)}))}_handleTokenChanged(e,t,s){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===s?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=s}}function Cs(){return new Is("https://zlnvivfgzgcjuspktadj.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbnZpdmZnemdjanVzcGt0YWRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2NjQyNTcsImV4cCI6MjA1OTI0MDI1N30.nOjNcWd-bneTXg5UHv2nlAc10l7Gi-cQTEm9V5yrqFY",e);var e}const Rs=({title:e,subtitle:t,buttonText:n})=>{const[a,o]=s.useState(!1),[l,c]=s.useState(""),[u,d]=s.useState(!1),[p,g]=s.useState(!1),[m,y]=s.useState(null),[v,w]=s.useState(null),_=f(),[b,k]=r({triggerOnce:!0,threshold:.2});if(s.useEffect((()=>{o(!0);(async()=>{const e=Cs(),{data:{session:t}}=await e.auth.getSession();if(t?.user){w(t.user);const{data:s}=await e.from("newsletter_subscribers").select("*").eq("email",t.user.email).eq("unsubscribed",!1).single();s&&d(!0)}})()}),[]),s.useEffect((()=>{a&&k&&_.start("visible")}),[_,k,a]),!a)return i.jsx("section",{className:"py-24 bg-design-background/50 relative overflow-hidden",children:i.jsx("div",{className:"max-w-4xl mx-auto px-4 text-center",children:i.jsx("p",{className:"text-design-muted-foreground",children:"Loading newsletter..."})})});const S={hidden:{opacity:0,y:20},visible:{opacity:1,y:0,transition:{duration:.5}}};return i.jsxs("section",{ref:b,id:"newsletter",className:"py-24 bg-design-background/50 relative overflow-hidden",children:[i.jsx("div",{className:"absolute inset-0 bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-20 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-10 z-0"}),[{icon:"✉️",delay:0,offsetX:-120,offsetY:-60},{icon:"🔔",delay:1.5,offsetX:150,offsetY:-80},{icon:"💰",delay:.8,offsetX:180,offsetY:30},{icon:"🎁",delay:2.2,offsetX:-150,offsetY:50},{icon:"🏷️",delay:3,offsetX:80,offsetY:-120}].map(((e,t)=>i.jsx(h.div,{className:"absolute text-2xl hidden md:block",style:{left:`calc(50% + ${e.offsetX}px)`,top:`calc(50% + ${e.offsetY}px)`},initial:{opacity:0,scale:0},animate:{opacity:[0,1,.8,0],scale:[0,1,1.2,0],y:[0,-20,-40,-60]},transition:{duration:4,delay:e.delay,repeat:1/0,repeatDelay:7},children:e.icon},t))),i.jsx("div",{className:"max-w-4xl mx-auto px-4 relative z-10",children:i.jsxs(h.div,{variants:{hidden:{opacity:0,y:30},visible:{opacity:1,y:0,transition:{duration:.6,staggerChildren:.2}}},initial:"hidden",animate:_,className:"bg-design-card/95 backdrop-blur-sm border border-design-border rounded-xl p-10 shadow-lg",children:[i.jsxs(h.div,{variants:S,className:"text-center mb-8",children:[i.jsx("h2",{className:"text-3xl md:text-[34px] font-normal text-design-foreground mb-4",children:e}),i.jsx("p",{className:"text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto",children:t})]}),i.jsxs(h.form,{variants:S,onSubmit:async e=>{if(e.preventDefault(),y(null),!l)return void y("Please enter your email address");if(!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(l))return void y("Please enter a valid email address");g(!0);const t="homepage",s=window.location.pathname,r=navigator.userAgent,i=new URLSearchParams(window.location.search),n={utm_source:i.get("utm_source"),utm_medium:i.get("utm_medium"),utm_campaign:i.get("utm_campaign"),utm_term:i.get("utm_term"),utm_content:i.get("utm_content")};try{if(v){const e=Cs(),{data:{session:i}}=await e.auth.getSession(),a=i?.access_token,o=await fetch("/api/newsletter/user-subscribe",{method:"POST",headers:{"Content-Type":"application/json",Authorization:`Bearer ${a}`},body:JSON.stringify({source:t,utm_params:n,device_info:r,initial_page:s})}),l=await o.json();if(g(!1),!o.ok)return void y(l.message||"Something went wrong. Please try again later.");d(!0),c("")}else{const e=await fetch("/api/newsletter/subscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:l,source:t,utm_params:n,device_info:r,initial_page:s})}),i=await e.json();if(g(!1),!e.ok)return void y(i.message||"Something went wrong. Please try again later.");d(!0),c("")}setTimeout((()=>{d(!1)}),5e3)}catch(a){g(!1),y("Something went wrong. Please try again later.")}},className:"max-w-xl mx-auto",children:[v&&u?i.jsxs(h.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"bg-design-success/10 border border-design-success/30 rounded-lg p-4 text-center",children:[i.jsx("p",{className:"font-medium text-design-success",children:"You're already subscribed to our newsletter!"}),i.jsxs("p",{className:"text-sm text-design-muted-foreground mt-1",children:["You can manage your subscription in your ",i.jsx("a",{href:"/account/notifications",className:"underline hover:text-design-foreground",children:"account settings"}),"."]})]}):i.jsxs(i.Fragment,{children:[i.jsxs("div",{className:"flex flex-col sm:flex-row gap-3 px-4 sm:px-0",children:[i.jsx("input",{type:"email",value:l,onChange:e=>c(e.target.value),placeholder:"Enter your email",className:"flex-1 h-14 px-5 rounded-full border border-design-border bg-design-background text-design-foreground focus:outline-none focus:ring-2 focus:ring-design-primary box-border text-base leading-normal min-h-[56px]",style:{lineHeight:"56px",paddingTop:0,paddingBottom:0},required:!0}),i.jsx(h.button,{whileHover:{scale:1.03,boxShadow:"0 10px 25px rgba(0, 0, 0, 0.1)"},whileTap:{scale:.97},type:"submit",disabled:p||u,className:`h-14 px-8 rounded-full font-bold transition-all duration-300 ${u?"bg-design-success text-white dark:text-black":"bg-design-primary text-white dark:text-black hover:bg-design-primary/90"} ${p?"opacity-70 cursor-not-allowed":""}`,children:p?i.jsxs("span",{className:"flex items-center justify-center",children:[i.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-5 w-5 text-white dark:text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[i.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),i.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing"]}):u?i.jsxs("span",{className:"flex items-center",children:[i.jsx("svg",{className:"mr-2 h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:i.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 13l4 4L19 7"})}),"Subscribed!"]}):n})]}),u&&i.jsx(h.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-center text-design-success font-medium",children:v?"Thanks for subscribing! You can manage your subscription in your account settings.":"Thanks for subscribing! Check your email to confirm."})]}),m&&i.jsx(h.div,{initial:{opacity:0,y:10},animate:{opacity:1,y:0},className:"mt-4 text-center text-design-destructive",children:m}),i.jsxs(h.div,{variants:S,className:"mt-6 text-sm text-center text-design-muted-foreground",children:[i.jsxs("div",{className:"flex flex-wrap justify-center gap-x-6 gap-y-2",children:[i.jsx("span",{children:"✓ Exclusive deals not found elsewhere"}),i.jsx("span",{children:"✓ No spam, unsubscribe anytime"}),i.jsx("span",{children:"✓ Weekly digest of best offers"})]}),i.jsxs("div",{className:"mt-4 text-xs",children:["By subscribing, you agree to our ",i.jsx("a",{href:"/privacy",className:"underline hover:text-design-foreground transition-colors",children:"Privacy Policy"}),"."]})]})]})]})})]})};export{Rs as NewsletterSection};
