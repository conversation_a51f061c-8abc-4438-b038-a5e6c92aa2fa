function e(o){var r,t,s="";if("string"==typeof o||"number"==typeof o)s+=o;else if("object"==typeof o)if(Array.isArray(o)){var a=o.length;for(r=0;r<a;r++)o[r]&&(t=e(o[r]))&&(s&&(s+=" "),s+=t)}else for(t in o)o[t]&&(s&&(s+=" "),s+=t);return s}function o(){for(var o,r,t=0,s="",a=arguments.length;t<a;t++)(o=arguments[t])&&(r=e(o))&&(s&&(s+=" "),s+=r);return s}const r=e=>{const o=n(e),{conflictingClassGroups:r,conflictingClassGroupModifiers:s}=e;return{getClassGroupId:e=>{const r=e.split("-");return""===r[0]&&1!==r.length&&r.shift(),t(r,o)||a(e)},getConflictingClassGroupIds:(e,o)=>{const t=r[e]||[];return o&&s[e]?[...t,...s[e]]:t}}},t=(e,o)=>{if(0===e.length)return o.classGroupId;const r=e[0],s=o.nextPart.get(r),a=s?t(e.slice(1),s):void 0;if(a)return a;if(0===o.validators.length)return;const n=e.join("-");return o.validators.find((({validator:e})=>e(n)))?.classGroupId},s=/^\[(.+)\]$/,a=e=>{if(s.test(e)){const o=s.exec(e)[1],r=o?.substring(0,o.indexOf(":"));if(r)return"arbitrary.."+r}},n=e=>{const{theme:o,classGroups:r}=e,t={nextPart:new Map,validators:[]};for(const s in r)l(r[s],t,s,o);return t},l=(e,o,r,t)=>{e.forEach((e=>{if("string"!=typeof e){if("function"==typeof e)return d(e)?void l(e(t),o,r,t):void o.validators.push({validator:e,classGroupId:r});Object.entries(e).forEach((([e,s])=>{l(s,i(o,e),r,t)}))}else{(""===e?o:i(o,e)).classGroupId=r}}))},i=(e,o)=>{let r=e;return o.split("-").forEach((e=>{r.nextPart.has(e)||r.nextPart.set(e,{nextPart:new Map,validators:[]}),r=r.nextPart.get(e)})),r},d=e=>e.isThemeGetter,c=e=>{if(e<1)return{get:()=>{},set:()=>{}};let o=0,r=new Map,t=new Map;const s=(s,a)=>{r.set(s,a),o++,o>e&&(o=0,t=r,r=new Map)};return{get(e){let o=r.get(e);return void 0!==o?o:void 0!==(o=t.get(e))?(s(e,o),o):void 0},set(e,o){r.has(e)?r.set(e,o):s(e,o)}}},m=e=>{const{prefix:o,experimentalParseClassName:r}=e;let t=e=>{const o=[];let r,t=0,s=0,a=0;for(let i=0;i<e.length;i++){let n=e[i];if(0===t&&0===s){if(":"===n){o.push(e.slice(a,i)),a=i+1;continue}if("/"===n){r=i;continue}}"["===n?t++:"]"===n?t--:"("===n?s++:")"===n&&s--}const n=0===o.length?e:e.substring(a),l=p(n);return{modifiers:o,hasImportantModifier:l!==n,baseClassName:l,maybePostfixModifierPosition:r&&r>a?r-a:void 0}};if(o){const e=o+":",r=t;t=o=>o.startsWith(e)?r(o.substring(e.length)):{isExternal:!0,modifiers:[],hasImportantModifier:!1,baseClassName:o,maybePostfixModifierPosition:void 0}}if(r){const e=t;t=o=>r({className:o,parseClassName:e})}return t},p=e=>e.endsWith("!")?e.substring(0,e.length-1):e.startsWith("!")?e.substring(1):e,u=e=>{const o=Object.fromEntries(e.orderSensitiveModifiers.map((e=>[e,!0])));return e=>{if(e.length<=1)return e;const r=[];let t=[];return e.forEach((e=>{"["===e[0]||o[e]?(r.push(...t.sort(),e),t=[]):t.push(e)})),r.push(...t.sort()),r}},f=/\s+/;function b(){let e,o,r=0,t="";for(;r<arguments.length;)(e=arguments[r++])&&(o=g(e))&&(t&&(t+=" "),t+=o);return t}const g=e=>{if("string"==typeof e)return e;let o,r="";for(let t=0;t<e.length;t++)e[t]&&(o=g(e[t]))&&(r&&(r+=" "),r+=o);return r};function h(e,...o){let t,s,a,n=function(i){const d=o.reduce(((e,o)=>o(e)),e());return t=(e=>({cache:c(e.cacheSize),parseClassName:m(e),sortModifiers:u(e),...r(e)}))(d),s=t.cache.get,a=t.cache.set,n=l,l(i)};function l(e){const o=s(e);if(o)return o;const r=((e,o)=>{const{parseClassName:r,getClassGroupId:t,getConflictingClassGroupIds:s,sortModifiers:a}=o,n=[],l=e.trim().split(f);let i="";for(let d=l.length-1;d>=0;d-=1){const e=l[d],{isExternal:o,modifiers:c,hasImportantModifier:m,baseClassName:p,maybePostfixModifierPosition:u}=r(e);if(o){i=e+(i.length>0?" "+i:i);continue}let f=!!u,b=t(f?p.substring(0,u):p);if(!b){if(!f){i=e+(i.length>0?" "+i:i);continue}if(b=t(p),!b){i=e+(i.length>0?" "+i:i);continue}f=!1}const g=a(c).join(":"),h=m?g+"!":g,k=h+b;if(n.includes(k))continue;n.push(k);const x=s(b,f);for(let r=0;r<x.length;++r){const e=x[r];n.push(h+e)}i=e+(i.length>0?" "+i:i)}return i})(e,t);return a(e,r),r}return function(){return n(b.apply(null,arguments))}}const k=e=>{const o=o=>o[e]||[];return o.isThemeGetter=!0,o},x=/^\[(?:(\w[\w-]*):)?(.+)\]$/i,w=/^\((?:(\w[\w-]*):)?(.+)\)$/i,v=/^\d+\/\d+$/,y=/^(\d+(\.\d+)?)?(xs|sm|md|lg|xl)$/,z=/\d+(%|px|r?em|[sdl]?v([hwib]|min|max)|pt|pc|in|cm|mm|cap|ch|ex|r?lh|cq(w|h|i|b|min|max))|\b(calc|min|max|clamp)\(.+\)|^0$/,j=/^(rgba?|hsla?|hwb|(ok)?(lab|lch)|color-mix)\(.+\)$/,C=/^(inset_)?-?((\d+)?\.?(\d+)[a-z]+|0)_-?((\d+)?\.?(\d+)[a-z]+|0)/,M=/^(url|image|image-set|cross-fade|element|(repeating-)?(linear|radial|conic)-gradient)\(.+\)$/,N=e=>v.test(e),G=e=>!!e&&!Number.isNaN(Number(e)),P=e=>!!e&&Number.isInteger(Number(e)),I=e=>e.endsWith("%")&&G(e.slice(0,-1)),$=e=>y.test(e),E=()=>!0,O=e=>z.test(e)&&!j.test(e),A=()=>!1,S=e=>C.test(e),W=e=>M.test(e),q=e=>!V(e)&&!J(e),T=e=>Y(e,re,A),V=e=>x.test(e),_=e=>Y(e,te,O),B=e=>Y(e,se,G),D=e=>Y(e,ee,A),F=e=>Y(e,oe,W),H=e=>Y(e,ne,S),J=e=>w.test(e),K=e=>Z(e,te),L=e=>Z(e,ae),Q=e=>Z(e,ee),R=e=>Z(e,re),U=e=>Z(e,oe),X=e=>Z(e,ne,!0),Y=(e,o,r)=>{const t=x.exec(e);return!!t&&(t[1]?o(t[1]):r(t[2]))},Z=(e,o,r=!1)=>{const t=w.exec(e);return!!t&&(t[1]?o(t[1]):r)},ee=e=>"position"===e||"percentage"===e,oe=e=>"image"===e||"url"===e,re=e=>"length"===e||"size"===e||"bg-size"===e,te=e=>"length"===e,se=e=>"number"===e,ae=e=>"family-name"===e,ne=e=>"shadow"===e,le=h((()=>{const e=k("color"),o=k("font"),r=k("text"),t=k("font-weight"),s=k("tracking"),a=k("leading"),n=k("breakpoint"),l=k("container"),i=k("spacing"),d=k("radius"),c=k("shadow"),m=k("inset-shadow"),p=k("text-shadow"),u=k("drop-shadow"),f=k("blur"),b=k("perspective"),g=k("aspect"),h=k("ease"),x=k("animate"),w=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",J,V],v=()=>[J,V,i],y=()=>[N,"full","auto",...v()],z=()=>[P,"none","subgrid",J,V],j=()=>["auto",{span:["full",P,J,V]},P,J,V],C=()=>[P,"auto",J,V],M=()=>["auto","min","max","fr",J,V],O=()=>["auto",...v()],A=()=>[N,"auto","full","dvw","dvh","lvw","lvh","svw","svh","min","max","fit",...v()],S=()=>[e,J,V],W=()=>["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom",Q,D,{position:[J,V]}],Y=()=>["auto","cover","contain",R,T,{size:[J,V]}],Z=()=>[I,K,_],ee=()=>["","none","full",d,J,V],oe=()=>["",G,K,_],re=()=>[G,I,Q,D],te=()=>["","none",f,J,V],se=()=>["none",G,J,V],ae=()=>["none",G,J,V],ne=()=>[G,J,V],le=()=>[N,"full",...v()];return{cacheSize:500,theme:{animate:["spin","ping","pulse","bounce"],aspect:["video"],blur:[$],breakpoint:[$],color:[E],container:[$],"drop-shadow":[$],ease:["in","out","in-out"],font:[q],"font-weight":["thin","extralight","light","normal","medium","semibold","bold","extrabold","black"],"inset-shadow":[$],leading:["none","tight","snug","normal","relaxed","loose"],perspective:["dramatic","near","normal","midrange","distant","none"],radius:[$],shadow:[$],spacing:["px",G],text:[$],"text-shadow":[$],tracking:["tighter","tight","normal","wide","wider","widest"]},classGroups:{aspect:[{aspect:["auto","square",N,V,J,g]}],container:["container"],columns:[{columns:[G,V,J,l]}],"break-after":[{"break-after":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-before":[{"break-before":["auto","avoid","all","avoid-page","page","left","right","column"]}],"break-inside":[{"break-inside":["auto","avoid","avoid-page","avoid-column"]}],"box-decoration":[{"box-decoration":["slice","clone"]}],box:[{box:["border","content"]}],display:["block","inline-block","inline","flex","inline-flex","table","inline-table","table-caption","table-cell","table-column","table-column-group","table-footer-group","table-header-group","table-row-group","table-row","flow-root","grid","inline-grid","contents","list-item","hidden"],sr:["sr-only","not-sr-only"],float:[{float:["right","left","none","start","end"]}],clear:[{clear:["left","right","both","none","start","end"]}],isolation:["isolate","isolation-auto"],"object-fit":[{object:["contain","cover","fill","none","scale-down"]}],"object-position":[{object:w()}],overflow:[{overflow:["auto","hidden","clip","visible","scroll"]}],"overflow-x":[{"overflow-x":["auto","hidden","clip","visible","scroll"]}],"overflow-y":[{"overflow-y":["auto","hidden","clip","visible","scroll"]}],overscroll:[{overscroll:["auto","contain","none"]}],"overscroll-x":[{"overscroll-x":["auto","contain","none"]}],"overscroll-y":[{"overscroll-y":["auto","contain","none"]}],position:["static","fixed","absolute","relative","sticky"],inset:[{inset:y()}],"inset-x":[{"inset-x":y()}],"inset-y":[{"inset-y":y()}],start:[{start:y()}],end:[{end:y()}],top:[{top:y()}],right:[{right:y()}],bottom:[{bottom:y()}],left:[{left:y()}],visibility:["visible","invisible","collapse"],z:[{z:[P,"auto",J,V]}],basis:[{basis:[N,"full","auto",l,...v()]}],"flex-direction":[{flex:["row","row-reverse","col","col-reverse"]}],"flex-wrap":[{flex:["nowrap","wrap","wrap-reverse"]}],flex:[{flex:[G,N,"auto","initial","none",V]}],grow:[{grow:["",G,J,V]}],shrink:[{shrink:["",G,J,V]}],order:[{order:[P,"first","last","none",J,V]}],"grid-cols":[{"grid-cols":z()}],"col-start-end":[{col:j()}],"col-start":[{"col-start":C()}],"col-end":[{"col-end":C()}],"grid-rows":[{"grid-rows":z()}],"row-start-end":[{row:j()}],"row-start":[{"row-start":C()}],"row-end":[{"row-end":C()}],"grid-flow":[{"grid-flow":["row","col","dense","row-dense","col-dense"]}],"auto-cols":[{"auto-cols":M()}],"auto-rows":[{"auto-rows":M()}],gap:[{gap:v()}],"gap-x":[{"gap-x":v()}],"gap-y":[{"gap-y":v()}],"justify-content":[{justify:["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe","normal"]}],"justify-items":[{"justify-items":["start","end","center","stretch","center-safe","end-safe","normal"]}],"justify-self":[{"justify-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],"align-content":[{content:["normal","start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"align-items":[{items:["start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"align-self":[{self:["auto","start","end","center","stretch","center-safe","end-safe",{baseline:["","last"]}]}],"place-content":[{"place-content":["start","end","center","between","around","evenly","stretch","baseline","center-safe","end-safe"]}],"place-items":[{"place-items":["start","end","center","stretch","center-safe","end-safe","baseline"]}],"place-self":[{"place-self":["auto","start","end","center","stretch","center-safe","end-safe"]}],p:[{p:v()}],px:[{px:v()}],py:[{py:v()}],ps:[{ps:v()}],pe:[{pe:v()}],pt:[{pt:v()}],pr:[{pr:v()}],pb:[{pb:v()}],pl:[{pl:v()}],m:[{m:O()}],mx:[{mx:O()}],my:[{my:O()}],ms:[{ms:O()}],me:[{me:O()}],mt:[{mt:O()}],mr:[{mr:O()}],mb:[{mb:O()}],ml:[{ml:O()}],"space-x":[{"space-x":v()}],"space-x-reverse":["space-x-reverse"],"space-y":[{"space-y":v()}],"space-y-reverse":["space-y-reverse"],size:[{size:A()}],w:[{w:[l,"screen",...A()]}],"min-w":[{"min-w":[l,"screen","none",...A()]}],"max-w":[{"max-w":[l,"screen","none","prose",{screen:[n]},...A()]}],h:[{h:["screen","lh",...A()]}],"min-h":[{"min-h":["screen","lh","none",...A()]}],"max-h":[{"max-h":["screen","lh",...A()]}],"font-size":[{text:["base",r,K,_]}],"font-smoothing":["antialiased","subpixel-antialiased"],"font-style":["italic","not-italic"],"font-weight":[{font:[t,J,B]}],"font-stretch":[{"font-stretch":["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded",I,V]}],"font-family":[{font:[L,V,o]}],"fvn-normal":["normal-nums"],"fvn-ordinal":["ordinal"],"fvn-slashed-zero":["slashed-zero"],"fvn-figure":["lining-nums","oldstyle-nums"],"fvn-spacing":["proportional-nums","tabular-nums"],"fvn-fraction":["diagonal-fractions","stacked-fractions"],tracking:[{tracking:[s,J,V]}],"line-clamp":[{"line-clamp":[G,"none",J,B]}],leading:[{leading:[a,...v()]}],"list-image":[{"list-image":["none",J,V]}],"list-style-position":[{list:["inside","outside"]}],"list-style-type":[{list:["disc","decimal","none",J,V]}],"text-alignment":[{text:["left","center","right","justify","start","end"]}],"placeholder-color":[{placeholder:S()}],"text-color":[{text:S()}],"text-decoration":["underline","overline","line-through","no-underline"],"text-decoration-style":[{decoration:["solid","dashed","dotted","double","wavy"]}],"text-decoration-thickness":[{decoration:[G,"from-font","auto",J,_]}],"text-decoration-color":[{decoration:S()}],"underline-offset":[{"underline-offset":[G,"auto",J,V]}],"text-transform":["uppercase","lowercase","capitalize","normal-case"],"text-overflow":["truncate","text-ellipsis","text-clip"],"text-wrap":[{text:["wrap","nowrap","balance","pretty"]}],indent:[{indent:v()}],"vertical-align":[{align:["baseline","top","middle","bottom","text-top","text-bottom","sub","super",J,V]}],whitespace:[{whitespace:["normal","nowrap","pre","pre-line","pre-wrap","break-spaces"]}],break:[{break:["normal","words","all","keep"]}],wrap:[{wrap:["break-word","anywhere","normal"]}],hyphens:[{hyphens:["none","manual","auto"]}],content:[{content:["none",J,V]}],"bg-attachment":[{bg:["fixed","local","scroll"]}],"bg-clip":[{"bg-clip":["border","padding","content","text"]}],"bg-origin":[{"bg-origin":["border","padding","content"]}],"bg-position":[{bg:W()}],"bg-repeat":[{bg:["no-repeat",{repeat:["","x","y","space","round"]}]}],"bg-size":[{bg:Y()}],"bg-image":[{bg:["none",{linear:[{to:["t","tr","r","br","b","bl","l","tl"]},P,J,V],radial:["",J,V],conic:[P,J,V]},U,F]}],"bg-color":[{bg:S()}],"gradient-from-pos":[{from:Z()}],"gradient-via-pos":[{via:Z()}],"gradient-to-pos":[{to:Z()}],"gradient-from":[{from:S()}],"gradient-via":[{via:S()}],"gradient-to":[{to:S()}],rounded:[{rounded:ee()}],"rounded-s":[{"rounded-s":ee()}],"rounded-e":[{"rounded-e":ee()}],"rounded-t":[{"rounded-t":ee()}],"rounded-r":[{"rounded-r":ee()}],"rounded-b":[{"rounded-b":ee()}],"rounded-l":[{"rounded-l":ee()}],"rounded-ss":[{"rounded-ss":ee()}],"rounded-se":[{"rounded-se":ee()}],"rounded-ee":[{"rounded-ee":ee()}],"rounded-es":[{"rounded-es":ee()}],"rounded-tl":[{"rounded-tl":ee()}],"rounded-tr":[{"rounded-tr":ee()}],"rounded-br":[{"rounded-br":ee()}],"rounded-bl":[{"rounded-bl":ee()}],"border-w":[{border:oe()}],"border-w-x":[{"border-x":oe()}],"border-w-y":[{"border-y":oe()}],"border-w-s":[{"border-s":oe()}],"border-w-e":[{"border-e":oe()}],"border-w-t":[{"border-t":oe()}],"border-w-r":[{"border-r":oe()}],"border-w-b":[{"border-b":oe()}],"border-w-l":[{"border-l":oe()}],"divide-x":[{"divide-x":oe()}],"divide-x-reverse":["divide-x-reverse"],"divide-y":[{"divide-y":oe()}],"divide-y-reverse":["divide-y-reverse"],"border-style":[{border:["solid","dashed","dotted","double","hidden","none"]}],"divide-style":[{divide:["solid","dashed","dotted","double","hidden","none"]}],"border-color":[{border:S()}],"border-color-x":[{"border-x":S()}],"border-color-y":[{"border-y":S()}],"border-color-s":[{"border-s":S()}],"border-color-e":[{"border-e":S()}],"border-color-t":[{"border-t":S()}],"border-color-r":[{"border-r":S()}],"border-color-b":[{"border-b":S()}],"border-color-l":[{"border-l":S()}],"divide-color":[{divide:S()}],"outline-style":[{outline:["solid","dashed","dotted","double","none","hidden"]}],"outline-offset":[{"outline-offset":[G,J,V]}],"outline-w":[{outline:["",G,K,_]}],"outline-color":[{outline:S()}],shadow:[{shadow:["","none",c,X,H]}],"shadow-color":[{shadow:S()}],"inset-shadow":[{"inset-shadow":["none",m,X,H]}],"inset-shadow-color":[{"inset-shadow":S()}],"ring-w":[{ring:oe()}],"ring-w-inset":["ring-inset"],"ring-color":[{ring:S()}],"ring-offset-w":[{"ring-offset":[G,_]}],"ring-offset-color":[{"ring-offset":S()}],"inset-ring-w":[{"inset-ring":oe()}],"inset-ring-color":[{"inset-ring":S()}],"text-shadow":[{"text-shadow":["none",p,X,H]}],"text-shadow-color":[{"text-shadow":S()}],opacity:[{opacity:[G,J,V]}],"mix-blend":[{"mix-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity","plus-darker","plus-lighter"]}],"bg-blend":[{"bg-blend":["normal","multiply","screen","overlay","darken","lighten","color-dodge","color-burn","hard-light","soft-light","difference","exclusion","hue","saturation","color","luminosity"]}],"mask-clip":[{"mask-clip":["border","padding","content","fill","stroke","view"]},"mask-no-clip"],"mask-composite":[{mask:["add","subtract","intersect","exclude"]}],"mask-image-linear-pos":[{"mask-linear":[G]}],"mask-image-linear-from-pos":[{"mask-linear-from":re()}],"mask-image-linear-to-pos":[{"mask-linear-to":re()}],"mask-image-linear-from-color":[{"mask-linear-from":S()}],"mask-image-linear-to-color":[{"mask-linear-to":S()}],"mask-image-t-from-pos":[{"mask-t-from":re()}],"mask-image-t-to-pos":[{"mask-t-to":re()}],"mask-image-t-from-color":[{"mask-t-from":S()}],"mask-image-t-to-color":[{"mask-t-to":S()}],"mask-image-r-from-pos":[{"mask-r-from":re()}],"mask-image-r-to-pos":[{"mask-r-to":re()}],"mask-image-r-from-color":[{"mask-r-from":S()}],"mask-image-r-to-color":[{"mask-r-to":S()}],"mask-image-b-from-pos":[{"mask-b-from":re()}],"mask-image-b-to-pos":[{"mask-b-to":re()}],"mask-image-b-from-color":[{"mask-b-from":S()}],"mask-image-b-to-color":[{"mask-b-to":S()}],"mask-image-l-from-pos":[{"mask-l-from":re()}],"mask-image-l-to-pos":[{"mask-l-to":re()}],"mask-image-l-from-color":[{"mask-l-from":S()}],"mask-image-l-to-color":[{"mask-l-to":S()}],"mask-image-x-from-pos":[{"mask-x-from":re()}],"mask-image-x-to-pos":[{"mask-x-to":re()}],"mask-image-x-from-color":[{"mask-x-from":S()}],"mask-image-x-to-color":[{"mask-x-to":S()}],"mask-image-y-from-pos":[{"mask-y-from":re()}],"mask-image-y-to-pos":[{"mask-y-to":re()}],"mask-image-y-from-color":[{"mask-y-from":S()}],"mask-image-y-to-color":[{"mask-y-to":S()}],"mask-image-radial":[{"mask-radial":[J,V]}],"mask-image-radial-from-pos":[{"mask-radial-from":re()}],"mask-image-radial-to-pos":[{"mask-radial-to":re()}],"mask-image-radial-from-color":[{"mask-radial-from":S()}],"mask-image-radial-to-color":[{"mask-radial-to":S()}],"mask-image-radial-shape":[{"mask-radial":["circle","ellipse"]}],"mask-image-radial-size":[{"mask-radial":[{closest:["side","corner"],farthest:["side","corner"]}]}],"mask-image-radial-pos":[{"mask-radial-at":["center","top","bottom","left","right","top-left","left-top","top-right","right-top","bottom-right","right-bottom","bottom-left","left-bottom"]}],"mask-image-conic-pos":[{"mask-conic":[G]}],"mask-image-conic-from-pos":[{"mask-conic-from":re()}],"mask-image-conic-to-pos":[{"mask-conic-to":re()}],"mask-image-conic-from-color":[{"mask-conic-from":S()}],"mask-image-conic-to-color":[{"mask-conic-to":S()}],"mask-mode":[{mask:["alpha","luminance","match"]}],"mask-origin":[{"mask-origin":["border","padding","content","fill","stroke","view"]}],"mask-position":[{mask:W()}],"mask-repeat":[{mask:["no-repeat",{repeat:["","x","y","space","round"]}]}],"mask-size":[{mask:Y()}],"mask-type":[{"mask-type":["alpha","luminance"]}],"mask-image":[{mask:["none",J,V]}],filter:[{filter:["","none",J,V]}],blur:[{blur:te()}],brightness:[{brightness:[G,J,V]}],contrast:[{contrast:[G,J,V]}],"drop-shadow":[{"drop-shadow":["","none",u,X,H]}],"drop-shadow-color":[{"drop-shadow":S()}],grayscale:[{grayscale:["",G,J,V]}],"hue-rotate":[{"hue-rotate":[G,J,V]}],invert:[{invert:["",G,J,V]}],saturate:[{saturate:[G,J,V]}],sepia:[{sepia:["",G,J,V]}],"backdrop-filter":[{"backdrop-filter":["","none",J,V]}],"backdrop-blur":[{"backdrop-blur":te()}],"backdrop-brightness":[{"backdrop-brightness":[G,J,V]}],"backdrop-contrast":[{"backdrop-contrast":[G,J,V]}],"backdrop-grayscale":[{"backdrop-grayscale":["",G,J,V]}],"backdrop-hue-rotate":[{"backdrop-hue-rotate":[G,J,V]}],"backdrop-invert":[{"backdrop-invert":["",G,J,V]}],"backdrop-opacity":[{"backdrop-opacity":[G,J,V]}],"backdrop-saturate":[{"backdrop-saturate":[G,J,V]}],"backdrop-sepia":[{"backdrop-sepia":["",G,J,V]}],"border-collapse":[{border:["collapse","separate"]}],"border-spacing":[{"border-spacing":v()}],"border-spacing-x":[{"border-spacing-x":v()}],"border-spacing-y":[{"border-spacing-y":v()}],"table-layout":[{table:["auto","fixed"]}],caption:[{caption:["top","bottom"]}],transition:[{transition:["","all","colors","opacity","shadow","transform","none",J,V]}],"transition-behavior":[{transition:["normal","discrete"]}],duration:[{duration:[G,"initial",J,V]}],ease:[{ease:["linear","initial",h,J,V]}],delay:[{delay:[G,J,V]}],animate:[{animate:["none",x,J,V]}],backface:[{backface:["hidden","visible"]}],perspective:[{perspective:[b,J,V]}],"perspective-origin":[{"perspective-origin":w()}],rotate:[{rotate:se()}],"rotate-x":[{"rotate-x":se()}],"rotate-y":[{"rotate-y":se()}],"rotate-z":[{"rotate-z":se()}],scale:[{scale:ae()}],"scale-x":[{"scale-x":ae()}],"scale-y":[{"scale-y":ae()}],"scale-z":[{"scale-z":ae()}],"scale-3d":["scale-3d"],skew:[{skew:ne()}],"skew-x":[{"skew-x":ne()}],"skew-y":[{"skew-y":ne()}],transform:[{transform:[J,V,"","none","gpu","cpu"]}],"transform-origin":[{origin:w()}],"transform-style":[{transform:["3d","flat"]}],translate:[{translate:le()}],"translate-x":[{"translate-x":le()}],"translate-y":[{"translate-y":le()}],"translate-z":[{"translate-z":le()}],"translate-none":["translate-none"],accent:[{accent:S()}],appearance:[{appearance:["none","auto"]}],"caret-color":[{caret:S()}],"color-scheme":[{scheme:["normal","dark","light","light-dark","only-dark","only-light"]}],cursor:[{cursor:["auto","default","pointer","wait","text","move","help","not-allowed","none","context-menu","progress","cell","crosshair","vertical-text","alias","copy","no-drop","grab","grabbing","all-scroll","col-resize","row-resize","n-resize","e-resize","s-resize","w-resize","ne-resize","nw-resize","se-resize","sw-resize","ew-resize","ns-resize","nesw-resize","nwse-resize","zoom-in","zoom-out",J,V]}],"field-sizing":[{"field-sizing":["fixed","content"]}],"pointer-events":[{"pointer-events":["auto","none"]}],resize:[{resize:["none","","y","x"]}],"scroll-behavior":[{scroll:["auto","smooth"]}],"scroll-m":[{"scroll-m":v()}],"scroll-mx":[{"scroll-mx":v()}],"scroll-my":[{"scroll-my":v()}],"scroll-ms":[{"scroll-ms":v()}],"scroll-me":[{"scroll-me":v()}],"scroll-mt":[{"scroll-mt":v()}],"scroll-mr":[{"scroll-mr":v()}],"scroll-mb":[{"scroll-mb":v()}],"scroll-ml":[{"scroll-ml":v()}],"scroll-p":[{"scroll-p":v()}],"scroll-px":[{"scroll-px":v()}],"scroll-py":[{"scroll-py":v()}],"scroll-ps":[{"scroll-ps":v()}],"scroll-pe":[{"scroll-pe":v()}],"scroll-pt":[{"scroll-pt":v()}],"scroll-pr":[{"scroll-pr":v()}],"scroll-pb":[{"scroll-pb":v()}],"scroll-pl":[{"scroll-pl":v()}],"snap-align":[{snap:["start","end","center","align-none"]}],"snap-stop":[{snap:["normal","always"]}],"snap-type":[{snap:["none","x","y","both"]}],"snap-strictness":[{snap:["mandatory","proximity"]}],touch:[{touch:["auto","none","manipulation"]}],"touch-x":[{"touch-pan":["x","left","right"]}],"touch-y":[{"touch-pan":["y","up","down"]}],"touch-pz":["touch-pinch-zoom"],select:[{select:["none","text","all","auto"]}],"will-change":[{"will-change":["auto","scroll","contents","transform",J,V]}],fill:[{fill:["none",...S()]}],"stroke-w":[{stroke:[G,K,_,B]}],stroke:[{stroke:["none",...S()]}],"forced-color-adjust":[{"forced-color-adjust":["auto","none"]}]},conflictingClassGroups:{overflow:["overflow-x","overflow-y"],overscroll:["overscroll-x","overscroll-y"],inset:["inset-x","inset-y","start","end","top","right","bottom","left"],"inset-x":["right","left"],"inset-y":["top","bottom"],flex:["basis","grow","shrink"],gap:["gap-x","gap-y"],p:["px","py","ps","pe","pt","pr","pb","pl"],px:["pr","pl"],py:["pt","pb"],m:["mx","my","ms","me","mt","mr","mb","ml"],mx:["mr","ml"],my:["mt","mb"],size:["w","h"],"font-size":["leading"],"fvn-normal":["fvn-ordinal","fvn-slashed-zero","fvn-figure","fvn-spacing","fvn-fraction"],"fvn-ordinal":["fvn-normal"],"fvn-slashed-zero":["fvn-normal"],"fvn-figure":["fvn-normal"],"fvn-spacing":["fvn-normal"],"fvn-fraction":["fvn-normal"],"line-clamp":["display","overflow"],rounded:["rounded-s","rounded-e","rounded-t","rounded-r","rounded-b","rounded-l","rounded-ss","rounded-se","rounded-ee","rounded-es","rounded-tl","rounded-tr","rounded-br","rounded-bl"],"rounded-s":["rounded-ss","rounded-es"],"rounded-e":["rounded-se","rounded-ee"],"rounded-t":["rounded-tl","rounded-tr"],"rounded-r":["rounded-tr","rounded-br"],"rounded-b":["rounded-br","rounded-bl"],"rounded-l":["rounded-tl","rounded-bl"],"border-spacing":["border-spacing-x","border-spacing-y"],"border-w":["border-w-x","border-w-y","border-w-s","border-w-e","border-w-t","border-w-r","border-w-b","border-w-l"],"border-w-x":["border-w-r","border-w-l"],"border-w-y":["border-w-t","border-w-b"],"border-color":["border-color-x","border-color-y","border-color-s","border-color-e","border-color-t","border-color-r","border-color-b","border-color-l"],"border-color-x":["border-color-r","border-color-l"],"border-color-y":["border-color-t","border-color-b"],translate:["translate-x","translate-y","translate-none"],"translate-none":["translate","translate-x","translate-y","translate-z"],"scroll-m":["scroll-mx","scroll-my","scroll-ms","scroll-me","scroll-mt","scroll-mr","scroll-mb","scroll-ml"],"scroll-mx":["scroll-mr","scroll-ml"],"scroll-my":["scroll-mt","scroll-mb"],"scroll-p":["scroll-px","scroll-py","scroll-ps","scroll-pe","scroll-pt","scroll-pr","scroll-pb","scroll-pl"],"scroll-px":["scroll-pr","scroll-pl"],"scroll-py":["scroll-pt","scroll-pb"],touch:["touch-x","touch-y","touch-pz"],"touch-x":["touch"],"touch-y":["touch"],"touch-pz":["touch"]},conflictingClassGroupModifiers:{"font-size":["leading"]},orderSensitiveModifiers:["*","**","after","backdrop","before","details-content","file","first-letter","first-line","marker","placeholder","selection"]}})),ie=e=>"boolean"==typeof e?`${e}`:0===e?"0":e,de=o,ce=(e,o)=>r=>{var t;if(null==(null==o?void 0:o.variants))return de(e,null==r?void 0:r.class,null==r?void 0:r.className);const{variants:s,defaultVariants:a}=o,n=Object.keys(s).map((e=>{const o=null==r?void 0:r[e],t=null==a?void 0:a[e];if(null===o)return null;const n=ie(o)||ie(t);return s[e][n]})),l=r&&Object.entries(r).reduce(((e,o)=>{let[r,t]=o;return void 0===t||(e[r]=t),e}),{}),i=null==o||null===(t=o.compoundVariants)||void 0===t?void 0:t.reduce(((e,o)=>{let{class:r,className:t,...s}=o;return Object.entries(s).every((e=>{let[o,r]=e;return Array.isArray(r)?r.includes({...a,...l}[o]):{...a,...l}[o]===r}))?[...e,r,t]:e}),[]);return de(e,n,i,null==r?void 0:r.class,null==r?void 0:r.className)};export{ce as a,o as c,le as t};
