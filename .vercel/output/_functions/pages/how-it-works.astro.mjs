import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$InfoPageLayout } from '../assets/js/InfoPageLayout-Bg861p5k.js';
import { $ as $$InfoPageHeader, a as $$InfoPageSection } from '../assets/js/InfoPageSection-CrV_q83G.js';
import { G as GlowingButton } from '../assets/js/app/design-system-CUssmfny.js';
export { renderers } from '../renderers.mjs';

const $$HowItWorks = createComponent(($$result, $$props, $$slots) => {
  const title = "How VapeHybrid Works | Find & Verify Vape Deals";
  const description = "Learn how VapeHybrid finds, verifies, and shares the best vape deals and coupons. Our expert team tests every deal to ensure you get genuine savings.";
  return renderTemplate`${renderComponent($$result, "InfoPageLayout", $$InfoPageLayout, { "title": title, "description": description, "pattern": "dots" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "InfoPageHeader", $$InfoPageHeader, { "title": "How VapeHybrid Works", "subtitle": "We make finding genuine vape deals simple, transparent, and reliable.", "pattern": "grid", "glass": true })} ${maybeRenderHead()}<div class="max-w-5xl mx-auto"> <!-- Process steps with modern design --> <div class="relative py-12"> <!-- Connecting line --> <div class="absolute left-[50%] top-0 bottom-0 w-1 bg-primary/20 dark:bg-primary/30 transform -translate-x-1/2 hidden md:block"></div> <!-- Step 1 --> <div class="mb-24 relative"> <div class="flex flex-col md:flex-row items-center"> <!-- Step number for mobile --> <div class="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-white/90 dark:text-black/90 font-bold text-xl mb-6 md:hidden">1</div> <!-- Left content (image on desktop) --> <div class="md:w-1/2 md:pr-12 order-2 md:order-1 flex justify-end"> <div class="md:max-w-sm"> <!-- Step number for desktop --> <div class="hidden md:flex items-center mb-6"> <div class="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-white/90 dark:text-black/90 font-bold text-xl z-10">1</div> <div class="h-1 bg-primary/20 dark:bg-primary/30 w-full ml-4"></div> </div> <h2 class="text-2xl md:text-3xl font-bold text-design-foreground mb-6">We Find the Best Deals</h2> <div class="bg-white/50 dark:bg-black/50 backdrop-blur-sm p-6 rounded-lg shadow-sm"> <p class="text-design-muted-foreground mb-4">
Our team of vape enthusiasts constantly scours the internet for the best deals from trusted brands and retailers. We have direct relationships with top vape companies to bring you exclusive discounts you won't find elsewhere.
</p> <p class="text-design-muted-foreground">
We focus on quality products from reputable brands, ensuring you never waste money on subpar vaping equipment.
</p> </div> </div> </div> <!-- Right content (image) --> <div class="md:w-1/2 md:pl-12 order-1 md:order-2 mb-8 md:mb-0"> <div class="relative"> <!-- Decorative elements --> <div class="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full filter blur-[20px] z-0"></div> <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-primary/10 rounded-full filter blur-[15px] z-0"></div> <picture class="relative z-10"> <source srcset="/how/Finding-vape-deals.webp" type="image/webp"> <img src="/how/Finding-vape-deals.jpeg" alt="Finding vape deals" class="w-full max-w-md h-auto rounded-[25px] shadow-md transition-all duration-500 hover:shadow-lg mx-auto" width="400" height="300" loading="eager"> </picture> </div> </div> </div> </div> <!-- Step 2 --> <div class="mb-24 relative"> <div class="flex flex-col md:flex-row items-center"> <!-- Step number for mobile --> <div class="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-white/90 dark:text-black/90 font-bold text-xl mb-6 md:hidden">2</div> <!-- Left content (image) --> <div class="md:w-1/2 md:pr-12 order-1 mb-8 md:mb-0"> <div class="relative"> <!-- Decorative elements --> <div class="absolute -top-4 -left-4 w-24 h-24 bg-primary/10 rounded-full filter blur-[20px] z-0"></div> <div class="absolute -bottom-4 -right-4 w-16 h-16 bg-primary/10 rounded-full filter blur-[15px] z-0"></div> <picture class="relative z-10"> <source srcset="/how/Verifying-vape-deals.webp" type="image/webp"> <img src="/how/Verifying-vape-deals.jpeg" alt="Verifying vape deals" class="w-full max-w-md h-auto rounded-[25px] shadow-md transition-all duration-500 hover:shadow-lg mx-auto" width="400" height="300" loading="lazy"> </picture> </div> </div> <!-- Right content (text) --> <div class="md:w-1/2 md:pl-12 order-2"> <div class="md:max-w-sm ml-auto"> <!-- Step number for desktop --> <div class="hidden md:flex items-center mb-6"> <div class="h-1 bg-primary/20 dark:bg-primary/30 w-full mr-4"></div> <div class="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-white/90 dark:text-black/90 font-bold text-xl z-10">2</div> </div> <h2 class="text-2xl md:text-3xl font-bold text-design-foreground mb-6">We Verify Every Deal</h2> <div class="bg-white/50 dark:bg-black/50 backdrop-blur-sm p-6 rounded-lg shadow-sm"> <p class="text-design-muted-foreground mb-4">
Unlike other coupon sites, we personally test every deal before publishing it. Our verification process includes:
</p> <ul class="space-y-3 mb-4"> <li class="flex items-start"> <div class="bg-primary/10 dark:bg-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary dark:text-design-secondary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <span class="text-design-muted-foreground">Testing coupon codes at checkout</span> </li> <li class="flex items-start"> <div class="bg-primary/10 dark:bg-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary dark:text-design-secondary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <span class="text-design-muted-foreground">Confirming discount amounts</span> </li> <li class="flex items-start"> <div class="bg-primary/10 dark:bg-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary dark:text-design-secondary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <span class="text-design-muted-foreground">Verifying expiration dates</span> </li> <li class="flex items-start"> <div class="bg-primary/10 dark:bg-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary dark:text-design-secondary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <span class="text-design-muted-foreground">Checking for hidden fees or restrictions</span> </li> </ul> <p class="text-design-muted-foreground">
We update our deals daily to ensure they're always current and working.
</p> </div> </div> </div> </div> </div> <!-- Step 3 --> <div class="relative"> <div class="flex flex-col md:flex-row items-center"> <!-- Step number for mobile --> <div class="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-white/90 dark:text-black/90 font-bold text-xl mb-6 md:hidden">3</div> <!-- Left content (text on desktop) --> <div class="md:w-1/2 md:pr-12 order-2 md:order-1 flex justify-end"> <div class="md:max-w-sm"> <!-- Step number for desktop --> <div class="hidden md:flex items-center mb-6"> <div class="flex items-center justify-center w-12 h-12 rounded-full bg-primary text-white/90 dark:text-black/90 font-bold text-xl z-10">3</div> <div class="h-1 bg-primary/20 dark:bg-primary/30 w-full ml-4"></div> </div> <h2 class="text-2xl md:text-3xl font-bold text-design-foreground mb-6">You Save Money</h2> <div class="bg-white/50 dark:bg-black/50 backdrop-blur-sm p-6 rounded-lg shadow-sm"> <p class="text-design-muted-foreground mb-4">
When you use our verified deals, you can save up to 50% on premium vape products. Our affiliate tracking ensures you get the best price while supporting our free service.
</p> <p class="text-design-muted-foreground">
We never charge users for access to deals, and we're transparent about how we make money through affiliate partnerships.
</p> </div> </div> </div> <!-- Right content (image) --> <div class="md:w-1/2 md:pl-12 order-1 md:order-2 mb-8 md:mb-0"> <div class="relative"> <!-- Decorative elements --> <div class="absolute -top-4 -right-4 w-24 h-24 bg-primary/10 rounded-full filter blur-[20px] z-0"></div> <div class="absolute -bottom-4 -left-4 w-16 h-16 bg-primary/10 rounded-full filter blur-[15px] z-0"></div> <picture class="relative z-10"> <source srcset="/how/Saving-money-on-vape-deals.webp" type="image/webp"> <img src="/how/Saving-money-on-vape-deals.jpeg" alt="Saving money on vape products" class="w-full max-w-md h-auto rounded-[25px] shadow-md transition-all duration-500 hover:shadow-lg mx-auto" width="400" height="300" loading="lazy"> </picture> </div> </div> </div> </div> </div> </div>  <div class="mt-16 mb-16"> <div class="max-w-xs mx-auto border-t border-design-border/30"></div> </div> ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Frequently Asked Questions", "centerTitle": true, "background": "transparent", "pattern": "dots", "glass": true, "className": "p-6" }, { "default": ($$result3) => renderTemplate` <div class="max-w-4xl mx-auto"> <div class="grid grid-cols-1 md:grid-cols-2 gap-6"> <!-- FAQ 1 --> <div class="bg-design-card/80 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-3 rounded-full mr-4 flex-shrink-0"> <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><path d="M12 17h.01"></path></svg> </div> <div> <h3 class="text-xl font-semibold mb-3 text-design-foreground">Are these deals really free to access?</h3> <p class="text-design-muted-foreground">
Yes! All deals on VapeHybrid are completely free to access. We make money through affiliate commissions when you make a purchase, but this never affects the price you pay.
</p> </div> </div> </div> <!-- FAQ 2 --> <div class="bg-design-card/80 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-3 rounded-full mr-4 flex-shrink-0"> <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><path d="M12 17h.01"></path></svg> </div> <div> <h3 class="text-xl font-semibold mb-3 text-design-foreground">How often are deals updated?</h3> <p class="text-design-muted-foreground">
We verify and update our deals daily. Expired or non-working deals are promptly removed to ensure you never waste time on outdated offers.
</p> </div> </div> </div> <!-- FAQ 3 --> <div class="bg-design-card/80 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-3 rounded-full mr-4 flex-shrink-0"> <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><path d="M12 17h.01"></path></svg> </div> <div> <h3 class="text-xl font-semibold mb-3 text-design-foreground">Do I need to create an account?</h3> <p class="text-design-muted-foreground">
No, you can browse and use all deals without creating an account. However, creating a free account allows you to save favorite deals and receive personalized recommendations.
</p> </div> </div> </div> <!-- FAQ 4 --> <div class="bg-design-card/80 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-3 rounded-full mr-4 flex-shrink-0"> <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><path d="M12 17h.01"></path></svg> </div> <div> <h3 class="text-xl font-semibold mb-3 text-design-foreground">How do you choose which brands to feature?</h3> <p class="text-design-muted-foreground">
We only partner with reputable brands that offer quality products. We consider factors like product quality, customer service, shipping policies, and return guarantees before featuring any brand.
</p> </div> </div> </div> </div> </div> ` })}  <div class="mt-16 mb-16"> <div class="max-w-xs mx-auto border-t border-design-border/30"></div> </div> ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Ready to Start Saving?", "centerTitle": true, "centerContent": true, "background": "transparent", "pattern": "wave", "glass": true, "className": "p-8 rounded-lg" }, { "default": ($$result3) => renderTemplate` <div class="max-w-4xl mx-auto"> <div class="text-center"> <p class="text-xl text-design-foreground mb-8 max-w-2xl mx-auto">
Join thousands of vapers who save money every day with VapeHybrid.
</p> <div class="flex justify-center"> ${renderComponent($$result3, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/coupons", "className": "text-lg px-8 py-4", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": ($$result4) => renderTemplate`
Browse Coupons Now
<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg> ` })} </div> </div> </div> ` })} ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/how-it-works.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/how-it-works.astro";
const $$url = "/how-it-works";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$HowItWorks,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
