import{r as e,j as t}from"./vendor/react-bq1VYqFi.js";import{m as a}from"./proxy-BAs1uqPX.js";import{A as i}from"./index-tFD8qxOu.js";const r=({title:r="Real-Time Savings",subtitle:n="See how much others are saving right now"})=>{const s=["Michael S.","Jessica T.","David R.","Sarah K.","Robert L.","<PERSON> W.","James B.","Olivia P.","Thomas H.","Emma C.","<PERSON>","Sophia G.","<PERSON>","Ava R.","Alexander D.","Mia L.","Ethan P.","Charlotte W.","Noah B.","Amelia K.","Benjamin F.","Harper S.","Mason T.","Evelyn Z."],o=["SMOK Nord 4","Geek Vape Aegis","Vaporesso XROS","Uwell Caliburn G2","VooPoo Drag X","Lost Vape Orion","Innokin Coolfire Z80","Aspire Nautilus","Freemax Maxus","OXVA Origin X","Dinner Lady E-Liquid","Naked 100 E-Juice","Jam Monster","Pachamama","Sadboy E-Liquid","Candy King","Cloud Nurdz","Coastal Clouds","Beard Vape Co","Bad Drip Labs"],d=["VAPE30","EJUICE25","COILS50","NEWVAPER","SUMMER20","FALL15","WINTER40","SPRING10","DEVICE25","LIQUID20","BUNDLE35","STARTER15","PREMIUM30","SAVE50","FLASH25","WEEKEND15","HOLIDAY40","LOYALTY20","WELCOME15","RETURN10"],l=["New York","Los Angeles","Chicago","Miami","Seattle","Austin","Denver","Boston","San Francisco","Dallas","Atlanta","Phoenix","Philadelphia","Houston","San Diego","Portland","Las Vegas","Nashville","Orlando","Detroit","Minneapolis","New Orleans"],m=()=>{const e=[{min:5,max:15},{min:10,max:25},{min:20,max:40},{min:30,max:60},{min:50,max:100}],t=Math.floor(Math.random()*e.length),{min:a,max:i}=e[t];return`$${(Math.random()*(i-a)+a).toFixed(2)}`},c=e.useCallback((()=>{const e=new Date,t=[];for(let a=0;a<30;a++){const i=Math.floor(30*Math.random())+1,r=new Date(e.getTime()-6e4*i),n=Math.random()>.5;t.push({id:a+1,user:s[Math.floor(Math.random()*s.length)],action:n?`used coupon ${d[Math.floor(Math.random()*d.length)]}`:`saved on ${o[Math.floor(Math.random()*o.length)]}`,amount:m(),time:r,location:l[Math.floor(Math.random()*l.length)]})}return t.sort(((e,t)=>t.time.getTime()-e.time.getTime()))}),[]),x=e=>{const t=(new Date).getTime()-e.getTime(),a=Math.floor(t/6e4);return a<1?"just now":1===a?"1 minute ago":`${a} minutes ago`},[h,u]=e.useState([]),[g,p]=e.useState([]),[f,y]=e.useState("$12,450.75");e.useEffect((()=>{u(c())}),[c]),e.useEffect((()=>{if(0===h.length)return;p(h.slice(0,4));const e=()=>setTimeout((()=>{if(Math.random()<.3){const e={id:h.length+1,user:s[Math.floor(Math.random()*s.length)],action:Math.random()>.5?`used coupon ${d[Math.floor(Math.random()*d.length)]}`:`saved on ${o[Math.floor(Math.random()*o.length)]}`,amount:m(),time:new Date,location:l[Math.floor(Math.random()*l.length)]};u((t=>[e,...t].slice(0,30))),p((t=>[e,...t.slice(0,3)]))}else p((e=>{const t=(h.findIndex((t=>t.id===e[0].id))+1)%(h.length-3);return h.slice(t,t+4)}));t.current=e()}),Math.floor(4e3*Math.random())+3e3),t={current:e()};return()=>{t.current&&clearTimeout(t.current)}}),[h]),e.useEffect((()=>{const e=setInterval((()=>{const e=parseFloat(f.replace(/[$,]/g,"")),t=45*Math.random()+5;y(`$${(e+t).toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g,",")}`)}),Math.floor(1e4*Math.random())+5e3);return()=>clearInterval(e)}),[f]);const b=e=>{const t=["bg-design-primary/20 text-design-primary","bg-design-tetradic1/20 text-design-tetradic1","bg-design-tetradic2/20 text-design-tetradic2","bg-design-secondary/20 text-design-secondary"];return t[e.charCodeAt(0)%t.length]};return t.jsxs("section",{className:"py-8 bg-transparent relative overflow-hidden",children:[t.jsx("div",{className:"absolute top-20 left-10 w-32 h-32 bg-[#262cbd]/10 dark:bg-[#1df292]/10 rounded-full blur-3xl"}),t.jsx("div",{className:"absolute bottom-20 right-10 w-40 h-40 bg-[#f21d6b]/10 dark:bg-[#f2d91d]/10 rounded-full blur-3xl"}),t.jsxs("div",{className:"container mx-auto px-6 md:px-8 lg:px-10 relative z-10",children:[t.jsxs("div",{className:"text-center mb-10",children:[t.jsxs(a.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"inline-flex items-center bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm px-4 py-2 rounded-full mb-4 border border-design-primary/20",children:[t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-design-primary mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:t.jsx("path",{fillRule:"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z",clipRule:"evenodd"})}),t.jsx("span",{className:"text-design-primary font-medium text-xs",children:"Live Activity"})]}),t.jsx(a.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.1},viewport:{once:!0},className:"text-3xl md:text-[34px] font-normal text-design-foreground mb-3",children:r}),t.jsx(a.p,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.2},viewport:{once:!0},className:"text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto",children:n})]}),t.jsxs("div",{className:"max-w-4xl mx-auto",children:[t.jsx("div",{className:"grid grid-cols-1 md:grid-cols-2 gap-4",children:t.jsx(i,{mode:"popLayout",children:g.map(((e,i)=>{return t.jsxs(a.div,{initial:{opacity:0,y:20},animate:{opacity:1,y:0},exit:{opacity:0,y:-20},transition:{duration:.5},className:"bg-design-card/95 backdrop-blur-sm border border-design-border rounded-xl p-4 flex items-center gap-4 hover:shadow-md transition-all duration-300 hover:border-design-primary/20",children:[t.jsx("div",{className:`w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${b(e.user)}`,children:(r=e.user,r.split(" ").map((e=>e[0])).join(""))}),t.jsx("div",{className:"flex-1",children:t.jsxs("div",{className:"flex justify-between items-start",children:[t.jsxs("div",{children:[t.jsxs("p",{className:"font-medium text-design-foreground text-sm",children:[e.user," ",t.jsx("span",{className:"font-normal text-design-muted-foreground",children:e.action})]}),t.jsxs("p",{className:"text-xs text-design-muted-foreground",children:[x(e.time)," • ",e.location]})]}),t.jsx("div",{className:"text-base font-bold text-design-tetradic1",children:e.amount})]})})]},`${e.id}-${i}`);var r}))})}),t.jsxs(a.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.3},viewport:{once:!0},className:"mt-10 bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm rounded-xl p-6 border border-design-primary/20 text-center",children:[t.jsx("p",{className:"text-design-muted-foreground mb-2 text-xs md:text-sm",children:"Total savings by VapeHybrid users today"}),t.jsxs("div",{className:"text-3xl md:text-4xl font-normal text-design-primary",children:[f,t.jsx("span",{className:"text-design-primary/50 text-sm ml-1 animate-pulse",children:"+"})]}),t.jsx("p",{className:"text-xs text-design-muted-foreground mt-2",children:"Based on verified coupon usage"})]})]})]})]})};export{r as RealTimeSavings};
