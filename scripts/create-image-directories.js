/**
 * Create Image Directories Script
 *
 * This script creates the necessary directory structure for image processing.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Define the directories to create
const directories = [
  'public/images/original',
  'public/images/sizes',
  'public/images/webp'
];

// Create each directory
directories.forEach(dir => {
  const fullPath = path.join(__dirname, '..', dir);

  if (!fs.existsSync(fullPath)) {
    fs.mkdirSync(fullPath, { recursive: true });
    console.log(`Created directory: ${fullPath}`);
  } else {
    console.log(`Directory already exists: ${fullPath}`);
  }
});

console.log('Directory structure created successfully!');
