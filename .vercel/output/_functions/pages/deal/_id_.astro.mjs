import { c as createAstro, a as createComponent } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import 'clsx';
import { c as createServerSupabaseClient } from '../../assets/js/server-e_5TR1Eu.js';
import { g as generateCouponUrl } from '../../assets/js/app/design-system-CUssmfny.js';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$id = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const { id } = Astro2.params;
  if (!id) {
    return Astro2.redirect("/coupons", 301);
  }
  const supabase = createServerSupabaseClient();
  const { data: deal, error } = await supabase.from("deals").select("id, slug, normalized_title, cleaned_title").eq("id", id).single();
  if (deal) {
    if (deal.slug) {
      return Astro2.redirect(`/coupon/${deal.slug}`, 301);
    }
    const couponUrl = generateCouponUrl(deal);
    return Astro2.redirect(couponUrl, 301);
  }
  return Astro2.redirect("/coupons", 301);
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/deal/[id].astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/deal/[id].astro";
const $$url = "/deal/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
