---
import MainLayout from '../layouts/MainLayout.astro';
import { But<PERSON> } from '../components/DesignSystem';

// Set the response status to 404
Astro.response.status = 404;
---

<MainLayout
  title="Page Not Found | VapeHybrid"
  description="The page you're looking for doesn't exist or has been moved."
>
  <div class="container mx-auto px-4 py-16 md:py-24 flex flex-col items-center justify-center min-h-[60vh]">
    <div class="max-w-2xl w-full bg-design-card/80 backdrop-blur-sm rounded-[25px] overflow-hidden border border-design-border/60 p-8 md:p-12 relative">
      <!-- Background pattern - visible in both light and dark mode with different opacities -->
      <div class="absolute inset-0 bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-5 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-10 z-0 pointer-events-none"></div>

      <div class="relative z-10 text-center">
        <h1 class="text-7xl md:text-9xl font-bold text-design-primary mb-4">404</h1>
        <h2 class="text-2xl md:text-3xl font-bold text-design-foreground mb-6">Page Not Found</h2>
        <p class="text-design-muted-foreground mb-8 max-w-md mx-auto">
          The page you're looking for doesn't exist or has been moved. Let's get you back on track.
        </p>

        <div class="flex flex-col sm:flex-row gap-4 justify-center">
          <a href="/" class="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-white bg-design-primary hover:bg-design-primary/90 rounded-lg transition-colors w-full sm:w-auto">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="m3 9 9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path><polyline points="9 22 9 12 15 12 15 22"></polyline></svg>
            Back to Home
          </a>
          <a href="/coupons" class="inline-flex items-center justify-center px-6 py-3 text-lg font-medium text-design-foreground bg-transparent border border-design-border hover:bg-design-muted/10 rounded-lg transition-colors w-full sm:w-auto">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M20.59 13.41l-7.17 7.17a2 2 0 0 1-2.83 0L2 12V2h10l8.59 8.59a2 2 0 0 1 0 2.82z"></path><line x1="7" y1="7" x2="7.01" y2="7"></line></svg>
            Browse Coupons
          </a>
        </div>

        <div class="mt-12 pt-8 border-t border-design-border/30">
          <p class="text-sm text-design-muted-foreground">
            If you believe this is an error, please <a href="/contact" class="text-design-primary hover:underline">contact us</a>.
          </p>
        </div>
      </div>
    </div>
  </div>
</MainLayout>

<script>
  // Define gtag for TypeScript
  interface Window {
    gtag?: (command: string, action: string, params: object) => void;
  }

  // Track 404 errors
  document.addEventListener('DOMContentLoaded', () => {
    // Get the current URL
    const currentUrl = window.location.href;
    const referrer = document.referrer;

    // Check if we're in the admin section
    const isAdmin = window.location.pathname.startsWith('/admin');

    // Only send to analytics if we're not in the admin section
    if (!isAdmin) {
      // Send to analytics (if available)
      if (typeof window.gtag !== 'undefined') {
        window.gtag('event', '404_error', {
          'event_category': 'Error',
          'event_label': currentUrl,
          'referrer': referrer
        });
      }
    } else {
      // For admin section, send to monitoring endpoint silently
      try {
        fetch('/admin/api/monitor/404', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            url: currentUrl,
            referrer: referrer,
            timestamp: new Date().toISOString(),
            userAgent: navigator.userAgent
          })
        });
      } catch (error) {
        // Silent error handling for production
      }
    }
  });
</script>
