import { c as createAstro, a as createComponent, r as renderTemplate, d as renderComponent, i as renderHead, u as unescapeHTML, b as addAttribute, e as renderSlot, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
/* empty css                                   */
import { T as ToastProvider, o as Navbar, G as GlowingButton, W as WebPImage } from '../assets/js/app/design-system-CUssmfny.js';
import { jsxs, jsx, Fragment } from 'react/jsx-runtime';
import { useState, useRef, useEffect, useCallback, useMemo } from 'react';
import 'clsx';
import 'sonner';
import { R as ReactProviders, B as BackToTop, E as ExitIntentPopup, F as FooterNewsletter } from '../assets/js/FooterNewsletter-CELCN2dO.js';
import { FeaturedDeals } from '../assets/js/featured-deals-ERmVkM9j.js';
import { NewsletterSection } from '../assets/js/newsletter-section-Bv7hy9NP.js';
import { FAQSection } from '../assets/js/faq-section-DnK0tX_o.js';
import { motion, AnimatePresence } from 'framer-motion';
import { c as createServerSupabaseClient } from '../assets/js/server-e_5TR1Eu.js';
import { f as faqs } from '../assets/js/faqs-CCHoNMx8.js';
export { renderers } from '../renderers.mjs';

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _a, _b;
const $$Astro$2 = createAstro("http://localhost:4321");
const $$HomeLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$HomeLayout;
  const {
    title = "VapeHybrid Coupons",
    description = "Find the best vape deals and coupons",
    structuredData
  } = Astro2.props;
  const canonical = "https://vapehybrid.com/";
  const darkModePreference = Astro2.cookies.get("darkMode")?.value === "true" || false;
  return renderTemplate(_b || (_b = __template(['<html lang="en"', '> <head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><link rel="alternate icon" type="image/x-icon" href="/favicon.ico"><link rel="shortcut icon" type="image/x-icon" href="/favicon.ico"><meta name="description"', "><title>", '</title><!-- Canonical URL for SEO --><link rel="canonical"', ">", `<!-- Fonts are loaded via generated-variables.css --><!-- Preload critical navigation assets --><link rel="preload" href="/logo-light.svg" as="image" type="image/svg+xml"><link rel="preload" href="/logo-dark.svg" as="image" type="image/svg+xml"><link rel="preload" href="/Vapehybrid light icon.svg" as="image" type="image/svg+xml"><link rel="preload" href="/Vapehybrid dark icon.svg" as="image" type="image/svg+xml"><!-- A/B Testing Script removed for performance optimization --><!-- Initialize theme (light is default) --><script>
      // Check for dark mode preference, but default to light mode
      const darkModePreference = localStorage.getItem('darkMode') === 'true';
      // Ignore system preference to ensure light mode is default

      // Apply dark mode class if explicitly set, otherwise use light mode
      if (darkModePreference) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        // Ensure light mode is set in localStorage
        localStorage.setItem('darkMode', 'false');
      }
    <\/script><!-- Critical assets preload -->`, '</head> <body class="min-h-screen bg-design-background text-design-foreground font-sans antialiased"> ', " </body></html>"])), addAttribute(darkModePreference ? "dark" : "", "class"), addAttribute(description, "content"), title, addAttribute(canonical, "href"), structuredData && renderTemplate(_a || (_a = __template(['<script type="application/ld+json">', "<\/script>"])), unescapeHTML(JSON.stringify(structuredData))), renderHead(), renderComponent($$result, "ToastProvider", ToastProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem", "client:component-export": "ToastProvider" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "ReactProviders", ReactProviders, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/react-providers", "client:component-export": "ReactProviders" }, { "default": ($$result3) => renderTemplate` <div class="relative flex min-h-screen flex-col"> ${renderComponent($$result3, "Navbar", Navbar, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Navbar", "client:component-export": "Navbar" })} <main id="main-content" class="flex-1" tabindex="-1"> ${renderSlot($$result3, $$slots["default"])} </main> ${renderComponent($$result3, "BackToTop", BackToTop, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/back-to-top", "client:component-export": "BackToTop" })} ${renderComponent($$result3, "ExitIntentPopup", ExitIntentPopup, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ExitIntentPopup", "client:component-export": "default" })} <footer class="border-t border-design-border py-10 bg-design-background"> <div class="container mx-auto px-4"> <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-10"> <div class="md:col-span-1"> <div class="flex items-center gap-2 mb-4"> <img src="/footer-logo-light.svg" alt="VapeHybrid Logo" class="w-8 h-8 dark:hidden" width="32" height="32"> <img src="/footer-logo-dark.svg" alt="VapeHybrid Logo" class="w-8 h-8 hidden dark:block" width="32" height="32"> <h3 class="text-lg font-semibold text-design-foreground">VapeHybrid</h3> </div> <p class="text-sm text-design-muted-foreground mb-4">
Helping vape users save money via vetted deals. We independently review everything we recommend.
</p> <div class="flex items-center"> <div class="flex items-center text-design-primary"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg> <span class="text-xs font-medium">Verified daily by our team</span> </div> </div> </div> <div class="md:col-span-1"> <ul class="space-y-2"> <li> <a href="/coupons" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">All Coupons</a> </li> <li> <a href="/coupons/brands" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Brands</a> </li> <li> <a href="/merchants" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Merchants</a> </li> <li> <a href="/coupons/categories" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Categories</a> </li> </ul> </div> <div class="md:col-span-1"> <ul class="space-y-2"> <li> <a href="/about" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">About Us</a> </li> <li> <a href="/contact" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Contact Us</a> </li> <li> <a href="/faq" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">FAQ</a> </li> <li> <a href="/blog" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Blog</a> </li> </ul> </div> <div class="md:col-span-1"> ${renderComponent($$result3, "FooterNewsletter", FooterNewsletter, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/FooterNewsletter", "client:component-export": "FooterNewsletter" })} </div> </div> <div class="w-full h-px bg-design-border mb-6"></div> <div class="flex flex-col md:flex-row items-center justify-between gap-4"> <div class="flex flex-col items-center md:items-start gap-2"> <p class="text-center text-[12px] text-design-muted-foreground md:text-left">
We may earn a commission on the items you choose to buy.
</p> <p class="text-center text-[10px] text-design-muted-foreground/70 md:text-left">
© 2024 VapeHybrid. All rights reserved.
</p> </div> <div class="flex flex-wrap justify-end gap-4"> <a href="/legal" class="text-[12px] text-design-muted-foreground hover:text-design-foreground transition-colors">Legal</a> <a href="/terms" class="text-[12px] text-design-muted-foreground hover:text-design-foreground transition-colors">Terms</a> <a href="/privacy" class="text-[12px] text-design-muted-foreground hover:text-design-foreground transition-colors">Privacy</a> </div> </div> </div> </footer> </div> ` })} ` }));
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/layouts/HomeLayout.astro", void 0);

const EnhancedHero = ({
  subtitle,
  ctaText,
  ctaLink,
  userCount = "50,000+",
  secondaryCTAText = "How It Works",
  secondaryCTALink = "/how-it-works"
}) => {
  return /* @__PURE__ */ jsxs("div", { className: "w-full relative overflow-hidden", children: [
    /* @__PURE__ */ jsx("div", { className: "absolute inset-0 bg-gradient-to-b from-white to-[#d6e6f6] dark:from-black dark:to-black z-0" }),
    /* @__PURE__ */ jsx("div", { className: "absolute inset-0\n        bg-[url('/patterns/circle-light.svg')] bg-repeat opacity-[0.02]\n        dark:bg-[url('/patterns/circle-dark.svg')] dark:opacity-[0.02]\n        z-0" }),
    /* @__PURE__ */ jsx("div", { className: "absolute inset-0 bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-[0.01]\n        dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-[0.01] z-0" }),
    /* @__PURE__ */ jsxs("div", { className: "absolute top-0 left-0 w-full h-full overflow-hidden z-0", children: [
      /* @__PURE__ */ jsx("div", { className: "absolute top-20 left-[10%] w-72 h-72 bg-design-primary/5 rounded-full blur-[80px] animate-pulse-slow" }),
      /* @__PURE__ */ jsx("div", { className: "absolute bottom-20 right-[10%] w-96 h-96 bg-design-primary/5 rounded-full blur-[100px] animate-pulse-slow animation-delay-1000" }),
      /* @__PURE__ */ jsx("div", { className: "absolute top-1/3 right-1/4 w-64 h-64 bg-design-primary/5 rounded-full blur-[70px] animate-pulse-slow animation-delay-2000" })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "absolute top-40 left-10 w-16 h-16 border-2 border-design-primary/10 rounded-full z-0 animate-float" }),
    /* @__PURE__ */ jsx("div", { className: "absolute bottom-40 right-10 w-20 h-20 border-2 border-design-primary/10 rounded-full z-0 animate-float animation-delay-1000" }),
    /* @__PURE__ */ jsx("div", { className: "absolute top-1/4 right-1/4 w-12 h-12 bg-design-primary/5 rounded-full z-0 animate-float animation-delay-2000" }),
    /* @__PURE__ */ jsx("div", { className: "relative z-10 container mx-auto px-4 pt-0 sm:pt-2 lg:pt-4 pb-16 md:pb-24", children: /* @__PURE__ */ jsx("div", { className: "max-w-7xl mx-auto", children: /* @__PURE__ */ jsxs("div", { className: "relative", children: [
      /* @__PURE__ */ jsx("div", { className: "absolute -left-4 top-20 hidden lg:block", children: /* @__PURE__ */ jsx("div", { className: "relative w-[220px] h-[220px] bg-white dark:bg-black rounded-3xl shadow-lg p-4 border border-gray-200/50 dark:border-gray-700/50 rotate-[-5deg] animate-float shadow-2xl", children: /* @__PURE__ */ jsxs("picture", { children: [
        /* @__PURE__ */ jsx("source", { srcSet: "/products/Crystal-Cave-Centaurus-N200-Mod-Kit.webp", type: "image/webp" }),
        /* @__PURE__ */ jsx(
          "img",
          {
            src: "/products/Crystal-Cave-Centaurus-N200-Mod-Kit.jpeg",
            alt: "Vape device",
            className: "w-full h-full object-contain",
            onError: (e) => {
              const target = e.target;
              target.src = "/images/hero-vape-deals.svg";
            }
          }
        )
      ] }) }) }),
      /* @__PURE__ */ jsx("div", { className: "absolute -right-4 top-40 hidden lg:block", children: /* @__PURE__ */ jsx("div", { className: "relative w-[260px] h-[260px] bg-white dark:bg-black rounded-3xl shadow-lg overflow-hidden animate-float animation-delay-1000 border border-gray-200/50 dark:border-gray-700/50", children: /* @__PURE__ */ jsx("div", { className: "absolute inset-0 flex items-center justify-center p-4", children: /* @__PURE__ */ jsxs("picture", { children: [
        /* @__PURE__ */ jsx("source", { srcSet: "/products/geekvape-digi.webp", type: "image/webp" }),
        /* @__PURE__ */ jsx(
          "img",
          {
            src: "/products/geekvape-digi.jpeg",
            alt: "Premium vape product",
            className: "w-full h-full object-contain",
            onError: (e) => {
              const target = e.target;
              target.src = "/images/hero-vape-deals.svg";
            }
          }
        )
      ] }) }) }) }),
      /* @__PURE__ */ jsx("div", { className: "absolute left-40 bottom-10 hidden lg:block", children: /* @__PURE__ */ jsx("div", { className: "relative w-[180px] h-[180px] bg-white dark:bg-black rounded-3xl shadow-lg overflow-hidden animate-float animation-delay-2000 border border-gray-200/50 dark:border-gray-700/50 rotate-[5deg]", children: /* @__PURE__ */ jsx("div", { className: "absolute inset-0 flex items-center justify-center p-4", children: /* @__PURE__ */ jsxs("picture", { children: [
        /* @__PURE__ */ jsx("source", { srcSet: "/products/Banana-Raspberry-Ice-waka-duo-27000.webp", type: "image/webp" }),
        /* @__PURE__ */ jsx(
          "img",
          {
            src: "/products/Banana-Raspberry-Ice-waka-duo-27000.jpeg",
            alt: "Vape juice",
            className: "w-full h-full object-contain",
            onError: (e) => {
              const target = e.target;
              target.src = "/images/hero-vape-deals.svg";
            }
          }
        )
      ] }) }) }) }),
      /* @__PURE__ */ jsxs("div", { className: "text-center max-w-3xl mx-auto pt-10 relative", children: [
        /* @__PURE__ */ jsxs("h1", { className: "text-5xl md:text-6xl lg:text-7xl font-normal text-design-foreground mb-6 animate-fade-in", children: [
          "Verified Vape Coupons ",
          /* @__PURE__ */ jsx("br", { className: "hidden md:block" }),
          "That Actually Work"
        ] }),
        /* @__PURE__ */ jsx("p", { className: "text-sm md:text-sm text-design-foreground mb-10 font-normal tracking-tight max-w-[80%] mx-auto animate-fade-in animation-delay-200", children: subtitle }),
        /* @__PURE__ */ jsxs("div", { className: "animate-fade-in animation-delay-800", children: [
          /* @__PURE__ */ jsx("div", { className: "flex flex-col sm:flex-row gap-6 mb-2 justify-center", children: /* @__PURE__ */ jsx(
            GlowingButton,
            {
              asLink: true,
              href: ctaLink,
              "data-testid": "primary-cta",
              children: ctaText
            }
          ) }),
          /* @__PURE__ */ jsx("div", { className: "mb-8 mt-4", children: /* @__PURE__ */ jsx(
            "a",
            {
              href: secondaryCTALink,
              className: "text-design-foreground hover:text-design-primary font-sm transition-colors underline-offset-4 text-sm",
              children: secondaryCTAText
            }
          ) }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-wrap items-center justify-center gap-3 text-xs text-design-foreground mb-6", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-center bg-white/70 dark:bg-black/20 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/30 dark:border-gray-700/30 shadow-sm", children: [
              /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-4 w-4 text-design-primary mr-1.5", viewBox: "0 0 20 20", fill: "yellow", children: /* @__PURE__ */ jsx("path", { fillRule: "evenodd", d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Verified Daily" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center bg-white/70 dark:bg-black/20 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/30 dark:border-gray-700/30 shadow-sm", children: [
              /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-4 w-4 text-design-primary mr-1.5", viewBox: "0 0 20 20", fill: "yellow", children: /* @__PURE__ */ jsx("path", { d: "M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" }) }),
              /* @__PURE__ */ jsx("span", { className: "font-medium", children: "98% Success Rate" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center bg-white/70 dark:bg-black/20 backdrop-blur-sm px-4 py-2 rounded-full border border-gray-200/30 dark:border-gray-700/30 shadow-sm", children: [
              /* @__PURE__ */ jsx("span", { className: "text-design-tetradic2 mr-1.5 text-xs", children: "★★★★★" }),
              /* @__PURE__ */ jsx("span", { className: "font-medium", children: "4.8 stars (1,200+ reviews)" })
            ] })
          ] })
        ] })
      ] })
    ] }) }) }),
    /* @__PURE__ */ jsx("div", { className: "py-16 relative z-10 mt-10", children: /* @__PURE__ */ jsx("div", { className: "container mx-auto px-4", children: /* @__PURE__ */ jsx("div", { className: "max-w-5xl mx-auto bg-white/90 dark:bg-gradient-to-br dark:from-[#000000] dark:via-[#0a0a0a] dark:to-[#000000] backdrop-blur-md rounded-3xl shadow-[0_0_30px_rgba(255,255,255,0.05)] border border-gray-200/50 dark:border-gray-800/70 py-12 px-8\n", children: /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 md:grid-cols-4 gap-6 md:gap-8 text-center", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
        /* @__PURE__ */ jsx("div", { className: "text-3xl md:text-5xl font-normal text-design-primary mb-3", children: "1000+" }),
        /* @__PURE__ */ jsx("div", { className: "text-sm md:text-base text-design-foreground font-normal", children: "Active Deals" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
        /* @__PURE__ */ jsx("div", { className: "text-3xl md:text-5xl font-normal text-design-primary mb-3", children: "238+" }),
        /* @__PURE__ */ jsx("div", { className: "text-sm md:text-base text-design-foreground font-normal", children: "Verified Brands" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
        /* @__PURE__ */ jsx("div", { className: "text-3xl md:text-5xl font-normal text-design-primary mb-3", children: "98%" }),
        /* @__PURE__ */ jsx("div", { className: "text-sm md:text-base text-design-foreground font-normal", children: "Success Rate" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
        /* @__PURE__ */ jsx("div", { className: "text-3xl md:text-5xl font-normal text-design-primary mb-3", children: "265+" }),
        /* @__PURE__ */ jsx("div", { className: "text-sm md:text-base text-design-foreground font-normal", children: "New Codes Daily" })
      ] })
    ] }) }) }) }),
    /* @__PURE__ */ jsx("div", { className: "py-16 relative z-10", children: /* @__PURE__ */ jsx("div", { className: "container mx-auto px-4", children: /* @__PURE__ */ jsxs("div", { className: "max-w-4xl mx-auto relative", children: [
      /* @__PURE__ */ jsx("div", { className: "absolute -left-20 top-10 hidden xl:block", children: /* @__PURE__ */ jsx("div", { className: "w-[120px] h-[120px] bg-white dark:bg-black rounded-3xl shadow-lg p-4 border border-gray-200/50 dark:border-gray-700/50 animate-float rotate-[-8deg]", children: /* @__PURE__ */ jsxs("picture", { children: [
        /* @__PURE__ */ jsx("source", { srcSet: "/products/ego-nexo-blue.webp", type: "image/webp" }),
        /* @__PURE__ */ jsx(
          "img",
          {
            src: "/products/ego-nexo-blue.jpeg",
            alt: "Vape pod",
            className: "w-full h-full object-contain",
            onError: (e) => {
              const target = e.target;
              target.src = "/images/hero-vape-deals.svg";
            }
          }
        )
      ] }) }) }),
      /* @__PURE__ */ jsx("div", { className: "absolute -right-16 bottom-10 hidden xl:block", children: /* @__PURE__ */ jsx("div", { className: "w-[100px] h-[100px] bg-white dark:bg-black rounded-3xl shadow-lg p-3 border border-gray-200/50 dark:border-gray-700/50 animate-float animation-delay-1000 rotate-[5deg]", children: /* @__PURE__ */ jsxs("picture", { children: [
        /* @__PURE__ */ jsx("source", { srcSet: "/products/grape-mint-drops-by-blis.webp", type: "image/webp" }),
        /* @__PURE__ */ jsx(
          "img",
          {
            src: "/products/grape-mint-drops-by-blis.jpeg",
            alt: "Vape juice",
            className: "w-full h-full object-contain",
            onError: (e) => {
              const target = e.target;
              target.src = "/images/hero-vape-deals.svg";
            }
          }
        )
      ] }) }) }),
      /* @__PURE__ */ jsxs("div", { className: "max-w-5xl mx-auto bg-white/90 dark:bg-gradient-to-br dark:from-[#000000] dark:via-[#0a0a0a] dark:to-[#000000] backdrop-blur-md rounded-3xl shadow-[0_0_30px_rgba(255,255,255,0.05)] border border-gray-200/50 dark:border-gray-800/70 py-12 px-8\n", children: [
        /* @__PURE__ */ jsx("h2", { className: "text-3xl md:text-4xl font-normal text-design-foreground mb-4 text-center", children: "Find the Best Vape Deals in Seconds" }),
        /* @__PURE__ */ jsx("p", { className: "text-design-foreground text-center mb-8 text-lg max-w-[80%] mx-auto", children: "Our search engine helps you find premium vape products at unbeatable prices. We verify every deal daily to ensure you get genuine savings." }),
        /* @__PURE__ */ jsxs("div", { className: "relative", children: [
          /* @__PURE__ */ jsx("form", { action: "/search", method: "get", className: "relative", children: /* @__PURE__ */ jsxs("div", { className: "flex items-center", children: [
            /* @__PURE__ */ jsx(
              "input",
              {
                type: "text",
                name: "q",
                placeholder: "Search for vape brands, products, or deals...",
                className: "w-full px-6 py-5 pr-14 bg-white dark:bg-black border border-gray-200 dark:border-gray-700 text-design-foreground placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-design-primary focus:border-transparent shadow-md rounded-full text-lg"
              }
            ),
            /* @__PURE__ */ jsx(
              "button",
              {
                type: "submit",
                className: "bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] text-white hover:text-white absolute right-2 top-1/2 transform -translate-y-1/2 p-3 h-12 w-12 flex items-center justify-center rounded-full transition-all duration-300 shadow-glow hover:shadow-glow-light hover:animate-glow-border hover:bg-animated-gradient bg-[length:400%_400%] hover:animate-gradient-move dark:bg-gradient-to-b dark:from-[#1df292] dark:to-[#0db875] dark:text-black dark:shadow-glow-dark dark:hover:shadow-glow-dark dark:hover:bg-animated-gradient-dark",
                "aria-label": "Search",
                children: /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-6 w-6 text-white dark:text-black", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor", children: /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" }) })
              }
            )
          ] }) }),
          /* @__PURE__ */ jsxs("div", { className: "mt-6 flex flex-wrap justify-center gap-3 text-sm", children: [
            /* @__PURE__ */ jsx("a", { href: "/search?q=pods", className: "bg-gray-100 dark:bg-gray-950 hover:bg-design-primary/10 dark:hover:bg-design-primary/20 text-design-foreground hover:text-design-primary px-4 py-2 rounded-full font-medium transition-colors", children: "Pods" }),
            /* @__PURE__ */ jsx("a", { href: "/search?q=disposable", className: "bg-gray-100 dark:bg-gray-950 hover:bg-design-primary/10 dark:hover:bg-design-primary/20 text-design-foreground hover:text-design-primary px-4 py-2 rounded-full font-medium transition-colors", children: "Disposables" }),
            /* @__PURE__ */ jsx("a", { href: "/search?q=e-juice", className: "bg-gray-100 dark:bg-gray-950 hover:bg-design-primary/10 dark:hover:bg-design-primary/20 text-design-foreground hover:text-design-primary px-4 py-2 rounded-full font-medium transition-colors", children: "E-Juice" }),
            /* @__PURE__ */ jsx("a", { href: "/search?q=mods", className: "bg-gray-100 dark:bg-gray-950 hover:bg-design-primary/10 dark:hover:bg-design-primary/20 text-design-foreground hover:text-design-primary px-4 py-2 rounded-full font-medium transition-colors", children: "Mods" }),
            /* @__PURE__ */ jsx("a", { href: "/search?q=starter-kits", className: "bg-gray-100 dark:bg-gray-950 hover:bg-design-primary/10 dark:hover:bg-design-primary/20 text-design-foreground hover:text-design-primary px-4 py-2 rounded-full font-medium transition-colors", children: "Starter Kits" })
          ] })
        ] })
      ] })
    ] }) }) })
  ] });
};

const CategoryNav = ({ categories }) => {
  const getCategoryIconPath = (title) => {
    const iconMap = {
      "Box Mods": "/icons/002-vape.png",
      "Disposable Pod Kits": "/icons/003-disposable.png",
      "Disposable Vapes": "/icons/004-vape-1.png",
      "E-Juice": "/icons/005-strawberry-juice.png",
      "Festival": "/icons/001-grape.png",
      "Giveaway": "/icons/010-electronic-cigarette.png",
      "Pod System Kits": "/icons/007-vape-2.png",
      "Sale": "/icons/009-atomizer.png",
      "Coils": "/icons/006-coil.png",
      "Tanks": "/icons/008-coil-1.png"
    };
    return iconMap[title] || "/icons/002-vape.png";
  };
  return /* @__PURE__ */ jsx("div", { className: "bg-design-background border-b border-design-border py-4 z-10", children: /* @__PURE__ */ jsx("div", { className: "container mx-auto px-4", children: /* @__PURE__ */ jsx("div", { className: "flex items-center justify-between", children: /* @__PURE__ */ jsx("div", { className: "flex-1 overflow-x-auto hide-scrollbar", children: /* @__PURE__ */ jsxs("div", { className: "flex space-x-8 md:space-x-10 px-2 justify-center", children: [
    categories.slice(0, 8).map((category) => /* @__PURE__ */ jsxs(
      "a",
      {
        href: `/coupons/categories/${category.slug}`,
        className: "flex flex-col items-center justify-center min-w-[80px] group",
        children: [
          /* @__PURE__ */ jsx("div", { className: "w-16 h-16 md:w-14 md:h-14 rounded-full bg-design-primary/10 dark:bg-design-primary/20 flex items-center justify-center mb-3 group-hover:bg-design-primary/20 dark:group-hover:bg-design-primary/30 transition-colors shadow-sm", children: /* @__PURE__ */ jsx(
            WebPImage,
            {
              src: getCategoryIconPath(category.title),
              alt: `${category.title} category icon`,
              className: "w-8 h-8 md:w-7 md:h-7 object-contain",
              fallbackSrc: "/icons/002-vape.png"
            }
          ) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-design-foreground whitespace-nowrap", children: category.title })
        ]
      },
      category.slug
    )),
    /* @__PURE__ */ jsxs(
      "a",
      {
        href: "/coupons/categories",
        className: "flex flex-col items-center justify-center min-w-[80px] group",
        children: [
          /* @__PURE__ */ jsx("div", { className: "w-16 h-16 md:w-14 md:h-14 rounded-full bg-design-muted flex items-center justify-center mb-3 group-hover:bg-design-muted/80 transition-colors shadow-sm", children: /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-8 w-8 md:h-6 md:w-6 text-design-foreground", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor", children: /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z" }) }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-design-foreground whitespace-nowrap", children: "More Categories" })
        ]
      }
    )
  ] }) }) }) }) });
};

const TestimonialsSection = ({
  testimonials,
  title
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isMobile, setIsMobile] = useState(false);
  const touchStartX = useRef(null);
  const touchEndX = useRef(null);
  const carouselRef = useRef(null);
  const minSwipeDistance = 50;
  useEffect(() => {
    setIsMounted(true);
  }, []);
  useEffect(() => {
    if (!isMounted) return;
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 768);
    };
    checkMobile();
    window.addEventListener("resize", checkMobile);
    return () => window.removeEventListener("resize", checkMobile);
  }, [isMounted]);
  useEffect(() => {
    if (!isMounted) return;
    const interval = setInterval(() => {
      handleNext();
    }, 5e3);
    return () => clearInterval(interval);
  }, [isMounted]);
  if (!isMounted) {
    return /* @__PURE__ */ jsx("section", { className: "py-20 bg-transparent relative overflow-hidden", children: /* @__PURE__ */ jsx("div", { className: "max-w-4xl mx-auto px-4 text-center", children: /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground", children: "Loading testimonials..." }) }) });
  }
  const handlePrev = () => {
    setCurrentIndex((prevIndex) => {
      const step = isMobile ? 1 : 3;
      const newIndex = prevIndex - step;
      if (newIndex < 0) {
        const lastValidIndex = Math.max(0, testimonials.length - (isMobile ? 1 : 3));
        return lastValidIndex;
      }
      return newIndex;
    });
  };
  const handleNext = () => {
    setCurrentIndex((prevIndex) => {
      const step = isMobile ? 1 : 3;
      const newIndex = prevIndex + step;
      if (newIndex >= testimonials.length) {
        return 0;
      }
      return newIndex;
    });
  };
  const onTouchStart = (e) => {
    touchStartX.current = e.targetTouches[0].clientX;
  };
  const onTouchMove = (e) => {
    touchEndX.current = e.targetTouches[0].clientX;
  };
  const onTouchEnd = () => {
    if (!touchStartX.current || !touchEndX.current) return;
    const distance = touchStartX.current - touchEndX.current;
    const isLeftSwipe = distance > minSwipeDistance;
    const isRightSwipe = distance < -minSwipeDistance;
    if (isLeftSwipe) {
      handleNext();
    } else if (isRightSwipe) {
      handlePrev();
    }
    touchStartX.current = null;
    touchEndX.current = null;
  };
  const pauseAutoAdvance = () => {
    if (carouselRef.current) {
      carouselRef.current.setAttribute("data-paused", "true");
    }
  };
  const resumeAutoAdvance = () => {
    if (carouselRef.current) {
      carouselRef.current.removeAttribute("data-paused");
    }
  };
  const getInitials = (name) => {
    const parts = name.split(" ");
    return parts.map((part) => part[0]).join("");
  };
  return /* @__PURE__ */ jsxs("section", { className: "py-20 bg-transparent relative overflow-hidden", children: [
    /* @__PURE__ */ jsx("div", { className: "absolute top-20 left-10 w-32 h-32 bg-[#262cbd]/20 dark:bg-[#1df292]/20 rounded-full blur-3xl" }),
    /* @__PURE__ */ jsx("div", { className: "absolute bottom-20 right-10 w-40 h-40 bg-[#f21d6b]/20 dark:bg-[#f2d91d]/20 rounded-full blur-3xl" }),
    /* @__PURE__ */ jsxs("div", { className: "max-w-4xl mx-auto px-4 relative z-10", children: [
      /* @__PURE__ */ jsxs("div", { className: "text-center mb-16", children: [
        /* @__PURE__ */ jsxs("div", { className: "inline-flex items-center bg-[#262cbd]/10 dark:bg-[#1df292]/10 backdrop-blur-sm px-4 py-2 rounded-full mb-6 border border-[#262cbd]/20 dark:border-[#1df292]/20", children: [
          /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-5 w-5 text-[#262cbd] dark:text-[#1df292] mr-2", viewBox: "0 0 20 20", fill: "currentColor", children: /* @__PURE__ */ jsx("path", { d: "M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-design-foreground font-medium text-sm", children: "Customer Reviews" })
        ] }),
        /* @__PURE__ */ jsx("h2", { className: "text-3xl md:text-[34px] font-normal text-design-foreground mb-3", children: title || "What Real Vapers Are Saying" }),
        /* @__PURE__ */ jsx("p", { className: "text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto mb-5", children: "Hear from our community of vapers who have found the best deals" }),
        /* @__PURE__ */ jsxs("div", { className: "flex flex-col md:flex-row justify-center items-center mb-4", children: [
          /* @__PURE__ */ jsx("div", { className: "flex mb-2 md:mb-0", children: [...Array(5)].map((_, i) => /* @__PURE__ */ jsx("span", { className: `text-xl md:text-2xl ${i < 4 ? "text-[#00b67a]" : "text-[#00b67a]/30"}`, children: "★" }, i)) }),
          /* @__PURE__ */ jsx("span", { className: "md:ml-3 text-center text-design-foreground font-medium text-xs md:text-sm", children: "4.4 out of 5 based on 24,115 reviews" })
        ] })
      ] }),
      /* @__PURE__ */ jsx(
        "div",
        {
          ref: carouselRef,
          className: "relative overflow-hidden mb-12",
          onTouchStart,
          onTouchMove,
          onTouchEnd,
          onMouseEnter: pauseAutoAdvance,
          onMouseLeave: resumeAutoAdvance,
          children: /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-6", children: testimonials.slice(currentIndex, currentIndex + (isMobile ? 1 : 3)).map((testimonial, index) => /* @__PURE__ */ jsx(
            "div",
            {
              className: "group",
              children: /* @__PURE__ */ jsxs("div", { className: "bg-white/50 dark:bg-black/10 backdrop-blur-sm rounded-xl p-6 border border-[#262cbd]/20 dark:border-[#1df292]/20 transition-all duration-300 h-full", children: [
                /* @__PURE__ */ jsxs("blockquote", { className: "text-design-foreground text-sm mb-5", children: [
                  '"',
                  testimonial.quote,
                  '"'
                ] }),
                (testimonial.product || testimonial.savedAmount) && /* @__PURE__ */ jsxs("div", { className: "mb-4 p-3 bg-[#262cbd]/5 dark:bg-[#1df292]/5 rounded-lg", children: [
                  testimonial.product && /* @__PURE__ */ jsxs("p", { className: "text-design-foreground/80 text-xs mb-1", children: [
                    /* @__PURE__ */ jsx("span", { className: "font-medium text-[#262cbd] dark:text-[#1df292]", children: "Product:" }),
                    " ",
                    testimonial.product
                  ] }),
                  testimonial.savedAmount && /* @__PURE__ */ jsxs("p", { className: "text-design-foreground text-xs font-bold", children: [
                    /* @__PURE__ */ jsx("span", { className: "text-[#262cbd] dark:text-[#1df292]", children: "Saved:" }),
                    " ",
                    testimonial.savedAmount
                  ] })
                ] }),
                /* @__PURE__ */ jsx("div", { className: "mb-4 flex", children: [...Array(5)].map((_, i) => /* @__PURE__ */ jsx(
                  "span",
                  {
                    className: `text-base ${i < testimonial.rating ? "text-[#00b67a]" : "text-[#00b67a]/30"}`,
                    children: "★"
                  },
                  i
                )) }),
                /* @__PURE__ */ jsxs("div", { className: "flex items-center", children: [
                  testimonial.image ? /* @__PURE__ */ jsx("div", { className: "w-12 h-12 rounded-full overflow-hidden mr-4", children: /* @__PURE__ */ jsx(
                    WebPImage,
                    {
                      src: testimonial.image,
                      alt: testimonial.name,
                      className: "w-full h-full object-cover",
                      fallbackSrc: `data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='48' height='48' viewBox='0 0 48 48'%3E%3Ctext x='50%25' y='50%25' dominant-baseline='middle' text-anchor='middle' font-size='20' font-weight='bold' fill='%234a4cd9'%3E${getInitials(testimonial.name)}%3C/text%3E%3C/svg%3E`
                    }
                  ) }) : /* @__PURE__ */ jsx("div", { className: "w-12 h-12 rounded-full bg-[#262cbd]/20 dark:bg-[#1df292]/20 flex items-center justify-center text-design-foreground font-bold mr-4", children: getInitials(testimonial.name) }),
                  /* @__PURE__ */ jsxs("div", { children: [
                    /* @__PURE__ */ jsx("div", { className: "text-design-foreground font-semibold text-sm", children: testimonial.name }),
                    /* @__PURE__ */ jsxs("div", { className: "text-design-foreground/70 text-xs flex items-center", children: [
                      testimonial.title,
                      testimonial.location && /* @__PURE__ */ jsxs(Fragment, { children: [
                        /* @__PURE__ */ jsx("span", { className: "mx-1", children: "•" }),
                        /* @__PURE__ */ jsx("span", { children: testimonial.location })
                      ] })
                    ] })
                  ] })
                ] })
              ] })
            },
            index + currentIndex
          )) })
        }
      ),
      /* @__PURE__ */ jsxs("div", { className: "flex justify-center items-center gap-4", children: [
        /* @__PURE__ */ jsx(
          "button",
          {
            onClick: handlePrev,
            className: "w-12 h-12 rounded-full bg-white/30 dark:bg-black/20 text-design-foreground flex items-center justify-center hover:bg-white/50 dark:hover:bg-black/30 transition-colors border border-[#262cbd]/20 dark:border-[#1df292]/20 focus:outline-none focus-visible:ring-2 focus-visible:ring-[#262cbd] dark:focus-visible:ring-[#1df292] focus-visible:ring-offset-2",
            "aria-label": "Previous testimonial",
            children: /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-5 w-5", viewBox: "0 0 20 20", fill: "currentColor", children: /* @__PURE__ */ jsx("path", { fillRule: "evenodd", d: "M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z", clipRule: "evenodd" }) })
          }
        ),
        /* @__PURE__ */ jsx("div", { className: "flex space-x-2", children: Array.from({ length: Math.ceil(testimonials.length / (isMobile ? 1 : 3)) }).map((_, index) => /* @__PURE__ */ jsx(
          "button",
          {
            onClick: () => setCurrentIndex(index * (isMobile ? 1 : 3)),
            className: `w-3 h-3 rounded-full transition-all duration-300 ${Math.floor(currentIndex / (isMobile ? 1 : 3)) === index ? "bg-[#262cbd] dark:bg-[#1df292] w-6" : "bg-[#262cbd]/30 dark:bg-[#1df292]/30 hover:bg-[#262cbd]/50 dark:hover:bg-[#1df292]/50"} focus:outline-none focus-visible:ring-2 focus-visible:ring-[#262cbd] dark:focus-visible:ring-[#1df292] focus-visible:ring-offset-1`,
            "aria-label": `Go to testimonial ${isMobile ? index + 1 : `group ${index + 1}`}`
          },
          index
        )) }),
        /* @__PURE__ */ jsx(
          "button",
          {
            onClick: handleNext,
            className: "w-12 h-12 rounded-full bg-white/30 dark:bg-black/20 text-design-foreground flex items-center justify-center hover:bg-white/50 dark:hover:bg-black/30 transition-colors border border-[#262cbd]/20 dark:border-[#1df292]/20 focus:outline-none focus-visible:ring-2 focus-visible:ring-[#262cbd] dark:focus-visible:ring-[#1df292] focus-visible:ring-offset-2",
            "aria-label": "Next testimonial",
            children: /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-5 w-5", viewBox: "0 0 20 20", fill: "currentColor", children: /* @__PURE__ */ jsx("path", { fillRule: "evenodd", d: "M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z", clipRule: "evenodd" }) })
          }
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "mt-12 mb-6 text-center", children: /* @__PURE__ */ jsx(
        "a",
        {
          href: "/deals",
          className: "inline-block text-sm font-medium transition-all duration-300 px-6 py-2.5 rounded-full\n            bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] text-white hover:text-white\n            shadow-glow hover:shadow-glow-light hover:animate-glow-border\n            hover:bg-animated-gradient bg-[length:400%_400%] hover:animate-gradient-move\n            dark:bg-gradient-to-b dark:from-[#1df292] dark:to-[#0db875] dark:text-black\n            dark:shadow-glow-dark dark:hover:shadow-glow-dark\n            dark:hover:bg-animated-gradient-dark",
          style: {
            minWidth: "180px",
            textAlign: "center"
          },
          children: "View All Deals"
        }
      ) })
    ] })
  ] });
};

const RealTimeSavings = ({
  title = "Real-Time Savings",
  subtitle = "See how much others are saving right now"
}) => {
  const users = [
    "Michael S.",
    "Jessica T.",
    "David R.",
    "Sarah K.",
    "Robert L.",
    "Emily W.",
    "James B.",
    "Olivia P.",
    "Thomas H.",
    "Emma C.",
    "Daniel M.",
    "Sophia G.",
    "William J.",
    "Ava R.",
    "Alexander D.",
    "Mia L.",
    "Ethan P.",
    "Charlotte W.",
    "Noah B.",
    "Amelia K.",
    "Benjamin F.",
    "Harper S.",
    "Mason T.",
    "Evelyn Z."
  ];
  const products = [
    "SMOK Nord 4",
    "Geek Vape Aegis",
    "Vaporesso XROS",
    "Uwell Caliburn G2",
    "VooPoo Drag X",
    "Lost Vape Orion",
    "Innokin Coolfire Z80",
    "Aspire Nautilus",
    "Freemax Maxus",
    "OXVA Origin X",
    "Dinner Lady E-Liquid",
    "Naked 100 E-Juice",
    "Jam Monster",
    "Pachamama",
    "Sadboy E-Liquid",
    "Candy King",
    "Cloud Nurdz",
    "Coastal Clouds",
    "Beard Vape Co",
    "Bad Drip Labs"
  ];
  const couponCodes = [
    "VAPE30",
    "EJUICE25",
    "COILS50",
    "NEWVAPER",
    "SUMMER20",
    "FALL15",
    "WINTER40",
    "SPRING10",
    "DEVICE25",
    "LIQUID20",
    "BUNDLE35",
    "STARTER15",
    "PREMIUM30",
    "SAVE50",
    "FLASH25",
    "WEEKEND15",
    "HOLIDAY40",
    "LOYALTY20",
    "WELCOME15",
    "RETURN10"
  ];
  const locations = [
    "New York",
    "Los Angeles",
    "Chicago",
    "Miami",
    "Seattle",
    "Austin",
    "Denver",
    "Boston",
    "San Francisco",
    "Dallas",
    "Atlanta",
    "Phoenix",
    "Philadelphia",
    "Houston",
    "San Diego",
    "Portland",
    "Las Vegas",
    "Nashville",
    "Orlando",
    "Detroit",
    "Minneapolis",
    "New Orleans"
  ];
  const generateAmount = () => {
    const baseAmounts = [
      { min: 5, max: 15 },
      // Small accessories/coils
      { min: 10, max: 25 },
      // E-liquids
      { min: 20, max: 40 },
      // Basic devices
      { min: 30, max: 60 },
      // Premium devices
      { min: 50, max: 100 }
      // Bundles/kits
    ];
    const category = Math.floor(Math.random() * baseAmounts.length);
    const { min, max } = baseAmounts[category];
    const amount = (Math.random() * (max - min) + min).toFixed(2);
    return `$${amount}`;
  };
  const generateActivities = useCallback(() => {
    const now = /* @__PURE__ */ new Date();
    const activities2 = [];
    for (let i = 0; i < 30; i++) {
      const minutesAgo = Math.floor(Math.random() * 30) + 1;
      const timestamp = new Date(now.getTime() - minutesAgo * 6e4);
      const isCouponUsage = Math.random() > 0.5;
      activities2.push({
        id: i + 1,
        user: users[Math.floor(Math.random() * users.length)],
        action: isCouponUsage ? `used coupon ${couponCodes[Math.floor(Math.random() * couponCodes.length)]}` : `saved on ${products[Math.floor(Math.random() * products.length)]}`,
        amount: generateAmount(),
        time: timestamp,
        location: locations[Math.floor(Math.random() * locations.length)]
      });
    }
    return activities2.sort((a, b) => b.time.getTime() - a.time.getTime());
  }, []);
  const formatTimeDiff = (date) => {
    const now = /* @__PURE__ */ new Date();
    const diffMs = now.getTime() - date.getTime();
    const diffMins = Math.floor(diffMs / 6e4);
    if (diffMins < 1) {
      return "just now";
    } else if (diffMins === 1) {
      return "1 minute ago";
    } else {
      return `${diffMins} minutes ago`;
    }
  };
  const [activities, setActivities] = useState([]);
  const [visibleActivities, setVisibleActivities] = useState([]);
  const [totalSavings, setTotalSavings] = useState("$12,450.75");
  useEffect(() => {
    setActivities(generateActivities());
  }, [generateActivities]);
  useEffect(() => {
    if (activities.length === 0) return;
    setVisibleActivities(activities.slice(0, 4));
    const updateInterval = () => {
      return Math.floor(Math.random() * 4e3) + 3e3;
    };
    const scheduleNextUpdate = () => {
      return setTimeout(() => {
        if (Math.random() < 0.3) {
          const newActivity = {
            id: activities.length + 1,
            user: users[Math.floor(Math.random() * users.length)],
            action: Math.random() > 0.5 ? `used coupon ${couponCodes[Math.floor(Math.random() * couponCodes.length)]}` : `saved on ${products[Math.floor(Math.random() * products.length)]}`,
            amount: generateAmount(),
            time: /* @__PURE__ */ new Date(),
            location: locations[Math.floor(Math.random() * locations.length)]
          };
          setActivities((prev) => [newActivity, ...prev].slice(0, 30));
          setVisibleActivities((prev) => [newActivity, ...prev.slice(0, 3)]);
        } else {
          setVisibleActivities((prev) => {
            const currentFirstIndex = activities.findIndex((a) => a.id === prev[0].id);
            const nextIndex = (currentFirstIndex + 1) % (activities.length - 3);
            return activities.slice(nextIndex, nextIndex + 4);
          });
        }
        timeoutRef.current = scheduleNextUpdate();
      }, updateInterval());
    };
    const timeoutRef = { current: scheduleNextUpdate() };
    return () => {
      if (timeoutRef.current) clearTimeout(timeoutRef.current);
    };
  }, [activities]);
  useEffect(() => {
    const interval = setInterval(() => {
      const currentTotal = parseFloat(totalSavings.replace(/[$,]/g, ""));
      const addAmount = Math.random() * 45 + 5;
      const newTotal = currentTotal + addAmount;
      setTotalSavings(`$${newTotal.toFixed(2).replace(/\B(?=(\d{3})+(?!\d))/g, ",")}`);
    }, Math.floor(Math.random() * 1e4) + 5e3);
    return () => clearInterval(interval);
  }, [totalSavings]);
  const getAvatarColor = (name) => {
    const colors = [
      "bg-design-primary/20 text-design-primary",
      "bg-design-tetradic1/20 text-design-tetradic1",
      "bg-design-tetradic2/20 text-design-tetradic2",
      "bg-design-secondary/20 text-design-secondary"
    ];
    const index = name.charCodeAt(0) % colors.length;
    return colors[index];
  };
  const getInitials = (name) => {
    const parts = name.split(" ");
    return parts.map((part) => part[0]).join("");
  };
  return /* @__PURE__ */ jsxs("section", { className: "py-8 bg-transparent relative overflow-hidden", children: [
    /* @__PURE__ */ jsx("div", { className: "absolute top-20 left-10 w-32 h-32 bg-[#262cbd]/10 dark:bg-[#1df292]/10 rounded-full blur-3xl" }),
    /* @__PURE__ */ jsx("div", { className: "absolute bottom-20 right-10 w-40 h-40 bg-[#f21d6b]/10 dark:bg-[#f2d91d]/10 rounded-full blur-3xl" }),
    /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-6 md:px-8 lg:px-10 relative z-10", children: [
      /* @__PURE__ */ jsxs("div", { className: "text-center mb-10", children: [
        /* @__PURE__ */ jsxs(
          motion.div,
          {
            initial: { opacity: 0, y: 20 },
            whileInView: { opacity: 1, y: 0 },
            transition: { duration: 0.5 },
            viewport: { once: true },
            className: "inline-flex items-center bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm px-4 py-2 rounded-full mb-4 border border-design-primary/20",
            children: [
              /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-4 w-4 text-design-primary mr-2", viewBox: "0 0 20 20", fill: "currentColor", children: /* @__PURE__ */ jsx("path", { fillRule: "evenodd", d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z", clipRule: "evenodd" }) }),
              /* @__PURE__ */ jsx("span", { className: "text-design-primary font-medium text-xs", children: "Live Activity" })
            ]
          }
        ),
        /* @__PURE__ */ jsx(
          motion.h2,
          {
            initial: { opacity: 0, y: 20 },
            whileInView: { opacity: 1, y: 0 },
            transition: { duration: 0.5, delay: 0.1 },
            viewport: { once: true },
            className: "text-3xl md:text-[34px] font-normal text-design-foreground mb-3",
            children: title
          }
        ),
        /* @__PURE__ */ jsx(
          motion.p,
          {
            initial: { opacity: 0, y: 20 },
            whileInView: { opacity: 1, y: 0 },
            transition: { duration: 0.5, delay: 0.2 },
            viewport: { once: true },
            className: "text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto",
            children: subtitle
          }
        )
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "max-w-4xl mx-auto", children: [
        /* @__PURE__ */ jsx("div", { className: "grid grid-cols-1 md:grid-cols-2 gap-4", children: /* @__PURE__ */ jsx(AnimatePresence, { mode: "popLayout", children: visibleActivities.map((activity, index) => /* @__PURE__ */ jsxs(
          motion.div,
          {
            initial: { opacity: 0, y: 20 },
            animate: { opacity: 1, y: 0 },
            exit: { opacity: 0, y: -20 },
            transition: { duration: 0.5 },
            className: "bg-design-card/95 backdrop-blur-sm border border-design-border rounded-xl p-4 flex items-center gap-4 hover:shadow-md transition-all duration-300 hover:border-design-primary/20",
            children: [
              /* @__PURE__ */ jsx("div", { className: `w-10 h-10 rounded-full flex items-center justify-center text-sm font-bold ${getAvatarColor(activity.user)}`, children: getInitials(activity.user) }),
              /* @__PURE__ */ jsx("div", { className: "flex-1", children: /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-start", children: [
                /* @__PURE__ */ jsxs("div", { children: [
                  /* @__PURE__ */ jsxs("p", { className: "font-medium text-design-foreground text-sm", children: [
                    activity.user,
                    " ",
                    /* @__PURE__ */ jsx("span", { className: "font-normal text-design-muted-foreground", children: activity.action })
                  ] }),
                  /* @__PURE__ */ jsxs("p", { className: "text-xs text-design-muted-foreground", children: [
                    formatTimeDiff(activity.time),
                    " • ",
                    activity.location
                  ] })
                ] }),
                /* @__PURE__ */ jsx("div", { className: "text-base font-bold text-design-tetradic1", children: activity.amount })
              ] }) })
            ]
          },
          `${activity.id}-${index}`
        )) }) }),
        /* @__PURE__ */ jsxs(
          motion.div,
          {
            initial: { opacity: 0, y: 20 },
            whileInView: { opacity: 1, y: 0 },
            transition: { duration: 0.5, delay: 0.3 },
            viewport: { once: true },
            className: "mt-10 bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm rounded-xl p-6 border border-design-primary/20 text-center",
            children: [
              /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground mb-2 text-xs md:text-sm", children: "Total savings by VapeHybrid users today" }),
              /* @__PURE__ */ jsxs("div", { className: "text-3xl md:text-4xl font-normal text-design-primary", children: [
                totalSavings,
                /* @__PURE__ */ jsx("span", { className: "text-design-primary/50 text-sm ml-1 animate-pulse", children: "+" })
              ] }),
              /* @__PURE__ */ jsx("p", { className: "text-xs text-design-muted-foreground mt-2", children: "Based on verified coupon usage" })
            ]
          }
        )
      ] })
    ] })
  ] });
};

const SimplifiedBrandGrid = ({
  brands = [],
  title = "Premium Vape Brands We Partner With",
  subtitle = "Exclusive deals from the most trusted names in the industry",
  viewAllLink = "/brands",
  maxBrands = 9
}) => {
  const displayBrands = useMemo(() => {
    const withLogos = brands.filter((brand) => brand.logo_url);
    return withLogos.length >= 3 ? withLogos : brands.slice(0, maxBrands);
  }, [brands, maxBrands]);
  return /* @__PURE__ */ jsxs("div", { className: "py-16 md:py-20 lg:py-24 bg-transparent relative overflow-hidden", children: [
    /* @__PURE__ */ jsx("div", { className: "absolute top-20 left-10 w-32 h-32 bg-[#262cbd]/10 dark:bg-[#1df292]/10 rounded-full blur-3xl" }),
    /* @__PURE__ */ jsx("div", { className: "absolute bottom-20 right-10 w-40 h-40 bg-[#f21d6b]/10 dark:bg-[#f2d91d]/10 rounded-full blur-3xl" }),
    /* @__PURE__ */ jsxs("div", { className: "max-w-4xl mx-auto px-4 relative z-10 flex flex-col items-center", children: [
      /* @__PURE__ */ jsxs("div", { className: "text-center mb-12", children: [
        /* @__PURE__ */ jsxs("div", { className: "inline-flex items-center bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm px-4 py-2 rounded-full mb-4 border border-design-primary/20", children: [
          /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-4 w-4 text-design-primary mr-2", viewBox: "0 0 20 20", fill: "currentColor", children: /* @__PURE__ */ jsx("path", { fillRule: "evenodd", d: "M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z", clipRule: "evenodd" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-design-primary font-medium text-xs", children: "Trusted Partners" })
        ] }),
        /* @__PURE__ */ jsx(
          motion.h2,
          {
            initial: { opacity: 0, y: 20 },
            whileInView: { opacity: 1, y: 0 },
            transition: { duration: 0.5 },
            viewport: { once: true },
            className: "text-3xl md:text-[34px] font-normal text-design-foreground mb-4",
            children: title
          }
        ),
        /* @__PURE__ */ jsx("p", { className: "text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto mb-8", children: subtitle })
      ] }),
      /* @__PURE__ */ jsx("div", { className: "grid grid-cols-2 sm:grid-cols-3 gap-6 md:gap-8 max-w-4xl mx-auto mb-12", children: displayBrands.slice(0, 9).map((brand, index) => /* @__PURE__ */ jsxs(
        "a",
        {
          href: `/brands/${brand.slug || brand.name.toLowerCase().replace(/\s+/g, "-")}`,
          className: "group flex flex-col items-center justify-center p-4 md:p-6 bg-design-card/50 dark:bg-design-card/30 backdrop-blur-sm rounded-xl border border-design-border hover:border-design-primary/40 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]",
          "aria-label": `View deals from ${brand.name}`,
          children: [
            /* @__PURE__ */ jsx("div", { className: "h-20 w-full flex items-center justify-center p-2 mb-3 transition-transform duration-300 group-hover:scale-105", children: brand.logo_url ? /* @__PURE__ */ jsx(
              "img",
              {
                src: brand.logo_url,
                alt: brand.name,
                className: "h-full w-full object-contain max-h-16",
                loading: "lazy",
                width: 120,
                height: 60
              }
            ) : /* @__PURE__ */ jsx("div", { className: "w-16 h-16 flex items-center justify-center bg-design-primary/10 rounded-full", children: /* @__PURE__ */ jsx("span", { className: "text-xl font-bold text-design-primary", children: brand.name.charAt(0).toUpperCase() }) }) }),
            /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-center text-design-foreground/90 group-hover:text-design-primary transition-colors duration-300", children: brand.name })
          ]
        },
        brand.id || index
      )) }),
      /* @__PURE__ */ jsx("div", { className: "text-center mt-8", children: /* @__PURE__ */ jsxs(
        "a",
        {
          href: viewAllLink,
          className: "inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-full transition-all duration-300\n              bg-gradient-to-b from-design-primary to-design-primary/90 text-white hover:text-white\n              shadow-glow hover:shadow-glow-light hover:animate-glow-border\n              hover:bg-animated-gradient bg-[length:400%_400%] hover:animate-gradient-move\n              dark:from-[#1df292] dark:to-[#0db875] dark:text-black dark:shadow-glow-dark\n              dark:hover:shadow-glow-dark dark:hover:bg-animated-gradient-dark",
          children: [
            "View All Brand Partners",
            /* @__PURE__ */ jsx("svg", { className: "ml-2 w-4 h-4", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", xmlns: "http://www.w3.org/2000/svg", children: /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M9 5l7 7-7 7" }) })
          ]
        }
      ) })
    ] })
  ] });
};

const $$Astro$1 = createAstro("http://localhost:4321");
const $$Section = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$Section;
  const {
    id,
    class: className = "",
    background = "default",
    pattern = "none",
    innerClass = ""
  } = Astro2.props;
  const backgroundStyles = {
    default: "bg-design-background",
    primary: "bg-design-primary text-white dark:text-black",
    secondary: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    accent: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    muted: "bg-design-muted"
  };
  const patternStyles = {
    none: "",
    dots: "bg-[url('/patterns/dot-pattern-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/dot-pattern-dark.svg')] dark:opacity-5",
    grid: "bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-5",
    circles: "bg-[url('/patterns/circle-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/circle-dark.svg')] dark:opacity-5"
  };
  const hasTransparentBg = className.includes("bg-transparent");
  const sectionClasses = [
    "home-section",
    !hasTransparentBg && backgroundStyles[background],
    className
  ].filter(Boolean).join(" ");
  return renderTemplate`${maybeRenderHead()}<section${addAttribute(id, "id")}${addAttribute(sectionClasses, "class")}>  ${pattern !== "none" && renderTemplate`<div${addAttribute(`absolute inset-0 ${patternStyles[pattern]} z-[-1]`, "class")}></div>`}  <div class="absolute top-20 left-10 w-32 h-32 bg-design-primary bg-opacity-5 dark:bg-design-primary dark:bg-opacity-5 rounded-full blur-3xl"></div> <div class="absolute bottom-20 right-10 w-40 h-40 bg-design-primary bg-opacity-5 dark:bg-design-primary dark:bg-opacity-5 rounded-full blur-3xl"></div> <div${addAttribute(`home-section-inner ${innerClass}`, "class")}> ${renderSlot($$result, $$slots["default"])} </div> </section>`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/Section.astro", void 0);

const $$Astro = createAstro("http://localhost:4321");
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const title = "Best Vape Coupons & Discount Codes - Tested by Real Vapers | VapeHybrid";
  const description = "Score verified vape coupon codes from Geek Bar, Lost Mary, Voopoo & SMOK! Fellow vapers test every code daily. Save up to 50% on your all-day vapes, sub-ohm setups, and nic salts. No expired codes, just working discounts for the vape community.";
  const websiteStructuredData = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": "VapeHybrid - Vape Coupons & Discount Codes",
    "alternateName": "VapeHybrid Coupon Site",
    "url": Astro2.url.origin,
    "potentialAction": [
      {
        "@type": "SearchAction",
        "target": `${Astro2.url.origin}/search?q={search_term_string}`,
        "query-input": "required name=search_term_string"
      },
      {
        "@type": "SearchAction",
        "target": `${Astro2.url.origin}/coupons?search={search_term_string}`,
        "query-input": "required name=search_term_string"
      }
    ],
    "description": description,
    "keywords": "vape coupons, vape discount codes, vape deals, ejuice connect coupon, geek bar coupons, lost mary discount, voopoo promo codes, vape community savings",
    "audience": {
      "@type": "Audience",
      "audienceType": "Vaping Community"
    },
    "publisher": {
      "@type": "Organization",
      "name": "VapeHybrid",
      "logo": {
        "@type": "ImageObject",
        "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`
      }
    }
  };
  const organizationStructuredData = {
    "@context": "https://schema.org",
    "@type": ["Organization", "LocalBusiness"],
    "name": "VapeHybrid",
    "alternateName": "VapeHybrid Coupon Platform",
    "url": Astro2.url.origin,
    "logo": `${Astro2.url.origin}/Vapehybrid light icon.svg`,
    "description": "The vaping community's trusted source for verified coupon codes and discount deals from top vape brands",
    "foundingDate": "2018",
    "slogan": "Verified Vape Coupons That Actually Work",
    "knowsAbout": [
      "Vape Coupons",
      "E-cigarette Discounts",
      "Vaping Community",
      "Coupon Verification",
      "Vape Brand Partnerships"
    ],
    "serviceArea": {
      "@type": "Country",
      "name": "United States"
    },
    "sameAs": [
      "https://twitter.com/vapehybrid",
      "https://facebook.com/vapehybrid",
      "https://instagram.com/vapehybrid"
    ],
    "contactPoint": {
      "@type": "ContactPoint",
      "telephone": "",
      "contactType": "customer service",
      "email": "<EMAIL>",
      "availableLanguage": "English",
      "areaServed": "US"
    },
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": "4.8",
      "reviewCount": "1200",
      "bestRating": "5",
      "worstRating": "1"
    }
  };
  const faqStructuredData = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.slice(0, 8).map((faq) => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
  const supabase = createServerSupabaseClient();
  const { data: featuredDeals } = await supabase.from("deals").select(`
    id,
    title,
    cleaned_title,
    discount,
    image_url,
    imagebig_url,
    tracking_url,
    merchants:merchant_id (name),
    brand_id,
    brands:brand_id (name, logo_url),
    is_featured,
    deal_end_date
  `).order("created_at", { ascending: false }).limit(6);
  const now = /* @__PURE__ */ new Date();
  const in24Hours = new Date(now);
  in24Hours.setHours(now.getHours() + 24);
  const { data: expiringDeals24h } = await supabase.from("deals").select(`
    id,
    title,
    cleaned_title,
    discount,
    image_url,
    imagebig_url,
    tracking_url,
    merchants:merchant_id (name),
    brand_id,
    brands:brand_id (name, logo_url),
    deal_end_date
  `).not("deal_end_date", "is", null).gte("deal_end_date", now.toISOString()).lte("deal_end_date", in24Hours.toISOString()).order("deal_end_date", { ascending: true }).limit(6);
  let flashDeals = expiringDeals24h;
  if (!flashDeals || flashDeals.length === 0) {
    const in72Hours = new Date(now);
    in72Hours.setHours(now.getHours() + 72);
    const { data: expiringDeals72h } = await supabase.from("deals").select(`
      id,
      title,
      cleaned_title,
      discount,
      image_url,
      imagebig_url,
      tracking_url,
      merchants:merchant_id (name),
      brand_id,
      brands:brand_id (name, logo_url),
      deal_end_date
    `).not("deal_end_date", "is", null).gte("deal_end_date", now.toISOString()).lte("deal_end_date", in72Hours.toISOString()).order("deal_end_date", { ascending: true }).limit(6);
    flashDeals = expiringDeals72h;
    if (!flashDeals || flashDeals.length === 0) {
      const in7Days = new Date(now);
      in7Days.setDate(now.getDate() + 7);
      const { data: expiringDeals7d } = await supabase.from("deals").select(`
        id,
        title,
        cleaned_title,
        discount,
        image_url,
        imagebig_url,
        tracking_url,
        merchants:merchant_id (name),
        brand_id,
        brands:brand_id (name, logo_url),
        deal_end_date
      `).not("deal_end_date", "is", null).gte("deal_end_date", now.toISOString()).lte("deal_end_date", in7Days.toISOString()).order("deal_end_date", { ascending: true }).limit(6);
      flashDeals = expiringDeals7d;
    }
  }
  const processedDeals = featuredDeals?.map((deal) => {
    const getBrandName = () => {
      if (!deal.brands) return void 0;
      if (typeof deal.brands === "object" && deal.brands && "name" in deal.brands) {
        return deal.brands.name;
      }
      return void 0;
    };
    const getBrandLogo = () => {
      if (!deal.brands) return void 0;
      if (typeof deal.brands === "object" && deal.brands && "logo_url" in deal.brands) {
        return deal.brands.logo_url;
      }
      return void 0;
    };
    const getMerchantName = () => {
      if (!deal.merchants) return void 0;
      if (typeof deal.merchants === "object" && deal.merchants && "name" in deal.merchants) {
        return deal.merchants.name;
      }
      return void 0;
    };
    return {
      id: deal.id,
      title: deal.title,
      cleaned_title: deal.cleaned_title,
      discount: deal.discount,
      image_url: deal.image_url,
      imagebig_url: deal.imagebig_url,
      tracking_url: deal.tracking_url,
      merchant_name: getMerchantName(),
      brand_id: deal.brand_id,
      brand_name: getBrandName(),
      brand_logo_url: getBrandLogo(),
      is_featured: deal.is_featured || false,
      deal_end_date: deal.deal_end_date
    };
  }) || [];
  const processedFlashDeals = flashDeals?.map((deal) => {
    const getBrandName = () => {
      if (!deal.brands) return void 0;
      if (typeof deal.brands === "object" && deal.brands && "name" in deal.brands) {
        return deal.brands.name;
      }
      return void 0;
    };
    const getBrandLogo = () => {
      if (!deal.brands) return void 0;
      if (typeof deal.brands === "object" && deal.brands && "logo_url" in deal.brands) {
        return deal.brands.logo_url;
      }
      return void 0;
    };
    const getMerchantName = () => {
      if (!deal.merchants) return void 0;
      if (typeof deal.merchants === "object" && deal.merchants && "name" in deal.merchants) {
        return deal.merchants.name;
      }
      return void 0;
    };
    return {
      id: deal.id,
      title: deal.title,
      cleaned_title: deal.cleaned_title,
      discount: deal.discount,
      image_url: deal.image_url,
      imagebig_url: deal.imagebig_url,
      tracking_url: deal.tracking_url,
      merchant_name: getMerchantName(),
      brand_id: deal.brand_id,
      brand_name: getBrandName(),
      brand_logo_url: getBrandLogo(),
      deal_end_date: deal.deal_end_date
    };
  }) || [];
  const { data: topBrands } = await supabase.from("brands").select("*").order("name").limit(12);
  const { data: categories } = await supabase.from("categories").select("*").order("name");
  const testimonials = [
    {
      quote: "Finally found a coupon site that gets the vape community! Saved over $200 on my ADV setup last month. Every code actually works - no more expired BS like other sites.",
      name: "Sarah K.",
      title: "Cloud Chaser",
      rating: 5,
      image: "/images/testimonials/sarah-k.jpeg",
      // WebP version will be automatically used by WebPImage component
      savedAmount: "$215.50",
      product: "SMOK Nord 4 Kit & Coil Bundle",
      location: "New York"
    },
    {
      quote: "These guys actually vape - you can tell from their deals. Found killer discounts on my favorite nic salts and the codes work every single time. No more hunting through expired coupons!",
      name: "Michael T.",
      title: "MTL Enthusiast",
      rating: 5,
      image: "/images/testimonials/michael-t.jpeg",
      savedAmount: "$178.25",
      product: "Vaporesso XROS 3 & Premium E-Juice",
      location: "Los Angeles"
    },
    {
      quote: "Been building coils for years and VapeHybrid always has the best deals on wire and cotton. Their exclusive codes beat going direct to manufacturers. Fellow vapers helping vapers!",
      name: "Jessica R.",
      title: "Coil Builder",
      rating: 4,
      image: "/images/testimonials/jessica-r.jpeg",
      savedAmount: "$320.75",
      product: "Geek Vape Aegis Legend 2 & Build Supplies",
      location: "Chicago"
    },
    {
      quote: "I run a local vape shop and send customers here when they want online deals. VapeHybrid knows their stuff - only legit codes from trusted brands. Real vapers supporting the community.",
      name: "David M.",
      title: "B&M Shop Owner",
      rating: 5,
      image: "/images/testimonials/david-m.jpeg",
      savedAmount: "$450.00",
      product: "Wholesale E-Liquid Stock",
      location: "Miami"
    },
    {
      quote: "Made the switch from stinkies last year and VapeHybrid saved me hundreds on starter gear. Their beginner-friendly deals helped me find my perfect all-day vape without breaking the bank.",
      name: "Robert J.",
      title: "Ex-Smoker",
      rating: 5,
      image: "/images/testimonials/robert-j.jpeg",
      savedAmount: "$85.30",
      product: "Uwell Caliburn G2 Starter Kit",
      location: "Seattle"
    },
    {
      quote: "Love the flash deals! Scored a Lost Mary at 40% off last week. The countdown timers keep me checking back - it's like Black Friday for vapers every day!",
      name: "Amanda P.",
      title: "Flavor Chaser",
      rating: 5,
      image: "/images/testimonials/amanda-p.jpg",
      savedAmount: "$125.45",
      product: "Lost Vape Ursa Quest Multi Kit",
      location: "Denver"
    }
  ];
  const categoryItems = categories?.map((category) => ({
    title: category.name,
    description: `Browse all ${category.name.toLowerCase()} deals and coupons`,
    link: `/coupons/categories/${category.slug}`,
    image: category.category_logo
  })) || [];
  const comparisonFeatures = [
    {
      feature: "Daily Deal Verification",
      vapehybrid: true,
      others: false
    },
    {
      feature: "Exclusive Coupon Codes",
      vapehybrid: true,
      others: "Sometimes"
    },
    {
      feature: "Number of Verified Brands",
      vapehybrid: "238+",
      others: "Varies"
    },
    {
      feature: "Independent Testing",
      vapehybrid: true,
      others: false
    },
    {
      feature: "User Reviews",
      vapehybrid: true,
      others: "Sometimes"
    },
    {
      feature: "Success Rate Tracking",
      vapehybrid: true,
      others: false
    },
    {
      feature: "Product Reviews",
      vapehybrid: true,
      others: "Limited"
    },
    {
      feature: "Giveaways",
      vapehybrid: true,
      others: "Rarely"
    },
    {
      feature: "Vape Industry News",
      vapehybrid: true,
      others: "No"
    }
  ];
  const offerStructuredData = processedDeals.map((deal, index) => {
    const seed = Number(deal.id) || index;
    const ratingValue = (4 + (Math.sin(seed * 0.1) * 0.5 + 0.5) * 1).toFixed(1);
    const ratingCount = Math.floor(15 + (Math.sin(seed * 0.2) * 0.5 + 0.5) * 85);
    return {
      "@context": "https://schema.org",
      "@type": "Offer",
      "itemOffered": {
        "@type": "Product",
        "name": deal.cleaned_title || deal.title,
        "description": `${deal.cleaned_title || deal.title} - Save with this exclusive deal from VapeHybrid`,
        "brand": {
          "@type": "Brand",
          "name": deal.brand_name || "Vape Brand"
        },
        "image": deal.imagebig_url || deal.image_url || deal.brand_logo_url || "/placeholder-deal.jpg",
        "aggregateRating": {
          "@type": "AggregateRating",
          "ratingValue": ratingValue,
          "bestRating": "5",
          "worstRating": "1",
          "ratingCount": ratingCount.toString()
        }
      },
      "offeredBy": {
        "@type": "Organization",
        "name": deal.merchant_name || "VapeHybrid Partner"
      },
      "price": "0",
      "priceCurrency": "USD",
      "availability": "https://schema.org/InStock",
      "url": `${Astro2.url.origin}/go/${deal.id}`,
      "seller": {
        "@type": "Organization",
        "name": "VapeHybrid"
      },
      "validFrom": (/* @__PURE__ */ new Date()).toISOString(),
      "priceValidUntil": new Date((/* @__PURE__ */ new Date()).setMonth((/* @__PURE__ */ new Date()).getMonth() + 1)).toISOString()
    };
  });
  const structuredData = [
    websiteStructuredData,
    organizationStructuredData,
    ...offerStructuredData,
    faqStructuredData
  ];
  return renderTemplate`${renderComponent($$result, "HomeLayout", $$HomeLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": async ($$result2) => renderTemplate`  ${renderComponent($$result2, "EnhancedHero", EnhancedHero, { "subtitle": "Score verified coupon codes for your favorite vape gear! Fellow vapers test every code daily so you get working discounts on everything from all-day vapes to sub-ohm setups. No expired codes, just real savings for the vape community.", "ctaText": "Find Vape Coupons", "ctaLink": "/coupons", "secondaryCTAText": "How We Verify Codes", "secondaryCTALink": "/how-it-works", "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/enhanced-hero", "client:component-export": "EnhancedHero" })}  ${renderComponent($$result2, "CategoryNav", CategoryNav, { "categories": categoryItems.map((cat) => ({
    title: cat.title,
    slug: cat.link.split("/").pop() || ""
  })), "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/category-nav", "client:component-export": "CategoryNav" })}  ${renderComponent($$result2, "Section", $$Section, { "class": "home-section bg-transparent backdrop-blur-sm py-0", "pattern": "dots" }, { "default": async ($$result3) => renderTemplate` ${renderComponent($$result3, "TestimonialsSection", TestimonialsSection, { "testimonials": testimonials, "title": "What Fellow Vapers Are Saying About Our Coupon Codes", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/testimonials-section", "client:component-export": "TestimonialsSection" })} ` })}  ${renderComponent($$result2, "Section", $$Section, { "class": "home-section bg-transparent backdrop-blur-sm py-0", "pattern": "dots" }, { "default": async ($$result3) => renderTemplate` ${renderComponent($$result3, "RealTimeSavings", RealTimeSavings, { "title": "Live Savings from the Vape Community", "subtitle": "Join thousands of fellow vapers scoring deals on their favorite gear with verified coupon codes", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/real-time-savings", "client:component-export": "RealTimeSavings" })} ` })}  ${renderComponent($$result2, "Section", $$Section, { "class": "home-section bg-transparent backdrop-blur-sm py-0", "pattern": "dots" }, { "default": async ($$result3) => renderTemplate` ${renderComponent($$result3, "FeaturedDeals", FeaturedDeals, { "deals": processedDeals, "flashDeals": processedFlashDeals, "viewAllLink": "/coupons", "viewAllText": "Browse All Vape Coupons", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/featured-deals", "client:component-export": "FeaturedDeals" })} ` })}  ${renderComponent($$result2, "SimplifiedBrandGrid", SimplifiedBrandGrid, { "brands": topBrands || [], "title": "Top Vape Brands with Exclusive Coupon Codes", "subtitle": "Score deals from community favorites like Geek Bar, Lost Mary, Voopoo, SMOK and more trusted names vapers love", "viewAllLink": "/coupons/brands", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/SimplifiedBrandGrid", "client:component-export": "SimplifiedBrandGrid" })}  ${renderComponent($$result2, "Section", $$Section, { "class": "home-section bg-transparent backdrop-blur-sm py-0", "pattern": "dots" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="max-w-4xl mx-auto px-4 py-20 flex flex-col items-center"> <div class="text-center mb-10"> <div class="inline-flex items-center bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm px-4 py-2 rounded-full mb-4 border border-design-primary/20"> <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 text-design-primary mr-2" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM7 9a1 1 0 000 2h6a1 1 0 100-2H7z" clip-rule="evenodd"></path> </svg> <span class="text-design-primary font-medium text-xs">Comparison</span> </div> <h2 class="text-3xl md:text-[34px] font-normal text-design-foreground mb-4">Why Vapers Choose VapeHybrid Over Other Coupon Sites</h2> <p class="text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto mb-8">
We're vapers helping vapers - see why our community trusts us for working coupon codes
</p> </div> <!-- Comparison Table --> <div class="bg-design-card/95 backdrop-blur-sm rounded-xl overflow-hidden border border-design-border"> <div class="grid grid-cols-4 text-center border-b border-design-border"> <div class="p-4 font-medium text-xs text-design-foreground">Features</div> <div class="p-4 font-bold text-xs text-design-primary">VapeHybrid</div> <div class="p-4 font-medium text-xs text-design-muted-foreground">Other Coupon Sites</div> <div class="p-4 font-medium text-xs text-design-muted-foreground">Vape Review Sites</div> </div> ${comparisonFeatures.map((feature, index) => renderTemplate`<div${addAttribute(`grid grid-cols-4 text-center ${index !== comparisonFeatures.length - 1 ? "border-b border-design-border/50" : ""}`, "class")}> <div class="p-3 text-xs font-medium text-design-foreground">${feature.feature}</div> <div class="p-3"> ${typeof feature.vapehybrid === "boolean" ? feature.vapehybrid ? renderTemplate`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary mx-auto" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg>` : renderTemplate`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-muted-foreground mx-auto" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path> </svg>` : renderTemplate`<span class="text-xs font-medium text-design-primary">${feature.vapehybrid}</span>`} </div> <div class="p-3"> ${typeof feature.others === "boolean" ? feature.others ? renderTemplate`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary mx-auto" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg>` : renderTemplate`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-muted-foreground mx-auto" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path> </svg>` : renderTemplate`<span class="text-xs font-medium text-design-muted-foreground">${feature.others}</span>`} </div> <div class="p-3"> ${typeof feature.others === "boolean" ? feature.others ? renderTemplate`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary mx-auto" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg>` : renderTemplate`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-muted-foreground mx-auto" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clip-rule="evenodd"></path> </svg>` : renderTemplate`<span class="text-xs font-medium text-design-muted-foreground">${feature.others}</span>`} </div> </div>`)} </div> <!-- How We Verify Coupon Codes for the Vape Community --> <div class="bg-design-card/95 backdrop-blur-sm rounded-xl p-6 border border-design-border mt-12 mb-12"> <h3 class="text-lg font-bold text-design-foreground mb-4 text-center">How We Verify Every Vape Coupon Code</h3> <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4"> <div class="flex items-start"> <div class="bg-design-primary/10 rounded-full p-2 mr-3"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <div> <p class="text-xs text-design-foreground font-medium">Real vapers test every coupon code with actual purchases before we publish</p> </div> </div> <div class="flex items-start"> <div class="bg-design-primary/10 rounded-full p-2 mr-3"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg> </div> <div> <p class="text-xs text-design-foreground font-medium">Our vape community team checks codes daily - expired ones get removed immediately</p> </div> </div> <div class="flex items-start"> <div class="bg-design-primary/10 rounded-full p-2 mr-3"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary" viewBox="0 0 20 20" fill="currentColor"> <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z"></path> </svg> </div> <div> <p class="text-xs text-design-foreground font-medium">We only work with trusted vape brands and merchants that vapers actually use</p> </div> </div> <div class="flex items-start"> <div class="bg-design-primary/10 rounded-full p-2 mr-3"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-design-primary" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M2.166 4.999A11.954 11.954 0 0010 1.944 11.954 11.954 0 0017.834 5c.11.65.166 1.32.166 2.001 0 5.225-3.34 9.67-8 11.317C5.34 16.67 2 12.225 2 7c0-.682.057-1.35.166-2.001zm11.541 3.708a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"></path> </svg> </div> <div> <p class="text-xs text-design-foreground font-medium">Our team are actual vapers - we understand what the community needs and wants</p> </div> </div> </div> </div> <!-- CTA Button - Larger Size --> <div class="text-center mt-12 mb-6"> <a href="/about" class="inline-block text-sm font-medium transition-all duration-300 px-6 py-2.5 rounded-full
        bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] text-white hover:text-white
        shadow-glow hover:shadow-glow-light hover:animate-glow-border
        hover:bg-animated-gradient bg-[length:400%_400%] hover:animate-gradient-move
        dark:bg-gradient-to-b dark:from-[#1df292] dark:to-[#0db875] dark:text-black
        dark:shadow-glow-dark dark:hover:shadow-glow-dark
        dark:hover:bg-animated-gradient-dark" style="min-width: 180px; text-align: center;">
Why We're Different
</a> </div> </div> ` })}  ${renderComponent($$result2, "Section", $$Section, { "class": "home-section bg-transparent backdrop-blur-sm py-0", "pattern": "dots" }, { "default": async ($$result3) => renderTemplate` ${renderComponent($$result3, "NewsletterSection", NewsletterSection, { "title": "Never Miss a Vape Deal Again", "subtitle": "Join 50,000+ fellow vapers and get the hottest coupon codes delivered straight to your inbox", "buttonText": "Get Vape Deals", "client:visible": true, "client:component-hydration": "visible", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/newsletter-section", "client:component-export": "NewsletterSection" })} ` })}  ${renderComponent($$result2, "Section", $$Section, { "class": "home-section bg-transparent backdrop-blur-sm py-0", "pattern": "dots" }, { "default": async ($$result3) => renderTemplate` ${renderComponent($$result3, "FAQSection", FAQSection, { "title": "Vape Coupon Questions from Fellow Vapers", "subtitle": "Everything the vape community wants to know about scoring the best deals on gear", "faqs": faqs, "client:visible": true, "client:component-hydration": "visible", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/faq-section", "client:component-export": "FAQSection" })} ` })} ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/index.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/index.astro";
const $$url = "";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
