import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../assets/js/MainLayout-BVJnMCp3.js';
export { renderers } from '../../renderers.mjs';

const $$Unsubscribed = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Unsubscribed - VapeHybrid" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-12"> <div class="max-w-2xl mx-auto"> <div class="bg-design-card border border-design-border rounded-lg p-8 text-center"> <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mx-auto mb-6 text-design-muted-foreground" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M3 19v-8.93a2 2 0 01.89-1.664l7-4.666a2 2 0 012.22 0l7 4.666A2 2 0 0121 10.07V19M3 19a2 2 0 002 2h14a2 2 0 002-2M3 19l6.75-4.5M21 19l-6.75-4.5M3 10l6.75 4.5M21 10l-6.75 4.5m0 0l-1.14.76a2 2 0 01-2.22 0l-1.14-.76"></path> </svg> <h1 class="text-3xl font-bold text-design-foreground mb-4">You've Been Unsubscribed</h1> <p class="text-design-muted-foreground mb-6">
We're sorry to see you go. Your email has been removed from our newsletter list.
</p> <p class="text-design-muted-foreground mb-8">
If you unsubscribed by mistake or change your mind, you can always subscribe again from our homepage.
</p> <div class="flex flex-col sm:flex-row gap-4 justify-center"> <a href="/" class="px-6 py-3 bg-design-primary text-white dark:text-black font-medium rounded-lg hover:bg-design-primary/90 transition-colors">
Return to Homepage
</a> <a href="/contact" class="px-6 py-3 border border-design-border text-design-muted-foreground font-medium rounded-lg hover:bg-design-muted/10 transition-colors">
Contact Us
</a> </div> </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/unsubscribed.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/unsubscribed.astro";
const $$url = "/newsletter/unsubscribed";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Unsubscribed,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
