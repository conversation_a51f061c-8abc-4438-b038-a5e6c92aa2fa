import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeR<PERSON>Head, b as addAttribute, e as renderSlot } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { c as createServerSupabaseClient } from '../assets/js/server-e_5TR1Eu.js';
import { F as FilterDrawer, a as SortSelect, V as ViewToggle, c as $$Pagination } from '../assets/js/app/design-system-CUssmfny.js';
import { c as DealsPageWrapper } from '../assets/js/app/deals-hegwWSnP.js';
import { $ as $$MainLayout } from '../assets/js/MainLayout-BVJnMCp3.js';
import { $ as $$InfoPageHeader, a as $$InfoPageSection } from '../assets/js/InfoPageSection-CrV_q83G.js';
export { renderers } from '../renderers.mjs';

const $$Astro$1 = createAstro("http://localhost:4321");
const $$SearchPageLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$SearchPageLayout;
  const {
    title,
    description,
    structuredData,
    background = "default",
    pattern = "dots"
  } = Astro2.props;
  const backgroundStyles = {
    default: "bg-design-background",
    primary: "bg-design-primary text-white dark:text-black",
    secondary: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    accent: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    muted: "bg-design-muted",
    transparent: "bg-transparent"
  };
  const patternStyles = {
    none: "",
    dots: "bg-[url('/patterns/dots-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/dots-dark.svg')] dark:opacity-10",
    grid: "bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-10",
    circles: "bg-[url('/patterns/circle-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/circle-dark.svg')] dark:opacity-10",
    hexagon: "bg-[url('/patterns/hexagon-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/hexagon-dark.svg')] dark:opacity-10",
    wave: "bg-[url('/patterns/wave-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/wave-dark.svg')] dark:opacity-10"
  };
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div${addAttribute(`relative min-h-screen ${backgroundStyles[background]}`, "class")}> <!-- Enhanced glass effect background pattern similar to homepage --> <div class="absolute inset-0 z-0 pointer-events-none overflow-hidden"> <!-- Background pattern based on pattern prop --> ${pattern !== "none" && renderTemplate`<div${addAttribute(`absolute inset-0 ${patternStyles[pattern]}`, "class")}></div>`} <!-- Secondary pattern for texture - only if pattern is not 'none' --> ${pattern !== "none" && pattern !== "hexagon" && renderTemplate`<div class="absolute inset-0 bg-[url('/patterns/hexagon-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/hexagon-dark.svg')] dark:opacity-5"></div>`} <!-- Gradient overlay with blur effect - only if background is not transparent --> ${background !== "transparent" && renderTemplate`<div class="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-design-background/80 via-design-background/60 to-design-background/40 backdrop-blur-[3px]"></div>`} <!-- Decorative elements - Monochromatic --> <div class="absolute top-[-5%] right-[-10%] w-[40%] h-[40%] bg-design-primary/10 dark:bg-design-primary/5 rounded-full filter blur-[80px]"></div> <div class="absolute bottom-[-5%] left-[-10%] w-[40%] h-[40%] bg-design-primary/10 dark:bg-design-primary/5 rounded-full filter blur-[80px]"></div> <!-- Additional decorative elements --> <div class="absolute top-[30%] left-[20%] w-[25%] h-[25%] bg-design-primary/8 dark:bg-design-primary/3 rounded-full filter blur-[60px] animate-float"></div> <div class="absolute bottom-[20%] right-[15%] w-[20%] h-[20%] bg-design-primary/8 dark:bg-design-primary/3 rounded-full filter blur-[50px] animate-float animation-delay-200"></div> </div> <!-- Content container - No width constraint --> <div class="relative z-10 container mx-auto px-4 py-16 md:py-14"> <!-- Page content will be inserted here --> ${renderSlot($$result2, $$slots["default"])} </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/SearchPageLayout.astro", void 0);

const $$Astro = createAstro("http://localhost:4321");
const $$Search = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Search;
  const url = new URL(Astro2.request.url);
  const query = url.searchParams.get("q") || "";
  const page = parseInt(url.searchParams.get("page") || "1");
  const perPage = parseInt(url.searchParams.get("perPage") || "12");
  const viewParam = url.searchParams.get("view") || "grid";
  const view = viewParam === "list" ? "list" : "grid";
  const sortBy = url.searchParams.get("sort") || "newest";
  const categoryIds = url.searchParams.get("categories")?.split(",").map(Number) || [];
  const merchantIds = url.searchParams.get("merchants")?.split(",").map(Number) || [];
  const brandIds = url.searchParams.get("brands")?.split(",").map(Number) || [];
  const minPrice = parseFloat(url.searchParams.get("minPrice") || "0");
  const maxPrice = parseFloat(url.searchParams.get("maxPrice") || "200");
  const minDiscount = parseFloat(url.searchParams.get("minDiscount") || "0");
  const maxDiscount = parseFloat(url.searchParams.get("maxDiscount") || "100");
  const expiringSoon = url.searchParams.get("expiringSoon") === "true";
  const validLongTerm = url.searchParams.get("validLongTerm") === "true";
  const from = (page - 1) * perPage;
  const to = from + perPage - 1;
  const supabase = createServerSupabaseClient();
  let deals = [];
  let categories = [];
  let merchants = [];
  let brands = [];
  let dealsCount = 0;
  let totalPages = 0;
  if (query && query.length >= 2) {
    let dealsQuery = supabase.from("deals").select(`
      *,
      merchants:merchant_id (name, website_url, logo_url),
      brands:brand_id (name, logo_url, slug),
      categories:category_id (name, slug)
    `, { count: "exact" }).or(`title.ilike.%${query}%, description.ilike.%${query}%, coupon_code.ilike.%${query}%`);
    if (categoryIds.length > 0) {
      dealsQuery = dealsQuery.in("category_id", categoryIds);
    }
    if (merchantIds.length > 0) {
      dealsQuery = dealsQuery.in("merchant_id", merchantIds);
    }
    if (brandIds.length > 0) {
      dealsQuery = dealsQuery.in("brand_id", brandIds);
    }
    if (minPrice > 0 || maxPrice < 200) {
      dealsQuery = dealsQuery.not("price", "is", null);
      dealsQuery = dealsQuery.gte("price", minPrice).lte("price", maxPrice);
    }
    if (minDiscount > 0 || maxDiscount < 100) {
      dealsQuery = dealsQuery.not("discount", "is", null);
      if (minDiscount > 0) {
        dealsQuery = dealsQuery.gte("discount", minDiscount);
      }
      if (maxDiscount < 100) {
        dealsQuery = dealsQuery.lte("discount", maxDiscount);
      }
    }
    if (expiringSoon) {
      const expiryDate = /* @__PURE__ */ new Date();
      expiryDate.setDate(expiryDate.getDate() + 15);
      dealsQuery = dealsQuery.lte("deal_end_date", expiryDate.toISOString());
    }
    if (validLongTerm) {
      const longTermDate = /* @__PURE__ */ new Date();
      longTermDate.setDate(longTermDate.getDate() + 30);
      dealsQuery = dealsQuery.gte("deal_end_date", longTermDate.toISOString());
    }
    switch (sortBy) {
      case "newest":
        dealsQuery = dealsQuery.order("created_at", { ascending: false });
        break;
      case "price_asc":
        dealsQuery = dealsQuery.not("price", "is", null);
        dealsQuery = dealsQuery.order("price", { ascending: true });
        break;
      case "price_desc":
        dealsQuery = dealsQuery.not("price", "is", null);
        dealsQuery = dealsQuery.order("price", { ascending: false });
        break;
      case "expiring_soon":
        dealsQuery = dealsQuery.not("deal_end_date", "is", null);
        dealsQuery = dealsQuery.order("deal_end_date", { ascending: true });
        break;
      case "discount_desc":
        dealsQuery = dealsQuery.not("discount", "is", null);
        dealsQuery = dealsQuery.order("discount", { ascending: false });
        break;
      case "most_popular":
        dealsQuery = dealsQuery.not("click_count", "is", null);
        dealsQuery = dealsQuery.order("click_count", { ascending: false });
        break;
      default:
        dealsQuery = dealsQuery.order("created_at", { ascending: false });
    }
    dealsQuery = dealsQuery.range(from, to);
    const { data: dealsData, error: dealsError, count } = await dealsQuery;
    if (dealsError) {
      console.error("Error searching deals:", dealsError);
    } else {
      deals = dealsData?.map((deal) => ({
        id: deal.id,
        title: deal.title,
        cleaned_title: deal.cleaned_title,
        coupon_code: deal.coupon_code,
        image_url: deal.image_url,
        imagebig_url: deal.imagebig_url,
        imagesmall_url: deal.imagesmall_url,
        merchant_name: deal.merchants?.name,
        brand_name: deal.brands?.name || "",
        discount: deal.discount,
        price: deal.price,
        currency: deal.currency,
        deal_end_date: deal.deal_end_date,
        tracking_url: deal.tracking_url,
        // Add logo URLs for fallback
        brand_logo_url: deal.brands?.logo_url,
        merchant_logo_url: deal.merchants?.logo_url,
        category_logo_url: deal.categories?.logo_url,
        // Add verification and success rate fields
        success_rate: deal.success_rate,
        verified: deal.verified,
        last_verified_at: deal.last_verified_at
      })) || [];
      dealsCount = count || 0;
      totalPages = Math.ceil(dealsCount / perPage);
    }
    const { data: categoriesData, error: categoriesError } = await supabase.from("categories").select("*").or(`name.ilike.%${query}%, slug.ilike.%${query}%`).limit(5);
    if (categoriesError) {
      console.error("Error searching categories:", categoriesError);
    } else {
      categories = categoriesData || [];
    }
    const { data: merchantsData, error: merchantsError } = await supabase.from("merchants").select("*").or(`name.ilike.%${query}%`).eq("status", "active").limit(5);
    if (merchantsError) {
      console.error("Error searching merchants:", merchantsError);
    } else {
      merchants = merchantsData || [];
    }
    const { data: brandsData, error: brandsError } = await supabase.from("brands").select("*").or(`name.ilike.%${query}%, aliases.cs.{${query}}`).limit(5);
    if (brandsError) {
      console.error("Error searching brands:", brandsError);
    } else {
      brands = brandsData || [];
    }
  }
  let allCategories = [];
  let allMerchants = [];
  let allBrands = [];
  const { data: allCategoriesData } = await supabase.from("categories").select("*").order("name");
  allCategories = allCategoriesData || [];
  const { data: allMerchantsData } = await supabase.from("merchants").select("*").eq("status", "active").order("name");
  allMerchants = allMerchantsData || [];
  const { data: allBrandsData } = await supabase.from("brands").select("*").order("name");
  allBrands = allBrandsData || [];
  const initialFilters = {
    categories: categoryIds,
    merchants: merchantIds,
    brands: brandIds,
    priceRange: [minPrice, maxPrice],
    discountRange: [minDiscount, maxDiscount],
    expiringSoon,
    validLongTerm
  };
  const title = query ? `Search Results for "${query}" | VapeHybrid Coupons` : "Search Vape Deals | VapeHybrid Coupons";
  const description = query ? `Find the best vape deals and coupons matching "${query}". Browse results across deals, brands, merchants, and categories.` : "Search for vape deals, coupons, brands, and merchants. Find the best discounts on your favorite vape products.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "SearchResultsPage",
    name: title,
    description,
    url: Astro2.url.href,
    mainEntity: {
      "@type": "ItemList",
      itemListElement: deals?.map((deal, index) => ({
        "@type": "ListItem",
        position: index + 1,
        item: {
          "@type": "Offer",
          name: deal.title,
          url: `${Astro2.url.origin}/deal/${deal.id}`,
          price: deal.price || "0",
          priceCurrency: "USD",
          availability: "https://schema.org/InStock",
          validFrom: deal.created_at ? new Date(deal.created_at).toISOString() : (/* @__PURE__ */ new Date()).toISOString(),
          validThrough: deal.deal_end_date ? new Date(deal.deal_end_date).toISOString() : void 0
        }
      }))
    }
  };
  return renderTemplate`${renderComponent($$result, "SearchPageLayout", $$SearchPageLayout, { "title": title, "description": description, "structuredData": structuredData, "pattern": "dots" }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "InfoPageHeader", $$InfoPageHeader, { "title": query ? `Search Results for "${query}"` : "Search", "subtitle": !query ? "Enter a search term to find deals, brands, merchants, and categories" : "", "pattern": "grid", "glass": true })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "iconName": "search", "centerTitle": false, "background": "transparent" }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<form action="/search" method="get" class="flex gap-2 max-w-2xl mx-auto"> <div class="relative flex-grow"> <input type="text" name="q"${addAttribute(query, "value")} placeholder="Search for deals, brands, merchants..." class="w-full h-12 px-4 pr-10 rounded-full border border-design-border bg-design-background text-design-foreground focus:outline-none focus:ring-2 focus:ring-design-primary"> ${query && renderTemplate`<button type="button" class="absolute right-3 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground" onclick="this.previousElementSibling.value = ''; this.form.submit();"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> </button>`} </div> <button type="submit" class="h-12 px-6 bg-design-primary text-white rounded-full hover:bg-design-primary/90 transition-colors flex items-center"> <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path> </svg>
Search
</button> </form> ` })} ${query && query.length < 2 && renderTemplate`${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "background": "transparent", "glass": true }, { "default": async ($$result3) => renderTemplate` <div class="bg-design-tetradic2/10 text-design-tetradic2-foreground p-4 rounded-xl mb-6 max-w-2xl mx-auto"> <p class="text-center">Please enter at least 2 characters to search.</p> </div> ` })}`}${query && query.length >= 2 && renderTemplate`<div class="flex flex-col gap-8"> <!-- Categories Results --> ${categories.length > 0 && renderTemplate`${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Categories", "background": "transparent", "glass": true }, { "default": async ($$result3) => renderTemplate` <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"> ${categories.map((category) => renderTemplate`<a${addAttribute(`/categories/${category.slug}`, "href")} class="flex items-center gap-3 p-3 bg-design-card hover:bg-design-card/80 border border-design-border rounded-xl transition-colors"> <div class="w-10 h-10 rounded-full bg-design-muted flex items-center justify-center overflow-hidden"> ${category.category_logo ? renderTemplate`<img${addAttribute(category.category_logo, "src")}${addAttribute(category.name, "alt")}${addAttribute(40, "width")}${addAttribute(40, "height")} class="object-contain w-full h-full">` : renderTemplate`<div class="text-sm font-bold text-design-muted-foreground"> ${category.name.charAt(0)} </div>`} </div> <span class="font-medium text-design-foreground">${category.name}</span> </a>`)} </div> ` })}`} <!-- Brands Results --> ${brands.length > 0 && renderTemplate`${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Brands", "background": "transparent", "glass": true }, { "default": async ($$result3) => renderTemplate` <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"> ${brands.map((brand) => renderTemplate`<a${addAttribute(`/brands/${brand.slug}`, "href")} class="flex items-center gap-3 p-3 bg-design-card hover:bg-design-card/80 border border-design-border rounded-xl transition-colors"> <div class="w-10 h-10 rounded-full bg-design-muted flex items-center justify-center overflow-hidden"> ${brand.logo_url ? renderTemplate`<img${addAttribute(brand.logo_url, "src")}${addAttribute(brand.name, "alt")}${addAttribute(40, "width")}${addAttribute(40, "height")} class="object-contain w-full h-full">` : renderTemplate`<div class="text-sm font-bold text-design-muted-foreground"> ${brand.name.charAt(0)} </div>`} </div> <span class="font-medium text-design-foreground">${brand.name}</span> </a>`)} </div> ` })}`} <!-- Merchants Results --> ${merchants.length > 0 && renderTemplate`${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Merchants", "background": "transparent", "glass": true }, { "default": async ($$result3) => renderTemplate` <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"> ${merchants.map((merchant) => renderTemplate`<a${addAttribute(`/merchants/${merchant.id}`, "href")} class="flex items-center gap-3 p-3 bg-design-card hover:bg-design-card/80 border border-design-border rounded-xl transition-colors"> <div class="w-10 h-10 rounded-full bg-design-muted flex items-center justify-center overflow-hidden"> ${merchant.logo_url ? renderTemplate`<img${addAttribute(merchant.logo_url, "src")}${addAttribute(merchant.name, "alt")}${addAttribute(40, "width")}${addAttribute(40, "height")} class="object-contain w-full h-full">` : renderTemplate`<div class="text-sm font-bold text-design-muted-foreground"> ${merchant.name.charAt(0)} </div>`} </div> <span class="font-medium text-design-foreground">${merchant.name}</span> </a>`)} </div> ` })}`} <!-- Deals Results --> ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": `${dealsCount} ${dealsCount === 1 ? "Deal" : "Deals"} Found`, "background": "transparent", "glass": true, "className": "search-deals-section" }, { "default": async ($$result3) => renderTemplate` <div class="flex flex-col md:flex-row justify-between items-center gap-4 mb-8 bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 rounded-xl p-4 shadow-sm"> <div class="flex flex-wrap items-center gap-2 sm:gap-4"> ${renderComponent($$result3, "FilterDrawer", FilterDrawer, { "categories": allCategories || [], "merchants": allMerchants || [], "brands": allBrands || [], "initialFilters": initialFilters, "currentUrl": Astro2.request.url, "activeFilterCount": categoryIds.length + merchantIds.length + brandIds.length + (expiringSoon ? 1 : 0) + (validLongTerm ? 1 : 0) + (minPrice > 0 || maxPrice < 200 ? 1 : 0) + (minDiscount > 0 || maxDiscount < 100 ? 1 : 0), "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/FilterDrawer", "client:component-export": "default" })} ${renderComponent($$result3, "SortSelect", SortSelect, { "value": sortBy, "currentUrl": Astro2.request.url, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/SortSelect", "client:component-export": "default" })} ${renderComponent($$result3, "ViewToggle", ViewToggle, { "currentView": view, "currentUrl": Astro2.request.url, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/ViewToggle", "client:component-export": "default" })} </div> </div> ${deals.length > 0 ? renderTemplate`${renderComponent($$result3, "DealsPageWrapper", DealsPageWrapper, { "deals": deals, "initialViewMode": view, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DealsPageWrapper", "client:component-export": "default" })}` : renderTemplate`<div class="text-center py-12 bg-design-card border border-design-border rounded-xl"> <h3 class="text-xl font-semibold text-design-foreground mb-2">No Deals Found</h3> <p class="text-design-muted-foreground mb-6">
We couldn't find any deals matching your search. Try different keywords or browse all deals.
</p> <a href="/deals" class="inline-block px-6 py-3 bg-design-primary text-white rounded-full hover:bg-design-primary/90 transition-colors">
Browse All Deals
</a> </div>`} ${totalPages > 1 && renderTemplate`<div class="mt-8"> ${renderComponent($$result3, "Pagination", $$Pagination, { "currentPage": page, "totalPages": totalPages, "baseUrl": "/search", "searchParams": Astro2.url.searchParams })} </div>`}` })} </div>`}${!query && renderTemplate`${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Start Searching", "centerTitle": true, "background": "transparent", "glass": true }, { "default": async ($$result3) => renderTemplate` <div class="text-center max-w-2xl mx-auto"> <p class="text-design-muted-foreground mb-6">
Enter a search term above to find deals, brands, merchants, and categories.
</p> <div class="flex flex-wrap justify-center gap-4"> <a href="/deals" class="inline-block px-6 py-3 bg-design-primary text-white rounded-full hover:bg-design-primary/90 transition-colors">
Browse All Deals
</a> <a href="/categories" class="inline-block px-6 py-3 bg-design-secondary text-design-secondary-foreground rounded-full hover:bg-design-secondary/90 transition-colors">
Browse Categories
</a> <a href="/brands" class="inline-block px-6 py-3 bg-design-muted text-design-muted-foreground rounded-full hover:bg-design-muted/90 transition-colors">
Browse Brands
</a> </div> </div> ` })}`}` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/search.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/search.astro";
const $$url = "/search";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Search,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
