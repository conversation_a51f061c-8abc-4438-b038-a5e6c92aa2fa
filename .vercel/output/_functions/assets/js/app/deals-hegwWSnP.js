import { jsx, jsxs } from 'react/jsx-runtime';
import React__default, { useState, useMemo, useEffect } from 'react';
import { toast } from 'sonner';

const LazyDealCard = React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.q).then((module) => ({
    default: module.DealCard
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.z).then((module) => ({
    default: module.OptimizedDealListCard
  }))
);
const LazyOptimizedDealListCard = React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.z).then((module) => ({
    default: module.OptimizedDealListCard
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.r).then((module) => ({
    default: module.DealListCard
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.A).then((module) => ({
    default: module.CompactDealCard
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.I).then((module) => ({
    default: module.ImprovedDealCard
  }))
);
const LazyOptimizedDealCard = React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.s).then((module) => ({
    default: module.OptimizedDealCard
  }))
);
const LazyDealCouponModal = React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.v).then((module) => ({
    default: module.default
  }))
);
React__default.lazy(
  () => import('../featured-deals-ERmVkM9j.js').then((module) => ({
    default: module.FeaturedDeals
  }))
);
React__default.lazy(
  () => import('../flash-deals-D3J9Pre8.js').then((module) => ({
    default: module.FlashDeals
  }))
);
React__default.lazy(
  () => import('../newsletter-section-Bv7hy9NP.js').then((module) => ({
    default: module.NewsletterSection
  }))
);
React__default.lazy(
  () => import('../faq-section-DnK0tX_o.js').then((module) => ({
    default: module.FAQSection
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.y).then((module) => ({
    default: module.default
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.x).then((module) => ({
    default: module.default
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.p).then((module) => ({
    default: module.default
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.t).then((module) => ({
    default: module.Select
  }))
);
React__default.lazy(
  () => import('./design-system-CUssmfny.js').then(n => n.w).then((module) => ({
    default: module.Sheet
  }))
);
const SuspenseWrapper = ({
  children,
  fallback = /* @__PURE__ */ jsx("div", { className: "animate-pulse bg-design-muted rounded-lg h-40 w-full" })
}) => {
  const [, startTransition] = React__default.useTransition();
  const [isMounted, setIsMounted] = React__default.useState(false);
  React__default.useEffect(() => {
    const timeout = setTimeout(() => {
      startTransition(() => {
        setIsMounted(true);
      });
    }, 10);
    return () => clearTimeout(timeout);
  }, []);
  if (!isMounted) {
    return /* @__PURE__ */ jsx("div", { className: "min-h-[40px]", children: fallback });
  }
  return /* @__PURE__ */ jsx(React__default.Suspense, { fallback, children });
};

const defaultFAQs = [
  {
    question: "How do I use a coupon code?",
    answer: "To use a coupon code, click on the 'Reveal Code' button on any deal. This will copy the code to your clipboard and open the merchant's website in a new tab. Paste the code at checkout to apply your discount."
  },
  {
    question: "Why didn't my code work?",
    answer: "Coupon codes may not work for several reasons: the code might have expired, it could be limited to specific products, or it might not be combinable with other offers. We verify our codes regularly, but merchant policies can change without notice."
  },
  {
    question: "How often are deals updated?",
    answer: "We update our deals daily. Our team verifies each deal to ensure it's still valid and offers the best possible savings for our users."
  },
  {
    question: "Do I have to pay extra for using these links?",
    answer: "No, using our links is completely free for you. When you make a purchase through our links, we may earn a commission from the merchant at no additional cost to you. This helps us maintain and improve our service."
  }
];
function DealsFAQ({ faqs = defaultFAQs }) {
  const [openIndex, setOpenIndex] = useState(null);
  const generateStructuredData = () => {
    const structuredData = {
      "@context": "https://schema.org",
      "@type": "FAQPage",
      "mainEntity": faqs.map((faq) => ({
        "@type": "Question",
        "name": faq.question,
        "acceptedAnswer": {
          "@type": "Answer",
          "text": faq.answer
        }
      }))
    };
    return JSON.stringify(structuredData);
  };
  const toggleFAQ = (index) => {
    setOpenIndex(openIndex === index ? null : index);
  };
  return /* @__PURE__ */ jsxs("div", { className: "w-full", children: [
    /* @__PURE__ */ jsx("script", { type: "application/ld+json", dangerouslySetInnerHTML: { __html: generateStructuredData() } }),
    /* @__PURE__ */ jsxs("div", { className: "mb-8 text-center", children: [
      /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold text-design-foreground mb-3", children: "Frequently Asked Questions" }),
      /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground max-w-2xl mx-auto", children: "Find answers to common questions about using our deals and coupons." })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "space-y-4 max-w-3xl mx-auto", children: faqs.map((faq, index) => /* @__PURE__ */ jsxs(
      "div",
      {
        className: `bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 rounded-lg overflow-hidden transition-all duration-300 ${openIndex === index ? "shadow-md" : "shadow-sm"}`,
        children: [
          /* @__PURE__ */ jsxs(
            "button",
            {
              onClick: () => toggleFAQ(index),
              className: "flex justify-between items-center w-full p-5 text-left font-medium text-design-foreground focus:outline-none",
              children: [
                /* @__PURE__ */ jsxs("span", { className: "flex items-center", children: [
                  /* @__PURE__ */ jsx("span", { className: `inline-flex items-center justify-center w-8 h-8 rounded-full mr-3 transition-colors duration-300 ${openIndex === index ? "bg-primary/20 text-primary" : "bg-primary/10 text-design-muted-foreground"}`, children: /* @__PURE__ */ jsxs(
                    "svg",
                    {
                      xmlns: "http://www.w3.org/2000/svg",
                      width: "18",
                      height: "18",
                      viewBox: "0 0 24 24",
                      fill: "none",
                      stroke: "currentColor",
                      strokeWidth: "2",
                      strokeLinecap: "round",
                      strokeLinejoin: "round",
                      children: [
                        /* @__PURE__ */ jsx("path", { d: "M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" }),
                        /* @__PURE__ */ jsx("circle", { cx: "12", cy: "12", r: "10" }),
                        /* @__PURE__ */ jsx("line", { x1: "12", y1: "17", x2: "12", y2: "17" })
                      ]
                    }
                  ) }),
                  faq.question
                ] }),
                /* @__PURE__ */ jsx(
                  "svg",
                  {
                    xmlns: "http://www.w3.org/2000/svg",
                    width: "20",
                    height: "20",
                    viewBox: "0 0 24 24",
                    fill: "none",
                    stroke: "currentColor",
                    strokeWidth: "2",
                    strokeLinecap: "round",
                    strokeLinejoin: "round",
                    className: `transition-transform duration-300 ${openIndex === index ? "rotate-180 text-primary" : "text-design-muted-foreground"}`,
                    children: /* @__PURE__ */ jsx("polyline", { points: "6 9 12 15 18 9" })
                  }
                )
              ]
            }
          ),
          /* @__PURE__ */ jsx(
            "div",
            {
              className: `overflow-hidden transition-all duration-300 ${openIndex === index ? "max-h-96" : "max-h-0"}`,
              children: /* @__PURE__ */ jsx("div", { className: "p-5 pt-0 text-design-muted-foreground border-t border-design-border/10", children: faq.answer })
            }
          )
        ]
      },
      index
    )) })
  ] });
}

const DealsPageWrapper = ({ deals, initialViewMode, onViewModeChange }) => {
  const memoizedDeals = useMemo(() => deals.slice(0, 24), [deals]);
  const [viewMode, setViewMode] = useState(initialViewMode);
  const [showCouponModalState, setShowCouponModalState] = useState(false);
  const [selectedDeal, setSelectedDeal] = useState(null);
  const [revealedCodes, setRevealedCodes] = useState({});
  useEffect(() => {
    if (typeof window !== "undefined") {
      const urlParams = new URLSearchParams(window.location.search);
      const dealId = urlParams.get("dealId");
      const showPopup = urlParams.get("showPopup");
      const viewParam = urlParams.get("view");
      if (viewParam === "grid" || viewParam === "list") {
        setViewMode(viewParam);
      }
      if (dealId && showPopup === "true") {
        const deal = deals.find((d) => d.id.toString() === dealId);
        if (deal) {
          showCouponModal(deal);
        }
      }
      const storedRevealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
      setRevealedCodes(storedRevealedCodes);
    }
  }, [deals]);
  const showCouponModal = (deal) => {
    setSelectedDeal(deal);
    setShowCouponModalState(true);
  };
  const closeModal = () => {
    setShowCouponModalState(false);
    setSelectedDeal(null);
    if (typeof window !== "undefined") {
      const currentUrl = new URL(window.location.href);
      currentUrl.searchParams.delete("dealId");
      currentUrl.searchParams.delete("showPopup");
      const viewMode2 = currentUrl.searchParams.get("view");
      const newUrl = new URL(window.location.pathname, window.location.origin);
      if (viewMode2) {
        newUrl.searchParams.set("view", viewMode2);
      }
      window.history.replaceState({}, "", newUrl.toString());
    }
  };
  return /* @__PURE__ */ jsxs("div", { children: [
    /* @__PURE__ */ jsx("div", { className: `deals-container ${viewMode === "grid" ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8" : "flex flex-col gap-8"}`, children: memoizedDeals.map((deal, index) => {
      let merchantName = deal.merchants?.name || deal.merchant_name || "Brand";
      if (merchantName === "Vapesourcing Electronics Co.,Ltd.") {
        merchantName = "Vapesourcing";
      }
      const transformedDeal = useMemo(() => ({
        ...deal,
        merchants: deal.merchants ? {
          ...deal.merchants,
          name: merchantName
        } : {
          name: merchantName
        },
        brands: deal.brands || {
          name: deal.brand_name || ""
        }
      }), [deal, merchantName]);
      const isPriority = index < 6;
      return viewMode === "grid" ? /* @__PURE__ */ jsx(SuspenseWrapper, { fallback: /* @__PURE__ */ jsx("div", { className: "animate-pulse bg-design-muted rounded-lg h-[320px] w-full" }), children: /* @__PURE__ */ jsx(
        LazyDealCard,
        {
          deal: transformedDeal,
          onShowCouponModal: showCouponModal,
          isRevealed: revealedCodes[deal.id] || false,
          view: "grid",
          priority: isPriority
        }
      ) }, deal.id) : /* @__PURE__ */ jsx(SuspenseWrapper, { fallback: /* @__PURE__ */ jsx("div", { className: "animate-pulse bg-design-muted rounded-lg h-[160px] w-full" }), children: /* @__PURE__ */ jsx(
        LazyOptimizedDealListCard,
        {
          deal: transformedDeal,
          onShowCouponModal: showCouponModal,
          isRevealed: revealedCodes[deal.id] || false,
          priority: isPriority
        }
      ) }, deal.id);
    }) }),
    showCouponModalState && selectedDeal && /* @__PURE__ */ jsx(SuspenseWrapper, { fallback: /* @__PURE__ */ jsx("div", { className: "fixed inset-0 z-50 flex items-center justify-center bg-black/50", children: /* @__PURE__ */ jsx("div", { className: "animate-pulse bg-design-card rounded-lg h-[400px] w-[90%] max-w-md" }) }), children: /* @__PURE__ */ jsx(
      LazyDealCouponModal,
      {
        deal: selectedDeal,
        onClose: closeModal,
        onVote: (dealId, isGood) => {
          fetch(`/api/vote`, {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              deal_id: dealId,
              vote_good: isGood
            })
          }).then((response) => {
            if (response.ok) {
              const message = isGood ? "Thanks for your positive feedback!" : "Thanks for your feedback!";
              toast.success(message);
            } else {
              toast.error("Failed to record your vote. Please try again.");
            }
          }).catch((error) => {
            console.error("Error voting:", error);
            toast.error("Failed to record your vote. Please try again.");
          });
        }
      }
    ) })
  ] });
};

export { DealsFAQ as D, LazyOptimizedDealCard as L, SuspenseWrapper as S, LazyOptimizedDealListCard as a, LazyDealCouponModal as b, DealsPageWrapper as c };
