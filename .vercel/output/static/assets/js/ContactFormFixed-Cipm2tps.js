import{r as e,u as t,j as s}from"./vendor/react-bq1VYqFi.js";import{p as a,A as r,q as n,r as i,s as o,U as l,M as d,L as c}from"./app/design-system-lJ5ci6hi.js";import"./vendor/ui-components-_FzKGR3X.js";import"./vendor/utils-hwhCH1Yb.js";var m={};!function e(t,s,a,r){var n=!!(t.Worker&&t.Blob&&t.Promise&&t.OffscreenCanvas&&t.OffscreenCanvasRenderingContext2D&&t.HTMLCanvasElement&&t.HTMLCanvasElement.prototype.transferControlToOffscreen&&t.URL&&t.URL.createObjectURL),i="function"==typeof Path2D&&"function"==typeof DOMMatrix,o=function(){if(!t.OffscreenCanvas)return!1;var e=new OffscreenCanvas(1,1),s=e.getContext("2d");s.fillRect(0,0,1,1);var a=e.transferToImageBitmap();try{s.createPattern(a,"no-repeat")}catch(r){return!1}return!0}();function l(){}function d(e){var a=s.exports.Promise,r=void 0!==a?a:t.Promise;return"function"==typeof r?new r(e):(e(l,l),null)}var c,m,u,h,g,x,p,f,b,v,y,w=(c=o,m=new Map,{transform:function(e){if(c)return e;if(m.has(e))return m.get(e);var t=new OffscreenCanvas(e.width,e.height);return t.getContext("2d").drawImage(e,0,0),m.set(e,t),t},clear:function(){m.clear()}}),j=(g=Math.floor(1e3/60),x={},p=0,"function"==typeof requestAnimationFrame&&"function"==typeof cancelAnimationFrame?(u=function(e){var t=Math.random();return x[t]=requestAnimationFrame((function s(a){p===a||p+g-1<a?(p=a,delete x[t],e()):x[t]=requestAnimationFrame(s)})),t},h=function(e){x[e]&&cancelAnimationFrame(x[e])}):(u=function(e){return setTimeout(e,g)},h=function(e){return clearTimeout(e)}),{frame:u,cancel:h}),N=(v={},function(){if(f)return f;if(!a&&n){var t=["var CONFETTI, SIZE = {}, module = {};","("+e.toString()+")(this, module, true, SIZE);","onmessage = function(msg) {","  if (msg.data.options) {","    CONFETTI(msg.data.options).then(function () {","      if (msg.data.callback) {","        postMessage({ callback: msg.data.callback });","      }","    });","  } else if (msg.data.reset) {","    CONFETTI && CONFETTI.reset();","  } else if (msg.data.resize) {","    SIZE.width = msg.data.resize.width;","    SIZE.height = msg.data.resize.height;","  } else if (msg.data.canvas) {","    SIZE.width = msg.data.canvas.width;","    SIZE.height = msg.data.canvas.height;","    CONFETTI = module.exports.create(msg.data.canvas);","  }","}"].join("\n");try{f=new Worker(URL.createObjectURL(new Blob([t])))}catch(s){return void 0!==typeof console&&console.warn,null}!function(e){function t(t,s){e.postMessage({options:t||{},callback:s})}e.init=function(t){var s=t.transferControlToOffscreen();e.postMessage({canvas:s},[s])},e.fire=function(s,a,r){if(b)return t(s,null),b;var n=Math.random().toString(36).slice(2);return b=d((function(a){function i(t){t.data.callback===n&&(delete v[n],e.removeEventListener("message",i),b=null,w.clear(),r(),a())}e.addEventListener("message",i),t(s,n),v[n]=i.bind(null,{data:{callback:n}})}))},e.reset=function(){for(var t in e.postMessage({reset:!0}),v)v[t](),delete v[t]}}(f)}return f}),M={particleCount:50,angle:90,spread:45,startVelocity:45,decay:.9,gravity:1,drift:0,ticks:200,x:.5,y:.5,shapes:["square","circle"],zIndex:100,colors:["#26ccff","#a25afd","#ff5e7e","#88ff5a","#fcff42","#ffa62d","#ff36ff"],disableForReducedMotion:!1,scalar:1};function k(e,t,s){return function(e,t){return t?t(e):e}(e&&null!=e[t]?e[t]:M[t],s)}function C(e){return e<0?0:Math.floor(e)}function S(e){return parseInt(e,16)}function P(e){return e.map(T)}function T(e){var t=String(e).replace(/[^0-9a-f]/gi,"");return t.length<6&&(t=t[0]+t[0]+t[1]+t[1]+t[2]+t[2]),{r:S(t.substring(0,2)),g:S(t.substring(2,4)),b:S(t.substring(4,6))}}function E(e){e.width=document.documentElement.clientWidth,e.height=document.documentElement.clientHeight}function I(e){var t=e.getBoundingClientRect();e.width=t.width,e.height=t.height}function O(e,t){t.x+=Math.cos(t.angle2D)*t.velocity+t.drift,t.y+=Math.sin(t.angle2D)*t.velocity+t.gravity,t.velocity*=t.decay,t.flat?(t.wobble=0,t.wobbleX=t.x+10*t.scalar,t.wobbleY=t.y+10*t.scalar,t.tiltSin=0,t.tiltCos=0,t.random=1):(t.wobble+=t.wobbleSpeed,t.wobbleX=t.x+10*t.scalar*Math.cos(t.wobble),t.wobbleY=t.y+10*t.scalar*Math.sin(t.wobble),t.tiltAngle+=.1,t.tiltSin=Math.sin(t.tiltAngle),t.tiltCos=Math.cos(t.tiltAngle),t.random=Math.random()+2);var s=t.tick++/t.totalTicks,a=t.x+t.random*t.tiltCos,r=t.y+t.random*t.tiltSin,n=t.wobbleX+t.random*t.tiltCos,o=t.wobbleY+t.random*t.tiltSin;if(e.fillStyle="rgba("+t.color.r+", "+t.color.g+", "+t.color.b+", "+(1-s)+")",e.beginPath(),i&&"path"===t.shape.type&&"string"==typeof t.shape.path&&Array.isArray(t.shape.matrix))e.fill(function(e,t,s,a,r,n,i){var o=new Path2D(e),l=new Path2D;l.addPath(o,new DOMMatrix(t));var d=new Path2D;return d.addPath(l,new DOMMatrix([Math.cos(i)*r,Math.sin(i)*r,-Math.sin(i)*n,Math.cos(i)*n,s,a])),d}(t.shape.path,t.shape.matrix,t.x,t.y,.1*Math.abs(n-a),.1*Math.abs(o-r),Math.PI/10*t.wobble));else if("bitmap"===t.shape.type){var l=Math.PI/10*t.wobble,d=.1*Math.abs(n-a),c=.1*Math.abs(o-r),m=t.shape.bitmap.width*t.scalar,u=t.shape.bitmap.height*t.scalar,h=new DOMMatrix([Math.cos(l)*d,Math.sin(l)*d,-Math.sin(l)*c,Math.cos(l)*c,t.x,t.y]);h.multiplySelf(new DOMMatrix(t.shape.matrix));var g=e.createPattern(w.transform(t.shape.bitmap),"no-repeat");g.setTransform(h),e.globalAlpha=1-s,e.fillStyle=g,e.fillRect(t.x-m/2,t.y-u/2,m,u),e.globalAlpha=1}else if("circle"===t.shape)e.ellipse?e.ellipse(t.x,t.y,Math.abs(n-a)*t.ovalScalar,Math.abs(o-r)*t.ovalScalar,Math.PI/10*t.wobble,0,2*Math.PI):function(e,t,s,a,r,n,i,o,l){e.save(),e.translate(t,s),e.rotate(n),e.scale(a,r),e.arc(0,0,1,i,o,l),e.restore()}(e,t.x,t.y,Math.abs(n-a)*t.ovalScalar,Math.abs(o-r)*t.ovalScalar,Math.PI/10*t.wobble,0,2*Math.PI);else if("star"===t.shape)for(var x=Math.PI/2*3,p=4*t.scalar,f=8*t.scalar,b=t.x,v=t.y,y=5,j=Math.PI/y;y--;)b=t.x+Math.cos(x)*f,v=t.y+Math.sin(x)*f,e.lineTo(b,v),x+=j,b=t.x+Math.cos(x)*p,v=t.y+Math.sin(x)*p,e.lineTo(b,v),x+=j;else e.moveTo(Math.floor(t.x),Math.floor(t.y)),e.lineTo(Math.floor(t.wobbleX),Math.floor(r)),e.lineTo(Math.floor(n),Math.floor(o)),e.lineTo(Math.floor(a),Math.floor(t.wobbleY));return e.closePath(),e.fill(),t.tick<t.totalTicks}function B(e,s){var i,o=!e,l=!!k(s||{},"resize"),c=!1,m=k(s,"disableForReducedMotion",Boolean),u=n&&!!k(s||{},"useWorker")?N():null,h=o?E:I,g=!(!e||!u)&&!!e.__confetti_initialized,x="function"==typeof matchMedia&&matchMedia("(prefers-reduced-motion)").matches;function p(t,s,n){for(var o,l,c,m,u,g=k(t,"particleCount",C),x=k(t,"angle",Number),p=k(t,"spread",Number),f=k(t,"startVelocity",Number),b=k(t,"decay",Number),v=k(t,"gravity",Number),y=k(t,"drift",Number),N=k(t,"colors",P),M=k(t,"ticks",Number),S=k(t,"shapes"),T=k(t,"scalar"),E=!!k(t,"flat"),I=function(e){var t=k(e,"origin",Object);return t.x=k(t,"x",Number),t.y=k(t,"y",Number),t}(t),B=g,F=[],A=e.width*I.x,z=e.height*I.y;B--;)F.push((o={x:A,y:z,angle:x,spread:p,startVelocity:f,color:N[B%N.length],shape:S[(m=0,u=S.length,Math.floor(Math.random()*(u-m))+m)],ticks:M,decay:b,gravity:v,drift:y,scalar:T,flat:E},l=void 0,c=void 0,l=o.angle*(Math.PI/180),c=o.spread*(Math.PI/180),{x:o.x,y:o.y,wobble:10*Math.random(),wobbleSpeed:Math.min(.11,.1*Math.random()+.05),velocity:.5*o.startVelocity+Math.random()*o.startVelocity,angle2D:-l+(.5*c-Math.random()*c),tiltAngle:(.5*Math.random()+.25)*Math.PI,color:o.color,shape:o.shape,tick:0,totalTicks:o.ticks,decay:o.decay,drift:o.drift,random:Math.random()+2,tiltSin:0,tiltCos:0,wobbleX:0,wobbleY:0,gravity:3*o.gravity,ovalScalar:.6,scalar:o.scalar,flat:o.flat}));return i?i.addFettis(F):(i=function(e,t,s,n,i){var o,l,c=t.slice(),m=e.getContext("2d"),u=d((function(t){function d(){o=l=null,m.clearRect(0,0,n.width,n.height),w.clear(),i(),t()}o=j.frame((function t(){!a||n.width===r.width&&n.height===r.height||(n.width=e.width=r.width,n.height=e.height=r.height),n.width||n.height||(s(e),n.width=e.width,n.height=e.height),m.clearRect(0,0,n.width,n.height),(c=c.filter((function(e){return O(m,e)}))).length?o=j.frame(t):d()})),l=d}));return{addFettis:function(e){return c=c.concat(e),u},canvas:e,promise:u,reset:function(){o&&j.cancel(o),l&&l()}}}(e,F,h,s,n),i.promise)}function f(s){var a=m||k(s,"disableForReducedMotion",Boolean),r=k(s,"zIndex",Number);if(a&&x)return d((function(e){e()}));o&&i?e=i.canvas:o&&!e&&(e=function(e){var t=document.createElement("canvas");return t.style.position="fixed",t.style.top="0px",t.style.left="0px",t.style.pointerEvents="none",t.style.zIndex=e,t}(r),document.body.appendChild(e)),l&&!g&&h(e);var n={width:e.width,height:e.height};function f(){if(u){var t={getBoundingClientRect:function(){if(!o)return e.getBoundingClientRect()}};return h(t),void u.postMessage({resize:{width:t.width,height:t.height}})}n.width=n.height=null}function b(){i=null,l&&(c=!1,t.removeEventListener("resize",f)),o&&e&&(document.body.contains(e)&&document.body.removeChild(e),e=null,g=!1)}return u&&!g&&u.init(e),g=!0,u&&(e.__confetti_initialized=!0),l&&!c&&(c=!0,t.addEventListener("resize",f,!1)),u?u.fire(s,n,b):p(s,n,b)}return f.reset=function(){u&&u.reset(),i&&i.reset()},f}function F(){return y||(y=B(null,{useWorker:!0,resize:!0})),y}s.exports=function(){return F().apply(this,arguments)},s.exports.reset=function(){F().reset()},s.exports.create=B,s.exports.shapeFromPath=function(e){if(!i)throw new Error("path confetti are not supported in this browser");var t,s;"string"==typeof e?t=e:(t=e.path,s=e.matrix);var a=new Path2D(t),r=document.createElement("canvas").getContext("2d");if(!s){for(var n,o,l=1e3,d=l,c=l,m=0,u=0,h=0;h<l;h+=2)for(var g=0;g<l;g+=2)r.isPointInPath(a,h,g,"nonzero")&&(d=Math.min(d,h),c=Math.min(c,g),m=Math.max(m,h),u=Math.max(u,g));n=m-d,o=u-c;var x=Math.min(10/n,10/o);s=[x,0,0,x,-Math.round(n/2+d)*x,-Math.round(o/2+c)*x]}return{type:"path",path:t,matrix:s}},s.exports.shapeFromText=function(e){var t,s=1,a="#000000",r='"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol", "Noto Color Emoji", "EmojiOne Color", "Android Emoji", "Twemoji Mozilla", "system emoji", sans-serif';"string"==typeof e?t=e:(t=e.text,s="scalar"in e?e.scalar:s,r="fontFamily"in e?e.fontFamily:r,a="color"in e?e.color:a);var n=10*s,i=n+"px "+r,o=new OffscreenCanvas(n,n),l=o.getContext("2d");l.font=i;var d=l.measureText(t),c=Math.ceil(d.actualBoundingBoxRight+d.actualBoundingBoxLeft),m=Math.ceil(d.actualBoundingBoxAscent+d.actualBoundingBoxDescent),u=d.actualBoundingBoxLeft+2,h=d.actualBoundingBoxAscent+2;c+=4,m+=4,(l=(o=new OffscreenCanvas(c,m)).getContext("2d")).font=i,l.fillStyle=a,l.fillText(t,u,h);var g=1/s;return{type:"bitmap",bitmap:o.transferToImageBitmap(),matrix:[g,0,0,g,-c*g/2,-m*g/2]}}}(function(){return"undefined"!=typeof window?window:"undefined"!=typeof self?self:this||{}}(),m,!1);const u=m.exports;m.exports.create;const h=[{value:"general",label:"General Inquiry",icon:"💬",subcategories:[{value:"services",label:"Services Question",description:"Ask about our services"},{value:"navigation",label:"Website Help",description:"Need help using the site"},{value:"account",label:"Account Issues",description:"Login or account problems"},{value:"other",label:"Other",description:"Something else"}]},{value:"bug",label:"Report a Bug",icon:"🐛",subcategories:[{value:"not-loading",label:"Site Not Loading",description:"Website loading issues"},{value:"broken-link",label:"Broken Link",description:"Missing or broken pages"},{value:"payment",label:"Payment Problem",description:"Checkout or payment issues"},{value:"coupon",label:"Coupon Not Working",description:"Coupon code problems"},{value:"other-technical",label:"Other Technical",description:"Other technical issues"}]},{value:"feedback",label:"Feedback",icon:"💡",subcategories:[{value:"feature-suggestion",label:"Feature Request",description:"Suggest new features"},{value:"improvement",label:"Improvement Idea",description:"Improve existing features"},{value:"negative",label:"Report Issue",description:"Report negative experience"},{value:"positive",label:"Share Success",description:"Share positive experience"},{value:"other-feedback",label:"Other Feedback",description:"Other feedback"}]},{value:"partnership",label:"Partnership",icon:"🤝",subcategories:[{value:"business-collab",label:"Business Partnership",description:"Business collaboration"},{value:"affiliate",label:"Affiliate Program",description:"Join our affiliate program"},{value:"merchant",label:"Merchant Listing",description:"List your business"},{value:"sponsorship",label:"Sponsorship",description:"Advertising opportunities"},{value:"other-partnership",label:"Other Partnership",description:"Other partnership requests"}]}],g=({id:t,label:a,type:r="text",value:n,onChange:i,error:o,icon:l,required:d,placeholder:c})=>{const[m,u]=e.useState(!1),h=n.length>0,g=m||h;return s.jsxs("div",{className:"relative mb-6",children:[s.jsxs("div",{className:"relative",children:[l&&s.jsx("div",{className:"absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-design-muted-foreground z-10",children:s.jsx("div",{className:"w-4 h-4 sm:w-5 sm:h-5",children:l})}),s.jsx("input",{id:t,type:r,value:n,onChange:e=>i(e.target.value),onFocus:()=>u(!0),onBlur:()=>u(!1),placeholder:m?c:"",className:`\n            w-full h-10 sm:h-14 px-2 sm:px-4 ${l?"pl-8 sm:pl-12":"pl-2 sm:pl-4"} pr-2 sm:pr-4\n            bg-design-background/50 backdrop-blur-sm\n            border-2 rounded-lg sm:rounded-2xl\n            text-design-foreground text-sm sm:text-base\n            transition-all duration-300 ease-out\n            focus:outline-none focus:ring-0\n            ${o?"border-red-500 focus:border-red-500":"border-design-border focus:border-design-primary"}\n            ${g?"pt-5 sm:pt-6 pb-1 sm:pb-2":"pt-3 sm:pt-4 pb-3 sm:pb-4"}\n          `}),s.jsxs("label",{htmlFor:t,className:`\n            absolute left-2 sm:left-4 ${l?"left-8 sm:left-12":"left-2 sm:left-4"}\n            transition-all duration-300 ease-out\n            pointer-events-none\n            text-design-muted-foreground\n            ${g?"top-1.5 sm:top-2 text-xs font-medium":"top-1/2 transform -translate-y-1/2 text-sm sm:text-base"}\n          `,children:[a," ",d&&s.jsx("span",{className:"text-red-500",children:"*"})]})]}),o&&s.jsx("p",{className:"text-red-500 text-sm mt-2 ml-1 animate-in slide-in-from-left-2 duration-200",children:o})]})},x=({id:t,label:a,value:r,onChange:n,error:i,required:o,placeholder:l,rows:d=4})=>{const[c,m]=e.useState(!1),u=r.length>0,h=c||u;return s.jsxs("div",{className:"relative mb-6",children:[s.jsxs("div",{className:"relative",children:[s.jsx("textarea",{id:t,value:r,onChange:e=>n(e.target.value),onFocus:()=>m(!0),onBlur:()=>m(!1),placeholder:c?l:"",rows:d,className:`\n            w-full px-2 sm:px-4 pr-2 sm:pr-4\n            bg-design-background/50 backdrop-blur-sm\n            border-2 rounded-lg sm:rounded-2xl\n            text-design-foreground text-sm sm:text-base\n            transition-all duration-300 ease-out\n            focus:outline-none focus:ring-0\n            resize-none\n            ${i?"border-red-500 focus:border-red-500":"border-design-border focus:border-design-primary"}\n            ${h?"pt-7 sm:pt-8 pb-3 sm:pb-4":"pt-5 sm:pt-6 pb-3 sm:pb-4"}\n          `}),s.jsxs("label",{htmlFor:t,className:`\n            absolute left-2 sm:left-4\n            transition-all duration-300 ease-out\n            pointer-events-none\n            text-design-muted-foreground\n            ${h?"top-1.5 sm:top-2 text-xs font-medium":"top-5 sm:top-6 text-sm sm:text-base"}\n          `,children:[a," ",o&&s.jsx("span",{className:"text-red-500",children:"*"})]})]}),i&&s.jsx("p",{className:"text-red-500 text-sm mt-2 ml-1 animate-in slide-in-from-left-2 duration-200",children:i})]})},p=({children:e,onClick:t,type:a="button",variant:r="primary",size:n="md",disabled:i,loading:o,icon:l,iconPosition:d="left",fullWidth:m,className:u=""})=>{const h=`\n    relative inline-flex items-center justify-center\n    font-medium rounded-2xl\n    transition-all duration-300 ease-out\n    focus:outline-none focus:ring-2 focus:ring-offset-2\n    disabled:opacity-50 disabled:cursor-not-allowed\n    transform active:scale-95\n    ${m?"w-full":""}\n  `,g={primary:"\n      bg-gradient-to-r from-design-primary to-design-primary/90\n      hover:from-design-primary/90 hover:to-design-primary\n      text-white shadow-lg hover:shadow-xl\n      focus:ring-design-primary/50\n    ",secondary:"\n      bg-design-muted hover:bg-design-muted/80\n      text-design-foreground\n      border border-design-border\n      focus:ring-design-primary/50\n    ",ghost:"\n      bg-transparent hover:bg-design-muted/50\n      text-design-foreground\n      focus:ring-design-primary/50\n    "};return s.jsxs("button",{type:a,onClick:t,disabled:i||o,className:`${h} ${g[r]} ${{sm:"h-8 px-3 text-sm",md:"h-10 px-4 text-sm sm:h-12 sm:px-6 sm:text-base",lg:"h-12 px-6 text-base sm:h-14 sm:px-8 sm:text-lg"}[n]} ${u}`,children:[o&&s.jsx(c,{className:"w-4 h-4 mr-2 animate-spin"}),!o&&l&&"left"===d&&s.jsx("span",{className:"mr-2",children:l}),e,!o&&l&&"right"===d&&s.jsx("span",{className:"ml-2",children:l})]})},f=()=>{const[c,m]=e.useState("category"),[f,b]=e.useState(!1),[v,y]=e.useState(!1),[w,j]=e.useState(null),[N,M]=e.useState(null),[k,C]=e.useState(null),{toast:S}=a(),P=e.useCallback((async()=>new Promise(((e,t)=>{e("development_mode_dummy_token")}))),[]),[T,E]=e.useState({category:"",subcategory:"",description:"",name:"",email:""}),{handleSubmit:I,setValue:O,formState:{errors:B},reset:F}=t({defaultValues:T}),A=e.useMemo((()=>({category:{title:"How can we help?",subtitle:"Choose a category",progress:20},subcategory:{title:"Be more specific",subtitle:"Select a subcategory",progress:40},description:{title:"Tell us more",subtitle:"Describe your inquiry",progress:60},user:{title:"Contact details",subtitle:"How can we reach you?",progress:80},submit:{title:"Review & submit",subtitle:"Check your information",progress:100}})),[]),z=e.useCallback(((e,t)=>{E((s=>({...s,[e]:t}))),O(e,t),j(null)}),[O]),$=e.useCallback((()=>{const e=h.find((e=>e.value===T.category));return e?.subcategories||[]}),[T.category]),R=e.useCallback((e=>{C(null),M(e.targetTouches[0].clientX)}),[]),L=e.useCallback((e=>{C(e.targetTouches[0].clientX)}),[]),D=e.useCallback((()=>{if(!N||!k)return;const e=N-k,t=e<-50;e>50&&q()&&W(),t&&_()&&Y()}),[N,k]),q=e.useCallback((()=>{switch(c){case"category":return!!T.category;case"subcategory":return!!T.subcategory;case"description":return!!T.description;case"user":return!!T.name&&!!T.email&&/^\S+@\S+\.\S+$/.test(T.email);case"submit":return!0;default:return!1}}),[c,T]),_=e.useCallback((()=>"category"!==c&&!v),[c,v]),W=e.useCallback((()=>{if(!q())return void j(U());const e=["category","subcategory","description","user","submit"],t=e.indexOf(c);t<e.length-1&&(m(e[t+1]),j(null))}),[c,q]),Y=e.useCallback((()=>{if(!_())return;const e=["category","subcategory","description","user","submit"],t=e.indexOf(c);t>0&&(m(e[t-1]),j(null))}),[c,_]),U=e.useCallback((()=>{switch(c){case"category":return"Please select a category";case"subcategory":return"Please select a subcategory";case"description":return"Please describe your inquiry";case"user":return T.name?T.email?/^\S+@\S+\.\S+$/.test(T.email)?"Please fill out all fields":"Please enter a valid email":"Please enter your email":"Please enter your name";case"submit":return"Please complete the verification";default:return"Please complete this step"}}),[c,T]),H=e.useCallback((async e=>{b(!0),j(null);try{const t=await P(),s={name:e.name.trim(),email:e.email.trim(),subject:`${h.find((t=>t.value===e.category))?.label||e.category} - ${$().find((t=>t.value===e.subcategory))?.label||e.subcategory}`,message:e.description.trim(),token:t},a=await fetch("/api/contact",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(s)});if(!a.ok){const e=await a.json().catch((()=>({error:"Unknown error"})));throw new Error(e.error||"Failed to send message")}y(!0),u({particleCount:100,spread:70,origin:{y:.6}}),"undefined"!=typeof window&&window.gtag&&window.gtag("event","form_submission",{event_category:"engagement",event_label:`${e.category} - ${e.subcategory}`,value:1}),S("Success",{description:"Your message has been sent successfully!",variant:"success"})}catch(t){j(t.message||"Failed to send message. Please try again."),S("Error",{description:t.message||"Failed to send message. Please try again.",variant:"destructive"})}finally{b(!1)}}),[P,$,S]);e.useEffect((()=>{const e=e=>{"Enter"===e.key&&q()&&(e.preventDefault(),W()),"Escape"===e.key&&_()&&(e.preventDefault(),Y())};return window.addEventListener("keydown",e),()=>window.removeEventListener("keydown",e)}),[q,_,W,Y]),e.useEffect((()=>{Object.entries(T).forEach((([e,t])=>{O(e,t)}))}),[T,O]);return s.jsx("div",{className:"w-full max-w-2xl mx-auto px-2 sm:px-6 lg:px-8",onTouchStart:R,onTouchMove:L,onTouchEnd:D,children:s.jsxs("div",{className:"bg-design-background/80 backdrop-blur-xl border border-design-border/50 rounded-xl sm:rounded-3xl shadow-2xl overflow-hidden",children:[!v&&s.jsxs("div",{className:"bg-gradient-to-r from-design-primary/10 to-design-primary/5 p-3 sm:p-6 border-b border-design-border/50",children:[s.jsxs("div",{className:"flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 space-y-2 sm:space-y-0",children:[s.jsxs("div",{className:"flex-1",children:[s.jsx("h1",{className:"text-base sm:text-xl font-bold text-design-foreground",children:A[c].title}),s.jsx("p",{className:"text-xs sm:text-sm text-design-muted-foreground mt-1",children:A[c].subtitle})]}),s.jsx("div",{className:"text-left sm:text-right",children:s.jsxs("div",{className:"text-xs font-medium text-design-primary bg-design-primary/10 px-2 py-1 rounded-full",children:["Step ",["category","subcategory","description","user","submit"].indexOf(c)+1," of 5"]})})]}),s.jsx("div",{className:"w-full bg-design-muted/30 rounded-full h-2",children:s.jsx("div",{className:"bg-gradient-to-r from-design-primary to-design-primary/80 h-2 rounded-full transition-all duration-500 ease-out",style:{width:`${A[c].progress}%`}})}),s.jsx("div",{className:"flex justify-center mt-3",children:s.jsxs("p",{className:"text-xs text-design-muted-foreground text-center",children:[s.jsx("span",{className:"hidden sm:inline",children:"💡 Swipe left/right or use arrow keys to navigate"}),s.jsx("span",{className:"sm:hidden",children:"💡 Swipe to navigate"})]})})]}),w&&s.jsx("div",{className:"mx-4 sm:mx-6 mt-4 sm:mt-6 p-3 sm:p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl sm:rounded-2xl",children:s.jsx("p",{className:"text-red-600 dark:text-red-400 text-sm font-medium text-center sm:text-left",children:w})}),s.jsx("div",{className:"p-3 sm:p-6 min-h-[300px] sm:min-h-[400px]",children:(()=>{if(v)return s.jsxs("div",{className:"text-center py-6 sm:py-8 animate-in fade-in duration-500",children:[s.jsx("div",{className:"w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-4 sm:mb-6 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center",children:s.jsx(o,{className:"w-6 h-6 sm:w-8 sm:h-8 text-green-600 dark:text-green-400"})}),s.jsx("h2",{className:"text-xl sm:text-2xl font-bold text-design-foreground mb-3 sm:mb-4",children:"Thank You!"}),s.jsx("p",{className:"text-design-muted-foreground mb-6 sm:mb-8 max-w-md mx-auto text-sm sm:text-base px-4",children:"Your message has been received. We'll get back to you within 1-3 business days."}),s.jsxs("div",{className:"bg-design-card/50 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 border border-design-border",children:[s.jsx("h3",{className:"font-semibold mb-3 sm:mb-4 text-design-foreground text-sm sm:text-base",children:"What happens next?"}),s.jsxs("div",{className:"space-y-2 sm:space-y-3 text-xs sm:text-sm text-design-muted-foreground text-left",children:[s.jsxs("div",{className:"flex items-start",children:[s.jsx(o,{className:"w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0"}),s.jsxs("span",{children:["Confirmation email sent to ",s.jsx("strong",{className:"text-design-foreground break-all",children:T.email})]})]}),s.jsxs("div",{className:"flex items-start",children:[s.jsx(o,{className:"w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0"}),s.jsx("span",{children:"Our team will review your message"})]}),s.jsxs("div",{className:"flex items-start",children:[s.jsx(o,{className:"w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0"}),s.jsx("span",{children:"You'll receive a reply within 1-3 business days"})]})]})]}),s.jsx(p,{onClick:()=>{y(!1),F(),E({category:"",subcategory:"",description:"",name:"",email:""}),m("category")},variant:"primary",size:"lg",fullWidth:!0,children:"Send Another Message"})]});switch(c){case"category":return s.jsx("div",{className:"space-y-6",children:s.jsx("div",{className:"grid gap-4",children:h.map((e=>s.jsx("div",{onClick:()=>z("category",e.value),className:`\n                    p-3 sm:p-4 rounded-xl sm:rounded-2xl border-2 cursor-pointer\n                    transition-all duration-300 ease-out\n                    transform hover:scale-[1.02] active:scale-[0.98]\n                    touch-manipulation\n                    ${T.category===e.value?"border-design-primary bg-design-primary/10 shadow-lg":"border-design-border bg-design-card/50 hover:border-design-primary/50"}\n                  `,children:s.jsxs("div",{className:"flex items-center",children:[s.jsx("div",{className:"text-xl sm:text-2xl mr-3 sm:mr-4 flex-shrink-0",children:e.icon}),s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("h3",{className:"font-semibold text-design-foreground text-sm sm:text-base",children:e.label}),s.jsxs("p",{className:"text-xs sm:text-sm text-design-muted-foreground mt-1 truncate",children:[e.subcategories.length," options available"]})]}),s.jsx("div",{className:`\n                      w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 flex items-center justify-center flex-shrink-0\n                      ${T.category===e.value?"border-design-primary bg-design-primary":"border-design-border"}\n                    `,children:T.category===e.value&&s.jsx("div",{className:"w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-white"})})]})},e.value)))})});case"subcategory":const e=$();return s.jsx("div",{className:"space-y-4",children:e.map((e=>s.jsx("div",{onClick:()=>z("subcategory",e.value),className:`\n                  p-3 sm:p-4 rounded-xl sm:rounded-2xl border-2 cursor-pointer\n                  transition-all duration-300 ease-out\n                  transform hover:scale-[1.02] active:scale-[0.98]\n                  touch-manipulation\n                  ${T.subcategory===e.value?"border-design-primary bg-design-primary/10 shadow-lg":"border-design-border bg-design-card/50 hover:border-design-primary/50"}\n                `,children:s.jsxs("div",{className:"flex items-center",children:[s.jsxs("div",{className:"flex-1 min-w-0",children:[s.jsx("h3",{className:"font-semibold text-design-foreground text-sm sm:text-base",children:e.label}),e.description&&s.jsx("p",{className:"text-xs sm:text-sm text-design-muted-foreground mt-1",children:e.description})]}),s.jsx("div",{className:`\n                    w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 flex items-center justify-center flex-shrink-0 ml-3\n                    ${T.subcategory===e.value?"border-design-primary bg-design-primary":"border-design-border"}\n                  `,children:T.subcategory===e.value&&s.jsx("div",{className:"w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-white"})})]})},e.value)))});case"description":return s.jsx("div",{children:s.jsx(x,{id:"description",label:"Message",value:T.description,onChange:e=>z("description",e),error:B.description?.message,required:!0,placeholder:"Please describe your issue or question in detail...",rows:6})});case"user":return s.jsxs("div",{className:"space-y-6",children:[s.jsx(g,{id:"name",label:"Full Name",value:T.name,onChange:e=>z("name",e),error:B.name?.message,icon:s.jsx(l,{className:"w-5 h-5"}),required:!0,placeholder:"Enter your full name"}),s.jsx(g,{id:"email",label:"Email Address",type:"email",value:T.email,onChange:e=>z("email",e),error:B.email?.message,icon:s.jsx(d,{className:"w-5 h-5"}),required:!0,placeholder:"Enter your email address"})]});case"submit":return s.jsxs("div",{className:"space-y-6",children:[s.jsxs("div",{className:"bg-design-card/50 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-design-border",children:[s.jsx("h3",{className:"font-semibold mb-4 text-design-foreground text-sm sm:text-base",children:"Review Your Information"}),s.jsxs("div",{className:"space-y-3 sm:space-y-4 text-sm",children:[s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-design-foreground text-xs sm:text-sm",children:"Category:"}),s.jsx("p",{className:"text-design-muted-foreground text-xs sm:text-sm mt-1",children:h.find((e=>e.value===T.category))?.label})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-design-foreground text-xs sm:text-sm",children:"Subcategory:"}),s.jsx("p",{className:"text-design-muted-foreground text-xs sm:text-sm mt-1",children:$().find((e=>e.value===T.subcategory))?.label})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-design-foreground text-xs sm:text-sm",children:"Message:"}),s.jsx("p",{className:"text-design-muted-foreground text-xs sm:text-sm mt-1 line-clamp-3",children:T.description})]}),s.jsxs("div",{children:[s.jsx("span",{className:"font-medium text-design-foreground text-xs sm:text-sm",children:"Contact:"}),s.jsxs("p",{className:"text-design-muted-foreground text-xs sm:text-sm mt-1 break-all",children:[T.name," (",T.email,")"]})]})]})]}),s.jsx("div",{className:"text-center text-sm text-gray-600 dark:text-gray-400",children:s.jsx("p",{children:"Protected by reCAPTCHA v3"})})]});default:return null}})()}),!v&&s.jsxs("div",{className:"bg-design-background/50 backdrop-blur-sm p-3 sm:p-6 border-t border-design-border/50",children:[s.jsxs("div",{className:"sm:hidden",children:[s.jsx("div",{className:"flex justify-center space-x-2 mb-3",children:["category","subcategory","description","user","submit"].map(((e,t)=>s.jsx("div",{className:"w-2 h-2 rounded-full transition-all duration-300 "+(e===c?"bg-design-primary scale-125":["category","subcategory","description","user","submit"].indexOf(c)>t?"bg-design-primary/60":"bg-design-muted")},e)))}),s.jsxs("div",{className:"flex gap-2",children:[s.jsx(p,{onClick:Y,disabled:!_(),variant:"ghost",size:"sm",icon:s.jsx(r,{className:"w-3 h-3"}),className:"flex-1 "+(_()?"":"invisible"),children:"Back"}),"submit"===c?s.jsx(p,{onClick:I(H),disabled:!q()||f,loading:f,variant:"primary",size:"sm",icon:s.jsx(n,{className:"w-3 h-3"}),iconPosition:"right",className:"flex-[2]",children:f?"Sending...":"Submit"}):s.jsx(p,{onClick:W,disabled:!q(),variant:"primary",size:"sm",icon:s.jsx(i,{className:"w-3 h-3"}),iconPosition:"right",className:"flex-[2]",children:"Continue"})]})]}),s.jsxs("div",{className:"hidden sm:flex justify-between items-center gap-4",children:[s.jsx(p,{onClick:Y,disabled:!_(),variant:"ghost",size:"lg",icon:s.jsx(r,{className:"w-4 h-4"}),className:""+(_()?"":"invisible"),children:"Back"}),s.jsx("div",{className:"flex space-x-2",children:["category","subcategory","description","user","submit"].map(((e,t)=>s.jsx("div",{className:"w-2 h-2 rounded-full transition-all duration-300 "+(e===c?"bg-design-primary scale-125":["category","subcategory","description","user","submit"].indexOf(c)>t?"bg-design-primary/60":"bg-design-muted")},e)))}),"submit"===c?s.jsx(p,{onClick:I(H),disabled:!q()||f,loading:f,variant:"primary",size:"lg",icon:s.jsx(n,{className:"w-4 h-4"}),iconPosition:"right",children:f?"Sending...":"Submit"}):s.jsx(p,{onClick:W,disabled:!q(),variant:"primary",size:"lg",icon:s.jsx(i,{className:"w-4 h-4"}),iconPosition:"right",children:"Continue"})]})]})]})})};export{f as default};
