import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, e as renderSlot } from './astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from './MainLayout-BVJnMCp3.js';

const $$Astro = createAstro("http://localhost:4321");
const $$LegalPageLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$LegalPageLayout;
  const { title, description, structuredData } = Astro2.props;
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-12"> <div class="max-w-4xl mx-auto"> <div class="bg-design-card/80 backdrop-blur-sm border border-design-border rounded-lg p-8 shadow-design-md legal-content"> ${renderSlot($$result2, $$slots["default"])} </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/LegalPageLayout.astro", void 0);

export { $$LegalPageLayout as $ };
