import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute, e as renderSlot } from './astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from './MainLayout-BVJnMCp3.js';

const $$Astro = createAstro("http://localhost:4321");
const $$InfoPageLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$InfoPageLayout;
  const {
    title,
    description,
    structuredData,
    background = "default",
    pattern = "dots"
  } = Astro2.props;
  const backgroundStyles = {
    default: "bg-design-background",
    primary: "bg-design-primary text-white dark:text-black",
    secondary: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    accent: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    muted: "bg-design-muted",
    transparent: "bg-transparent"
  };
  const patternStyles = {
    none: "",
    dots: "bg-[url('/patterns/dots-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/dots-dark.svg')] dark:opacity-10",
    grid: "bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-10",
    circles: "bg-[url('/patterns/circle-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/circle-dark.svg')] dark:opacity-10",
    hexagon: "bg-[url('/patterns/hexagon-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/hexagon-dark.svg')] dark:opacity-10",
    wave: "bg-[url('/patterns/wave-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/wave-dark.svg')] dark:opacity-10"
  };
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div${addAttribute(`relative min-h-screen ${backgroundStyles[background]}`, "class")}> <!-- Enhanced glass effect background pattern similar to homepage --> <div class="absolute inset-0 z-0 pointer-events-none overflow-hidden"> <!-- Background pattern based on pattern prop --> ${pattern !== "none" && renderTemplate`<div${addAttribute(`absolute inset-0 ${patternStyles[pattern]}`, "class")}></div>`} <!-- Secondary pattern for texture - only if pattern is not 'none' --> ${pattern !== "none" && pattern !== "hexagon" && renderTemplate`<div class="absolute inset-0 bg-[url('/patterns/hexagon-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/hexagon-dark.svg')] dark:opacity-5"></div>`} <!-- Gradient overlay with blur effect - only if background is not transparent --> ${background !== "transparent" && renderTemplate`<div class="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-design-background/80 via-design-background/60 to-design-background/40 backdrop-blur-[3px]"></div>`} <!-- Decorative elements - Monochromatic --> <div class="absolute top-[-5%] right-[-10%] w-[40%] h-[40%] bg-design-primary/10 dark:bg-design-primary/5 rounded-full filter blur-[80px]"></div> <div class="absolute bottom-[-5%] left-[-10%] w-[40%] h-[40%] bg-design-primary/10 dark:bg-design-primary/5 rounded-full filter blur-[80px]"></div> <!-- Additional decorative elements --> <div class="absolute top-[30%] left-[20%] w-[25%] h-[25%] bg-design-primary/8 dark:bg-design-primary/3 rounded-full filter blur-[60px] animate-float"></div> <div class="absolute bottom-[20%] right-[15%] w-[20%] h-[20%] bg-design-primary/8 dark:bg-design-primary/3 rounded-full filter blur-[50px] animate-float animation-delay-200"></div> </div> <!-- Content container --> <div class="relative z-10 container mx-auto px-2 sm:px-4 pt-0 sm:pt-2 lg:pt-4 pb-16"> <div class="max-w-4xl mx-auto"> <!-- Page content will be inserted here --> ${renderSlot($$result2, $$slots["default"])} </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/InfoPageLayout.astro", void 0);

export { $$InfoPageLayout as $ };
