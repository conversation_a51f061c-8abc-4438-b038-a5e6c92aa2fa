/**
 * <PERSON>flare Worker for Supabase Edge Functions Cron Jobs
 * 
 * This worker handles scheduled execution of Supabase Edge Functions
 * Deploy this as a separate Cloudflare Worker with cron triggers
 */

// Configuration
const CONFIG = {
  SUPABASE_URL: 'https://zlnvivfgzgcjuspktadj.supabase.co',
  SUPABASE_KEY: 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbnZpdmZnemdjanVzcGt0YWRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2NjQyNTcsImV4cCI6MjA1OTI0MDI1N30.nOjNcWd-bneTXg5UHv2nlAc10l7Gi-cQTEm9V5yrqFY',
  INTERVAL_MINUTES: 10
};

// Edge Functions to execute
const EDGE_FUNCTIONS = [
  {
    name: 'raw-sync',
    url: `${CONFIG.SUPABASE_URL}/functions/v1/raw-sync`,
    description: 'Sync raw deal data'
  },
  {
    name: 'enrichment', 
    url: `${CONFIG.SUPABASE_URL}/functions/v1/enrichment`,
    description: 'Enrich deal data'
  },
  {
    name: 'normalize-price-discount-deals',
    url: `${CONFIG.SUPABASE_URL}/functions/v1/normalize-price-discount-deals`, 
    description: 'Normalize prices and discounts'
  },
  {
    name: 'group-products',
    url: `${CONFIG.SUPABASE_URL}/functions/v1/group-products`,
    description: 'Group related products'
  }
];

/**
 * Execute a single edge function
 */
async function executeEdgeFunction(func) {
  const startTime = Date.now();
  
  try {
    const response = await fetch(func.url, {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${CONFIG.SUPABASE_KEY}`,
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name: 'Functions' })
    });

    const duration = Date.now() - startTime;
    const result = await response.text();

    if (response.ok) {
      return { 
        success: true, 
        duration, 
        response: result.substring(0, 500),
        status: response.status
      };
    } else {
      return { 
        success: false, 
        duration, 
        error: `${response.status}: ${result}`,
        status: response.status
      };
    }
  } catch (error) {
    const duration = Date.now() - startTime;
    return { 
      success: false, 
      duration, 
      error: error.message,
      status: 0
    };
  }
}

/**
 * Execute all functions with delays
 */
async function executeAllFunctions() {
  const results = [];
  const startTime = new Date();
  
  console.log(`🎯 Starting Supabase sync at ${startTime.toISOString()}`);
  
  for (let i = 0; i < EDGE_FUNCTIONS.length; i++) {
    const func = EDGE_FUNCTIONS[i];
    
    console.log(`🚀 Executing ${func.name} (${i + 1}/${EDGE_FUNCTIONS.length})`);
    
    const result = await executeEdgeFunction(func);
    results.push({
      function: func.name,
      ...result,
      executedAt: new Date().toISOString()
    });
    
    console.log(`${result.success ? '✅' : '❌'} ${func.name}: ${result.success ? 'Success' : result.error}`);
    
    // Wait 10 minutes before next function (except for the last one)
    if (i < EDGE_FUNCTIONS.length - 1) {
      console.log(`⏳ Waiting ${CONFIG.INTERVAL_MINUTES} minutes...`);
      await new Promise(resolve => setTimeout(resolve, CONFIG.INTERVAL_MINUTES * 60 * 1000));
    }
  }
  
  const endTime = new Date();
  const totalDuration = endTime - startTime;
  const successCount = results.filter(r => r.success).length;
  
  console.log(`🏁 Completed at ${endTime.toISOString()}`);
  console.log(`📊 Success: ${successCount}/${EDGE_FUNCTIONS.length}, Duration: ${Math.round(totalDuration / 1000)}s`);
  
  return {
    success: successCount === EDGE_FUNCTIONS.length,
    results,
    summary: {
      startTime: startTime.toISOString(),
      endTime: endTime.toISOString(),
      totalDuration: Math.round(totalDuration / 1000),
      successCount,
      totalCount: EDGE_FUNCTIONS.length
    }
  };
}

/**
 * Handle scheduled events (cron triggers)
 */
async function handleScheduled(event) {
  console.log('📅 Cron trigger activated:', event.cron);
  
  try {
    const result = await executeAllFunctions();
    
    // Log to Cloudflare Analytics or external service
    console.log('📊 Execution completed:', JSON.stringify(result.summary));
    
    return new Response(JSON.stringify({
      success: true,
      message: 'Supabase sync completed',
      ...result
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  } catch (error) {
    console.error('💥 Cron execution failed:', error);
    
    return new Response(JSON.stringify({
      success: false,
      error: error.message,
      timestamp: new Date().toISOString()
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
}

/**
 * Handle HTTP requests (manual triggers)
 */
async function handleRequest(request) {
  const url = new URL(request.url);
  
  // Health check
  if (url.pathname === '/health') {
    return new Response(JSON.stringify({
      status: 'healthy',
      timestamp: new Date().toISOString(),
      functions: EDGE_FUNCTIONS.map(f => f.name)
    }), {
      headers: { 'Content-Type': 'application/json' }
    });
  }
  
  // Manual trigger
  if (url.pathname === '/trigger' && request.method === 'POST') {
    try {
      const result = await executeAllFunctions();
      
      return new Response(JSON.stringify({
        success: true,
        message: 'Manual sync completed',
        ...result
      }), {
        headers: { 'Content-Type': 'application/json' }
      });
    } catch (error) {
      return new Response(JSON.stringify({
        success: false,
        error: error.message,
        timestamp: new Date().toISOString()
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }
  }
  
  // Default response
  return new Response(JSON.stringify({
    message: 'VapeHybrid Supabase Cron Worker',
    endpoints: {
      health: '/health',
      trigger: '/trigger (POST)'
    },
    schedule: 'Daily at 16:00 UTC',
    functions: EDGE_FUNCTIONS.map(f => f.name)
  }), {
    headers: { 'Content-Type': 'application/json' }
  });
}

/**
 * Main worker event listener
 */
addEventListener('fetch', event => {
  event.respondWith(handleRequest(event.request));
});

addEventListener('scheduled', event => {
  event.waitUntil(handleScheduled(event));
});

export default {
  async fetch(request) {
    return handleRequest(request);
  },
  
  async scheduled(event) {
    return handleScheduled(event);
  }
};
