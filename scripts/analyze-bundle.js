/**
 * Bundle Analysis Script
 * 
 * This script analyzes the JavaScript bundle size and provides recommendations
 * for reducing it. It works with the Astro build output.
 * 
 * Usage:
 * node scripts/analyze-bundle.js
 * 
 * Requirements:
 * - source-map-explorer: npm install source-map-explorer
 */

const { execSync } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const buildDir = 'dist';
const outputFile = 'bundle-analysis.html';

// Check if build directory exists
if (!fs.existsSync(buildDir)) {
  console.error(`Build directory '${buildDir}' not found. Run 'npm run build' first.`);
  process.exit(1);
}

// Find all JS files in the build directory
const findJsFiles = (dir) => {
  const results = [];
  const files = fs.readdirSync(dir);
  
  for (const file of files) {
    const filePath = path.join(dir, file);
    const stat = fs.statSync(filePath);
    
    if (stat.isDirectory()) {
      results.push(...findJsFiles(filePath));
    } else if (file.endsWith('.js') && !file.includes('legacy')) {
      results.push(filePath);
    }
  }
  
  return results;
};

// Analyze bundle size
const analyzeBundle = () => {
  console.log('Analyzing JavaScript bundle size...');
  
  try {
    // Find all JS files
    const jsFiles = findJsFiles(buildDir);
    console.log(`Found ${jsFiles.length} JavaScript files to analyze`);
    
    if (jsFiles.length === 0) {
      console.error('No JavaScript files found in the build directory');
      return;
    }
    
    // Run source-map-explorer on all JS files
    const command = `npx source-map-explorer ${jsFiles.join(' ')} --html ${outputFile}`;
    execSync(command, { stdio: 'inherit' });
    
    console.log(`\nBundle analysis complete! Results saved to ${outputFile}`);
    console.log('\nRecommendations for reducing bundle size:');
    console.log('1. Use dynamic imports for large components that are not needed immediately');
    console.log('2. Split vendor code into separate chunks');
    console.log('3. Remove unused dependencies and code');
    console.log('4. Use tree-shaking friendly imports (e.g., import { Button } from \'@/components\' instead of import * as Components from \'@/components\')');
    console.log('5. Consider using smaller alternatives for large libraries');
    console.log('6. Implement code splitting based on routes');
    
  } catch (error) {
    console.error('Error analyzing bundle:', error.message);
  }
};

// Run the script
analyzeBundle();
