name = "vapehybrid"
main = "dist/_worker.js/index.js"
compatibility_date = "2024-01-15"
compatibility_flags = ["nodejs_compat"]

[site]
bucket = "dist"

# Environment variables (secrets will be set via CLI)
[vars]
NODE_ENV = "production"
ASTRO_SITE_URL = "https://vapehybrid.com"
PUBLIC_SITE_URL = "https://vapehybrid.com"
AFFILIATE_CLOAK_BASE_URL = "/go"
REVEAL_COOKIE_PREFIX = "revealed_deal_"
ADMIN_EMAIL = "<EMAIL>"
JWT_EXPIRATION = "86400"
SUPABASE_URL = "https://zlnvivfgzgcjuspktadj.supabase.co"
PUBLIC_SUPABASE_URL = "https://zlnvivfgzgcjuspktadj.supabase.co"
