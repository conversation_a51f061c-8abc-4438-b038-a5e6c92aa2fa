feat(supabase): v3.12.0 - Add automated Supabase Edge Functions scheduler

- Created complete automation system for 4 Supabase Edge Functions
- Configured scheduled execution at 16:00 UTC with 10-minute intervals
- Implemented GitHub Actions workflow for daily execution
- Added Cloudflare Worker alternative with cron triggers
- Created testing utilities for validation and monitoring
- Updated package.json with new automation scripts
- Functions executed in sequence: raw-sync, enrichment, normalize-price-discount-deals, group-products
- Added comprehensive error handling and logging
