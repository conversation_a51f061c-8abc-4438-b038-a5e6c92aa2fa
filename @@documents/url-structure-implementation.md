# URL Structure Implementation Plan

## Current Problem
- EJuice Connect URL: `https://vapehybrid.com/merchants/71617`
- Competitor URLs: `simplycodes.com/store/ejuiceconnect.com`
- GSC shows 24 impressions, 0 clicks, position 65.25

## Solution: Slug-Based URLs

### Target URL Structure:
- New: `https://vapehybrid.com/merchants/ejuice-connect`
- SEO-friendly, keyword-rich, memorable

## Implementation Steps

### Step 1: Database Schema Update

```sql
-- Add slug column to merchants table
ALTER TABLE merchants ADD COLUMN slug TEXT UNIQUE;

-- Create index for performance
CREATE INDEX idx_merchants_slug ON merchants(slug);

-- Update existing merchants with slugs
UPDATE merchants SET slug = 'ejuice-connect' WHERE id = 71617;
UPDATE merchants SET slug = 'eightvape' WHERE name = 'EightVape';
UPDATE merchants SET slug = 'vapordna' WHERE name = 'VaporDNA';
UPDATE merchants SET slug = 'vapesourcing' WHERE name = 'VapeSourcing';
-- Add more as needed
```

### Step 2: Create New Route Handler

**File: `src/pages/merchants/[slug].astro`**

```astro
---
import MainLayout from '../../layouts/MainLayout.astro';
import { createServerSupabaseClient } from '../../utils/supabase/server';
// ... other imports

// Get the merchant slug from the URL
const { slug } = Astro.params;

// Fetch merchant by slug instead of ID
const supabase = createServerSupabaseClient({
  cookies: () => Astro.request.headers.get('cookie') || ''
});

const { data: merchant, error } = await supabase
  .from('merchants')
  .select('*')
  .eq('slug', slug)
  .single();

// Rest of the logic remains the same...
---
```

### Step 3: Update Existing Route for Backwards Compatibility

**File: `src/pages/merchants/[id].astro`**

```astro
---
// Add redirect logic for numeric IDs
const { id } = Astro.params;

// Check if ID is numeric (old format)
if (/^\d+$/.test(id)) {
  // Fetch merchant to get slug
  const { data: merchant } = await supabase
    .from('merchants')
    .select('slug')
    .eq('id', id)
    .single();
  
  if (merchant?.slug) {
    return Astro.redirect(`/merchants/${merchant.slug}`, 301);
  }
}

// If not numeric, treat as slug and continue...
---
```

### Step 4: Update All Internal Links

**Files to Update:**
1. `src/pages/merchants.astro` - merchant listing page
2. `src/components/OptimizedDealsPageWrapper.tsx` - deal cards
3. Any other components linking to merchants

**Example Update:**
```astro
<!-- OLD -->
<a href={`/merchants/${merchant.id}`}>

<!-- NEW -->
<a href={`/merchants/${merchant.slug || merchant.id}`}>
```

### Step 5: Sitemap Updates

**File: `src/pages/sitemap.xml.ts`**

```typescript
// Update sitemap generation to use slugs
const merchantUrls = merchants.map(merchant => ({
  url: `${SITE_URL}/merchants/${merchant.slug || merchant.id}`,
  lastmod: merchant.updated_at || new Date().toISOString(),
  changefreq: 'weekly',
  priority: 0.7
}));
```

## SEO Optimization for EJuice Connect

### Enhanced Page Content

**Title Optimization:**
```astro
const title = merchant?.name === 'EJuice Connect' 
  ? `EJuice Connect Coupon Codes & Promo Codes - 15% Off June 2025 | VapeHybrid`
  : `${merchant.name} Coupons & Deals | VapeHybrid`;
```

**Description Optimization:**
```astro
const description = merchant?.name === 'EJuice Connect'
  ? `Save with verified EJuice Connect coupon codes and promo codes. Get up to 15% off e-liquids, vape devices, and coils. ${dealCount} active coupons updated daily.`
  : `Save with the best ${merchant.name} coupons and deals...`;
```

**H1 Optimization:**
```astro
<h1 class="text-3xl font-bold text-design-foreground mb-4">
  {merchant.name === 'EJuice Connect' 
    ? 'EJuice Connect Coupon Codes & Promo Codes'
    : `${merchant.name} Coupons & Deals`
  }
</h1>
```

### Content Enhancements

**Add Verification Information:**
```astro
{deals && deals.length > 0 && (
  <div class="mb-6 p-4 bg-design-card border border-design-border rounded-lg">
    <div class="flex items-center justify-between">
      <span class="text-sm text-design-muted-foreground">
        {deals.length} Active Coupons
      </span>
      <span class="text-sm text-design-muted-foreground">
        Last verified {getLastVerifiedTime(deals)}
      </span>
    </div>
  </div>
)}
```

**Add Savings Information:**
```astro
{merchant.name === 'EJuice Connect' && (
  <div class="mb-6">
    <h2 class="text-lg font-semibold text-design-foreground mb-2">
      How to Save at EJuice Connect
    </h2>
    <ul class="text-design-muted-foreground space-y-2">
      <li>• Get up to 15% off with verified coupon codes</li>
      <li>• Free shipping on orders over $50</li>
      <li>• Product-specific discounts on e-liquids and devices</li>
      <li>• Newsletter signup for exclusive deals</li>
    </ul>
  </div>
)}
```

## Testing Plan

### 1. URL Functionality Testing
- Test new slug URLs work correctly
- Verify old numeric URLs redirect properly
- Check all internal links updated

### 2. SEO Testing
- Verify meta tags are correct
- Check structured data is valid
- Test canonical URLs

### 3. Performance Testing
- Ensure database queries are optimized
- Check page load times
- Verify caching works correctly

## Deployment Strategy

### Phase 1: Database Updates
1. Add slug column to merchants table
2. Populate slugs for existing merchants
3. Test database queries

### Phase 2: Code Deployment
1. Deploy new route handlers
2. Update internal links
3. Test redirects

### Phase 3: SEO Monitoring
1. Submit updated sitemap to GSC
2. Monitor ranking changes
3. Track click-through rates

## Expected Results

### Week 1:
- New URLs live and functional
- Old URLs redirecting properly
- Improved page content for EJuice Connect

### Week 2-4:
- Improved rankings for target keywords
- Increased click-through rates
- Better user experience with memorable URLs

### Success Metrics:
- Move from position 65+ to 45+ for "ejuice connect coupon"
- Generate first clicks from target keywords
- Improve overall merchant page SEO scores

## Risk Mitigation

### Potential Issues:
1. **Broken Links**: Internal links not updated
2. **SEO Impact**: Temporary ranking drops during transition
3. **User Confusion**: Multiple URLs for same content

### Solutions:
1. Comprehensive link audit and updates
2. Proper 301 redirects to maintain SEO value
3. Clear navigation and canonical URLs

## Next Steps

1. **Immediate**: Add slug column to database
2. **Day 1-2**: Implement new route handlers
3. **Day 3-4**: Update all internal links
4. **Day 5**: Deploy and test
5. **Week 2+**: Monitor SEO performance and iterate
