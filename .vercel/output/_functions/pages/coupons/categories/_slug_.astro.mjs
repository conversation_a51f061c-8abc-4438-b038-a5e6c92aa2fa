import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../../assets/js/MainLayout-BVJnMCp3.js';
import { c as createServerSupabaseClient } from '../../../assets/js/server-e_5TR1Eu.js';
import { O as OptimizedDealsPageWrapper } from '../../../assets/js/OptimizedDealsPageWrapper-EctMrJfb.js';
import { $ as $$WebPImage, F as FilterDrawer, a as SortSelect, V as ViewToggle, b as $$PerPageSelect, c as $$Pagination, G as GlowingButton } from '../../../assets/js/app/design-system-CUssmfny.js';
import { U as UniversalEmailCaptureForm } from '../../../assets/js/UniversalEmailCaptureForm-BUBlyReO.js';
import { jsxs, jsx } from 'react/jsx-runtime';
import { useState } from 'react';
import { c as calculateCouponStats, b as generateRecentVerifications, f as generateCategorySEOMeta, a as generateSocialProofText, h as generateCategoryPageSchema, e as generateMetaTagsObject } from '../../../assets/js/seo-meta-generators-Bu4YtbQp.js';
export { renderers } from '../../../renderers.mjs';

const CategoryFAQ = ({
  categoryName,
  activeCoupons,
  bestDiscount
}) => {
  return /* @__PURE__ */ jsxs("div", { id: "faq", className: "mb-6", children: [
    /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-3", children: [
      categoryName,
      " Coupon Codes FAQ"
    ] }),
    /* @__PURE__ */ jsx("div", { className: "text-sm text-gray-600 dark:text-gray-300", children: /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700", style: { fontSize: "17px" }, children: [
          "How do I use ",
          categoryName,
          " coupon codes?"
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-3 pt-0 text-sm", children: [
          "Copy the coupon code from our site, visit the store, add ",
          categoryName.toLowerCase(),
          " items to your cart, and paste the code at checkout to apply the discount."
        ] })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700", style: { fontSize: "17px" }, children: [
          "Are ",
          categoryName,
          " coupons verified?"
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-3 pt-0 text-sm", children: [
          "Yes, all our ",
          categoryName,
          " coupon codes are verified by our team and updated regularly to ensure they work. We currently have ",
          activeCoupons,
          " verified coupons for ",
          categoryName,
          "."
        ] })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700", style: { fontSize: "17px" }, children: [
          "What if my ",
          categoryName,
          " coupon doesn't work?"
        ] }),
        /* @__PURE__ */ jsx("div", { className: "p-3 pt-0 text-sm", children: "Check the expiration date and terms. If it still doesn't work, try another code from our list. We regularly update our coupons to ensure they're valid." })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700", style: { fontSize: "17px" }, children: [
          "How often are new ",
          categoryName,
          " coupons added?"
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-3 pt-0 text-sm", children: [
          "We update our ",
          categoryName,
          " coupon codes daily and add new ones as soon as they become available. Check back regularly for the latest deals."
        ] })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700", style: { fontSize: "17px" }, children: [
          "What's the best ",
          categoryName,
          " discount available?"
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-3 pt-0 text-sm", children: [
          "Currently, our best ",
          categoryName,
          " discount is ",
          bestDiscount,
          "% off. This can change as new deals become available, so check back often."
        ] })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700", style: { fontSize: "17px" }, children: [
          "Can I get email alerts for new ",
          categoryName,
          " coupons?"
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-3 pt-0 text-sm", children: [
          "Yes! Sign up for our ",
          categoryName,
          " alerts using the form on this page. We'll email you whenever new ",
          categoryName.toLowerCase(),
          " coupons become available."
        ] })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700", style: { fontSize: "17px" }, children: [
          "Where can I find the best ",
          categoryName,
          " products?"
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-3 pt-0 text-sm", children: [
          'We recommend checking out the merchants listed in our "Related Merchants" section below. They all offer quality ',
          categoryName.toLowerCase(),
          " products with verified coupon codes."
        ] })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700", style: { fontSize: "17px" }, children: [
          "Do ",
          categoryName,
          " coupons work on all products?"
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-3 pt-0 text-sm", children: [
          "Some coupons apply to all ",
          categoryName.toLowerCase(),
          " products, while others may be specific to certain brands or items. Check the coupon details for any restrictions."
        ] })
      ] })
    ] }) })
  ] });
};

const CategoryActivity = ({
  categoryName,
  activeCoupons,
  avgDiscount,
  successRate
}) => {
  const generateRecentVerifications = (name, count = 4) => {
    const baseVerifications = [
      { title: "10% Off Sitewide", time: "2 hours ago" },
      { title: "Free Shipping", time: "5 hours ago" },
      { title: "15% Off First Order", time: "yesterday" },
      { title: "Buy One Get One Free", time: "2 days ago" },
      { title: "20% Off Sale Items", time: "3 days ago" }
    ];
    const hash = name.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    const shuffled = [...baseVerifications].sort(() => hash % 3 - 1);
    return shuffled.slice(0, Math.min(count, baseVerifications.length));
  };
  const recentVerifications = generateRecentVerifications(categoryName);
  return /* @__PURE__ */ jsxs("div", { id: "activity", className: "mb-6", children: [
    /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: [
      categoryName,
      " Coupon Activity"
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4 mb-6", children: [
      /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4", children: [
        /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-2", children: "Last 24 Hours" }),
        /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-green-600", children: Math.floor(Math.random() * 50) + 20 }),
        /* @__PURE__ */ jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300", children: "Codes used successfully" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4", children: [
        /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-2", children: "This Week" }),
        /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-blue-600", children: Math.floor(Math.random() * 200) + 150 }),
        /* @__PURE__ */ jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300", children: "Total coupon clicks" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4", children: [
        /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-2", children: "Success Rate" }),
        /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-purple-600", children: successRate }),
        /* @__PURE__ */ jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300", children: "Average this month" })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4", children: [
      /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-3", children: "Recent Verifications" }),
      /* @__PURE__ */ jsx("div", { className: "space-y-2", children: recentVerifications.map((verification, index) => /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
        /* @__PURE__ */ jsx("span", { className: "text-gray-600 dark:text-gray-300", children: verification.title }),
        /* @__PURE__ */ jsxs("span", { className: "text-green-600 font-medium", children: [
          "✓ Verified ",
          verification.time
        ] })
      ] }, index)) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4", children: [
      /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-3", children: "Community Engagement" }),
      /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
          /* @__PURE__ */ jsxs("span", { className: "text-gray-600 dark:text-gray-300", children: [
            "Active users tracking ",
            categoryName
          ] }),
          /* @__PURE__ */ jsxs("span", { className: "font-medium text-gray-900 dark:text-white", children: [
            activeCoupons * 3,
            " members"
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
          /* @__PURE__ */ jsx("span", { className: "text-gray-600 dark:text-gray-300", children: "Average savings per user" }),
          /* @__PURE__ */ jsxs("span", { className: "font-medium text-gray-900 dark:text-white", children: [
            avgDiscount.toFixed(0),
            "% off"
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
          /* @__PURE__ */ jsx("span", { className: "text-gray-600 dark:text-gray-300", children: "Most popular code type" }),
          /* @__PURE__ */ jsx("span", { className: "font-medium text-gray-900 dark:text-white", children: "Percentage Off" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
          /* @__PURE__ */ jsx("span", { className: "text-gray-600 dark:text-gray-300", children: "Category popularity rank" }),
          /* @__PURE__ */ jsx("span", { className: "font-medium text-gray-900 dark:text-white", children: "Top 5" })
        ] })
      ] })
    ] })
  ] });
};

const CategoryRelatedSections = ({
  categoryName
}) => {
  return /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxs("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: [
        "Top Vape Stores for ",
        categoryName
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 sm:grid-cols-4 gap-4", children: [
        /* @__PURE__ */ jsxs("a", { href: "/coupons/merchants/eightvape", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-gray-600 dark:text-gray-300", children: "8V" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "EightVape Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/merchants/vapordna", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-gray-600 dark:text-gray-300", children: "VD" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "VaporDNA Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/merchants/vapesourcing", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-gray-600 dark:text-gray-300", children: "VS" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "VapeSourcing Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/merchants/directvapor", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-gray-600 dark:text-gray-300", children: "DV" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "DirectVapor Coupons" })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsx("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: "Explore Other Vape Categories" }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 sm:grid-cols-4 gap-4", children: [
        /* @__PURE__ */ jsxs("a", { href: "/coupons/categories/e-liquids", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg", children: "💧" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "E-Liquids" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/categories/vape-kits", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg", children: "📦" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Vape Kits" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/categories/coils", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg", children: "🔧" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Coils" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/categories/accessories", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg", children: "⚡" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Accessories" })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsx("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: "Popular Vape Brands" }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 sm:grid-cols-4 gap-4", children: [
        /* @__PURE__ */ jsxs("a", { href: "/coupons/brands/smok", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-red-600 dark:text-red-400", children: "SM" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "SMOK Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/brands/voopoo", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-blue-600 dark:text-blue-400", children: "VP" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "VooPoo Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/brands/geekvape", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-purple-600 dark:text-purple-400", children: "GV" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "GeekVape Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/brands/uwell", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-green-600 dark:text-green-400", children: "UW" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Uwell Coupons" })
        ] })
      ] })
    ] })
  ] });
};

const ExpandableCategoryInfo = ({
  categoryName,
  activeCoupons
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setTimeout(() => {
        const element = document.getElementById("expanded-category-content");
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start"
          });
        }
      }, 100);
    }
  };
  const generateVerificationTime = (name) => {
    const hash = name.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    const hours = [2, 4, 6, 8, 10, 12, 18, 24][Math.abs(hash) % 8];
    if (hours <= 12) {
      return `${hours} hours ago`;
    } else {
      return `${Math.floor(hours / 24)} day${Math.floor(hours / 24) > 1 ? "s" : ""} ago`;
    }
  };
  const verificationTime = generateVerificationTime(categoryName);
  return /* @__PURE__ */ jsxs("div", { className: "mb-2", children: [
    /* @__PURE__ */ jsxs("p", { className: "text-base text-gray-600 dark:text-gray-300", style: { lineHeight: "1.1em" }, children: [
      "Last verified ",
      verificationTime,
      " - ",
      activeCoupons,
      " active community members tracking ",
      categoryName,
      " coupons this past week.",
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: toggleExpanded,
          className: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 ml-1 transition-colors",
          children: isExpanded ? "Show less ↑" : "Show more ↓"
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      "div",
      {
        id: "expanded-category-content",
        className: `transition-all duration-300 ease-in-out overflow-hidden ${isExpanded ? "max-h-none opacity-100 mt-4" : "max-h-0 opacity-0"}`,
        children: /* @__PURE__ */ jsxs("div", { className: "space-y-6 text-sm text-gray-600 dark:text-gray-300", children: [
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Everything you need to know about ",
              categoryName,
              " coupons"
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "How to use ",
                  categoryName,
                  " coupon codes"
                ] }),
                /* @__PURE__ */ jsxs("ol", { className: "list-decimal list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Browse our verified ",
                    categoryName,
                    " coupons above and click ",
                    /* @__PURE__ */ jsx("strong", { children: '"Show Code"' }),
                    " on the one you want"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Copy the code that appears and click ",
                    /* @__PURE__ */ jsx("strong", { children: '"Visit Store"' }),
                    " to shop"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Add your favorite ",
                    categoryName.toLowerCase(),
                    " products to your cart"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Look for the ",
                    /* @__PURE__ */ jsx("strong", { children: "coupon box" }),
                    " at checkout and paste your code"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Hit ",
                    /* @__PURE__ */ jsx("strong", { children: '"Apply"' }),
                    " and watch your total drop - ",
                    /* @__PURE__ */ jsx("em", { children: "that's money back in your pocket!" })
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Code not working? Here's what to check" }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Double-check you typed the code ",
                    /* @__PURE__ */ jsx("strong", { children: "exactly right" }),
                    " (no extra spaces!)"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Make sure your cart hits the ",
                    /* @__PURE__ */ jsx("strong", { children: "minimum spend" }),
                    " if there's one required"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Check the ",
                    /* @__PURE__ */ jsx("strong", { children: "expiry date" }),
                    " - some codes have time limits"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Some codes only work on specific ",
                    categoryName.toLowerCase(),
                    " products"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Still stuck? Try another ",
                    /* @__PURE__ */ jsx("em", { children: "verified code" }),
                    " from our list above"
                  ] })
                ] })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Smart ways to save on ",
              categoryName
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Pro tips from fellow vapers" }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Sign up for store newsletters - they often send exclusive ",
                    categoryName.toLowerCase(),
                    " deals"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Follow vape shops on social media for flash sales on ",
                    categoryName.toLowerCase(),
                    " products"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Check clearance sections first - you might find ",
                    categoryName.toLowerCase(),
                    " items on sale"
                  ] }),
                  /* @__PURE__ */ jsx("li", { children: "Create accounts at your favorite vape shops to get member-only deals" }),
                  /* @__PURE__ */ jsx("li", { children: "Buy in bulk when possible - many stores offer quantity discounts" })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "When to find the best ",
                  categoryName,
                  " deals"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  "The vaping industry typically offers the biggest ",
                  categoryName.toLowerCase(),
                  " discounts during Black Friday, Cyber Monday, and major holidays. But many stores also run surprise sales throughout the year, so it pays to check back regularly or follow our updates."
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "Never miss a ",
                  categoryName,
                  " deal again"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  "Want to stay in the loop? Bookmark this page and check back weekly. We update our ",
                  categoryName,
                  " coupon codes as soon as we find new ones. You can also sign up for our email alerts to get notified when fresh ",
                  categoryName.toLowerCase(),
                  " discounts drop."
                ] })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Why vapers love our ",
              categoryName,
              " coupons"
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "What makes ",
                  categoryName,
                  " products special"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  categoryName,
                  " products are essential for many vapers, offering unique benefits and experiences. Whether you're a beginner or experienced vaper, finding quality ",
                  categoryName.toLowerCase(),
                  " at a discount helps you enjoy your vaping experience without breaking the bank."
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "Why VapeHybrid has the best ",
                  categoryName,
                  " deals"
                ] }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Real vaper testing:" }),
                    " Our team actually tries these codes before posting them"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Live success rates:" }),
                    " See which codes are working right now for other vapers"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "No fake codes:" }),
                    " We remove expired or broken codes immediately"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Vaper community:" }),
                    " Real feedback from actual customers who buy ",
                    categoryName.toLowerCase()
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Category experts:" }),
                    " Our team specializes in finding the best ",
                    categoryName.toLowerCase(),
                    " deals"
                  ] })
                ] })
              ] })
            ] })
          ] })
        ] })
      }
    )
  ] });
};

const CategoryAbout = ({ categoryName, className = "" }) => {
  const getCategoryInfo = (category) => {
    const normalizedCategory = category.toLowerCase();
    if (normalizedCategory.includes("e-liquid") || normalizedCategory.includes("juice")) {
      return {
        description: `E-liquids are the heart of the vaping experience, offering an incredible variety of flavors, nicotine strengths, and VG/PG ratios. From premium artisanal blends to budget-friendly options, the e-liquid category provides something for every vaper's taste preferences and device requirements.`,
        specialties: [
          "Premium flavor profiles from top brands",
          "Various nicotine strengths (0mg to 50mg)",
          "Different VG/PG ratios for optimal performance",
          "Salt nicotine and freebase options",
          "Bulk purchasing options for savings"
        ],
        trustSignals: [
          "Lab-tested for quality and safety",
          "Child-resistant packaging",
          "Authentic brand products only",
          "Fresh stock with proper storage",
          "Detailed ingredient information"
        ],
        popularProducts: [
          "Fruit and dessert flavor profiles",
          "Menthol and mint varieties",
          "Tobacco and classic flavors",
          "Nicotine salt formulations",
          "High VG cloud-chasing blends"
        ],
        shoppingTips: [
          "Start with sample packs to find your favorites",
          "Check VG/PG ratios for your device compatibility",
          "Buy in bulk during sales for better pricing",
          "Store e-liquids in cool, dark places",
          "Always verify authenticity codes"
        ]
      };
    }
    if (normalizedCategory.includes("disposable") || normalizedCategory.includes("pod")) {
      return {
        description: `Disposable vapes and pod systems represent the most convenient and user-friendly segment of vaping. Perfect for beginners and experienced vapers alike, these devices offer hassle-free vaping with no maintenance required, featuring pre-filled e-liquid and long-lasting batteries.`,
        specialties: [
          "Ready-to-use convenience",
          "High puff count devices (5000+ puffs)",
          "Consistent flavor delivery",
          "Compact and portable designs",
          "Wide variety of flavors and nicotine strengths"
        ],
        trustSignals: [
          "Authentic brand verification",
          "Quality control testing",
          "Leak-proof construction",
          "Consistent performance",
          "Proper age verification required"
        ],
        popularProducts: [
          "High-capacity disposables (5000-10000 puffs)",
          "Rechargeable disposable systems",
          "Prefilled pod cartridges",
          "Compact pen-style devices",
          "Mesh coil technology devices"
        ],
        shoppingTips: [
          "Check puff count for value comparison",
          "Verify authenticity with QR codes",
          "Buy from authorized retailers only",
          "Consider rechargeable options for longer use",
          "Read reviews for flavor accuracy"
        ]
      };
    }
    if (normalizedCategory.includes("mod") || normalizedCategory.includes("kit") || normalizedCategory.includes("device")) {
      return {
        description: `Vape mods and kits offer the ultimate in customization and performance for serious vapers. From beginner-friendly starter kits to advanced box mods with temperature control, this category provides the hardware foundation for an exceptional vaping experience.`,
        specialties: [
          "Advanced temperature control features",
          "High-wattage output capabilities",
          "Customizable settings and modes",
          "Durable construction materials",
          "Compatible with various tank systems"
        ],
        trustSignals: [
          "Authentic manufacturer products",
          "Safety certifications and protections",
          "Warranty coverage included",
          "Quality construction materials",
          "Comprehensive user manuals"
        ],
        popularProducts: [
          "Variable wattage box mods",
          "All-in-one starter kits",
          "Temperature control devices",
          "Mechanical mods for enthusiasts",
          "Compact pod mod systems"
        ],
        shoppingTips: [
          "Match wattage requirements to your tanks",
          "Consider battery life for your usage",
          "Check for firmware update capabilities",
          "Verify compatibility with your preferred tanks",
          "Read safety features and protections"
        ]
      };
    }
    return {
      description: `${category} products represent an essential part of the vaping ecosystem, offering quality options for vapers of all experience levels. This category features carefully curated products from trusted brands, ensuring authenticity, performance, and value for money.`,
      specialties: [
        "Wide selection of quality products",
        "Authentic brand merchandise only",
        "Competitive pricing and deals",
        "Regular stock updates",
        "Expert product curation"
      ],
      trustSignals: [
        "Authorized retailer network",
        "Quality assurance testing",
        "Authentic product verification",
        "Customer satisfaction guarantee",
        "Secure shopping experience"
      ],
      popularProducts: [
        "Top-rated brand products",
        "Customer favorite items",
        "Latest product releases",
        "Value-priced options",
        "Premium quality selections"
      ],
      shoppingTips: [
        "Compare prices across multiple retailers",
        "Read customer reviews before purchasing",
        "Check for current coupon codes",
        "Verify product authenticity",
        "Consider bulk purchases for savings"
      ]
    };
  };
  const categoryInfo = getCategoryInfo(categoryName);
  return /* @__PURE__ */ jsxs("div", { id: "about", className: `mb-6 ${className}`, children: [
    /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: [
      "About ",
      categoryName
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsx("summary", { className: "cursor-pointer p-4 font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors", children: "Category Overview" }),
        /* @__PURE__ */ jsx("div", { className: "p-4 pt-0 text-sm text-gray-600 dark:text-gray-300", children: /* @__PURE__ */ jsx("p", { className: "mb-3", children: categoryInfo.description }) })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-4 font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors", children: [
          "What Makes ",
          categoryName,
          " Special"
        ] }),
        /* @__PURE__ */ jsx("div", { className: "p-4 pt-0 text-sm text-gray-600 dark:text-gray-300", children: /* @__PURE__ */ jsx("ul", { className: "space-y-2 list-disc list-inside", children: categoryInfo.specialties.map((specialty, index) => /* @__PURE__ */ jsx("li", { children: specialty }, index)) }) })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-4 font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors", children: [
          "Popular ",
          categoryName,
          " Products"
        ] }),
        /* @__PURE__ */ jsx("div", { className: "p-4 pt-0 text-sm text-gray-600 dark:text-gray-300", children: /* @__PURE__ */ jsx("ul", { className: "space-y-2 list-disc list-inside", children: categoryInfo.popularProducts.map((product, index) => /* @__PURE__ */ jsx("li", { children: product }, index)) }) })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-4 font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors", children: [
          "Shopping Tips for ",
          categoryName
        ] }),
        /* @__PURE__ */ jsx("div", { className: "p-4 pt-0 text-sm text-gray-600 dark:text-gray-300", children: /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Smart Shopping" }),
            /* @__PURE__ */ jsx("ul", { className: "list-disc list-inside space-y-1 text-sm", children: categoryInfo.shoppingTips.map((tip, index) => /* @__PURE__ */ jsx("li", { children: tip }, index)) })
          ] }),
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Quality Assurance" }),
            /* @__PURE__ */ jsx("ul", { className: "list-disc list-inside space-y-1 text-sm", children: categoryInfo.trustSignals.map((signal, index) => /* @__PURE__ */ jsx("li", { children: signal }, index)) })
          ] })
        ] }) })
      ] })
    ] })
  ] });
};

const $$Astro = createAstro("http://localhost:4321");
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/coupons/categories", 302);
  }
  const supabase = createServerSupabaseClient();
  const url = new URL(Astro2.request.url);
  const view = url.searchParams.get("view") || "grid";
  const sort = url.searchParams.get("sort") || "newest";
  const page = parseInt(url.searchParams.get("page") || "1");
  const limit = parseInt(url.searchParams.get("limit") || "24");
  const merchantIds = url.searchParams.get("merchants")?.split(",").map((id) => parseInt(id)).filter((id) => !isNaN(id)) || [];
  const brandIds = url.searchParams.get("brands")?.split(",").map((id) => parseInt(id)).filter((id) => !isNaN(id)) || [];
  const minPrice = parseFloat(url.searchParams.get("min_price") || "0");
  const maxPrice = parseFloat(url.searchParams.get("max_price") || "200");
  const minDiscount = parseFloat(url.searchParams.get("min_discount") || "0");
  const maxDiscount = parseFloat(url.searchParams.get("max_discount") || "100");
  const expiringSoon = url.searchParams.get("expiring_soon") === "true";
  const validLongTerm = url.searchParams.get("valid_long_term") === "true";
  const { data: category, error: categoryError } = await supabase.from("categories").select("*").eq("slug", slug).single();
  if (categoryError || !category) {
    console.error("Category not found:", categoryError);
    return Astro2.redirect("/coupons/categories", 302);
  }
  let dealsQuery = supabase.from("deals").select(`
    *,
    merchants:merchant_id (name, website_url, logo_url),
    brands:brand_id (name, logo_url, slug),
    categories:category_id (name, slug)
  `).eq("category_id", category.id).gt("deal_end_date", (/* @__PURE__ */ new Date()).toISOString());
  if (merchantIds.length > 0) {
    dealsQuery = dealsQuery.in("merchant_id", merchantIds);
  }
  if (brandIds.length > 0) {
    dealsQuery = dealsQuery.in("brand_id", brandIds);
  }
  if (minPrice > 0 || maxPrice < 200) {
    dealsQuery = dealsQuery.gte("price", minPrice).lte("price", maxPrice);
  }
  if (minDiscount > 0 || maxDiscount < 100) {
    dealsQuery = dealsQuery.gte("discount_percentage", minDiscount).lte("discount_percentage", maxDiscount);
  }
  if (expiringSoon) {
    const threeDaysFromNow = /* @__PURE__ */ new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    dealsQuery = dealsQuery.lte("deal_end_date", threeDaysFromNow.toISOString());
  }
  if (validLongTerm) {
    const thirtyDaysFromNow = /* @__PURE__ */ new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    dealsQuery = dealsQuery.gte("deal_end_date", thirtyDaysFromNow.toISOString());
  }
  switch (sort) {
    case "newest":
      dealsQuery = dealsQuery.order("created_at", { ascending: false });
      break;
    case "expiring_soon":
      dealsQuery = dealsQuery.order("deal_end_date", { ascending: true });
      break;
    case "discount_desc":
      dealsQuery = dealsQuery.order("discount_percentage", { ascending: false, nullsLast: true });
      break;
    case "most_popular":
      dealsQuery = dealsQuery.order("click_count", { ascending: false, nullsLast: true });
      break;
    default:
      dealsQuery = dealsQuery.order("created_at", { ascending: false });
  }
  const offset = (page - 1) * limit;
  dealsQuery = dealsQuery.range(offset, offset + limit - 1);
  const { data: deals, error: dealsError } = await dealsQuery;
  let countQuery = supabase.from("deals").select("*", { count: "exact", head: true }).eq("category_id", category.id).gt("deal_end_date", (/* @__PURE__ */ new Date()).toISOString());
  if (merchantIds.length > 0) {
    countQuery = countQuery.in("merchant_id", merchantIds);
  }
  if (brandIds.length > 0) {
    countQuery = countQuery.in("brand_id", brandIds);
  }
  if (minPrice > 0 || maxPrice < 200) {
    countQuery = countQuery.gte("price", minPrice).lte("price", maxPrice);
  }
  if (minDiscount > 0 || maxDiscount < 100) {
    countQuery = countQuery.gte("discount_percentage", minDiscount).lte("discount_percentage", maxDiscount);
  }
  if (expiringSoon) {
    const threeDaysFromNow = /* @__PURE__ */ new Date();
    threeDaysFromNow.setDate(threeDaysFromNow.getDate() + 3);
    countQuery = countQuery.lte("deal_end_date", threeDaysFromNow.toISOString());
  }
  if (validLongTerm) {
    const thirtyDaysFromNow = /* @__PURE__ */ new Date();
    thirtyDaysFromNow.setDate(thirtyDaysFromNow.getDate() + 30);
    countQuery = countQuery.gte("deal_end_date", thirtyDaysFromNow.toISOString());
  }
  const { count: totalCount } = await countQuery;
  const totalPages = Math.ceil((totalCount || 0) / limit);
  const { data: allCategoryDeals } = await supabase.from("deals").select("*").eq("category_id", category.id).gt("deal_end_date", (/* @__PURE__ */ new Date()).toISOString());
  const couponStats = calculateCouponStats(allCategoryDeals || [], category?.name || "");
  generateRecentVerifications(category?.name || "", allCategoryDeals, 3);
  const seoMeta = category ? generateCategorySEOMeta(category.name, couponStats) : null;
  category ? generateSocialProofText(category.name, "category", couponStats) : null;
  const title = seoMeta?.title || `${category.name} Coupon Codes & Discount Deals - Save Up to ${couponStats.bestDiscount} | VapeHybrid`;
  const description = seoMeta?.description || `Get the latest ${category.name.toLowerCase()} coupon codes and discount deals. Save on premium ${category.name.toLowerCase()} with ${couponStats.activeCoupons} verified promo codes. Updated daily.`;
  const structuredData = category ? generateCategoryPageSchema(
    category,
    deals || [],
    Astro2.url.origin,
    couponStats
  ) : void 0;
  const { data: allMerchants } = await supabase.from("merchants").select("id, name").eq("status", "active").order("name");
  const { data: allBrands } = await supabase.from("brands").select("id, name").order("name");
  const activeFilterCount = merchantIds.length + brandIds.length + (minPrice > 0 || maxPrice < 200 ? 1 : 0) + (minDiscount > 0 || maxDiscount < 100 ? 1 : 0) + (expiringSoon ? 1 : 0) + (validLongTerm ? 1 : 0);
  const metaTags = seoMeta ? generateMetaTagsObject(seoMeta, Astro2.url.href, category?.category_logo) : {};
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData, "openGraph": {
    basic: {
      title: metaTags["og:title"] || title,
      type: "website",
      image: metaTags["og:image"] || category?.category_logo,
      url: metaTags["og:url"] || Astro2.url.href
    },
    optional: {
      description: metaTags["og:description"] || description,
      siteName: "VapeHybrid"
    },
    image: {
      alt: metaTags["og:image:alt"] || `${category?.name} Coupons`,
      width: 1200,
      height: 630
    }
  }, "twitter": {
    card: "summary_large_image",
    site: "@vapehybrid",
    creator: "@vapehybrid",
    title: metaTags["twitter:title"] || title,
    description: metaTags["twitter:description"] || description,
    image: metaTags["twitter:image"] || category?.category_logo
  } }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="bg-[#fbfafc] dark:bg-gray-900 min-h-screen"> <div class="max-w-[1280px] mx-auto px-4 sm:px-6 lg:px-8 py-8"> <!-- Breadcrumbs --> <nav class="mb-6" aria-label="Breadcrumb"> <ol class="flex items-center space-x-2 text-sm text-gray-600 dark:text-gray-300"> <li><a href="/" class="hover:text-gray-900 dark:hover:text-white transition-colors">Home</a></li> <li><span class="mx-2 text-gray-400">/</span></li> <li><a href="/coupons" class="hover:text-gray-900 dark:hover:text-white transition-colors">Coupons</a></li> <li><span class="mx-2 text-gray-400">/</span></li> <li><a href="/coupons/categories" class="hover:text-gray-900 dark:hover:text-white transition-colors">Categories</a></li> <li><span class="mx-2 text-gray-400">/</span></li> <li class="text-gray-900 dark:text-white font-medium">${category.name}</li> </ol> </nav> <!-- Seamless Layout: 1/4 Left + 3/4 Right --> <div class="grid grid-cols-1 lg:grid-cols-4 gap-8"> <!-- Left Sidebar (1/4 width) --> <div class="lg:col-span-1"> <!-- Category Logo & Info Box --> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-6 mb-6"> <!-- Category Logo --> <div class="flex justify-center mb-4"> <div class="w-20 h-20 rounded-lg bg-gradient-to-br from-blue-50 to-blue-100 dark:from-blue-900 dark:to-blue-800 flex items-center justify-center overflow-hidden"> ${category.category_logo ? renderTemplate`${renderComponent($$result2, "WebPImage", $$WebPImage, { "src": category.category_logo, "alt": `${category.name} category icon`, "className": "w-14 h-14 object-contain", "fallbackSrc": "/icons/002-vape.png" })}` : renderTemplate`<div class="w-14 h-14 bg-blue-200 dark:bg-blue-700 rounded-lg flex items-center justify-center"> <span class="text-blue-600 dark:text-blue-300 font-bold text-xl">${category.name.charAt(0)}</span> </div>`} </div> </div> <!-- Category Name & Title --> <h1 class="text-xl font-bold text-gray-900 dark:text-white text-center mb-2"> ${category.name} </h1> <p class="text-sm text-gray-600 dark:text-gray-300 text-center mb-4">
Coupon Codes & Deals
</p> <!-- Quick Stats --> <div class="space-y-3"> <div class="flex justify-between items-center"> <span class="text-sm text-gray-600 dark:text-gray-300">Last ✓ Verified :</span> <span class="font-semibold text-gray-900 dark:text-white">8 hours ago</span> </div> <div class="flex justify-between items-center"> <span class="text-sm text-gray-600 dark:text-gray-300">Last user 💰 Recent Saved:</span> <span class="font-semibold text-green-600">$23.50</span> </div> <div class="flex justify-between items-center"> <span class="text-sm text-gray-600 dark:text-gray-300">Active Coupons:</span> <span class="font-semibold text-blue-600">${couponStats.activeCoupons}</span> </div> <div class="flex justify-between items-center"> <span class="text-sm text-gray-600 dark:text-gray-300">Best Discount:</span> <span class="font-semibold text-green-600">${couponStats.bestDiscount}</span> </div> </div> </div> <!-- Sticky Email Capture --> <div class="sticky top-4"> ${renderComponent($$result2, "UniversalEmailCaptureForm", UniversalEmailCaptureForm, { "client:load": true, "targetName": category.name, "subscriptionType": "category", "className": "mb-4", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/shared/UniversalEmailCaptureForm", "client:component-export": "default" })} </div> </div> <!-- Right Content Area (3/4 width) --> <div class="lg:col-span-3"> <!-- Main Title & Description --> <div class="mb-6"> <h2 class="text-2xl md:text-3xl font-bold text-gray-900 dark:text-white mb-3"> ${category.name} Coupon Codes & Discount Deals
</h2> <p class="text-lg text-gray-600 dark:text-gray-300 mb-4">
Get the latest ${category.name.toLowerCase()} coupon codes and discount deals. Save on premium ${category.name.toLowerCase()} products with verified promo codes updated daily.
</p> </div> <!-- Enhanced Statistics Dashboard --> <div class="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6"> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center"> <div class="text-2xl font-bold text-blue-600 mb-1">${couponStats.activeCoupons}</div> <div class="text-xs text-gray-600 dark:text-gray-300">Active Coupons</div> </div> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center"> <div class="text-2xl font-bold text-green-600 mb-1">${couponStats.bestDiscount}</div> <div class="text-xs text-gray-600 dark:text-gray-300">Best Discount</div> </div> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center"> <div class="text-2xl font-bold text-purple-600 mb-1">${couponStats.avgSavings}</div> <div class="text-xs text-gray-600 dark:text-gray-300">Avg. Savings</div> </div> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 text-center"> <div class="text-2xl font-bold text-orange-600 mb-1">${couponStats.successRate}</div> <div class="text-xs text-gray-600 dark:text-gray-300">Success Rate</div> </div> </div> <!-- Verification Info with Show More --> <div class="mb-6"> ${renderComponent($$result2, "ExpandableCategoryInfo", ExpandableCategoryInfo, { "client:load": true, "categoryName": category.name, "activeCoupons": couponStats.activeCoupons, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/ExpandableCategoryInfo", "client:component-export": "default" })} </div> <!-- Navigation Tabs --> <div class="flex gap-6 mb-6 border-b border-gray-200 dark:border-gray-600"> <a href="#activity" class="pb-2 border-b-2 border-green-500 text-green-600 font-medium hover:text-green-700">Activity</a> <a href="#about" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">About</a> <a href="#faq" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">FAQ</a> <a href="#related" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white">Related</a> </div> <!-- Controls Section --> <div class="mb-8"> <!-- Desktop Controls --> <div class="hidden md:flex items-center justify-between gap-4 p-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg"> <div class="flex items-center gap-4"> <h2 class="text-lg font-semibold text-gray-900 dark:text-white"> ${totalCount || 0} ${category.name} Coupons Available
</h2> </div> <div class="flex items-center gap-3"> ${renderComponent($$result2, "FilterDrawer", FilterDrawer, { "categories": [], "merchants": allMerchants || [], "brands": allBrands || [], "currentUrl": Astro2.url.href, "activeFilterCount": activeFilterCount, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/FilterDrawer", "client:component-export": "default" })} ${renderComponent($$result2, "SortSelect", SortSelect, { "value": sort, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/SortSelect", "client:component-export": "default" })} ${renderComponent($$result2, "ViewToggle", ViewToggle, { "value": view, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/ViewToggle", "client:component-export": "default" })} </div> </div> <!-- Mobile Controls --> <div class="md:hidden space-y-3"> <h2 class="text-lg font-semibold text-gray-900 dark:text-white text-center"> ${totalCount || 0} ${category.name} Coupons Available
</h2> <div class="flex items-center justify-center gap-3"> ${renderComponent($$result2, "FilterDrawer", FilterDrawer, { "categories": [], "merchants": allMerchants || [], "brands": allBrands || [], "currentUrl": Astro2.url.href, "activeFilterCount": activeFilterCount, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/FilterDrawer", "client:component-export": "default" })} ${renderComponent($$result2, "SortSelect", SortSelect, { "value": sort, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/SortSelect", "client:component-export": "default" })} ${renderComponent($$result2, "ViewToggle", ViewToggle, { "value": view, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/ViewToggle", "client:component-export": "default" })} </div> </div> </div> <!-- Error Messages --> ${categoryError && renderTemplate`<div class="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 p-4 rounded-md mb-6"> <p>There was an error loading the category information. Please try again later.</p> </div>`} ${dealsError && renderTemplate`<div class="bg-red-50 dark:bg-red-900/20 text-red-800 dark:text-red-200 p-4 rounded-md mb-6"> <p>There was an error loading coupons. Please try again later.</p> </div>`} <!-- Coupons Grid/List --> ${deals && deals.length > 0 ? renderTemplate`<div> ${renderComponent($$result2, "OptimizedDealsPageWrapper", OptimizedDealsPageWrapper, { "deals": deals, "initialViewMode": view, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/OptimizedDealsPageWrapper", "client:component-export": "default" })}  <div class="mt-12 mb-8 flex flex-col md:flex-row justify-between items-center gap-6 bg-white/50 dark:bg-gray-800/50 backdrop-blur-sm border border-gray-200/10 dark:border-gray-600/10 rounded-xl p-4 shadow-sm"> ${renderComponent($$result2, "PerPageSelect", $$PerPageSelect, { "value": limit, "options": [12, 24, 48, 96], "url": url })} ${renderComponent($$result2, "Pagination", $$Pagination, { "currentPage": page, "totalPages": totalPages, "baseUrl": `/coupons/categories/${slug}`, "searchParams": url.searchParams })} </div> </div>` : renderTemplate`<div class="text-center py-12 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg"> <h3 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">No Coupons Found</h3> <p class="text-gray-600 dark:text-gray-300 mb-6">
We couldn't find any active coupons for ${category.name.toLowerCase()} at the moment. Check back soon for new deals!
</p> <div class="flex flex-col sm:flex-row gap-4 justify-center"> ${renderComponent($$result2, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/coupons", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result3) => renderTemplate`
Browse All Coupons
` })} <a href="/coupons/categories" class="inline-flex items-center px-6 py-3 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors">
Browse Other Categories
</a> </div> </div>`} <!-- Enhanced Category Content Sections --> <div class="mt-8 space-y-8"> <!-- Enhanced Activity Section --> ${renderComponent($$result2, "CategoryActivity", CategoryActivity, { "client:load": true, "categoryName": category.name, "activeCoupons": couponStats.activeCoupons, "avgDiscount": couponStats.bestDiscount ? parseFloat(couponStats.bestDiscount.replace("%", "")) : 15, "successRate": couponStats.successRate, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/CategoryActivity", "client:component-export": "default" })} <!-- About Category Section --> <div class="mb-6"> ${renderComponent($$result2, "CategoryAbout", CategoryAbout, { "client:load": true, "categoryName": category.name, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/CategoryAbout", "client:component-export": "default" })} </div> <!-- FAQ Section --> <div id="faq" class="mb-6"> ${renderComponent($$result2, "CategoryFAQ", CategoryFAQ, { "client:load": true, "categoryName": category.name, "activeCoupons": couponStats.activeCoupons, "bestDiscount": couponStats.bestDiscount ? parseFloat(couponStats.bestDiscount.replace("%", "")) : 15, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/CategoryFAQ", "client:component-export": "default" })} </div> <!-- Enhanced Related Sections --> <div id="related"> ${renderComponent($$result2, "CategoryRelatedSections", CategoryRelatedSections, { "client:load": true, "categoryName": category.name, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/CategoryRelatedSections", "client:component-export": "default" })} </div> <!-- Category Description (if available) --> ${category.description && renderTemplate`<div class="p-6 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg"> <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3">About ${category.name}</h3> <p class="text-gray-600 dark:text-gray-300">${category.description}</p> </div>`} <!-- Quick Navigation --> <div class="mt-8"> <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-6">More Category Coupons</h3> <div class="flex flex-wrap gap-3"> <a href="/coupons/categories" class="inline-flex items-center px-4 py-2 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors text-sm">
View All Categories
</a> <a href="/coupons" class="inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors text-sm">
Browse All Coupons
</a> </div> </div> </div> </div> <!-- Close right content area --> </div> <!-- Close seamless layout grid --> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/categories/[slug].astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/categories/[slug].astro";
const $$url = "/coupons/categories/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
