import{r as o,j as e}from"./vendor/react-bq1VYqFi.js";import{k as r}from"./app/design-system-lJ5ci6hi.js";import"./vendor/ui-components-_FzKGR3X.js";import"./vendor/utils-hwhCH1Yb.js";const t=()=>{const[t,s]=o.useState(!1);o.useEffect((()=>{const o=()=>{window.scrollY>500?s(!0):s(!1)};return window.addEventListener("scroll",o),()=>window.removeEventListener("scroll",o)}),[]);return e.jsx(e.Fragment,{children:t&&e.jsx("button",{onClick:()=>{window.scrollTo({top:0,behavior:"smooth"})},"aria-label":"Back to top",className:"fixed bottom-4 right-4 p-2 rounded-full bg-design-primary text-white shadow-md hover:bg-design-primary/90 hover:scale-105 hover:shadow-lg transition-all duration-300 z-50 animate-fade-in backdrop-blur-sm",children:e.jsx(r,{size:18})})})};export{t as BackToTop};
