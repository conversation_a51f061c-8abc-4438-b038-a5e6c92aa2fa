import{R as e,r as t,j as s}from"../vendor/react-bq1VYqFi.js";import{t as a,O as r,P as n,C as i,a as o,T as l,D as d,R as c,b as u,c as m,d as g,e as h,f as p,g as x,h as f,i as b,I as y,j as w,k as v,V as j,L as k,l as N,m as _,n as C,S,o as E,G as L,p as $,q as R,r as M,s as D,u as O,v as P}from"../vendor/ui-components-_FzKGR3X.js";import{t as z,c as I,a as T}from"../vendor/utils-hwhCH1Yb.js";function V(...e){return z(I(e))}const U=({children:a,onClick:r,className:n="",variant:i="primary",asLink:o=!1,href:l="#",isFullWidth:d=!1,forceSpan:c=!1,...u})=>{const[,m]=e.useTransition(),[g,h]=t.useState(!1);t.useEffect((()=>{const e=setTimeout((()=>{m((()=>{h(!0)}))}),10);return()=>clearTimeout(e)}),[]);const p=V("relative text-lg px-6 py-2 rounded-full transition-all duration-300 inline-flex items-center justify-center gap-2",d?"w-full":"",n),x=V("relative z-10 text-white dark:text-black flex items-center justify-center gap-2"),f=V("absolute inset-0 rounded-full bg-gradient-to-tr from-[#4a4cd9] to-[#262cbd] dark:from-[#1df292] dark:to-[#0db875]",g&&"shadow-glow dark:shadow-glow-dark"),b=V("absolute inset-[1px] rounded-full bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] dark:from-[#1df292] dark:to-[#0db875]"),y=s.jsxs(s.Fragment,{children:[s.jsx("span",{className:f}),s.jsx("span",{className:b}),s.jsx("span",{className:x,children:a})]});if("undefined"==typeof window||!g)return s.jsx("span",{className:`${p} group`,...u,children:y});if(c)return s.jsx("span",{className:`${p} group`,...u,children:y});if(o){const{forceSpan:e,...t}=u;return s.jsx("a",{href:l,className:`${p} group`,...t,children:y})}return s.jsx("div",{onClick:r,className:`${p} group`,role:"button",tabIndex:0,...u,children:y})},W=e=>{const t=(e=>e.replace(/^([A-Z])|[\s-_]+(\w)/g,((e,t,s)=>s?s.toUpperCase():t.toLowerCase())))(e);return t.charAt(0).toUpperCase()+t.slice(1)},B=(...e)=>e.filter(((e,t,s)=>Boolean(e)&&""!==e.trim()&&s.indexOf(e)===t)).join(" ").trim();
/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */
var F={xmlns:"http://www.w3.org/2000/svg",width:24,height:24,viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:2,strokeLinecap:"round",strokeLinejoin:"round"};
/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */const A=t.forwardRef((({color:e="currentColor",size:s=24,strokeWidth:a=2,absoluteStrokeWidth:r,className:n="",children:i,iconNode:o,...l},d)=>t.createElement("svg",{ref:d,...F,width:s,height:s,stroke:e,strokeWidth:r?24*Number(a)/Number(s):a,className:B("lucide",n),...l},[...o.map((([e,s])=>t.createElement(e,s))),...Array.isArray(i)?i:[i]]))),J=(e,s)=>{const a=t.forwardRef((({className:a,...r},n)=>{return t.createElement(A,{ref:n,iconNode:s,className:B(`lucide-${i=W(e),i.replace(/([a-z0-9])([A-Z])/g,"$1-$2").toLowerCase()}`,`lucide-${e}`,a),...r});var i}));return a.displayName=W(e),a},H=J("arrow-left",[["path",{d:"m12 19-7-7 7-7",key:"1l729n"}],["path",{d:"M19 12H5",key:"x3x0zl"}]]),q=J("arrow-right",[["path",{d:"M5 12h14",key:"1ays0h"}],["path",{d:"m12 5 7 7-7 7",key:"xquz4c"}]]),G=J("check",[["path",{d:"M20 6 9 17l-5-5",key:"1gmf2c"}]]),K=J("chevron-down",[["path",{d:"m6 9 6 6 6-6",key:"qrunsl"}]]),Q=J("chevron-right",[["path",{d:"m9 18 6-6-6-6",key:"mthhwq"}]]),Y=J("chevron-up",[["path",{d:"m18 15-6-6-6 6",key:"153udz"}]]),Z=J("circle-x",[["circle",{cx:"12",cy:"12",r:"10",key:"1mglay"}],["path",{d:"m15 9-6 6",key:"1uzhvr"}],["path",{d:"m9 9 6 6",key:"z0biqf"}]]),X=J("copy",[["rect",{width:"14",height:"14",x:"8",y:"8",rx:"2",ry:"2",key:"17jyea"}],["path",{d:"M4 16c-1.1 0-2-.9-2-2V4c0-1.1.9-2 2-2h10c1.1 0 2 .9 2 2",key:"zix9uf"}]]),ee=J("external-link",[["path",{d:"M15 3h6v6",key:"1q9fwt"}],["path",{d:"M10 14 21 3",key:"gplh6r"}],["path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6",key:"a6xqqp"}]]),te=J("eye",[["path",{d:"M2.062 12.348a1 1 0 0 1 0-.696 10.75 10.75 0 0 1 19.876 0 1 1 0 0 1 0 .696 10.75 10.75 0 0 1-19.876 0",key:"1nclc0"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]]),se=J("funnel",[["path",{d:"M10 20a1 1 0 0 0 .553.895l2 1A1 1 0 0 0 14 21v-7a2 2 0 0 1 .517-1.341L21.74 4.67A1 1 0 0 0 21 3H3a1 1 0 0 0-.742 1.67l7.225 7.989A2 2 0 0 1 10 14z",key:"sc7q7i"}]]),ae=J("grid-3x3",[["rect",{width:"18",height:"18",x:"3",y:"3",rx:"2",key:"afitv7"}],["path",{d:"M3 9h18",key:"1pudct"}],["path",{d:"M3 15h18",key:"5xshup"}],["path",{d:"M9 3v18",key:"fh3hqa"}],["path",{d:"M15 3v18",key:"14nvp0"}]]),re=J("heart",[["path",{d:"M19 14c1.49-1.46 3-3.21 3-5.5A5.5 5.5 0 0 0 16.5 3c-1.76 0-3 .5-4.5 2-1.5-1.5-2.74-2-4.5-2A5.5 5.5 0 0 0 2 8.5c0 2.3 1.5 4.05 3 5.5l7 7Z",key:"c3ymky"}]]),ne=J("list",[["path",{d:"M3 12h.01",key:"nlz23k"}],["path",{d:"M3 18h.01",key:"1tta3j"}],["path",{d:"M3 6h.01",key:"1rqtza"}],["path",{d:"M8 12h13",key:"1za7za"}],["path",{d:"M8 18h13",key:"1lx6n3"}],["path",{d:"M8 6h13",key:"ik3vkj"}]]),ie=J("loader-circle",[["path",{d:"M21 12a9 9 0 1 1-6.219-8.56",key:"13zald"}]]),oe=J("mail",[["rect",{width:"20",height:"16",x:"2",y:"4",rx:"2",key:"18n3k1"}],["path",{d:"m22 7-8.97 5.7a1.94 1.94 0 0 1-2.06 0L2 7",key:"1ocrg3"}]]),le=J("menu",[["line",{x1:"4",x2:"20",y1:"12",y2:"12",key:"1e0a9i"}],["line",{x1:"4",x2:"20",y1:"6",y2:"6",key:"1owob3"}],["line",{x1:"4",x2:"20",y1:"18",y2:"18",key:"yk5zj1"}]]),de=J("moon",[["path",{d:"M12 3a6 6 0 0 0 9 9 9 9 0 1 1-9-9Z",key:"a7tn18"}]]),ce=J("refresh-cw",[["path",{d:"M3 12a9 9 0 0 1 9-9 9.75 9.75 0 0 1 6.74 2.74L21 8",key:"v9h5vc"}],["path",{d:"M21 3v5h-5",key:"1q7to0"}],["path",{d:"M21 12a9 9 0 0 1-9 9 9.75 9.75 0 0 1-6.74-2.74L3 16",key:"3uifl3"}],["path",{d:"M8 16H3v5",key:"1cv678"}]]),ue=J("search",[["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}],["path",{d:"m21 21-4.3-4.3",key:"1qie3q"}]]),me=J("send",[["path",{d:"M14.536 21.686a.5.5 0 0 0 .937-.024l6.5-19a.496.496 0 0 0-.635-.635l-19 6.5a.5.5 0 0 0-.024.937l7.93 3.18a2 2 0 0 1 1.112 1.11z",key:"1ffxy3"}],["path",{d:"m21.854 2.147-10.94 10.939",key:"12cjpa"}]]),ge=J("shield-check",[["path",{d:"M20 13c0 5-3.5 7.5-7.66 8.95a1 1 0 0 1-.67-.01C7.5 20.5 4 18 4 13V6a1 1 0 0 1 1-1c2 0 4.5-1.2 6.24-2.72a1.17 1.17 0 0 1 1.52 0C14.51 3.81 17 5 19 5a1 1 0 0 1 1 1z",key:"oel41y"}],["path",{d:"m9 12 2 2 4-4",key:"dzmm74"}]]),he=J("shopping-bag",[["path",{d:"M6 2 3 6v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2V6l-3-4Z",key:"hou9p0"}],["path",{d:"M3 6h18",key:"d0wm0j"}],["path",{d:"M16 10a4 4 0 0 1-8 0",key:"1ltviw"}]]),pe=J("sun",[["circle",{cx:"12",cy:"12",r:"4",key:"4exip2"}],["path",{d:"M12 2v2",key:"tus03m"}],["path",{d:"M12 20v2",key:"1lh1kg"}],["path",{d:"m4.93 4.93 1.41 1.41",key:"149t6j"}],["path",{d:"m17.66 17.66 1.41 1.41",key:"ptbguv"}],["path",{d:"M2 12h2",key:"1t8f8n"}],["path",{d:"M20 12h2",key:"1q8mjw"}],["path",{d:"m6.34 17.66-1.41 1.41",key:"1m8zz5"}],["path",{d:"m19.07 4.93-1.41 1.41",key:"1shlcs"}]]),xe=J("thumbs-down",[["path",{d:"M17 14V2",key:"8ymqnk"}],["path",{d:"M9 18.12 10 14H4.17a2 2 0 0 1-1.92-2.56l2.33-8A2 2 0 0 1 6.5 2H20a2 2 0 0 1 2 2v8a2 2 0 0 1-2 2h-2.76a2 2 0 0 0-1.79 1.11L12 22a3.13 3.13 0 0 1-3-3.88Z",key:"m61m77"}]]),fe=J("thumbs-up",[["path",{d:"M7 10v12",key:"1qc93n"}],["path",{d:"M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2a3.13 3.13 0 0 1 3 3.88Z",key:"emmmcr"}]]),be=J("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]]),ye=J("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]]),we=e.forwardRef((({variant:e="primary",size:t="md",loading:a=!1,isLoading:r=!1,disabled:n=!1,leftIcon:i,rightIcon:o,glowEffect:l=!1,isFullWidth:d=!1,withAnimation:c=!1,className:u,children:m,...g},h)=>{const p=a||r,x={primary:"c-button c-button--primary",secondary:"c-button c-button--secondary",outline:"c-button c-button--outline",ghost:"c-button c-button--ghost",destructive:"c-button c-button--destructive",accent:"c-button c-button--accent",link:"c-button c-button--link"}[e],f={xs:"c-button--xs",sm:"c-button--sm",md:"",lg:"c-button--lg",xl:"c-button--xl",icon:"c-button--icon"}[t],b=l?"c-button-glow":"",y=c?"c-button--animated":"",w=p?"c-button--loading":"",v=n?"c-button--disabled":"",j=d?"c-button--full-width":"",k={primary:"design-button design-button-primary",secondary:"design-button design-button-secondary",outline:"design-button design-button-outline",ghost:"design-button",destructive:"design-button",accent:"design-button",link:"design-button"}[e],N={xs:"design-button-sm",sm:"design-button-sm",md:"",lg:"design-button-lg",xl:"design-button-lg",icon:""}[t],_=l?"button-glow border-glow":"";return s.jsxs("button",{ref:h,className:V(x,f,b,w,v,j,y,k,N,_,u),disabled:n||p,...g,children:[i&&s.jsx("span",{className:V("c-button__icon c-button__icon--left"),children:i}),s.jsx("span",{className:"c-button__label",children:m}),o&&s.jsx("span",{className:V("c-button__icon c-button__icon--right"),children:o}),p&&s.jsx("span",{className:"c-button__spinner",children:s.jsxs("svg",{className:"animate-spin h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]})})]})}));
/**
 * @license lucide-react v0.488.0 - ISC
 *
 * This source code is licensed under the ISC license.
 * See the LICENSE file in the root directory of this source tree.
 */we.displayName="Button";const ve=Object.freeze(Object.defineProperty({__proto__:null,default:we},Symbol.toStringTag,{value:"Module"}));function je(){return{toast:(e,{description:t,variant:s="default",duration:r=3e3}={})=>{(()=>{switch(s){case"success":return a.success;case"destructive":return a.error;default:return a}})()(e,{...t?{description:t}:{},duration:r})},sonnerToast:a}}const ke=async e=>{if(!e)return a.error("Nothing to copy"),!1;try{return navigator.clipboard&&navigator.clipboard.writeText?(await navigator.clipboard.writeText(e),a.success("Coupon code copied to clipboard!"),!0):Ne(e)}catch(t){return Ne(e)}},Ne=e=>{const t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select();let s=!1;try{s=document.execCommand("copy"),s?a.success("Coupon code copied to clipboard!"):a.error("Failed to copy code")}catch(r){a.error("Failed to copy code")}return document.body.removeChild(t),s};function _e({dealId:e,className:r,showText:n=!1}){const[i,o]=t.useState(!1),l=e.toString();t.useEffect((()=>{if("undefined"!=typeof window){try{const e=localStorage.getItem("bookmarkIds");if(e){const t=JSON.parse(e);o(t.includes(l))}}catch(e){}const t=t=>{if("bookmarkIds"===t.key)try{const e=t.newValue?JSON.parse(t.newValue):[];o(e.includes(l))}catch(e){}},s=t=>{try{const e=t.detail?t.detail:[];o(e.includes(l))}catch(e){}};return window.addEventListener("storage",t),window.addEventListener("bookmarkChange",s),()=>{window.removeEventListener("storage",t),window.removeEventListener("bookmarkChange",s)}}}),[l]);return s.jsxs(we,{variant:"ghost",size:"sm",onClick:e=>{if(e.preventDefault(),e.stopPropagation(),"undefined"!=typeof window)try{const e=localStorage.getItem("bookmarkIds");let t=e?JSON.parse(e):[];i?(t=t.filter((e=>e!==l)),a.success("Removed from bookmarks")):t.includes(l)||(t.push(l),a.success("Added to bookmarks")),localStorage.setItem("bookmarkIds",JSON.stringify(t)),o(!i),window.dispatchEvent(new StorageEvent("storage",{key:"bookmarkIds",newValue:JSON.stringify(t)})),window.dispatchEvent(new CustomEvent("bookmarkChange",{detail:t}))}catch(t){a.error("Error saving bookmark")}},className:`${n?"flex items-center gap-2":""} ${r||""}`,"aria-label":i?"Remove from bookmarks":"Add to bookmarks","data-bookmarked":i,children:[s.jsx(re,{size:n?16:20,fill:i?"red":"none"}),n&&s.jsx("span",{className:"text-sm",children:i?"Saved":"Save"})]})}const Ce=e.forwardRef((({variant:e="default",interactive:t=!1,hoverable:a=!0,glowEffect:r=!1,className:n,children:i,...o},l)=>{const d=a?"hover:shadow-design-md hover:scale-[1.02] hover:border-design-border/50":"",c=t?"cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-design-primary focus-visible:ring-offset-2 focus-visible:ring-offset-design-background":"",u=r?"card-glow border-glow":"";return s.jsx("div",{ref:l,className:V("rounded-2xl border text-design-card-foreground transition-all duration-design-normal",{default:"border-design-border bg-design-card shadow-design",outline:"border-design-border bg-transparent",glass:"border-design-border/20 bg-design-card/80 backdrop-blur-sm shadow-design",filled:"border-design-primary/20 bg-design-primary/10",elevated:"shadow-design-md border-design-border bg-design-card"}[e],d,c,u,n),tabIndex:t?0:void 0,"data-interactive":t?"true":void 0,...o,children:i})}));Ce.displayName="Card";e.forwardRef((({className:e,spacing:t="default",...a},r)=>s.jsx("div",{ref:r,className:V("flex flex-col p-5",{tight:"space-y-1",default:"space-y-1.5",loose:"space-y-2.5"}[t],e),...a}))).displayName="CardHeader";e.forwardRef((({className:e,as:t="h3",size:a="md",...r},n)=>s.jsx(t,{ref:n,className:V({sm:"text-sm",md:"text-base",lg:"text-lg"}[a],"font-semibold leading-tight line-clamp-2",e),...r}))).displayName="CardTitle";e.forwardRef((({className:e,size:t="sm",...a},r)=>s.jsx("p",{ref:r,className:V({xs:"text-xs",sm:"text-sm",md:"text-base"}[t],"text-design-muted-foreground",e),...a}))).displayName="CardDescription";e.forwardRef((({className:e,spacing:t="default",noPadding:a=!1,...r},n)=>{const i=a?"":"px-5 pt-0";return s.jsx("div",{ref:n,className:V(i,{tight:"space-y-2",default:"space-y-4",loose:"space-y-6"}[t],e),...r})})).displayName="CardContent";e.forwardRef((({className:e,align:t="between",noPadding:a=!1,...r},n)=>{const i=a?"":"px-5 pt-0 pb-5";return s.jsx("div",{ref:n,className:V("flex items-center",{start:"justify-start",center:"justify-center",end:"justify-end",between:"justify-between"}[t],i,e),...r})})).displayName="CardFooter";e.forwardRef((({className:e,aspectRatio:t="auto",overlay:a=!1,alt:r="",...n},i)=>s.jsxs("div",{className:V("relative overflow-hidden",{auto:"",square:"aspect-square",video:"aspect-video",portrait:"aspect-[3/4]"}[t]),children:[s.jsx("img",{ref:i,className:V("w-full h-full object-cover transition-transform duration-500 group-hover:scale-105",e),alt:r,...n}),a&&s.jsx("div",{className:"absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"})]}))).displayName="CardImage";e.forwardRef((({className:e,variant:t="primary",position:a="top-left",children:r,...n},i)=>s.jsx("div",{ref:i,className:V("text-xs font-bold px-3 py-1 rounded-full shadow-md",{primary:"bg-design-primary text-white",secondary:"bg-design-secondary text-design-foreground dark:text-black",accent:"bg-design-tetradic1 text-white",success:"bg-green-500 text-white",warning:"bg-design-tetradic2 text-design-foreground",danger:"bg-red-500 text-white"}[t],{"top-left":"absolute top-4 left-4","top-right":"absolute top-4 right-4","bottom-left":"absolute bottom-4 left-4","bottom-right":"absolute bottom-4 right-4"}[a],e),...n,children:r}))).displayName="CardBadge";const Se=[{name:"Chyou Shen",initials:"CS",webp:"/staff/Chyou_Shen-removebg.webp",fallback:"/staff/Chyou_Shen-removebg.png"},{name:"Cynthia S. Garcia",initials:"CG",webp:"/staff/Cynthia_S._Garcia-removebg.webp",fallback:"/staff/Cynthia_S._Garcia-removebg.png"},{name:"Dannielle E. Benn",initials:"DB",webp:"/staff/Dannielle_E._Benn-removebg.webp",fallback:"/staff/Dannielle_E._Benn-removebg.png"},{name:"Geoffrey J. Koehler",initials:"GK",webp:"/staff/Geoffrey_J._Koehler-removebg.webp",fallback:"/staff/Geoffrey_J._Koehler-removebg.png"},{name:"Harvey B. Green",initials:"HG",webp:"/staff/Harvey_B._Green-removebg-preview.webp",fallback:"/staff/Harvey_B._Green-removebg-preview.png"},{name:"Victoria F. Ingram",initials:"VI",webp:"/staff/Victoria_F._Ingram-removebg.webp",fallback:"/staff/Victoria_F._Ingram-removebg.png"},{name:"Kevin H. Anthony",initials:"KA",webp:null,fallback:null},{name:"Lachlan Finch-Hatton",initials:"LH",webp:null,fallback:null},{name:"Rodolfo M. Tom",initials:"RT",webp:null,fallback:null},{name:"Winnie D. Ratcliffe",initials:"WR",webp:null,fallback:null}],Ee=["#4F46E5","#0EA5E9","#10B981","#F59E0B","#EF4444","#8B5CF6","#EC4899","#06B6D4","#F97316","#84CC16"],Le=[{unit:"hour",max:24,text:"h"},{unit:"day",max:7,text:"d"},{unit:"week",max:4,text:"w"}];function $e(e=3,t){const s=t||42,a=[];for(let r=0;r<e;r++){const e=Se[(s+r)%Se.length],t=Ee[(s+r)%Ee.length];a.push({imagePath:e.fallback,webpPath:e.webp,color:t,initials:e.initials,name:e.name})}return a}function Re(e){const t=e%100,s=Le[t%Le.length],a=1+t%5;return{timeAgo:`${1+t%(s.max-1)}${s.text}`,count:a}}const Me=e=>{if(!e||"string"!=typeof e||""===e.trim()||"YOUR_LOGO_URL"===e)return null;const t=e.trim();return t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:`/${t}`},De=({src:e,alt:a,width:r=400,height:n=400,className:i="",priority:o=!1,fetchpriority:l="auto",fallbackSrc:d="/placeholder-image.svg",onLoad:c,index:u=0})=>{const[m,g]=t.useState(!1),[h,p]=t.useState(!1),[x,f]=t.useState(d);t.useEffect((()=>{if(!e)return void f(d);const t=Me(e);f(t||d)}),[e,d]);return s.jsxs("div",{className:"relative w-full h-full flex items-center justify-center bg-white",children:[!h&&!m&&s.jsxs("div",{className:"absolute inset-0 bg-design-muted animate-pulse flex flex-col items-center justify-center z-20",children:[s.jsx("div",{className:"w-16 h-16 rounded-full bg-design-muted-foreground/20 flex items-center justify-center mb-2",children:s.jsx("svg",{className:"w-8 h-8 text-design-muted-foreground/40",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M4 16L8.586 11.414C8.96106 11.0391 9.46967 10.8284 10 10.8284C10.5303 10.8284 11.0389 11.0391 11.414 11.414L16 16M14 14L15.586 12.414C15.9611 12.0391 16.4697 11.8284 17 11.8284C17.5303 11.8284 18.0389 12.0391 18.414 12.414L20 14M14 8H14.01M6 20H18C18.5304 20 19.0391 19.7893 19.4142 19.4142C19.7893 19.0391 20 18.5304 20 18V6C20 5.46957 19.7893 4.96086 19.4142 4.58579C19.0391 4.21071 18.5304 4 18 4H6C5.46957 4 4.96086 4.21071 4.58579 4.58579C4.21071 4.96086 4 5.46957 4 6V18C4 18.5304 4.21071 19.0391 4.58579 19.4142C4.96086 19.7893 5.46957 20 6 20Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),s.jsx("div",{className:"text-design-primary dark:text-design-primary font-bold text-sm",children:"Vape Hybrid"}),s.jsx("div",{className:"text-design-muted-foreground text-xs mt-1",children:"Loading image..."})]}),s.jsxs("picture",{className:"flex items-center justify-center w-full h-full",children:[x!==d&&!x.endsWith(".svg")&&!x.startsWith("http")&&s.jsx("source",{srcSet:`${x.replace(/\.(jpg|jpeg|png)$/i,".webp")}`,type:"image/webp"}),s.jsx("img",{src:x,alt:a,width:r,height:n,className:`${i} object-contain z-10 ${m?"opacity-0":""}`,loading:o?"eager":"lazy",decoding:"async",onError:()=>{g(!0),f(d)},onLoad:()=>{p(!0),c&&c()},style:{display:"block",margin:"0 auto",maxWidth:"100%",maxHeight:"100%",width:"auto",height:"auto"},fetchpriority:l})]}),m&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center bg-white dark:bg-white/10 z-20",children:s.jsxs("div",{className:"text-design-muted-foreground text-center p-4",children:[s.jsx("div",{className:"mb-2",children:s.jsx("svg",{className:"w-12 h-12 mx-auto text-design-muted-foreground/40",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M4 16L8.586 11.414C8.96106 11.0391 9.46967 10.8284 10 10.8284C10.5303 10.8284 11.0389 11.0391 11.414 11.414L16 16M14 14L15.586 12.414C15.9611 12.0391 16.4697 11.8284 17 11.8284C17.5303 11.8284 18.0389 12.0391 18.414 12.414L20 14M14 8H14.01M6 20H18C18.5304 20 19.0391 19.7893 19.4142 19.4142C19.7893 19.0391 20 18.5304 20 18V6C20 5.46957 19.7893 4.96086 19.4142 4.58579C19.0391 4.21071 18.5304 4 18 4H6C5.46957 4 4.96086 4.21071 4.58579 4.58579C4.21071 4.96086 4 5.46957 4 6V18C4 18.5304 4.21071 19.0391 4.58579 19.4142C4.96086 19.7893 5.46957 20 6 20Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})})}),s.jsx("div",{className:"text-sm font-medium",children:"Image not available"})]})})]})},Oe=({deal:e,imageUrl:t})=>{const a=e.success_rate?e.success_rate/20:4.5,r=e.usage_count||Math.floor(50*Math.random())+10,n={"@type":"Offer",name:e.title,description:e.description||`${e.title} - Save with this deal`,price:e.price||"0",priceCurrency:(e=>{if(!e)return"USD";return/^[A-Z]{3}$/.test(e)?e:{$:"USD","€":"EUR","£":"GBP","¥":"JPY","₹":"INR","₽":"RUB","CN¥":"CNY",A$:"AUD",C$:"CAD"}[e]||"USD"})(e.currency),availability:"https://schema.org/InStock",url:`${"undefined"!=typeof window?window.location.origin:""}/deal/${e.id}`,seller:{"@type":"Organization",name:e.merchants?.name||e.brands?.name||"Retailer"},discount:e.discount?`${e.discount}%`:void 0,validFrom:e.deal_start_date||void 0,validThrough:e.deal_end_date||void 0,priceValidUntil:e.deal_end_date||void 0},i={name:e.title,description:e.description||`${e.title} - Save with this deal`,image:t,brand:{"@type":"Brand",name:e.brands?.name||""},aggregateRating:{"@type":"AggregateRating",ratingValue:a.toFixed(1),bestRating:"5",worstRating:"1",ratingCount:r}},o={"@context":"https://schema.org","@type":"Product",name:i.name,description:i.description,image:i.image,brand:i.brand,offers:n,aggregateRating:i.aggregateRating},l=JSON.parse(JSON.stringify(o,((e,t)=>void 0===t?null:t)).replace(/"null"/g,"null"));return s.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:JSON.stringify(l)}})},Pe=e=>{if(e.slug)return`/coupon/${e.slug}`;return`/coupon/${e.normalized_title||e.cleaned_title||ze(e.title||e.id.toString())}`},ze=e=>e.toLowerCase().replace(/[^a-z0-9\s-]/g,"").replace(/\s+/g,"-").replace(/-+/g,"-").replace(/^-+|-+$/g,"").trim();function Ie(e,s={}){const{delay:a=100,once:r=!0}=s,n=t.useRef(!1),i=t.useRef(null),o=t.useCallback((e=>{if((!r||!n.current)&&e&&"undefined"!=typeof window)try{const t=document.createElement("link");t.rel="prefetch",t.href=e,t.as=e.endsWith(".js")?"script":e.endsWith(".css")?"style":e.includes("/api/")?"fetch":"document",document.head.appendChild(t),n.current=!0}catch(t){}}),[r]),l=t.useCallback((e=>{if((!r||!n.current)&&e&&"undefined"!=typeof window)try{o(`/deal/${e}`),n.current=!0}catch(t){}}),[o,r]);return{onMouseEnter:t.useCallback((()=>{i.current&&window.clearTimeout(i.current),i.current=window.setTimeout((()=>{if(e.includes("/deal/")){const t=e.split("/").pop();t&&l(t)}else o(e)}),a)}),[e,a,o,l]),onMouseLeave:t.useCallback((()=>{i.current&&(window.clearTimeout(i.current),i.current=null)}),[]),prefetch:o,prefetchDeal:l}}function Te(e,s="component",a={}){const{minDwellTime:r=2e3,trackOnce:n=!0,trackOnUnmount:i=!0,trackHover:o=!0,trackScroll:l=!1}=a,d=t.useRef(0),c=t.useRef(0),u=t.useRef(!1),m=t.useRef(!1),g=t.useRef(!1),h=t.useRef(!1),p=t.useRef(null),[x,f]=t.useState(!1),b=t.useCallback((()=>{u.current||(u.current=!0,d.current=Date.now())}),[]),y=t.useCallback((()=>{u.current&&(u.current=!1,c.current+=Date.now()-d.current)}),[]),w=t.useCallback((()=>{m.current=!0}),[]),v=t.useCallback((()=>{m.current=!1}),[]),j=t.useCallback((()=>{g.current=!0}),[]),k=t.useCallback((()=>{if(n&&h.current)return;const t=u.current?c.current+(Date.now()-d.current):c.current,a={id:e,type:s,dwellTime:t,hovered:m.current,clicked:g.current,engaged:t>=r,firstVisible:d.current,lastVisible:Date.now()};if(a.engaged||a.clicked)try{if("function"==typeof navigator.sendBeacon){const e=new Blob([JSON.stringify(a)],{type:"application/json"});navigator.sendBeacon("/api/track-engagement",e)}else fetch("/api/track-engagement",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify(a),keepalive:!0}).catch((e=>{}));h.current=!0}catch(i){}}),[e,s,r,n]);return t.useEffect((()=>()=>{p.current&&window.clearTimeout(p.current),i&&k()}),[i,k]),t.useEffect((()=>{if(x&&!h.current)return p.current=window.setTimeout((()=>{k()}),r),()=>{p.current&&(window.clearTimeout(p.current),p.current=null)}}),[x,r,k]),{isVisible:x,setIsVisible:f,handleVisible:b,handleInvisible:y,handleMouseEnter:w,handleMouseLeave:v,handleClick:j,trackEngagement:k}}const Ve=({deal:e,imageUrl:a})=>{const[r,n]=t.useState(!1);return t.useEffect((()=>{const e=(window.requestIdleCallback||(e=>setTimeout(e,1)))((()=>{n(!0)}));return()=>{(window.cancelIdleCallback||(e=>clearTimeout(e)))(e)}}),[]),r?s.jsx(Oe,{deal:e,imageUrl:a}):null},Ue=({deal:e,className:a,isRevealed:r=!1,priority:n=!1,onShowCouponModal:i,viewMode:o,...l})=>{const[d,c]=t.useState(r),u=t.useRef(null),m=`/deal/${e.id}`,{onMouseEnter:g,onMouseLeave:h}=Ie(m,{delay:150}),{setIsVisible:p,handleMouseEnter:x,handleMouseLeave:f,handleClick:b}=Te(e.id,"deal",{minDwellTime:2e3}),y=t.useMemo((()=>$e(5,e.id)),[e.id]),w=t.useMemo((()=>Re(e.id)),[e.id]),v=t.useMemo((()=>w.timeAgo),[w]),j=t.useMemo((()=>e.usage_count||w.count),[e.usage_count,w.count]),k=t.useMemo((()=>e.deal_end_date?Math.ceil((new Date(e.deal_end_date).getTime()-(new Date).getTime())/864e5):null),[e.deal_end_date]);t.useMemo((()=>{if(null===k||k<=0)return null;if(k>365){const e=Math.floor(k/365);return`${e}+ ${1===e?"year":"years"} left`}if(k>30){const e=Math.floor(k/30);return`${e} ${1===e?"month":"months"} left`}return k>1?`${k}d left`:"Ends today"}),[k]),t.useMemo((()=>null!==k&&k>0&&k<=7),[k]);const N=t.useMemo((()=>{const t=Me(e.imagebig_url)||Me(e.image_url)||Me(e.imagesmall_url);if(t)return t;const s=Me(e.brands?.logo_url)||Me(e.brand_logo_url);if(s)return s;const a=Me(e.merchants?.logo_url)||Me(e.merchant_logo_url);return a||"/placeholder-image.svg"}),[e.imagebig_url,e.image_url,e.imagesmall_url,e.brands?.logo_url,e.brand_logo_url,e.merchants?.logo_url,e.merchant_logo_url]);t.useEffect((()=>{if(!u.current)return;const t=new IntersectionObserver((t=>{t.forEach((t=>{if(p(t.isIntersecting),t.isIntersecting)try{let t=localStorage.getItem("vh_session_id");if(t||(t="session_"+Math.random().toString(36).substring(2,15),localStorage.setItem("vh_session_id",t)),"function"==typeof navigator.sendBeacon){const s=new FormData;s.append("deal_id",e.id.toString()),s.append("session_id",t),navigator.sendBeacon("/api/track-impression?deal_id="+e.id+"&session_id="+t,s)}else fetch("/api/track-impression?deal_id="+e.id+"&session_id="+t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({deal_id:e.id,session_id:t}),keepalive:!0})}catch(s){}}))}),{threshold:.5});return t.observe(u.current),()=>{t.disconnect()}}),[e.id,u,p]);const _=t.useCallback((()=>{if(c(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("clickedDeals")||"{}"),s=t[e.id],a=JSON.parse(localStorage.getItem("revealedCodes")||"{}");a[e.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(a)),t[e.id]=!0,localStorage.setItem("clickedDeals",JSON.stringify(t)),e.coupon_code&&ke(e.coupon_code);const r=new URL(window.location.href).searchParams.get("view")||"grid",n=new URL(window.location.href);n.searchParams.set("dealId",e.id.toString()),n.searchParams.set("showPopup","true"),n.searchParams.set("view",r),window.open(n.toString(),"_blank"),i&&i(e),!s&&e.tracking_url?window.location.href=e.tracking_url:s||(window.location.href=`/go/${e.id}`)}}),[e,i]),C=t.useCallback((t=>{t.stopPropagation(),b(),requestAnimationFrame((()=>{if(c(!0),"undefined"!=typeof window){if("function"==typeof navigator.sendBeacon)try{const t=new FormData;t.append("deal_id",e.id.toString()),t.append("fallback","true"),navigator.sendBeacon("/api/track-click",t)}catch(t){}const s=JSON.parse(localStorage.getItem("clickedDeals")||"{}"),a=JSON.parse(localStorage.getItem("revealedCodes")||"{}");a[e.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(a)),s[e.id]=!0,localStorage.setItem("clickedDeals",JSON.stringify(s)),e.coupon_code&&ke(e.coupon_code);const r=new URL(window.location.href).searchParams.get("view")||"grid",n=new URL(window.location.href);n.searchParams.set("dealId",e.id.toString()),n.searchParams.set("showPopup","true"),n.searchParams.set("solidBg","true"),n.searchParams.set("view",r),window.open(n.toString(),"_blank"),e.tracking_url?window.location.href=e.tracking_url:window.location.href=`/go/${e.id}`}}))}),[e,b]);return s.jsxs(s.Fragment,{children:["undefined"!=typeof window&&s.jsx(Ve,{deal:e,imageUrl:N}),s.jsxs(Ce,{ref:u,variant:"default",interactive:!0,glowEffect:!0,className:V("optimized-deal-card deal-card relative overflow-hidden cursor-pointer w-full flex flex-col max-h-[550px]","transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.01]","hover:border-design-primary/50 dark:hover:border-design-primary/50","border-black/15 dark:border-white/15","optimized-card",a),style:{borderRadius:"25px",borderWidth:"1.5px"},"data-theme-border":"true",onClick:()=>{_(),b()},onMouseEnter:()=>{g(),x()},onMouseLeave:()=>{h(),f()},"aria-label":`Deal: ${e.title}`,role:"button",tabIndex:0,...l,children:[s.jsx("div",{id:`tracking-pixel-${e.id}`,style:{position:"absolute",opacity:0,pointerEvents:"none"},"aria-hidden":"true"}),s.jsx("div",{className:"w-full p-3",style:{height:"52%"},children:s.jsx("div",{className:"flex justify-center h-full",children:s.jsx("div",{className:"relative overflow-hidden rounded-lg flex items-center justify-center w-full h-full image-container bg-white dark:bg-white",style:{maxHeight:"calc(550px * 0.52 - 24px)",aspectRatio:"1/1",boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.05)",padding:"8px"},children:s.jsx(De,{src:N,alt:e.title||"Deal image",width:400,height:400,priority:n,fetchpriority:n?"high":"auto",className:"",fallbackSrc:"/placeholder-image.svg",index:0})})})}),s.jsxs("div",{className:"px-3 pb-0 flex-1 flex flex-col",style:{height:"48%",maxHeight:"calc(550px * 0.48 - 24px)"},children:[s.jsxs("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("div",{className:"relative",children:s.jsx("div",{className:"text-xl font-bold text-design-foreground",children:e.discount?s.jsxs("span",{className:"font-bold text-primary dark:text-primary",children:[e.discount,"% ",s.jsx("span",{className:"text-xs font-bold text-gray-900 dark:text-gray-400",children:"off "})]}):e.price?`${e.currency||"$"}${e.price}`:"20% Off"})}),s.jsx("div",{className:"flex items-center ml-1",children:s.jsx("div",{className:"deal-coupon-code text-xs bg-design-muted px-2 py-1 rounded-full border border-design-muted-foreground/10 relative overflow-hidden",children:e.coupon_code?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"transition-all duration-300 "+(d?"blur-none opacity-100":"blur-[3px] select-none"),"aria-hidden":!d,children:e.coupon_code}),d&&s.jsx("span",{className:"absolute inset-0 bg-design-primary/10 animate-reveal-sweep","aria-hidden":"true"})]}):"NO CODE"})})]}),s.jsxs("div",{className:"text-xs text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1",children:[s.jsx("span",{className:"font-medium truncate max-w-[120px] hover:underline cursor-help",title:`Brand: ${e.merchants?.name||e.brands?.name||"Unknown"}`,children:e.merchants?.name||e.brands?.name||"Brand"}),e.verified&&s.jsxs("span",{className:"c-verified-badge verified-badge ml-2 inline-flex items-center",children:[s.jsx("img",{src:"/Vapehybrid light icon.svg",alt:"Verified",className:"dark:hidden inline-block",width:"10",height:"10"}),s.jsx("img",{src:"/Vapehybrid dark icon.svg",alt:"Verified",className:"hidden dark:inline-block",width:"10",height:"10"}),s.jsx("span",{className:"text-design-primary dark:text-design-primary text-[10px] inline-block",children:"Verified"})]}),void 0!==e.success_rate&&s.jsxs("span",{className:"ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full "+(e.success_rate>=90?"bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground":e.success_rate>=70?"bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground":"bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"),title:`${Math.round(e.success_rate||85)}% success rate`,children:[s.jsx(fe,{size:10,className:"mr-1"}),Math.round(e.success_rate||85),"%"]}),null!==k&&k>0&&s.jsxs("span",{className:"ml-2 text-xs flex items-center text-design-muted-foreground",children:[s.jsx("svg",{className:"w-3 h-3 mr-1",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),k>365?`${Math.floor(k/365)}+ ${1===Math.floor(k/365)?"year":"years"} left`:k>30?`${Math.floor(k/30)} ${1===Math.floor(k/30)?"month":"months"} left`:1===k?"Ends today":`${k}d left`]})]}),s.jsx("h3",{className:"deal-card-title line-clamp-2 mb-2 text-sm font-semibold overflow-hidden",title:e.cleaned_title&&"null"!==e.cleaned_title?e.cleaned_title:e.title,children:e.cleaned_title&&"null"!==e.cleaned_title?e.cleaned_title:e.title}),s.jsxs("div",{className:"flex items-center text-xs text-design-muted-foreground mb-1",children:[s.jsxs("div",{className:"flex -space-x-2",children:[y.slice(0,Math.min(3,j||3)).map(((e,t)=>s.jsxs("div",{className:"relative inline-flex items-center justify-center w-5 h-5 rounded-full border-2 border-white bg-white dark:border-gray-800 dark:bg-gray-800 overflow-hidden",style:{zIndex:3-t,backgroundColor:e.color||"#6b7280"},title:`Staff member: ${e.name}`,children:[e.webpPath||e.imagePath?s.jsx("img",{src:e.webpPath||e.imagePath||"",alt:e.initials,className:"w-full h-full object-cover",onError:e=>{const t=e.target;t.style.display="none";const s=t.nextElementSibling;s&&(s.style.display="flex")},loading:"lazy"}):null,s.jsx("div",{className:"absolute inset-0 flex items-center justify-center text-white font-bold",style:{display:e.webpPath||e.imagePath?"none":"flex",fontSize:"8px",textShadow:"0 1px 1px rgba(0,0,0,0.3)"},children:e.initials})]},t))),j>3&&s.jsxs("div",{className:"relative flex items-center justify-center w-5 h-5 rounded-full border-2 border-white bg-gray-100 dark:border-gray-800 dark:bg-gray-700 text-gray-600 dark:text-gray-300",style:{zIndex:0,fontSize:"8px",fontWeight:"bold",boxShadow:"0 1px 2px rgba(0,0,0,0.1)"},title:j-3+" more staff members",children:["+",j-3]})]}),s.jsxs("span",{className:"ml-2",children:["Verified ",s.jsx("time",{dateTime:e.last_verified_at,title:new Date(e.last_verified_at||"").toLocaleString(),children:v})," by ",j||3," staffer",(j||3)>1?"s":""]})]}),s.jsx("div",{className:"flex-grow"}),s.jsxs("div",{className:"flex justify-between items-center w-full mt-auto mb-2 px-2 pt-2 border-t border-design-muted border-opacity-20",role:"group","aria-label":"Deal actions",style:{touchAction:"manipulation"},children:[s.jsxs("button",{className:"eye-button p-2 rounded-full hover:bg-design-muted transition-colors relative z-10",onClick:t=>{t.stopPropagation(),window.open(Pe(e),"_blank")},"aria-label":"View coupon details",title:"View coupon details",tabIndex:0,style:{minWidth:"36px",minHeight:"36px"},children:[s.jsx(te,{size:18,className:"text-design-muted-foreground hover:text-design-foreground transition-colors"}),s.jsx("span",{className:"sr-only",children:"View deal details"})]}),s.jsx("button",{className:"copy-code-button h-8 px-4 text-sm mx-2 transition-colors hover:bg-design-primary hover:text-white dark:hover:text-black focus:outline-none focus:ring-2 focus:ring-design-primary focus:ring-offset-2 rounded-[25px]",onClick:C,"aria-label":"Copy Code",title:"Copy coupon code",tabIndex:0,children:"Copy Code"}),s.jsx(_e,{dealId:e.id.toString(),className:"bookmark-button p-2 rounded-full hover:bg-design-muted transition-colors"})]})]})]})]})},We=Object.freeze(Object.defineProperty({__proto__:null,OptimizedDealCard:Ue,default:Ue},Symbol.toStringTag,{value:"Module"})),Be=({deal:r,onClose:n,onVote:i})=>{const[o,l]=t.useState(!1),d=e.useMemo((()=>{const e=e=>{if(!e||""===e.trim())return null;const t=e.trim();return t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:`/${t}`};return r.brand_logo_url?e(r.brand_logo_url):r.brands?.logo_url?e(r.brands.logo_url):r.merchant_logo_url?e(r.merchant_logo_url):r.merchants?.logo_url?e(r.merchants.logo_url):e(r.image_url)}),[r]);let c=r.merchants?.name||r.merchant_name||"Brand";"Vapesourcing Electronics Co.,Ltd."===c&&(c="Vapesourcing");const u=t.useCallback((()=>{if(r.coupon_code)try{navigator.clipboard.writeText(r.coupon_code).then((()=>{l(!0),a.success("Coupon code copied!",{duration:2e3,position:"top-center"}),setTimeout((()=>l(!1)),2e3)}))}catch(e){const t=document.createElement("textarea");t.value=r.coupon_code,document.body.appendChild(t),t.select(),document.execCommand("copy"),document.body.removeChild(t),l(!0),a.success("Coupon code copied!",{duration:2e3,position:"top-center"}),setTimeout((()=>l(!1)),2e3)}}),[r.coupon_code]),m=t.useCallback((()=>{r.tracking_url?window.open(r.tracking_url,"_blank"):window.open(`/go/${r.id}`,"_blank")}),[r]),g=t.useCallback((e=>{i&&(i(r.id,e),a.success(e?"Thanks for your positive feedback!":"Thanks for your feedback!"))}),[r.id,i]),h=t.useCallback((e=>{e.target===e.currentTarget&&n()}),[n]);return s.jsx("div",{className:"fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4",onClick:h,style:{animation:"fadeIn 0.2s ease-out"},role:"dialog","aria-modal":"true","aria-labelledby":"modal-title",children:s.jsxs("div",{className:"bg-design-card text-design-card-foreground border border-design-border rounded-[25px] p-6 pt-8 pb-8 max-w-md w-full shadow-xl relative",style:{animation:"scaleIn 0.2s ease-out",maxHeight:"85vh",overflowY:"auto"},children:[s.jsx("button",{onClick:n,className:"absolute top-4 right-4 p-2 text-design-muted-foreground hover:text-design-foreground hover:bg-design-muted rounded-full transition-colors z-10","aria-label":"Close",children:s.jsx(ye,{className:"h-5 w-5"})}),s.jsxs("div",{className:"flex flex-col items-center",children:[s.jsx("div",{className:"deal-coupon-modal-logo mt-3 mb-4",children:d&&s.jsx("img",{src:d,alt:`${c} logo`,className:"max-h-24 max-w-full object-contain",width:"120",height:"60",loading:"eager"})}),s.jsx("h3",{id:"modal-title",className:"text-2xl font-bold text-center mb-2",children:r.discount?`${r.discount}% Off`:"Special Deal"}),s.jsx("p",{className:"text-sm text-design-muted-foreground text-center mb-4",children:r.title}),r.coupon_code&&s.jsx("p",{className:"text-center text-xs text-design-primary mb-3 font-medium",children:"Click the button below to copy the code"}),r.coupon_code?s.jsx("div",{className:"bg-design-muted dark:bg-gray-800 w-full rounded-[25px] p-0.5 shadow-sm select-all cursor-pointer mb-4",onClick:u,onKeyDown:e=>"Enter"===e.key&&u(),role:"button",tabIndex:0,"aria-label":`Copy coupon code ${r.coupon_code}`,children:s.jsxs("div",{className:"flex justify-between items-center",children:[s.jsx("div",{className:"font-mono font-bold tracking-wider pl-6 py-3 text-lg",children:r.coupon_code}),s.jsxs("div",{className:"bg-design-primary rounded-r-[25px] text-white dark:text-black py-3 px-4 flex items-center font-medium",children:[o?s.jsx(G,{className:"h-4 w-4 mr-2"}):s.jsx(X,{className:"h-4 w-4 mr-2"}),o?"Copied":"Copy"]})]})}):s.jsx("div",{className:"bg-design-muted w-full rounded-[25px] p-4 text-center mb-4",children:s.jsx("span",{className:"font-medium",children:"No code needed"})}),s.jsxs("div",{className:"flex flex-col items-center justify-center gap-3 mb-6",children:[s.jsx("span",{className:"text-sm text-design-muted-foreground",children:"Did this code work?"}),s.jsxs("div",{className:"flex gap-4",children:[s.jsxs("button",{onClick:()=>g(!1),className:"flex items-center gap-2 px-3 py-1.5 bg-design-muted hover:bg-red-100 dark:hover:bg-red-900/20 rounded-full text-sm transition-colors","aria-label":"Vote that the code did not work",children:[s.jsx(xe,{className:"h-4 w-4 text-red-500"}),s.jsx("span",{children:"No"})]}),s.jsxs("button",{onClick:()=>g(!0),className:"flex items-center gap-2 px-3 py-1.5 bg-design-muted hover:bg-green-100 dark:hover:bg-green-900/20 rounded-full text-sm transition-colors","aria-label":"Vote that the code worked",children:[s.jsx(fe,{className:"h-4 w-4 text-green-500"}),s.jsx("span",{children:"Yes"})]})]})]}),s.jsxs("button",{onClick:m,className:"w-full bg-design-primary hover:bg-design-primary/90 text-white dark:text-black py-3 px-4 rounded-[25px] font-medium transition-colors flex items-center justify-center gap-2 mb-5",children:[s.jsx(ee,{className:"h-4 w-4"}),s.jsx("span",{children:"Get Deal Now"})]}),s.jsxs("div",{className:"flex justify-center items-center gap-6 text-sm text-design-muted-foreground",children:[s.jsx("a",{href:`/deal/${r.id}`,className:"flex items-center text-design-muted-foreground hover:text-design-primary transition-colors","aria-label":"View deal details",children:s.jsx(te,{className:"h-4 w-4"})}),s.jsx(_e,{dealId:r.id}),r.verified&&r.last_verified_at&&s.jsxs("div",{className:"flex items-center text-green-600 dark:text-green-400",children:[s.jsx(ge,{className:"h-4 w-4 mr-2"}),s.jsx("span",{children:"Verified"})]})]})]})]})})},Fe=Object.freeze(Object.defineProperty({__proto__:null,default:Be},Symbol.toStringTag,{value:"Module"})),Ae=c,Je=u,He=o,qe=n,Ge=t.forwardRef((({className:e,...t},a)=>s.jsx(r,{className:`fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ${e}`,...t,ref:a})));Ge.displayName=r.displayName;const Ke=T("fixed z-50 gap-4 bg-design-background p-6 shadow-design-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",{variants:{side:{top:"inset-x-0 top-0 border-b border-design-border data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",bottom:"inset-x-0 bottom-0 border-t border-design-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",left:"inset-y-0 left-0 h-full w-3/4 border-r border-design-border data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",right:"inset-y-0 right-0 h-full w-3/4 border-l border-design-border data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"}},defaultVariants:{side:"right"}}),Qe=t.forwardRef((({side:e="right",className:t,children:a,...r},n)=>s.jsxs(qe,{children:[s.jsx(Ge,{}),s.jsxs(i,{ref:n,className:Ke({side:e,className:t}),"aria-describedby":"sheet-description",...r,children:[s.jsx("div",{id:"sheet-description",className:"sr-only",children:"Sheet content containing filters for deals"}),a,s.jsxs(o,{className:"absolute right-4 top-[12.5px] rounded-full w-5 h-5 flex items-center justify-center bg-design-primary text-white dark:text-black opacity-90 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-1 focus:ring-design-ring focus:ring-offset-2 disabled:pointer-events-none z-50 shadow-sm","aria-label":"Close",children:[s.jsx(ye,{className:"h-3 w-3"}),s.jsx("span",{className:"sr-only",children:"Close"})]})]})]})));Qe.displayName=i.displayName;const Ye=({className:e,...t})=>s.jsx("div",{className:`flex flex-col space-y-2 text-center sm:text-left ${e}`,...t});Ye.displayName="SheetHeader";const Ze=({className:e,...t})=>s.jsx("div",{className:`flex flex-col-reverse sm:flex-row sm:justify-end sm:space-x-2 ${e}`,...t});Ze.displayName="SheetFooter";const Xe=t.forwardRef((({className:e,...t},a)=>s.jsx(l,{ref:a,className:`text-lg font-semibold text-design-foreground ${e}`,...t})));Xe.displayName=l.displayName;const et=t.forwardRef((({className:e,...t},a)=>s.jsx(d,{ref:a,className:`text-sm text-design-muted-foreground ${e}`,...t})));et.displayName=d.displayName;const tt=Object.freeze(Object.defineProperty({__proto__:null,Sheet:Ae,SheetClose:He,SheetContent:Qe,SheetDescription:et,SheetFooter:Ze,SheetHeader:Ye,SheetOverlay:Ge,SheetPortal:qe,SheetTitle:Xe,SheetTrigger:Je},Symbol.toStringTag,{value:"Module"})),st=t.forwardRef((({className:e,...t},a)=>s.jsxs(m,{ref:a,className:`relative flex w-full touch-none select-none items-center ${e}`,...t,children:[s.jsx(g,{className:"relative h-2 w-full grow overflow-hidden rounded-full bg-design-muted",children:s.jsx(h,{className:"absolute h-full bg-design-primary"})}),t.defaultValue?.map(((e,t)=>s.jsx(p,{className:"block h-5 w-5 rounded-full border-2 border-design-primary bg-design-background shadow-md ring-offset-background transition-colors hover:bg-design-primary/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-design-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"},t)))]})));st.displayName=m.displayName;const at=t.forwardRef((({className:e,...t},a)=>s.jsx(x,{ref:a,className:`peer h-4 w-4 shrink-0 rounded-sm border border-design-border bg-design-background ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-design-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-design-primary data-[state=checked]:text-white ${e}`,...t,children:s.jsx(f,{className:"flex items-center justify-center text-current",children:s.jsx(G,{className:"h-3.5 w-3.5"})})})));at.displayName=x.displayName;const rt=e.forwardRef((({required:e=!1,className:t,children:a,...r},n)=>s.jsxs("label",{ref:n,className:V("design-label",t),...r,children:[a,e&&s.jsx("span",{className:"design-label-required",children:"*"})]})));function nt({categories:e,merchants:a,brands:r,initialFilters:n,currentUrl:i="/coupons"}){const[o,l]=t.useState(n||{categories:[],merchants:[],brands:[],priceRange:[0,200],discountRange:[0,100],expiringSoon:!1,validLongTerm:!1}),[d,c]=o.priceRange,[u,m]=o.discountRange,[g,h]=t.useState(""),[p,x]=t.useState(""),[f,b]=t.useState(""),y=e.filter((e=>e.name.toLowerCase().includes(g.toLowerCase()))),w=a.filter((e=>e.name.toLowerCase().includes(p.toLowerCase()))),v=r.filter((e=>e.name.toLowerCase().includes(f.toLowerCase()))),j=(e,t,s)=>{l((a=>t?{...a,[s]:[...a[s],e]}:{...a,[s]:a[s].filter((t=>t!==e))}))},k=(e,t)=>{l((s=>({...s,[e]:t})))},N=t.useRef(null),_=t.useRef(null),C=t.useCallback((e=>{N.current&&window.clearTimeout(N.current),N.current=window.setTimeout((()=>{requestAnimationFrame((()=>{l((t=>({...t,priceRange:[e[0],e[1]]})))}))}),50)}),[]),S=t.useCallback((e=>{_.current&&window.clearTimeout(_.current),_.current=window.setTimeout((()=>{requestAnimationFrame((()=>{l((t=>({...t,discountRange:[e[0],e[1]]})))}))}),50)}),[]),E=o.categories.length+o.merchants.length+o.brands.length+(o.expiringSoon?1:0)+(o.validLongTerm?1:0)+(d>0||c<200?1:0)+(u>0||m<100?1:0),L=(()=>{try{if("undefined"==typeof window)return new URLSearchParams;if(i&&i.startsWith("http"))return new URL(i).searchParams;if(i&&i.startsWith("/")){return new URL(i,window.location.origin).searchParams}return new URL(window.location.href).searchParams}catch(e){return new URLSearchParams}})();return s.jsxs("form",{action:i,method:"get",className:"space-y-4",children:[s.jsx("input",{type:"hidden",name:"view",value:L.get("view")||"grid"}),s.jsx("input",{type:"hidden",name:"sort",value:L.get("sort")||"newest"}),s.jsx("input",{type:"hidden",name:"minPrice",value:d}),s.jsx("input",{type:"hidden",name:"maxPrice",value:c}),s.jsx("input",{type:"hidden",name:"minDiscount",value:u}),s.jsx("input",{type:"hidden",name:"maxDiscount",value:m}),o.expiringSoon&&s.jsx("input",{type:"hidden",name:"expiringSoon",value:"true"}),o.validLongTerm&&s.jsx("input",{type:"hidden",name:"validLongTerm",value:"true"}),o.categories.map((e=>s.jsx("input",{type:"hidden",name:"categories",value:e},e))),o.merchants.map((e=>s.jsx("input",{type:"hidden",name:"merchants",value:e},e))),o.brands.map((e=>s.jsx("input",{type:"hidden",name:"brands",value:e},e))),E>0&&s.jsx("div",{className:"mb-2 -mt-1",children:s.jsxs(we,{type:"button",variant:"ghost",size:"sm",onClick:()=>{l({categories:[],merchants:[],brands:[],priceRange:[0,200],discountRange:[0,100],expiringSoon:!1,validLongTerm:!1}),h(""),x(""),b("")},className:"text-design-destructive hover:text-design-destructive/90 font-medium w-full justify-center border border-design-border rounded-sm text-xs py-1",children:["Reset (",E,")"]})}),s.jsxs("div",{className:"space-y-2 bg-design-muted/10 p-2 rounded-md",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h4",{className:"text-xs font-medium text-design-foreground",children:"Price Range"}),s.jsxs("div",{className:"text-xs text-design-muted-foreground",children:["$",d," - $",c]})]}),s.jsx(st,{defaultValue:[d,c],min:0,max:200,step:5,value:[d,c],onValueChange:C,className:"py-1"})]}),s.jsxs("div",{className:"space-y-2 bg-design-muted/10 p-2 rounded-md",children:[s.jsxs("div",{className:"flex items-center justify-between",children:[s.jsx("h4",{className:"text-xs font-medium text-design-foreground",children:"Discount Range"}),s.jsxs("div",{className:"text-xs text-design-muted-foreground",children:[u,"% - ",m,"%"]})]}),s.jsx(st,{defaultValue:[u,m],min:0,max:100,step:5,value:[u,m],onValueChange:S,className:"py-1"})]}),s.jsxs("div",{className:"space-y-2 bg-design-muted/10 p-2 rounded-md",children:[s.jsx("h4",{className:"text-xs font-medium text-design-foreground mb-1",children:"Deal Expiry"}),s.jsxs("div",{className:"space-y-1.5",children:[s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(at,{id:"expiringSoon",checked:o.expiringSoon,onCheckedChange:e=>k("expiringSoon",e),className:"h-3.5 w-3.5"}),s.jsx(rt,{htmlFor:"expiringSoon",className:"text-xs font-normal cursor-pointer",children:"Expiring Soon (15 days or less)"})]}),s.jsxs("div",{className:"flex items-center space-x-2",children:[s.jsx(at,{id:"validLongTerm",checked:o.validLongTerm,onCheckedChange:e=>k("validLongTerm",e),className:"h-3.5 w-3.5"}),s.jsx(rt,{htmlFor:"validLongTerm",className:"text-xs font-normal cursor-pointer",children:"Valid Long Term (30+ days)"})]})]})]}),s.jsxs("div",{className:"space-y-2 bg-design-muted/10 p-2 rounded-md",children:[s.jsx("h4",{className:"text-xs font-medium text-design-foreground mb-1",children:"Categories"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:"text",placeholder:"Search categories...",value:g,onChange:e=>h(e.target.value),className:"w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"}),g&&s.jsxs("button",{type:"button",onClick:()=>h(""),className:"absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground",children:[s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),s.jsx("span",{className:"sr-only",children:"Clear search"})]})]}),s.jsx("div",{className:"space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1",children:y?.length>0?y.map((e=>s.jsxs("div",{className:"flex items-center space-x-1.5",children:[s.jsx(at,{id:`category-${e.id}`,checked:o.categories.includes(e.id),onCheckedChange:t=>j(e.id,t,"categories"),className:"h-3.5 w-3.5"}),s.jsx(rt,{htmlFor:`category-${e.id}`,className:"text-xs font-normal cursor-pointer",children:e.name})]},e.id))):s.jsx("p",{className:"text-xs text-design-muted-foreground py-1",children:e.length>0?"No matching categories":"No categories found"})})]}),s.jsxs("div",{className:"space-y-2 bg-design-muted/10 p-2 rounded-md",children:[s.jsx("h4",{className:"text-xs font-medium text-design-foreground mb-1",children:"Merchants"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:"text",placeholder:"Search merchants...",value:p,onChange:e=>x(e.target.value),className:"w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"}),p&&s.jsxs("button",{type:"button",onClick:()=>x(""),className:"absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground",children:[s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),s.jsx("span",{className:"sr-only",children:"Clear search"})]})]}),s.jsx("div",{className:"space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1",children:w?.length>0?w.map((e=>s.jsxs("div",{className:"flex items-center space-x-1.5",children:[s.jsx(at,{id:`merchant-${e.id}`,checked:o.merchants.includes(e.id),onCheckedChange:t=>j(e.id,t,"merchants"),className:"h-3.5 w-3.5"}),s.jsx(rt,{htmlFor:`merchant-${e.id}`,className:"text-xs font-normal cursor-pointer",children:e.name})]},e.id))):s.jsx("p",{className:"text-xs text-design-muted-foreground py-1",children:a.length>0?"No matching merchants":"No merchants found"})})]}),s.jsxs("div",{className:"space-y-2 bg-design-muted/10 p-2 rounded-md",children:[s.jsx("h4",{className:"text-xs font-medium text-design-foreground mb-1",children:"Brands"}),s.jsxs("div",{className:"relative",children:[s.jsx("input",{type:"text",placeholder:"Search brands...",value:f,onChange:e=>b(e.target.value),className:"w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"}),f&&s.jsxs("button",{type:"button",onClick:()=>b(""),className:"absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground",children:[s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]}),s.jsx("span",{className:"sr-only",children:"Clear search"})]})]}),s.jsx("div",{className:"space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1",children:v?.length>0?v.map((e=>s.jsxs("div",{className:"flex items-center space-x-1.5",children:[s.jsx(at,{id:`brand-${e.id}`,checked:o.brands.includes(e.id),onCheckedChange:t=>j(e.id,t,"brands"),className:"h-3.5 w-3.5"}),s.jsx(rt,{htmlFor:`brand-${e.id}`,className:"text-xs font-normal cursor-pointer",children:e.name})]},e.id))):s.jsx("p",{className:"text-xs text-design-muted-foreground py-1",children:r.length>0?"No matching brands":"No brands found"})})]}),s.jsx("div",{className:"sticky bottom-0 pt-2 pb-2 bg-design-background border-t border-design-border z-40 shadow-md",children:s.jsx(we,{type:"submit",variant:"primary",className:"w-full text-xs font-medium bg-design-primary text-white dark:text-black cursor-pointer rounded-sm px-3 py-2 text-center transition-colors hover:bg-design-primary/90 flex items-center justify-center",children:s.jsxs("div",{className:"flex items-center justify-center",children:[s.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-3.5 w-3.5 mr-1 inline-block",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:s.jsx("path",{d:"M22 3H2l8 9.46V19l4 2v-8.54L22 3z"})}),s.jsxs("span",{children:["Apply ",E>0&&`(${E})`]})]})})})]})}rt.displayName="Label";const it=Object.freeze(Object.defineProperty({__proto__:null,default:nt},Symbol.toStringTag,{value:"Module"}));function ot({categories:e,merchants:a,brands:r,initialFilters:n,currentUrl:i="/",activeFilterCount:o=0}){const[l,d]=t.useState(!1);return s.jsxs(s.Fragment,{children:[s.jsx(we,{onClick:()=>d(!0),variant:"outline",size:"sm",className:"h-7 border border-design-border/40 bg-design-background/60 text-design-foreground hover:bg-design-muted/50 rounded-md text-[10px] sm:text-xs px-1 sm:px-2 transition-all duration-200 backdrop-blur-sm flex-shrink-0",children:s.jsxs("div",{className:"flex items-center justify-center gap-1",children:[s.jsx(se,{size:10}),s.jsx("span",{className:"font-medium",children:"Filters"}),o>0&&s.jsx("span",{className:"ml-1 bg-design-primary text-white rounded-full w-4 h-4 flex items-center justify-center text-[9px] font-bold",children:o})]})}),s.jsx(Ae,{open:l,onOpenChange:d,children:s.jsxs(Qe,{side:"left",className:"w-full sm:max-w-md bg-design-background border-design-border p-0 overflow-hidden transition-transform duration-300 ease-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left",children:[s.jsxs("div",{className:"border-b border-design-border h-10 sticky top-0 bg-design-background z-10 relative",children:[s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:s.jsx(Xe,{className:"text-sm font-medium text-design-foreground m-0",children:"Filters"})}),s.jsx(et,{className:"sr-only",children:"Filter options for deals"})]}),s.jsx("div",{className:"px-4 pt-3 pb-24 overflow-y-auto h-[calc(100vh-2.5rem)] custom-scrollbar",children:l&&s.jsx(nt,{categories:e,merchants:a,brands:r,initialFilters:n,isMobile:!0,currentUrl:i})})]})})]})}const lt=Object.freeze(Object.defineProperty({__proto__:null,default:ot},Symbol.toStringTag,{value:"Module"})),dt=E,ct=L,ut=$,mt=t.forwardRef((({className:e,children:t,...a},r)=>s.jsxs(b,{ref:r,className:`flex h-10 w-full items-center justify-between rounded-md border border-design-border bg-design-background px-3 py-2 text-sm ring-offset-background placeholder:text-design-muted-foreground focus:outline-none focus:ring-2 focus:ring-design-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${e}`,"aria-label":a["aria-label"]||"Select option",...a,children:[t,s.jsx(y,{asChild:!0,children:s.jsx(K,{className:"h-4 w-4 opacity-50","aria-hidden":"true"})})]})));mt.displayName=b.displayName;const gt=t.forwardRef((({className:e,children:t,position:a="popper",...r},n)=>s.jsx(w,{children:s.jsx(v,{ref:n,className:`relative z-50 min-w-[8rem] overflow-hidden rounded-md border border-design-border bg-design-card text-design-foreground shadow-design-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 ${"popper"===a&&"data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1"} ${e}`,position:a,...r,children:s.jsx(j,{className:`p-1 ${"popper"===a&&"h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"}`,children:t})})})));gt.displayName=v.displayName;const ht=t.forwardRef((({className:e,...t},a)=>s.jsx(k,{ref:a,className:`py-1.5 pl-8 pr-2 text-sm font-semibold text-design-foreground ${e}`,...t})));ht.displayName=k.displayName;const pt=t.forwardRef((({className:e,children:t,...a},r)=>s.jsxs(N,{ref:r,className:`relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-design-muted focus:text-design-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 ${e}`,...a,children:[s.jsx("span",{className:"absolute left-2 flex h-3.5 w-3.5 items-center justify-center",children:s.jsx(_,{children:s.jsx(G,{className:"h-4 w-4"})})}),s.jsx(C,{children:t})]})));pt.displayName=N.displayName;const xt=t.forwardRef((({className:e,...t},a)=>s.jsx(S,{ref:a,className:`-mx-1 my-1 h-px bg-design-border ${e}`,...t})));xt.displayName=S.displayName;const ft=Object.freeze(Object.defineProperty({__proto__:null,Select:dt,SelectContent:gt,SelectGroup:ct,SelectItem:pt,SelectLabel:ht,SelectSeparator:xt,SelectTrigger:mt,SelectValue:ut},Symbol.toStringTag,{value:"Module"}));function bt({value:e,currentUrl:a}){const r=t.useCallback((e=>{requestAnimationFrame((()=>{if(a){const t=new URL(a);t.searchParams.set("sort",e),window.location.href=t.toString()}else{const t=window.location.pathname,s=new URLSearchParams(window.location.search);s.set("sort",e),window.location.href=`${t}?${s.toString()}`}}))}),[a]),n=[{value:"newest",label:"Newest First"},{value:"price_asc",label:"Price: Low to High"},{value:"price_desc",label:"Price: High to Low"},{value:"expiring_soon",label:"Expiring Soon"},{value:"discount_desc",label:"Discount: High to Low"},{value:"most_popular",label:"Most Popular"}],i=n.find((t=>t.value===e))?.label||"Sort by";return s.jsx("div",{className:"min-w-[70px] sm:min-w-[80px] lg:min-w-[100px] flex-shrink-0",children:s.jsxs(dt,{defaultValue:e,onValueChange:r,children:[s.jsx(mt,{className:"w-full bg-design-background/60 border border-design-border/40 text-design-foreground rounded-md h-7 px-1 sm:px-2 text-[10px] sm:text-xs focus:ring-1 focus:ring-design-primary/30 focus:border-design-primary/50 transition-all duration-200 backdrop-blur-sm",children:s.jsx(ut,{placeholder:"Sort",children:i})}),s.jsx(gt,{className:"bg-design-background border-design-border rounded-md overflow-hidden z-50 min-w-[120px] shadow-lg backdrop-blur-sm",children:n.map((e=>s.jsx(pt,{value:e.value,className:"text-design-foreground hover:bg-design-muted/50 focus:bg-design-muted/50 text-xs cursor-pointer transition-colors",children:e.label},e.value)))})]})})}function yt({currentView:e,currentUrl:a,client:r,onViewChange:n}){const i=t.useCallback((e=>{n?n(e):"undefined"!=typeof window&&requestAnimationFrame((()=>{const t=window.location.pathname,s=new URLSearchParams(window.location.search);s.set("view",e),window.location.href=`${t}?${s.toString()}`}))}),[n]);return s.jsxs("div",{className:"flex items-center overflow-hidden border border-design-border/40 bg-design-background/60 rounded-md flex-shrink-0 backdrop-blur-sm",children:[s.jsx("button",{onClick:()=>i("grid"),className:"flex items-center justify-center w-7 h-7 transition-all duration-200 "+("grid"===e?"bg-design-primary text-white shadow-sm":"text-design-muted-foreground hover:bg-design-muted/50 hover:text-design-foreground"),"aria-label":"Grid view",title:"Grid view",children:s.jsx(ae,{size:10,className:"flex-shrink-0"})}),s.jsx("button",{onClick:()=>i("list"),className:"hidden sm:flex items-center justify-center w-7 h-7 transition-all duration-200 "+("list"===e?"bg-design-primary text-white shadow-sm":"text-design-muted-foreground hover:bg-design-muted/50 hover:text-design-foreground"),"aria-label":"List view",title:"List view",children:s.jsx(ne,{size:10,className:"flex-shrink-0"})})]})}const wt=({message:e,linkText:a,linkUrl:r,dismissible:n=!0,onDismiss:i,variant:o="default",className:l,...d})=>{const[c,u]=t.useState(!0);t.useEffect((()=>{"true"===localStorage.getItem("noticeBarDismissed")&&u(!1)}),[]);return c?s.jsx("div",{className:V("w-full py-1 px-2 text-center text-[12px] h-[28px] flex items-center justify-center","default"===o?"bg-[#f4efefdb] text-gray-600 dark:bg-[#0f1011] dark:text-gray-300":"bg-[#0f1011] text-gray-300",l),role:"alert",...d,children:s.jsxs("div",{className:"container mx-auto flex items-center justify-center gap-2 h-full",children:[s.jsx("span",{className:"block min-h-[16px]",children:e}),a&&r&&s.jsx("a",{href:r,className:V("font-medium hover:underline whitespace-nowrap","default"===o?"text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300":"text-blue-400 hover:text-blue-300"),children:a}),n&&s.jsx("button",{onClick:()=>{u(!1),localStorage.setItem("noticeBarDismissed","true"),i&&i()},className:"ml-2 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 flex-shrink-0","aria-label":"Dismiss",children:s.jsx(ye,{size:14})})]})}):null};function vt({placeholder:e="Search deals...",className:a="",id:r="search-box"}){const[n,i]=t.useState("");return s.jsxs("form",{onSubmit:e=>{if(e.preventDefault(),n.trim().length<2)return;const t=n.toLowerCase().trim(),s=new URL(window.location.href);s.searchParams.delete("search"),s.searchParams.delete("q"),s.searchParams.delete("brands"),s.searchParams.delete("categories");const a=["geekvape","voopoo","smok","vaporesso","aspire"].find((e=>t.includes(e))),r={"e-juice":"3","e-liquid":"3",liquid:"3",disposable:"1",disposables:"1",puff:"1",pod:"2",pods:"2"},i=Object.keys(r).find((e=>t.includes(e)));a?s.searchParams.set("search",a):i?s.searchParams.set("categories",r[i]):s.searchParams.set("search",n),window.location.href=s.toString()},className:`relative flex-1 lg:flex-none ${a}`,children:[s.jsx("label",{htmlFor:r,className:"sr-only",children:e}),s.jsx("input",{type:"search",id:r,name:r,value:n,onChange:e=>i(e.target.value),placeholder:e,autoComplete:"off",className:"w-full h-7 pl-6 pr-2 text-xs bg-white dark:bg-black text-design-foreground border border-design-border/40 rounded-md focus:outline-none focus:ring-1 focus:ring-design-primary/30 focus:border-design-primary/50 transition-all duration-200 placeholder:text-design-muted-foreground/60"}),s.jsx("svg",{className:"absolute left-1.5 top-1/2 transform -translate-y-1/2 w-2.5 h-2.5 text-design-muted-foreground/60",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",children:s.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:"2",d:"m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})})]})}const jt=({src:e,alt:a,width:r,height:n,className:i="",loading:o="lazy",decoding:l="async",fetchpriority:d="auto",style:c,sizes:u,fallbackSrc:m="/placeholder-image.svg",onLoad:g,onError:h})=>{const[p,x]=t.useState(!1),[f,b]=t.useState(!1);if(!e)return s.jsx("img",{src:m,alt:a,className:i,style:c,width:r,height:n});const y=e.replace(/%20/g,"-").replace(/ /g,"-"),w=(v=y)&&!v.endsWith(".svg")&&!v.endsWith(".webp")&&/\.(jpe?g|png|gif)$/i.test(v)?function(e){if(!e)return"";if(e.endsWith(".svg"))return e;if(e.endsWith(".webp"))return e;const t=e.lastIndexOf("/"),s=-1!==t?e.substring(0,t+1):"",a=-1!==t?e.substring(t+1):e;return/\.(jpe?g|png|gif)$/i.test(a)?s+a.replace(/\.(jpe?g|png|gif)$/i,".webp"):""}(y):null;var v;const j=w&&w.endsWith(".webp")&&w.length>0?w:null,k=j?`${j} 1x`:"";return s.jsxs("picture",{children:[j&&s.jsx("source",{srcSet:k,type:"image/webp"}),s.jsx("img",{src:y,alt:a,width:r,height:n,className:i,loading:o,decoding:l,sizes:u,style:{maxWidth:"100%",height:"auto",display:"block",...c},onError:e=>{x(!0),h&&h();e.target.src=m},onLoad:()=>{b(!0),g&&g()},...d?{fetchpriority:d}:{}})]})},kt={dealCard:{sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",widths:[320,480,640,800],isLCP:!0},brandLogo:{sizes:"(max-width: 640px) 80px, 128px",widths:[80,128,256],isLCP:!1},heroImage:{sizes:"100vw",widths:[640,960,1280,1920],isLCP:!0},merchantLogo:{sizes:"(max-width: 640px) 60px, 100px",widths:[60,100,200],isLCP:!1},categoryImage:{sizes:"(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw",widths:[320,480,640],isLCP:!1}},Nt=({src:e,alt:a,preset:r="custom",sizes:n,widths:i,width:o,height:l,className:d="",loading:c="lazy",priority:u=!1,fallbackSrc:m="/placeholder-image.svg",fallbackContent:g,onLoad:h,objectFit:p="contain"})=>{const[x,f]=t.useState(!1),[b,y]=t.useState(!1),[w,v]=t.useState(!0),j=u||"custom"!==r&&kt[r]?.isLCP?"eager":c;return x&&g?s.jsx(s.Fragment,{children:g}):s.jsxs("div",{className:"relative w-full h-full",children:[w&&!b&&s.jsx("div",{className:"absolute inset-0 bg-design-muted"}),!x&&s.jsx("img",{src:e||m,alt:a,width:o,height:l,className:`${d} ${"contain"===p?"object-contain":"cover"===p?"object-cover":`object-${p}`}`,loading:j,onError:e=>{const t=e.target;t.src!==m&&(t.src=m,f(!0),v(!1))},onLoad:()=>{y(!0),v(!1),h&&h()},style:{maxWidth:"100%",maxHeight:"100%",display:"block",margin:"0 auto"}}),x&&g&&s.jsx("div",{className:"absolute inset-0 flex items-center justify-center",children:g}),x&&!g&&s.jsx("img",{src:m,alt:a,width:o,height:l,className:d,style:{maxWidth:"100%",maxHeight:"100%",display:"block",margin:"0 auto"}})]})},_t=e.forwardRef((({size:e="lg",padded:t=!0,className:a,children:r,...n},i)=>{const o={sm:"design-container-sm",md:"design-container-md",lg:"design-container-lg",xl:"design-container",full:"w-full"}[e];return s.jsx("div",{ref:i,className:V(o,t&&"px-4 sm:px-6 lg:px-8",a),...n,children:r})}));_t.displayName="Container";e.forwardRef((({size:e="md",containerSize:t="lg",background:a="default",bordered:r=!1,container:n=!0,className:i,children:o,...l},d)=>{const c={sm:"design-section-sm",md:"design-section",lg:"design-section-lg"}[e],u={default:"bg-design-background",muted:"bg-design-muted",primary:"bg-design-primary text-design-on-primary",secondary:"bg-design-secondary text-design-on-secondary"}[a],m=n?s.jsx(_t,{size:t,children:o}):o;return s.jsx("section",{ref:d,className:V(c,u,r&&"border-y border-design-border",i),...l,children:m})})).displayName="Section";const Ct=e=>{const t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select();let s=!1;try{s=document.execCommand("copy"),s?a.success("Coupon code copied to clipboard!"):a.error("Failed to copy code")}catch(r){a.error("Failed to copy code")}return document.body.removeChild(t),s},St=Object.freeze(Object.defineProperty({__proto__:null,DealCard:({deal:r,isRevealed:n=!1,onShowCouponModal:i,className:o,priority:l=!1,...d})=>{const[c,u]=t.useState(n),m=t.useRef(null),g=t.useMemo((()=>$e(3,r.id)),[r.id]),h=t.useMemo((()=>Re(r.id)),[r.id]),p=h.timeAgo,x=h.count,f=r.deal_end_date?Math.ceil((new Date(r.deal_end_date).getTime()-(new Date).getTime())/864e5):null,b=null!==f&&f>0&&f<=7,y=null!==f&&f<=0,w=(()=>{const e=e=>{if(!e||"string"!=typeof e||""===e.trim()||"YOUR_LOGO_URL"===e)return null;const t=e.trim();return t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:`/${t}`},t=e(r.imagebig_url)||e(r.image_url)||e(r.imagesmall_url);if(t)return t;const s=e(r.brands?.logo_url)||e(r.brand_logo_url);if(s)return s;const a=e(r.merchants?.logo_url)||e(r.merchant_logo_url);return a||"/placeholder-image.svg"})(),v=e.useCallback((()=>{requestAnimationFrame((()=>{if(u(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("clickedDeals")||"{}"),s=t[r.id],n=JSON.parse(localStorage.getItem("revealedCodes")||"{}");if(n[r.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(n)),t[r.id]=!0,localStorage.setItem("clickedDeals",JSON.stringify(t)),r.coupon_code)try{navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(r.coupon_code||"NO CODE REQUIRED").then((()=>{a.success("Coupon code copied to clipboard!")})).catch((e=>{Ct(r.coupon_code||"NO CODE REQUIRED")})):Ct(r.coupon_code||"NO CODE REQUIRED")}catch(e){a.info(`Your code is: ${r.coupon_code}`,{description:"Please copy it manually"})}const o=new URL(window.location.href).searchParams.get("view")||"grid",l=new URL(window.location.href);l.searchParams.set("dealId",r.id.toString()),l.searchParams.set("showPopup","true"),l.searchParams.set("view",o),setTimeout((()=>{window.open(l.toString(),"_blank"),i&&i(r),!s&&r.tracking_url&&(window.location.href=r.tracking_url)}),50)}}))}),[r,i]),j=e.useCallback((e=>{e.stopPropagation(),requestAnimationFrame((()=>{if(u(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("clickedDeals")||"{}"),s=t[r.id],n=JSON.parse(localStorage.getItem("revealedCodes")||"{}");if(n[r.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(n)),t[r.id]=!0,localStorage.setItem("clickedDeals",JSON.stringify(t)),r.coupon_code)try{navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(r.coupon_code||"NO CODE REQUIRED").then((()=>{a.success("Coupon code copied to clipboard!")})).catch((e=>{Ct(r.coupon_code||"NO CODE REQUIRED")})):Ct(r.coupon_code||"NO CODE REQUIRED")}catch(e){a.info(`Your code is: ${r.coupon_code}`,{description:"Please copy it manually"})}const o=new URL(window.location.href).searchParams.get("view")||"grid",l=new URL(window.location.href);l.searchParams.set("dealId",r.id.toString()),l.searchParams.set("showPopup","true"),l.searchParams.set("view",o),setTimeout((()=>{window.open(l.toString(),"_blank"),i&&i(r),!s&&r.tracking_url&&(window.location.href=r.tracking_url)}),50)}}))}),[r,i]);return s.jsxs(Ce,{ref:m,variant:"default",interactive:!0,glowEffect:!0,className:V("deal-card relative overflow-hidden cursor-pointer w-full flex flex-col",o),style:{borderRadius:"25px"},onClick:v,"aria-label":`Deal: ${r.title}`,...d,children:[b&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full",children:1===f?"Expires today":`Expires in ${f} days`}),y&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full",children:"Expired"}),s.jsx("div",{className:"w-full p-4",children:s.jsx("div",{className:"flex justify-center",children:s.jsx("div",{className:"relative overflow-hidden rounded-lg flex items-center justify-center w-full aspect-square min-h-[280px] bg-white dark:bg-white",style:{boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.05)",padding:"8px"},children:s.jsx(De,{src:w,alt:r.title||"Deal image",width:400,height:400,priority:l,className:"",fallbackSrc:"/placeholder-image.svg"})})})}),s.jsxs("div",{className:"px-4 pb-4 flex-1",children:[s.jsxs("div",{className:"flex justify-between items-center mb-2",children:[s.jsxs("div",{className:"text-xl font-bold text-design-foreground",children:[r.discount?`${r.discount}% Off`:r.price?`${r.currency||"$"}${r.price}`:"20% Off",b&&s.jsx("span",{className:"ml-2 text-xs font-normal text-design-warning animate-pulse",children:1===f?"Ends today!":`Ends in ${f} days!`})]}),s.jsxs("div",{className:"flex items-center gap-2",children:[s.jsx("div",{className:"deal-coupon-code",children:r.coupon_code?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:c?"":"blur-[4px] select-none",style:{color:"var(--design-foreground)",fontWeight:"bold"},children:r.coupon_code}),s.jsx("span",{className:"sr-only",children:c?`Coupon code: ${r.coupon_code}`:"Click to reveal coupon code"})]}):"NO CODE"}),s.jsxs("div",{className:"deal-card-actions",children:[s.jsx(_e,{dealId:r.id.toString(),className:"heart-bookmark-button"}),s.jsx("button",{className:"eye-button p-1 rounded-full hover:bg-design-muted transition-colors relative z-10",onClick:e=>{e.stopPropagation(),window.open(`/deal/${r.id}`,"_blank")},title:"View deal details",style:{marginRight:"2px"},children:s.jsx(te,{size:18,className:"text-design-muted-foreground hover:text-design-foreground transition-colors"})})]})]})]}),s.jsxs("div",{className:"text-sm text-design-muted-foreground mb-1 flex items-center",children:[s.jsx("span",{className:"font-medium",children:r.merchants?.name||r.brands?.name||"Brand"}),r.verified&&s.jsxs("span",{className:"c-verified-badge verified-badge ml-2",children:[s.jsx("img",{src:"/Vapehybrid light icon.svg",alt:"Verified",className:"dark:hidden"}),s.jsx("img",{src:"/Vapehybrid dark icon.svg",alt:"Verified",className:"hidden dark:block"}),s.jsx("span",{className:"text-design-primary dark:text-design-primary text-xs",children:"Verified"})]}),void 0!==r.success_rate&&s.jsxs("span",{className:"c-success-rate-badge success-rate-badge ml-2 "+(r.success_rate>=80?"high":r.success_rate>=50?"medium":"low"),children:[s.jsx(fe,{size:10}),s.jsxs("span",{className:"text-design-primary dark:text-design-primary text-xs",children:[Math.round(r.success_rate||85),"%"]})]})]}),s.jsx("h3",{className:"deal-card-title line-clamp-2 mb-2 text-base font-semibold",children:r.cleaned_title&&"null"!==r.cleaned_title?r.cleaned_title:r.title}),s.jsxs("div",{className:"flex items-center text-xs text-design-muted-foreground mb-3",children:[s.jsx("div",{className:"flex -space-x-2",children:g.slice(0,x||r.usage_count||3).map(((e,t)=>s.jsx("div",{className:"relative group","data-tooltip-id":`staff-tooltip-${r.id}-${t}`,"data-tooltip-content":e.name,children:s.jsx("div",{className:"w-6 h-6 rounded-full border-2 border-design-card overflow-hidden flex-shrink-0 transition-all duration-200 group-hover:-translate-y-0.5",style:{backgroundColor:e.color},children:e.webpPath&&e.imagePath?s.jsxs("picture",{children:[s.jsx("source",{srcSet:e.webpPath,type:"image/webp"}),s.jsx("img",{src:e.imagePath,alt:`Staff member ${e.name}`,className:"w-full h-full object-cover",onError:t=>{const s=t.target;s.style.display="none";const a=document.createElement("div");a.className="w-full h-full flex items-center justify-center text-white text-xs font-bold",a.textContent=e.initials,s.parentNode?.replaceChild(a,s)},loading:"lazy",width:24,height:24})]}):s.jsx("div",{className:"w-full h-full flex items-center justify-center text-white text-xs font-bold",children:e.initials})})},t)))}),s.jsxs("span",{className:"ml-2",children:["Verified ",p," by ",x||r.usage_count||3," staffer",(x||r.usage_count||3)>1?"s":""]})]}),b&&s.jsxs("div",{className:"mb-3 text-xs text-design-warning px-2 py-1 rounded flex items-center",children:[s.jsx("span",{className:"animate-pulse mr-1",children:"⏱"}),s.jsxs("span",{children:["Limited time offer! ",1===f?"Ends today":`Ends in ${f} days`]})]}),s.jsx("div",{className:"flex justify-center w-full",children:s.jsx("button",{className:"copy-code-button",onClick:e=>{e.stopPropagation(),j(e)},"aria-label":`Copy coupon code ${r.coupon_code||"for this deal"}`,children:"Copy Code"})})]})]})}},Symbol.toStringTag,{value:"Module"})),Et=e=>{const t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select();let s=!1;try{s=document.execCommand("copy"),s?a.success("Coupon code copied to clipboard!"):a.error("Failed to copy code")}catch(r){a.error("Failed to copy code")}return document.body.removeChild(t),s},Lt=Object.freeze(Object.defineProperty({__proto__:null,DealListCard:({deal:e,isRevealed:r=!1,onShowCouponModal:n,className:i,...o})=>{const[l,d]=t.useState(r),c=t.useRef(null),u=t.useMemo((()=>$e(3)),[]),m=t.useMemo((()=>Re(e.id)),[e.id]),g=m.timeAgo,h=m.count,p=e.deal_end_date?Math.ceil((new Date(e.deal_end_date).getTime()-(new Date).getTime())/864e5):null,x=null!==p&&p>0&&p<=7,f=null!==p&&p<=0,b=(()=>{const t=e=>{if(!e||"string"!=typeof e||""===e.trim()||"YOUR_LOGO_URL"===e)return null;const t=e.trim();return t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:`/${t}`},s=t(e.imagebig_url)||t(e.image_url)||t(e.imagesmall_url);if(s)return s;const a=t(e.brands?.logo_url)||t(e.brand_logo_url);if(a)return a;const r=t(e.merchants?.logo_url)||t(e.merchant_logo_url);return r||"/placeholder-image.svg"})(),y=t=>{if(t.stopPropagation(),d(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("revealedCodes")||"{}");t[e.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(t))}if(e.coupon_code)try{navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(e.coupon_code||"NO CODE REQUIRED").then((()=>{a.success("Coupon code copied to clipboard!")})).catch((t=>{Et(e.coupon_code||"NO CODE REQUIRED")})):Et(e.coupon_code||"NO CODE REQUIRED")}catch(i){a.info(`Your code is: ${e.coupon_code}`,{description:"Please copy it manually"})}const s=new URL(window.location.href).searchParams.get("view")||"list",r=new URL(window.location.href);r.searchParams.set("dealId",e.id.toString()),r.searchParams.set("showPopup","true"),r.searchParams.set("view",s),window.open(r.toString(),"_blank"),n&&n(e),e.tracking_url&&(window.location.href=e.tracking_url)};return s.jsxs(Ce,{ref:c,variant:"default",interactive:!0,glowEffect:!0,className:V("deal-list-card relative cursor-pointer w-full flex flex-col sm:flex-row items-center justify-between",i),style:{padding:"0.5rem",borderRadius:"25px"},onClick:()=>{if(d(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("revealedCodes")||"{}");t[e.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(t))}if(e.coupon_code)try{navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(e.coupon_code||"NO CODE REQUIRED").then((()=>{a.success("Coupon code copied to clipboard!")})).catch((t=>{Et(e.coupon_code||"NO CODE REQUIRED")})):Et(e.coupon_code||"NO CODE REQUIRED")}catch(r){a.info(`Your code is: ${e.coupon_code}`,{description:"Please copy it manually"})}const t=new URL(window.location.href).searchParams.get("view")||"list",s=new URL(window.location.href);s.searchParams.set("dealId",e.id.toString()),s.searchParams.set("showPopup","true"),s.searchParams.set("view",t),window.open(s.toString(),"_blank"),n&&n(e),e.tracking_url&&(window.location.href=e.tracking_url)},"aria-label":`Deal: ${e.title}`,...o,children:[x&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full",children:1===p?"Expires today":`Expires in ${p} days`}),f&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full",children:"Expired"}),s.jsx("div",{className:"w-full sm:w-[200px] py-2 sm:py-0",children:s.jsx("div",{className:"flex justify-center h-full",children:s.jsx("div",{className:"relative flex items-center justify-center w-full h-full aspect-square max-w-[160px] sm:min-w-[160px] sm:max-w-[180px] sm:max-h-[180px]",style:{background:"transparent"},children:s.jsx(De,{src:b,alt:e.title||"Deal image",width:180,height:180,className:"w-full h-full mx-auto",fallbackSrc:"/placeholder-image.svg"})})})}),s.jsx("div",{className:"flex-1 p-3 w-full",children:s.jsxs("div",{className:"h-full flex flex-col justify-between",children:[s.jsx("div",{className:"mb-2",children:s.jsxs("div",{className:"text-xl font-bold text-design-foreground",children:[e.discount?`${e.discount}% Off`:e.price?`${e.currency||"$"}${e.price}`:"20% Off",x&&s.jsx("span",{className:"ml-2 text-xs font-normal text-design-warning animate-pulse",children:1===p?"Ends today!":`Ends in ${p} days!`})]})}),s.jsxs("div",{className:"text-sm text-design-muted-foreground mb-1 flex items-center",children:[e.merchants?.name||e.brands?.name||"Brand",e.verified&&s.jsxs("span",{className:"c-verified-badge verified-badge ml-2",children:[s.jsx("img",{src:"/Vapehybrid light icon.svg",alt:"Verified",className:"dark:hidden"}),s.jsx("img",{src:"/Vapehybrid dark icon.svg",alt:"Verified",className:"hidden dark:block"}),s.jsx("span",{className:"text-design-primary dark:text-design-primary",children:"Verified"})]}),void 0!==e.success_rate&&s.jsxs("span",{className:"c-success-rate-badge success-rate-badge ml-2 "+(e.success_rate>=80?"high":e.success_rate>=50?"medium":"low"),children:[s.jsx(fe,{size:10}),s.jsxs("span",{className:"text-design-primary dark:text-design-primary",children:[Math.round(e.success_rate||85),"% success"]})]})]}),s.jsx("h3",{className:"deal-card-title line-clamp-2 mb-2 text-base font-semibold",children:e.cleaned_title&&"null"!==e.cleaned_title?e.cleaned_title:e.title}),s.jsx("div",{className:"flex flex-1",children:s.jsxs("div",{className:"flex-1",children:[e.description&&s.jsx("p",{className:"text-sm text-design-muted-foreground mb-3 line-clamp-2",children:e.description}),s.jsxs("div",{className:"flex items-center text-xs text-design-muted-foreground mb-3",children:[s.jsx("div",{className:"c-avatar-group user-avatar-group",children:u.slice(0,h||e.usage_count||3).map(((e,t)=>s.jsx("div",{className:"c-avatar c-avatar--xs user-avatar",style:{backgroundColor:e.color},children:s.jsxs("picture",{children:[e.webpPath&&s.jsx("source",{srcSet:e.webpPath,type:"image/webp"}),e.imagePath&&s.jsx("img",{src:e.imagePath,alt:"User",className:"w-full h-full object-cover",onError:t=>{t.target.style.display="none",t.target.parentElement.parentElement.textContent=e.initials}})]})},t)))}),s.jsxs("span",{children:["Verified ",g," by ",h||e.usage_count||3," staffer",(h||e.usage_count||3)>1?"s":""]})]}),x&&s.jsxs("div",{className:"mb-3 text-xs text-design-warning px-2 py-1 rounded flex items-center",children:[s.jsx("span",{className:"animate-pulse mr-1",children:"⏱"}),s.jsxs("span",{children:["Limited time offer! ",1===p?"Ends today":`Ends in ${p} days`]})]})]})})]})}),s.jsxs("div",{className:"flex flex-col items-center justify-center px-4 sm:pr-4 sm:pl-0 gap-2 w-full sm:min-w-[150px] sm:w-auto py-3 sm:py-0",children:[s.jsx("div",{className:"deal-coupon-code relative cursor-pointer px-3 py-1 w-full text-center",onClick:e=>{e.stopPropagation(),y(e)},title:l?"Click to copy":"Click to reveal code",children:e.coupon_code?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"text-base font-bold "+(l?"":"blur-[4px] select-none"),style:{color:"var(--design-foreground)"},children:e.coupon_code}),s.jsx("span",{className:"sr-only",children:l?`Coupon code: ${e.coupon_code}`:"Click to reveal coupon code"})]}):"NO CODE"}),s.jsx("button",{className:"copy-code-button",onClick:e=>{e.stopPropagation(),y(e)},"aria-label":`Copy coupon code ${e.coupon_code||"for this deal"}`,children:"Copy Code"}),s.jsxs("div",{className:"deal-card-actions",children:[s.jsx(_e,{dealId:e.id.toString(),className:"heart-bookmark-button"}),s.jsx("button",{className:"eye-button p-1 rounded-full hover:bg-design-muted transition-colors relative z-10",onClick:t=>{t.stopPropagation(),window.open(`/deal/${e.id}`,"_blank")},title:"View deal details",style:{marginRight:"2px"},children:s.jsx(te,{size:18,className:"text-design-muted-foreground hover:text-design-foreground transition-colors"})})]})]})]})}},Symbol.toStringTag,{value:"Module"})),$t=e=>{const t=document.createElement("textarea");t.value=e,t.style.position="fixed",t.style.left="-999999px",t.style.top="-999999px",document.body.appendChild(t),t.focus(),t.select();let s=!1;try{s=document.execCommand("copy"),s?a.success("Coupon code copied to clipboard!"):a.error("Failed to copy code")}catch(r){a.error("Failed to copy code")}return document.body.removeChild(t),s},Rt=({deal:r,className:n,isRevealed:i=!1,priority:o=!1,...l})=>{const[d,c]=t.useState(i),u=t.useRef(null),m=t.useMemo((()=>$e(5,r.id)),[r.id]),g=t.useMemo((()=>Re(r.id)),[r.id]),h=g.timeAgo,p=r.usage_count||g.count,x=r.deal_end_date?Math.ceil((new Date(r.deal_end_date).getTime()-(new Date).getTime())/864e5):null,f=null!==x&&x>0&&x<=7,b=t.useMemo((()=>{const e=Me(r.imagebig_url)||Me(r.image_url)||Me(r.imagesmall_url);if(e)return e;const t=Me(r.brands?.logo_url)||Me(r.brand_logo_url);if(t)return t;const s=Me(r.merchants?.logo_url)||Me(r.merchant_logo_url);return s||"/placeholder-image.svg"}),[r.imagebig_url,r.image_url,r.imagesmall_url,r.brands?.logo_url,r.brand_logo_url,r.merchants?.logo_url,r.merchant_logo_url]),y=e.useCallback((()=>{requestAnimationFrame((()=>{if(c(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("clickedDeals")||"{}"),s=JSON.parse(localStorage.getItem("revealedCodes")||"{}");if(s[r.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(s)),t[r.id]=!0,localStorage.setItem("clickedDeals",JSON.stringify(t)),r.coupon_code)try{navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(r.coupon_code||"NO CODE REQUIRED").then((()=>{a.success("Coupon code copied to clipboard!")})).catch((e=>{$t(r.coupon_code||"NO CODE REQUIRED")})):$t(r.coupon_code||"NO CODE REQUIRED")}catch(e){a.info(`Your code is: ${r.coupon_code}`,{description:"Please copy it manually"})}const n=new URL(window.location.href).searchParams.get("view")||"grid",i=new URL(window.location.href);i.searchParams.set("dealId",r.id.toString()),i.searchParams.set("showPopup","true"),i.searchParams.set("solidBg","true"),i.searchParams.set("view",n),window.open(i.toString(),"_blank"),r.tracking_url?window.location.href=r.tracking_url:window.location.href=`/go/${r.id}`}}))}),[r,l.onShowCouponModal]),w=e.useCallback((e=>{e.stopPropagation(),requestAnimationFrame((()=>{if(c(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("clickedDeals")||"{}"),s=JSON.parse(localStorage.getItem("revealedCodes")||"{}");if(s[r.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(s)),t[r.id]=!0,localStorage.setItem("clickedDeals",JSON.stringify(t)),r.coupon_code)try{navigator.clipboard&&navigator.clipboard.writeText?navigator.clipboard.writeText(r.coupon_code||"NO CODE REQUIRED").then((()=>{a.success("Coupon code copied to clipboard!")})).catch((e=>{$t(r.coupon_code||"NO CODE REQUIRED")})):$t(r.coupon_code||"NO CODE REQUIRED")}catch(e){a.info(`Your code is: ${r.coupon_code}`,{description:"Please copy it manually"})}const n=new URL(window.location.href).searchParams.get("view")||"grid",i=new URL(window.location.href);i.searchParams.set("dealId",r.id.toString()),i.searchParams.set("showPopup","true"),i.searchParams.set("solidBg","true"),i.searchParams.set("view",n),window.open(i.toString(),"_blank"),r.tracking_url?window.location.href=r.tracking_url:window.location.href=`/go/${r.id}`}}))}),[r,l.onShowCouponModal]);return s.jsxs(Ce,{ref:u,variant:"default",interactive:!0,glowEffect:!0,className:V("improved-deal-card deal-card relative overflow-hidden cursor-pointer w-full flex flex-col max-h-[550px] transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-design-primary/50 dark:hover:border-design-primary/50","border-black/15 dark:border-white/15",n),style:{borderRadius:"25px",borderWidth:"1.5px"},"data-theme-border":"true",onClick:y,"aria-label":`Deal: ${r.title}`,role:"button",tabIndex:0,...l,children:[s.jsx("div",{className:"w-full p-3",style:{height:"52%"},children:s.jsx("div",{className:"flex justify-center h-full",children:s.jsx("div",{className:"relative overflow-hidden rounded-lg flex items-center justify-center w-full h-full image-container bg-white dark:bg-white",style:{maxHeight:"calc(550px * 0.52 - 24px)",aspectRatio:"1/1",boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.05)"},children:s.jsx(De,{src:b,alt:r.title||"Deal image",width:400,height:400,priority:o,fetchpriority:o?"high":"auto",className:"w-full h-full mx-auto",fallbackSrc:"/placeholder-image.svg",index:0})})})}),s.jsxs("div",{className:"px-3 pb-0 flex-1 flex flex-col",style:{height:"48%",maxHeight:"calc(550px * 0.48 - 24px)"},children:[s.jsxs("div",{className:"flex justify-between items-center mb-2",children:[s.jsx("div",{className:"relative",children:s.jsxs("div",{className:"text-xl font-bold text-design-foreground",children:[r.discount?s.jsxs("span",{className:"text-green-600 dark:text-green-500",children:["Save ",r.discount,"% 🔥"]}):r.price?`${r.currency||"$"}${r.price}`:"20% Off",f&&s.jsx("span",{className:"ml-2 text-xs font-normal text-design-warning animate-pulse",children:1===x?"Ends today!":`Ends in ${x} days!`})]})}),s.jsx("div",{className:"flex items-center ml-1",children:s.jsx("div",{className:"deal-coupon-code text-xs bg-design-muted px-2 py-1 rounded-full border border-design-muted-foreground/10 relative overflow-hidden",children:r.coupon_code?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"transition-all duration-300 "+(d?"blur-none opacity-100":"blur-[3px] select-none"),"aria-hidden":!d,children:r.coupon_code}),d&&s.jsx("span",{className:"absolute inset-0 bg-design-primary/10 animate-reveal-sweep","aria-hidden":"true"})]}):"NO CODE"})})]}),s.jsxs("div",{className:"text-xs text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1",children:[s.jsx("span",{className:"font-medium truncate max-w-[120px]",title:r.merchants?.name||r.brands?.name||"Brand",children:r.merchants?.name||r.brands?.name||"Brand"}),r.verified&&s.jsxs("span",{className:"c-verified-badge verified-badge ml-2",children:[s.jsx("img",{src:"/Vapehybrid light icon.svg",alt:"Verified",className:"dark:hidden"}),s.jsx("img",{src:"/Vapehybrid dark icon.svg",alt:"Verified",className:"hidden dark:block"}),s.jsx("span",{className:"text-design-primary dark:text-design-primary text-xs",children:"Verified"})]}),void 0!==r.success_rate&&s.jsxs("span",{className:"ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full "+(r.success_rate>=90?"bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground":r.success_rate>=70?"bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground":"bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"),title:`${Math.round(r.success_rate||85)}% success rate`,children:[s.jsx(fe,{size:10,className:"mr-1"}),Math.round(r.success_rate||85),"%"]}),f&&s.jsxs("span",{className:"ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full "+(1===x?"bg-design-destructive/15 text-design-destructive dark:bg-design-destructive/25 dark:text-design-destructive-foreground":"bg-design-warning/15 text-design-warning dark:bg-design-warning/25 dark:text-design-warning-foreground"),title:`Expires in ${x} ${1===x?"day":"days"}`,children:[s.jsx("svg",{className:"w-3 h-3 mr-1",viewBox:"0 0 24 24",fill:"none",xmlns:"http://www.w3.org/2000/svg",children:s.jsx("path",{d:"M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round"})}),1===x?"Ends today":`${x}d left`]})]}),s.jsx("h3",{className:"deal-card-title line-clamp-2 mb-2 text-sm font-semibold",children:r.cleaned_title&&"null"!==r.cleaned_title?r.cleaned_title:r.title}),s.jsxs("div",{className:"flex items-center text-xs text-design-muted-foreground mb-1",children:[s.jsxs("div",{className:"c-avatar-group user-avatar-group flex relative",style:{marginRight:"0.35rem"},children:[m.slice(0,Math.min(3,p||3)).map(((e,t)=>s.jsxs("div",{className:"c-avatar c-avatar--xs user-avatar relative transition-transform hover:scale-110 hover:z-10 overflow-hidden",style:{width:"20px",height:"20px",marginLeft:t>0?"-8px":"0",borderRadius:"50%",border:"1.5px solid white",boxShadow:"0 1px 2px rgba(0,0,0,0.1)",zIndex:3-t,backgroundColor:e.color},title:`Staff member: ${e.name}`,children:[e.webpPath&&e.imagePath?s.jsxs("picture",{children:[s.jsx("source",{srcSet:e.webpPath,type:"image/webp"}),s.jsx("img",{src:e.imagePath,alt:e.initials,className:"w-full h-full object-cover",onError:e=>{const t=e.target;t.style.display="none";const s=t.parentElement?.parentElement;s&&s.classList.add("fallback-active")}})]}):s.jsx("div",{className:"w-full h-full flex items-center justify-center text-white font-bold",children:s.jsx("span",{style:{fontSize:"8px"},children:e.initials})}),s.jsx("div",{className:"fallback-content absolute inset-0 flex items-center justify-center text-white font-bold opacity-0",children:s.jsx("span",{style:{fontSize:"8px"},children:e.initials})})]},t))),p>3&&s.jsxs("div",{className:"c-avatar c-avatar--xs user-avatar flex items-center justify-center bg-design-muted-foreground/20",style:{width:"20px",height:"20px",marginLeft:"-8px",borderRadius:"50%",border:"1.5px solid white",boxShadow:"0 1px 2px rgba(0,0,0,0.1)",fontSize:"8px"},title:p-3+" more staff members",children:["+",p-3]})]}),s.jsxs("span",{children:["Verified ",s.jsx("time",{dateTime:r.last_verified_at,title:new Date(r.last_verified_at||"").toLocaleString(),children:h})," by ",p||3," staffer",(p||3)>1?"s":""]})]}),s.jsx("div",{className:"flex-grow"}),s.jsxs("div",{className:"flex justify-between items-center w-full mt-auto mb-2 px-2 pt-2 border-t border-design-muted border-opacity-20",role:"group","aria-label":"Deal actions",style:{touchAction:"manipulation"},children:[s.jsxs("button",{className:"eye-button p-2 rounded-full hover:bg-design-muted transition-colors relative z-10",onClick:e=>{e.stopPropagation(),window.open(`/deal/${r.id}`,"_blank")},"aria-label":"View deal details",title:"View deal details",tabIndex:0,style:{minWidth:"36px",minHeight:"36px"},children:[s.jsx(te,{size:18,className:"text-design-muted-foreground hover:text-design-foreground transition-colors"}),s.jsx("span",{className:"sr-only",children:"View deal details"})]}),s.jsx("button",{className:"copy-code-button h-8 px-4 text-sm mx-2 transition-colors hover:bg-design-primary hover:text-white dark:hover:text-black focus:outline-none focus:ring-2 focus:ring-design-primary focus:ring-offset-2",onClick:e=>{e.preventDefault(),e.stopPropagation(),w(e)},"aria-label":"Copy Code",title:"Copy coupon code",tabIndex:0,children:"Copy Code"}),s.jsx(_e,{dealId:r.id.toString(),className:"heart-bookmark-button"})]})]})]})},Mt=Object.freeze(Object.defineProperty({__proto__:null,ImprovedDealCard:Rt,default:Rt},Symbol.toStringTag,{value:"Module"}));e.forwardRef((({name:e,logoUrl:t,description:a,dealCount:r,averageDiscount:n,onClick:i,className:o,...l},d)=>s.jsx(Ce,{ref:d,className:V("merchant-card",i&&"cursor-pointer",o),interactive:!!i,onClick:i,...l,children:s.jsxs("div",{className:"merchant-card-content",children:[t&&s.jsx("div",{className:"merchant-card-logo",children:s.jsx("img",{src:t,alt:e})}),s.jsxs("div",{className:"merchant-card-info",children:[s.jsx("h3",{className:"merchant-card-name",children:e}),a&&s.jsx("p",{className:"merchant-card-description",children:a}),s.jsxs("div",{className:"merchant-card-stats",children:[void 0!==r&&s.jsxs("div",{className:"merchant-card-stat",children:[s.jsx("span",{className:"merchant-card-stat-value",children:r}),s.jsx("span",{className:"merchant-card-stat-label",children:1===r?"Deal":"Deals"})]}),n&&s.jsxs("div",{className:"merchant-card-stat",children:[s.jsx("span",{className:"merchant-card-stat-value",children:n}),s.jsx("span",{className:"merchant-card-stat-label",children:"Avg. Discount"})]})]})]})]})}))).displayName="MerchantCard";e.forwardRef((({name:e,imageUrl:t,dealCount:a,onClick:r,className:n,...i},o)=>s.jsxs(Ce,{ref:o,className:V("category-card",r&&"cursor-pointer",n),interactive:!!r,onClick:r,...i,children:[t&&s.jsx("div",{className:"category-card-image",children:s.jsx("img",{src:t,alt:e})}),s.jsxs("div",{className:"category-card-info",children:[s.jsx("h3",{className:"category-card-name",children:e}),void 0!==a&&s.jsxs("div",{className:"category-card-count",children:[s.jsx("span",{children:a}),s.jsx("span",{children:1===a?"Deal":"Deals"})]})]})]}))).displayName="CategoryCard";e.forwardRef((({icon:e,variant:t="primary",size:a="md",round:r=!1,loading:n=!1,disabled:i=!1,className:o,...l},d)=>{const c={primary:"design-button-primary",secondary:"design-button-secondary",outline:"design-button-outline",ghost:"design-button-ghost",destructive:"design-button-destructive",accent:"design-button-accent",link:"design-button-link"}[t],u={xs:"design-button-sm p-1",sm:"design-button-sm p-1",md:"p-2",lg:"design-button-lg p-3",xl:"design-button-lg p-3",icon:"p-2"}[a];return s.jsx("button",{ref:d,className:V("design-button","design-icon-button",c,u,r&&"rounded-full",n&&"design-button-loading",i&&"opacity-50 cursor-not-allowed",o),disabled:i||n,...l,children:n?s.jsxs("svg",{className:"animate-spin h-5 w-5",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}):e})})).displayName="IconButton";e.forwardRef((({variant:e="primary",size:t="md",disabled:a=!1,leftIcon:r,rightIcon:n,className:i,children:o,...l},d)=>{const c={primary:"bg-primary text-white/90 dark:text-black/90 hover:text-white hover:bg-primary/90 shadow-lg",secondary:"bg-design-secondary text-white/90 dark:text-black/90 hover:text-white hover:bg-design-secondary/90 shadow-lg",outline:"border border-design-border bg-transparent text-design-foreground hover:bg-design-accent/5",ghost:"bg-transparent text-design-foreground hover:bg-design-accent/5",destructive:"bg-red-600 text-white/90 dark:text-white/90 hover:text-white hover:bg-red-700 shadow-lg"}[e],u={sm:"px-4 py-2 text-sm",md:"px-6 py-3 text-base",lg:"px-8 py-4 text-lg"}[t];return a?s.jsxs("span",{className:V("inline-flex items-center justify-center font-bold rounded-full transition-all duration-300",c,u,"opacity-50 cursor-not-allowed",i),children:[r&&s.jsx("span",{className:"mr-2",children:r}),o,n&&s.jsx("span",{className:"ml-2",children:n})]}):s.jsxs("a",{ref:d,className:V("inline-flex items-center justify-center font-bold rounded-full transition-all duration-300",c,u,i),...l,children:[r&&s.jsx("span",{className:"mr-2",children:r}),o,n&&s.jsx("span",{className:"ml-2",children:n})]})})).displayName="LinkButton";e.forwardRef((({error:e=!1,errorMessage:t,leftIcon:a,rightIcon:r,label:n,helperText:i,className:o,id:l,...d},c)=>{const u=l||`input-${Math.random().toString(36).substring(2,9)}`;return s.jsxs("div",{className:"design-form-group",children:[n&&s.jsx("label",{htmlFor:u,className:"design-label",children:n}),s.jsxs("div",{className:"design-input-wrapper",children:[a&&s.jsx("div",{className:"design-input-icon-left",children:a}),s.jsx("input",{ref:c,id:u,className:V("design-input",e&&"design-input-error",a&&"pl-10",r&&"pr-10",o),...d}),r&&s.jsx("div",{className:"design-input-icon-right",children:r})]}),e&&t&&s.jsx("div",{className:"design-input-error-message",children:t}),!e&&i&&s.jsx("div",{className:"design-input-helper-text",children:i})]})})).displayName="Input";t.forwardRef((({className:e,...t},a)=>s.jsx("textarea",{className:V("design-textarea",e),ref:a,...t}))).displayName="Textarea";e.forwardRef((({level:e=1,display:t=!1,centered:a=!1,className:r,children:n,...i},o)=>{const l=`h${e}`,d=t?`typography-display-${e<=2?e:1}`:`typography-h${e}`;return s.jsx(l,{ref:o,className:V(d,a&&"text-center",r),...i,children:n})})).displayName="Heading";e.forwardRef((({variant:e="body",color:t="default",truncate:a=!1,lineClamp:r,className:n,children:i,...o},l)=>{const d={body:"typography-body","body-large":"typography-body-large","body-small":"typography-body-small","body-xs":"typography-body-xs",caption:"typography-caption",overline:"typography-overline"}[e],c="default"!==t?{primary:"typography-primary",secondary:"typography-secondary",muted:"typography-muted",success:"typography-success",warning:"typography-warning",error:"typography-error"}[t]:"";return s.jsx("p",{ref:l,className:V(d,c,a&&"typography-truncate",r&&`typography-line-clamp-${r}`,n),...o,children:i})})).displayName="Text";e.forwardRef((({variant:e="default",external:t=!1,className:a,children:r,...n},i)=>{const o=t?{target:"_blank",rel:"noopener noreferrer"}:{};return s.jsxs("a",{ref:i,className:V("typography-link","muted"===e&&"typography-link-muted","hover-only"===e&&"typography-link-hover-only",a),...o,...n,children:[r,t&&s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"ml-1 inline-block",children:[s.jsx("path",{d:"M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"}),s.jsx("polyline",{points:"15 3 21 3 21 9"}),s.jsx("line",{x1:"10",y1:"14",x2:"21",y2:"3"})]})]})})).displayName="Link";e.forwardRef((({variant:e="info",title:t,icon:a,dismissible:r=!1,onDismiss:n,className:i,children:o,...l},d)=>{const c=a||{info:s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"12",y1:"16",x2:"12",y2:"12"}),s.jsx("line",{x1:"12",y1:"8",x2:"12.01",y2:"8"})]}),success:s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("path",{d:"M22 11.08V12a10 10 0 1 1-5.93-9.14"}),s.jsx("polyline",{points:"22 4 12 14.01 9 11.01"})]}),warning:s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("path",{d:"M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z"}),s.jsx("line",{x1:"12",y1:"9",x2:"12",y2:"13"}),s.jsx("line",{x1:"12",y1:"17",x2:"12.01",y2:"17"})]}),error:s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("line",{x1:"15",y1:"9",x2:"9",y2:"15"}),s.jsx("line",{x1:"9",y1:"9",x2:"15",y2:"15"})]})}[e];return s.jsxs("div",{ref:d,className:V("design-alert",`design-alert-${e}`,i),role:"alert",...l,children:[c&&s.jsx("div",{className:"design-alert-icon",children:c}),s.jsxs("div",{className:"design-alert-content",children:[t&&s.jsx("div",{className:"design-alert-title",children:t}),s.jsx("div",{className:"design-alert-message",children:o})]}),r&&n&&s.jsx("button",{className:"design-alert-dismiss",onClick:n,"aria-label":"Dismiss",children:s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("line",{x1:"18",y1:"6",x2:"6",y2:"18"}),s.jsx("line",{x1:"6",y1:"6",x2:"18",y2:"18"})]})})]})})).displayName="Alert";e.forwardRef((({variant:e="primary",className:t,children:a,...r},n)=>{const i={primary:"design-badge-primary",secondary:"design-badge-secondary",outline:"design-badge-outline",success:"design-badge-success",warning:"design-badge-warning",error:"design-badge-error"}[e];return s.jsx("span",{ref:n,className:V("design-badge",i,t),...r,children:a})})).displayName="Badge";e.forwardRef((({size:e="md",variant:t="primary",label:a="Loading...",className:r,...n},i)=>{const o={sm:"design-spinner-sm",md:"",lg:"design-spinner-lg"}[e],l={primary:"design-spinner-primary",secondary:"design-spinner-secondary",muted:"design-spinner-muted"}[t];return s.jsxs("div",{ref:i,className:V("design-spinner",o,l,r),role:"status","aria-label":a,...n,children:[s.jsxs("svg",{className:"animate-spin",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),s.jsx("span",{className:"sr-only",children:a})]})})).displayName="Spinner";e.forwardRef((({width:e,height:t,rounded:a=!0,animated:r=!0,className:n,style:i,...o},l)=>s.jsx("div",{ref:l,className:V("design-skeleton",r&&"animate-pulse",!0===a&&"rounded","full"===a&&"rounded-full",n),style:{width:void 0!==e?"number"==typeof e?`${e}px`:e:void 0,height:void 0!==t?"number"==typeof t?`${t}px`:t:void 0,...i},"aria-hidden":"true",...o}))).displayName="Skeleton";e.forwardRef((({orientation:e="horizontal",variant:t="default",label:a,labelPosition:r="center",className:n,...i},o)=>{const l={default:"design-divider-default",muted:"design-divider-muted",primary:"design-divider-primary"}[t],d=a?{left:"design-divider-label-left",center:"design-divider-label-center",right:"design-divider-label-right"}[r]:"";return s.jsx("div",{ref:o,className:V("design-divider","vertical"===e?"design-divider-vertical":"design-divider-horizontal",l,a&&"design-divider-with-label",d,n),role:"separator","aria-orientation":e,...i,children:a&&s.jsx("span",{className:"design-divider-label",children:a})})})).displayName="Divider";e.forwardRef((({code:e,revealed:t=!1,onReveal:a,onCopy:r,className:n,...i},o)=>s.jsxs("div",{ref:o,className:V("coupon-code",t&&"coupon-code-revealed",n),...i,children:[s.jsx("div",{className:"coupon-code-display",children:t?s.jsx("code",{className:"coupon-code-text",children:e}):s.jsxs("div",{className:"coupon-code-hidden",children:[s.jsx("span",{className:"coupon-code-dots",children:"••••••"}),a&&s.jsx("button",{className:"coupon-code-reveal-button",onClick:a,"aria-label":"Reveal coupon code",children:"Reveal Code"})]})}),t&&s.jsxs("button",{className:"copy-code-button",onClick:()=>{r?r():navigator.clipboard.writeText(e).then((()=>{})).catch((e=>{}))},"aria-label":"Copy coupon code",children:[s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"16",height:"16",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"mr-1",children:[s.jsx("rect",{width:"8",height:"4",x:"8",y:"2",rx:"1",ry:"1"}),s.jsx("path",{d:"M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2"}),s.jsx("path",{d:"m9 14 2 2 4-4"})]}),s.jsx("span",{children:"Copy"})]})]}))).displayName="CouponCode";function Dt({children:e,position:t="top-right",duration:a=3e3}){return s.jsxs(s.Fragment,{children:[e,s.jsx(R,{position:t,toastOptions:{style:{background:"var(--color-card)",color:"var(--color-card-foreground)",border:"1px solid var(--color-border)"},className:"font-sans",duration:a}})]})}e.forwardRef((({expiryDate:e,format:t="relative",className:a,...r},n)=>{const i="string"==typeof e?new Date(e):e,o=new Date,l=Math.ceil((i.getTime()-o.getTime())/864e5),d=i.toLocaleDateString("en-US",{year:"numeric",month:"short",day:"numeric"});let c="",u="";l<0?(c="Expired",u="coupon-expiry-expired"):0===l?(c="Expires today",u="coupon-expiry-today"):1===l?(c="Expires tomorrow",u="coupon-expiry-soon"):l<=3?(c=`Expires in ${l} days`,u="coupon-expiry-soon"):(c=`Expires in ${l} days`,u="coupon-expiry-future");let m="";return"relative"===t?m=c:"absolute"===t?m=`Expires on ${d}`:"both"===t&&(m=`${c} (${d})`),s.jsxs("div",{ref:n,className:V("coupon-expiry",u,a),...r,children:[s.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"12",height:"12",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[s.jsx("circle",{cx:"12",cy:"12",r:"10"}),s.jsx("polyline",{points:"12 6 12 12 16 14"})]}),s.jsx("span",{children:m})]})})).displayName="CouponExpiry";const Ot=D,Pt=O,zt=P,It=t.forwardRef((({className:e,sideOffset:t=4,...a},r)=>s.jsx(M,{ref:r,sideOffset:t,className:V("z-50 overflow-hidden rounded-md border border-design-border bg-design-card px-3 py-1.5 text-sm text-design-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",e),...a})));function Tt(){return"undefined"!=typeof window&&document.documentElement.classList.contains("dark")}function Vt(e,t={}){"undefined"!=typeof window&&window.gtag&&window.gtag("event",e,{event_category:"Navigation",...t,anonymize_ip:!0}),"undefined"!=typeof window&&window.plausible&&window.plausible(e,{props:t})}function Ut({children:e}){const a=t.useRef(!1),r=t.useRef(null),n=t.useRef(null),[i,o]=t.useState(!1),[l,d]=t.useState(!1),[c,u]=t.useState(!1),[m,g]=t.useState(0),[h,p]=t.useState(!0),[x,f]=t.useState(null),[b,y]=t.useState(""),[w,v]=t.useState(""),j=t.useMemo((()=>[{label:"Coupons",href:"/coupons"},{label:"Brands",href:"/coupons/brands"},{label:"Merchants",href:"/coupons/merchants"},{label:"Categories",href:"/coupons/categories"}]),[]),k=t.useMemo((()=>({headerClasses:V("relative z-50 w-full transition-all duration-300",i?"nav-backdrop border-b border-design-border shadow-sm":"bg-transparent backdrop-blur-0 border-b border-transparent","supports-[backdrop-filter]:bg-design-background/80"),mobileMenuClasses:V("mobile-menu md:hidden fixed top-0 left-0 right-0 bg-white dark:bg-gray-900 border-b border-design-border shadow-lg","transform transition-all duration-300 ease-out","overflow-hidden",l?"opacity-100 translate-y-0 visible":"opacity-0 -translate-y-full invisible","origin-top z-50"),bookmarksAriaLabel:"Saved Deals"+(m>0?` (${m} items)`:""),themeToggleAriaLabel:c?"Switch to light mode":"Switch to dark mode",menuToggleAriaLabel:l?"Close menu":"Open menu"})),[i,l,m,c]),N=t.useCallback((()=>{"undefined"!=typeof window&&(document.documentElement.classList.contains("dark")?(document.documentElement.classList.remove("dark"),localStorage.setItem("darkMode","false")):(document.documentElement.classList.add("dark"),localStorage.setItem("darkMode","true")));const e=Tt();return u(e),v(e?"Switched to dark mode":"Switched to light mode"),e}),[]),_=()=>{const e=!l;d(e),v(e?"Menu opened":"Menu closed"),e||setTimeout((()=>r.current?.focus()),0)};t.useEffect((()=>{a.current=!0,p(!0);const e=300*Math.random()+200,t=Date.now(),s=setTimeout((()=>{if("undefined"!=typeof window&&a.current)try{const e=localStorage.getItem("bookmarkIds");if(e&&a.current){const s=JSON.parse(e);g(s.length),s.length>0&&Vt("bookmark_count_loaded",{count:s.length,loading_time:Date.now()-t,has_bookmarks:!0})}p(!1);const s=e=>{if("bookmarkIds"===e.key&&a.current)try{const t=e.newValue?JSON.parse(e.newValue):[],s=m;g(t.length),Vt("bookmark_count_changed",{old_count:s,new_count:t.length,change_source:"storage_event",change_type:t.length>s?"added":"removed"})}catch(t){}};window.addEventListener("storage",s);const r=()=>{if(a.current)try{const e=localStorage.getItem("bookmarkIds"),t=e?JSON.parse(e).length:0,s=m;g(t),Vt("bookmark_count_changed",{old_count:s,new_count:t,change_source:"custom_event",change_type:t>s?"added":"removed"})}catch(e){g(0)}},n=e=>{if(a.current)try{const t=e.detail?e.detail:[],s=m;g(t.length),Vt("bookmark_count_changed",{old_count:s,new_count:t.length,change_source:"bookmark_change_event",change_type:t.length>s?"added":"removed",event_detail:!!e.detail})}catch(t){g(0)}};return window.addEventListener("savedDealsUpdated",r),window.addEventListener("bookmarkChange",n),()=>{window.removeEventListener("storage",s),window.removeEventListener("savedDealsUpdated",r),window.removeEventListener("bookmarkChange",n)}}catch(e){p(!1)}}),e);return()=>{clearTimeout(s),a.current=!1}}),[]);const C=t.useCallback((()=>{let e;return()=>{clearTimeout(e),e=setTimeout((()=>{o(window.scrollY>10)}),10)}}),[]);t.useEffect((()=>{const e=C();return window.addEventListener("scroll",e,{passive:!0}),"undefined"!=typeof window&&u(Tt()),()=>{window.removeEventListener("scroll",e)}}),[C]);const S=t.useCallback((e=>{(e.metaKey||e.ctrlKey)&&"k"===e.key&&(e.preventDefault(),window.location.href="/search",v("Navigating to search page")),"Escape"===e.key&&l&&(_(),Vt("mobile_menu_closed",{method:"escape_key"})),(e.metaKey||e.ctrlKey)&&"b"===e.key&&(e.preventDefault(),window.location.href="/bookmarks",Vt("keyboard_shortcut_used",{shortcut:"cmd_b",action:"open_bookmarks",platform:e.metaKey?"mac":"windows"})),(e.metaKey||e.ctrlKey)&&"d"===e.key&&(e.preventDefault(),window.location.href="/coupons",Vt("keyboard_shortcut_used",{shortcut:"cmd_d",action:"open_coupons",platform:e.metaKey?"mac":"windows"}))}),[l]);return t.useEffect((()=>(u(Tt()),"undefined"!=typeof window&&y(window.location.pathname),document.addEventListener("keydown",S),setTimeout((()=>{p(!1)}),1e3),()=>{document.removeEventListener("keydown",S)})),[S]),t.useEffect((()=>{if(!l)return;const e=document.getElementById("mobile-navigation");if(!e)return;const t=e.querySelectorAll('a[href], button:not([disabled]), [tabindex]:not([tabindex="-1"])'),s=t[0],a=t[t.length-1],r=e=>{if("Escape"===e.key)return e.preventDefault(),void _();"Tab"===e.key&&(e.shiftKey?document.activeElement===s&&(e.preventDefault(),a?.focus()):document.activeElement===a&&(e.preventDefault(),s?.focus()))};return document.addEventListener("keydown",r),setTimeout((()=>s?.focus()),0),()=>{document.removeEventListener("keydown",r)}}),[l]),s.jsxs(Ot,{children:[s.jsx("div",{"aria-live":"polite","aria-atomic":"true",className:"sr-only",role:"status",children:w}),s.jsx("a",{href:"#main-content",className:"sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-[100] focus:px-4 focus:py-2 focus:bg-design-primary focus:text-white focus:rounded-md focus:shadow-lg transition-all",onFocus:e=>e.currentTarget.classList.remove("sr-only"),onBlur:e=>e.currentTarget.classList.add("sr-only"),children:"Skip to main content"}),s.jsxs("header",{ref:n,role:"banner",className:k.headerClasses,style:{"--scroll-offset":i?"0.25rem":"0",boxShadow:i?"0 1px 3px rgba(0, 0, 0, 0.1)":"0 1px 1px rgba(0, 0, 0, 0.02)"},children:[s.jsxs("div",{className:"container mx-auto flex h-14 sm:h-16 md:h-20 items-center justify-between px-4 md:px-8 lg:px-12",children:[s.jsxs("a",{href:"/",className:"flex items-center","aria-label":"VapeHybrid Home",children:[s.jsxs("div",{className:"logo-placeholder relative hidden md:block",style:{width:"160px",height:"32px"},children:[s.jsx("img",{src:"/logo-dark.svg",alt:"VapeHybrid",className:"h-8 w-auto absolute top-0 left-0 hidden dark:block",width:"160",height:"32",loading:"eager"}),s.jsx("img",{src:"/logo-light.svg",alt:"VapeHybrid",className:"h-8 w-auto absolute top-0 left-0 block dark:hidden",width:"160",height:"32",loading:"eager"})]}),s.jsxs("div",{className:"logo-placeholder relative md:hidden",style:{width:"32px",height:"32px"},children:[s.jsx("img",{src:"/Vapehybrid dark icon.svg",alt:"VapeHybrid",className:"h-8 w-8 absolute top-0 left-0 hidden dark:block",width:"32",height:"32",loading:"eager"}),s.jsx("img",{src:"/Vapehybrid light icon.svg",alt:"VapeHybrid",className:"h-8 w-8 absolute top-0 left-0 block dark:hidden",width:"32",height:"32",loading:"eager"})]})]}),s.jsx("nav",{className:"hidden md:flex items-center gap-6","aria-label":"Main navigation",role:"navigation",children:j.map((e=>s.jsx("a",{href:e.href,className:V("nav-link text-design-foreground hover:text-design-primary font-medium",b===e.href&&"text-design-primary"),"aria-current":b===e.href?"page":void 0,children:e.label},e.href)))}),s.jsx("div",{className:"hidden md:flex items-center gap-3",children:s.jsxs(Ot,{children:[s.jsxs(Pt,{children:[s.jsx(zt,{asChild:!0,children:s.jsx("a",{href:"/search",className:"flex items-center justify-center w-10 h-10 hover:bg-design-accent/10 rounded-md transition-colors","aria-label":"Search deals",children:s.jsx(ue,{size:20,className:"text-design-foreground dark:text-white"})})}),s.jsx(It,{children:s.jsx("p",{children:"Search deals (⌘K)"})})]}),s.jsxs(Pt,{children:[s.jsx(zt,{asChild:!0,children:s.jsx("button",{onClick:N,"aria-label":c?"Switch to light mode":"Switch to dark mode",className:"flex items-center justify-center w-10 h-10 hover:bg-design-accent/10 rounded-md transition-colors",children:c?s.jsx(pe,{size:20,className:"text-design-foreground dark:text-white"}):s.jsx(de,{size:20,className:"text-design-foreground dark:text-white"})})}),s.jsx(It,{children:s.jsx("p",{children:c?"Switch to light mode":"Switch to dark mode"})})]}),s.jsxs(Pt,{children:[s.jsx(zt,{asChild:!0,children:s.jsxs("a",{href:"/bookmarks","aria-label":"Saved Deals"+(m>0?` (${m} items)`:""),"aria-describedby":m>0?"bookmarks-count-desc":void 0,className:"flex items-center justify-center w-10 h-10 hover:bg-design-accent/10 rounded-md transition-colors relative "+(m>0?"has-badge overflow-visible":""),children:[s.jsx(re,{size:20,className:"text-design-foreground dark:text-white"}),h?s.jsx("span",{className:"bookmark-count-skeleton absolute -top-1 -right-1 z-10"}):m>0?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:V("nav-badge absolute -top-1 -right-1 bg-design-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center z-10",h&&"loading"),children:m>9?"9+":m}),s.jsxs("span",{id:"bookmarks-count-desc",className:"sr-only",children:["You have ",m," saved deal",1!==m?"s":""]})]}):null]})}),s.jsx(It,{children:s.jsxs("p",{children:["Saved Deals",m>0?` (${m})`:""]})})]})]})}),s.jsxs("div",{className:"flex md:hidden items-center gap-1",children:[s.jsxs(Ot,{children:[s.jsxs(Pt,{children:[s.jsx(zt,{asChild:!0,children:s.jsx("a",{href:"/search",className:V("nav-action-button relative inline-flex items-center justify-center","w-10 h-10 rounded-full","text-design-foreground hover:text-design-primary","hover:bg-design-accent/10","focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary","dark:text-white/90 dark:hover:text-white","flex-shrink-0","m-1"),"aria-label":"Search",children:s.jsx(ue,{className:"w-5 h-5","aria-hidden":"true"})})}),s.jsx(It,{side:"bottom",sideOffset:8,children:s.jsx("p",{children:"Search"})})]}),s.jsxs(Pt,{children:[s.jsx(zt,{asChild:!0,children:s.jsxs("a",{href:"/bookmarks",className:V("relative inline-flex items-center justify-center","w-10 h-10 rounded-full","text-design-foreground hover:text-design-primary","hover:bg-design-accent/10","focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary","transition-colors duration-200","dark:text-white/90 dark:hover:text-white","flex-shrink-0","m-1"),"aria-label":"Saved Deals"+(m>0?` (${m} items)`:""),children:[s.jsx(re,{className:"w-5 h-5","aria-hidden":"true"}),m>0&&s.jsx("span",{className:V("absolute -top-0.5 -right-0.5","flex items-center justify-center min-w-[1.25rem] h-5 px-1","text-xs font-semibold text-white","bg-design-primary rounded-full","ring-2 ring-white dark:ring-gray-900","shadow-sm"),"aria-hidden":"true",children:m>9?"9+":m})]})}),s.jsx(It,{side:"bottom",sideOffset:8,children:s.jsxs("p",{children:["Saved Deals",m>0?` (${m})`:""]})})]}),s.jsxs(Pt,{children:[s.jsx(zt,{asChild:!0,children:s.jsx("button",{onClick:N,className:V("relative inline-flex items-center justify-center","w-10 h-10 rounded-full","text-design-foreground hover:text-design-primary","hover:bg-design-accent/10","focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary","transition-colors duration-200","dark:text-white/90 dark:hover:text-white","flex-shrink-0","m-1"),"aria-label":c?"Switch to light mode":"Switch to dark mode",children:c?s.jsx(pe,{className:"w-5 h-5","aria-hidden":"true"}):s.jsx(de,{className:"w-5 h-5","aria-hidden":"true"})})}),s.jsx(It,{side:"bottom",sideOffset:8,children:s.jsx("p",{children:c?"Light Mode":"Dark Mode"})})]})]}),s.jsxs(Pt,{children:[s.jsx(zt,{asChild:!0,children:s.jsx("button",{ref:r,onClick:_,className:V("relative inline-flex items-center justify-center","w-10 h-10 rounded-full","text-design-foreground hover:text-design-primary","hover:bg-design-accent/10","focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary","transition-colors duration-200","dark:text-white/90 dark:hover:text-white","flex-shrink-0","m-1","md:hidden"),"aria-expanded":l,"aria-controls":"mobile-navigation","aria-label":l?"Close menu":"Open menu",children:l?s.jsx(ye,{className:"w-6 h-6","aria-hidden":"true"}):s.jsx(le,{className:"w-6 h-6","aria-hidden":"true"})})}),s.jsx(It,{side:"bottom",sideOffset:8,children:s.jsx("p",{children:l?"Close menu":"Menu"})})]})]})]}),l&&s.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden",onClick:_,"aria-hidden":"true",style:{zIndex:40}}),s.jsx("div",{id:"mobile-navigation",className:k.mobileMenuClasses,"aria-hidden":!1,style:{transitionProperty:"opacity, transform",transitionTimingFunction:"cubic-bezier(0.16, 1, 0.3, 1)",transitionDuration:"300ms",zIndex:50},children:s.jsxs("div",{className:"container mx-auto px-4 py-4 pt-20",children:[s.jsxs("div",{className:"flex items-center justify-between mb-4 pb-4 border-b border-gray-200 dark:border-gray-700",children:[s.jsx("h2",{className:"text-lg font-semibold text-gray-900 dark:text-white",children:"Menu"}),s.jsx("button",{onClick:_,className:"p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors","aria-label":"Close menu",children:s.jsx(ye,{className:"w-5 h-5 text-gray-900 dark:text-white"})})]}),s.jsx("nav",{className:"flex flex-col space-y-1","aria-label":"Mobile navigation",role:"navigation",children:j.map((e=>s.jsxs("a",{href:e.href,onClick:()=>{setTimeout((()=>{d(!1)}),100)},className:V("group mobile-menu-item py-4 px-4 rounded-xl relative z-50","text-gray-900 dark:text-white hover:text-design-primary","hover:bg-gray-100 dark:hover:bg-gray-800 hover:translate-x-1","focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary focus-visible:ring-offset-2","flex items-center justify-between","text-base font-medium","active:bg-gray-200 dark:active:bg-gray-700 active:scale-98","transition-all duration-200 ease-out","border border-transparent hover:border-gray-200 dark:hover:border-gray-700","cursor-pointer",b===e.href&&"text-design-primary font-semibold bg-design-primary/10 border-design-primary/20"),onKeyDown:e=>((e,t=!1)=>{if(!n.current)return;const s=Array.from(n.current.querySelectorAll("a, button")),a=s.findIndex((e=>e===document.activeElement));let i=a;switch(e.key){case"ArrowRight":case"ArrowDown":e.preventDefault(),i=(a+1)%s.length;break;case"ArrowLeft":case"ArrowUp":e.preventDefault(),i=(a-1+s.length)%s.length;break;case"Home":e.preventDefault(),i=0;break;case"End":e.preventDefault(),i=s.length-1;break;case"Escape":return void(t&&(e.preventDefault(),d(!1),r.current?.focus()));default:return}s[i].focus()})(e,!0),onFocus:e=>f(e.currentTarget),onBlur:()=>f(null),"aria-current":b===e.href?"page":void 0,children:[s.jsx("span",{className:"flex-1 text-left",children:e.label}),s.jsx(Q,{className:"w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-200 group-hover:translate-x-1","aria-hidden":"true"})]},e.href)))})]})}),e]})]})}It.displayName=M.displayName;const Wt=t.memo((({isOpen:e,onClose:r,title:n="Never Miss a Deal",subtitle:i="Join 50,000+ subscribers and get the best deals delivered to your inbox",buttonText:o="Subscribe Now",exitIntent:l=!1})=>{const[d,c]=t.useState((()=>"undefined"!=typeof window&&localStorage.getItem("newsletter_email_cache")||"")),[u,m]=t.useState(!1),[g,h]=t.useState(!1),[p,x]=t.useState(null),[f,b]=t.useState(e);t.useEffect((()=>{b(e)}),[e]),t.useEffect((()=>{d&&"undefined"!=typeof window&&localStorage.setItem("newsletter_email_cache",d)}),[d]);const y=t.useCallback((e=>{e.target===e.currentTarget&&r()}),[r]),w=t.useCallback((async e=>{e.preventDefault(),x(null),m(!0);if(!/^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/.test(d))return x("Please enter a valid email address"),void m(!1);const t=l?"exit_intent_popup":"newsletter_popup",s=window.location.pathname,n=navigator.userAgent,i=new URLSearchParams(window.location.search),o={utm_source:i.get("utm_source"),utm_medium:i.get("utm_medium"),utm_campaign:i.get("utm_campaign"),utm_term:i.get("utm_term"),utm_content:i.get("utm_content")};try{const e=await fetch("/api/newsletter/subscribe",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({email:d,source:t,utm_params:o,device_info:n,initial_page:s})}),i=await e.json();if(!e.ok)throw new Error(i.error||"Something went wrong. Please try again later.");h(!0),c(""),a.success(i.message||"Thanks for subscribing! Check your email to confirm."),localStorage.setItem("newsletterSubscribed","true"),setTimeout((()=>{r(),l&&localStorage.setItem("exitIntentPopupSeen","true")}),3e3)}catch(u){x(u.message||"Failed to subscribe. Please try again later."),a.error(u.message||"Failed to subscribe. Please try again later.")}finally{m(!1)}}),[d,l,r]);return f?s.jsx("div",{className:"fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",onClick:y,children:s.jsxs("div",{className:"bg-white dark:bg-gray-800 border border-design-border rounded-xl p-8 max-w-md w-full shadow-lg relative",children:[s.jsx("button",{onClick:r,className:"absolute top-4 right-4 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors","aria-label":"Close",children:s.jsx(ye,{className:"h-6 w-6"})}),s.jsxs("div",{className:"text-center mb-6",children:[s.jsx("h2",{className:"text-2xl font-bold text-gray-800 dark:text-white mb-2",children:n}),s.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:i})]}),g?s.jsxs("div",{className:"bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 text-center",children:[s.jsx("p",{className:"font-medium text-green-700 dark:text-green-400",children:"Thanks for subscribing!"}),s.jsx("p",{className:"text-sm text-gray-600 dark:text-gray-300 mt-1",children:"Please check your email to confirm your subscription."})]}):s.jsxs("form",{onSubmit:w,children:[s.jsx("div",{className:"mb-4",children:s.jsx("input",{type:"email",value:d,onChange:e=>c(e.target.value),placeholder:"Enter your email",className:"w-full h-12 px-4 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-design-primary",required:!0})}),p&&s.jsx("div",{className:"mb-4 text-center text-red-600 dark:text-red-400 text-sm",children:p}),s.jsx("button",{type:"submit",disabled:u,className:`w-full h-12 rounded-lg font-bold transition-all duration-300 ${u?"bg-design-primary/70 cursor-not-allowed":"bg-design-primary hover:bg-design-primary/90"} text-white dark:text-black`,children:u?s.jsxs("span",{className:"flex items-center justify-center",children:[s.jsxs("svg",{className:"animate-spin -ml-1 mr-2 h-4 w-4 text-white dark:text-black",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24",children:[s.jsx("circle",{className:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor",strokeWidth:"4"}),s.jsx("path",{className:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]}),"Processing..."]}):o}),s.jsxs("div",{className:"mt-4 text-xs text-center text-gray-600 dark:text-gray-300",children:[s.jsxs("div",{className:"flex flex-wrap justify-center gap-x-4 gap-y-1",children:[s.jsx("span",{children:"✓ Exclusive deals"}),s.jsx("span",{children:"✓ No spam"}),s.jsx("span",{children:"✓ Weekly digest"})]}),s.jsxs("div",{className:"mt-2",children:["By subscribing, you agree to our ",s.jsx("a",{href:"/privacy",className:"underline text-design-primary hover:text-design-primary/80 transition-colors",children:"Privacy Policy"}),"."]})]})]})]})}):null})),Bt=({deal:e,imageUrl:a})=>{const[r,n]=t.useState(!1);return t.useEffect((()=>{const e=(window.requestIdleCallback||(e=>setTimeout(e,1)))((()=>{n(!0)}));return()=>{(window.cancelIdleCallback||(e=>clearTimeout(e)))(e)}}),[]),r?s.jsx(Oe,{deal:e,imageUrl:a}):null},Ft=({deal:e,className:a,isRevealed:r=!1,priority:n=!1,onShowCouponModal:i,...o})=>{const[l,d]=t.useState(r),c=t.useRef(null),u=`/deal/${e.id}`,{onMouseEnter:m,onMouseLeave:g}=Ie(u,{delay:150}),{setIsVisible:h,handleMouseEnter:p,handleMouseLeave:x,handleClick:f}=Te(e.id,"deal",{minDwellTime:2e3}),b=t.useMemo((()=>$e(5,e.id)),[e.id]),y=t.useMemo((()=>Re(e.id)),[e.id]),w=t.useMemo((()=>y.timeAgo),[y]),v=t.useMemo((()=>e.usage_count||y.count),[e.usage_count,y.count]),j=t.useMemo((()=>e.deal_end_date?Math.ceil((new Date(e.deal_end_date).getTime()-(new Date).getTime())/864e5):null),[e.deal_end_date]),k=t.useMemo((()=>null!==j&&j>0&&j<=7),[j]),N=t.useMemo((()=>null!==j&&j<=0),[j]),_=t.useMemo((()=>{const t=Me(e.imagebig_url)||Me(e.image_url)||Me(e.imagesmall_url);if(t)return t;const s=Me(e.brands?.logo_url)||Me(e.brand_logo_url);if(s)return s;const a=Me(e.merchants?.logo_url)||Me(e.merchant_logo_url);return a||"/placeholder-image.svg"}),[e.imagebig_url,e.image_url,e.imagesmall_url,e.brands?.logo_url,e.brand_logo_url,e.merchants?.logo_url,e.merchant_logo_url]);t.useEffect((()=>{if(!c.current)return;const t=new IntersectionObserver((t=>{t.forEach((t=>{if(h(t.isIntersecting),t.isIntersecting)try{let t=localStorage.getItem("vh_session_id");if(t||(t="session_"+Math.random().toString(36).substring(2,15),localStorage.setItem("vh_session_id",t)),"function"==typeof navigator.sendBeacon){const s=new FormData;s.append("deal_id",e.id.toString()),s.append("session_id",t),navigator.sendBeacon("/api/track-impression?deal_id="+e.id+"&session_id="+t,s)}else fetch("/api/track-impression?deal_id="+e.id+"&session_id="+t,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({deal_id:e.id,session_id:t}),keepalive:!0})}catch(s){}}))}),{threshold:.5});return t.observe(c.current),()=>{t.disconnect()}}),[e.id,c,h]);const C=t.useCallback((()=>{if(d(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("revealedCodes")||"{}");t[e.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(t)),e.coupon_code&&ke(e.coupon_code);const s=new URL(window.location.href).searchParams.get("view")||"list",a=new URL(window.location.href);a.searchParams.set("dealId",e.id.toString()),a.searchParams.set("showPopup","true"),a.searchParams.set("view",s),window.open(a.toString(),"_blank"),i&&i(e),e.tracking_url?window.location.href=e.tracking_url:window.location.href=`/go/${e.id}`}}),[e,i]),S=t.useCallback((t=>{t.stopPropagation(),f(),requestAnimationFrame((()=>{if(d(!0),"undefined"!=typeof window){if("function"==typeof navigator.sendBeacon)try{const t=new FormData;t.append("deal_id",e.id.toString()),t.append("fallback","true"),navigator.sendBeacon("/api/track-click",t)}catch(t){}const s=JSON.parse(localStorage.getItem("clickedDeals")||"{}"),a=JSON.parse(localStorage.getItem("revealedCodes")||"{}");a[e.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(a)),s[e.id]=!0,localStorage.setItem("clickedDeals",JSON.stringify(s)),e.coupon_code&&ke(e.coupon_code);const r=new URL(window.location.href).searchParams.get("view")||"list",n=new URL(window.location.href);n.searchParams.set("dealId",e.id.toString()),n.searchParams.set("showPopup","true"),n.searchParams.set("solidBg","true"),n.searchParams.set("view",r),i&&i(e),window.open(n.toString(),"_blank"),e.tracking_url?window.location.href=e.tracking_url:window.location.href=`/go/${e.id}`}}))}),[e,f,i]);return s.jsxs(s.Fragment,{children:["undefined"!=typeof window&&s.jsx(Bt,{deal:e,imageUrl:_}),s.jsxs(Ce,{ref:c,variant:"default",interactive:!0,glowEffect:!0,className:V("optimized-deal-list-card deal-list-card relative cursor-pointer w-full flex flex-col sm:flex-row items-center justify-between","transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.005]","hover:border-design-primary/50 dark:hover:border-design-primary/50","border-black/15 dark:border-white/15","optimized-card",a),style:{padding:"0.5rem",borderRadius:"25px",borderWidth:"1.5px"},onClick:()=>{C(),f()},onMouseEnter:()=>{m(),p()},onMouseLeave:()=>{g(),x()},"aria-label":`Deal: ${e.title}`,role:"button",tabIndex:0,...o,children:[s.jsx("div",{id:`tracking-pixel-${e.id}`,style:{position:"absolute",opacity:0,pointerEvents:"none"},"aria-hidden":"true"}),k&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full",children:1===j?"Expires today":`Expires in ${j} days`}),N&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full",children:"Expired"}),s.jsx("div",{className:"w-full sm:w-[200px] py-2 sm:py-0",children:s.jsx("div",{className:"flex justify-center h-full",children:s.jsx("div",{className:"relative flex items-center justify-center w-full h-full aspect-square max-w-[160px] sm:min-w-[160px] sm:max-w-[180px] sm:max-h-[180px] bg-white dark:bg-white rounded-lg overflow-hidden",style:{boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.05)",padding:"8px"},children:s.jsx(De,{src:_,alt:e.title||"Deal image",width:180,height:180,priority:n,fetchpriority:n?"high":"auto",className:"",fallbackSrc:"/placeholder-image.svg",index:0})})})}),s.jsx("div",{className:"flex-1 p-3 w-full",children:s.jsxs("div",{className:"h-full flex flex-col justify-between",children:[s.jsx("div",{className:"mb-2",children:s.jsxs("div",{className:"text-xl font-bold text-design-foreground",children:[e.discount?s.jsxs("span",{className:"text-green-600 dark:text-green-500",children:[e.discount,"% ",s.jsx("span",{className:"text-xs font-normal text-gray-500 dark:text-gray-400",children:"off "}),"🔥"]}):e.price?`${e.currency||"$"}${e.price}`:"20% Off",null!==j&&j>0&&s.jsx("span",{className:"ml-2 text-xs font-normal text-design-warning animate-pulse",children:j>365?`${Math.floor(j/365)}+ ${1===Math.floor(j/365)?"year":"years"} left`:j>30?`${Math.floor(j/30)} ${1===Math.floor(j/30)?"month":"months"} left`:1===j?"Ends today!":`Ends in ${j} days!`})]})}),s.jsxs("div",{className:"text-sm text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1",children:[s.jsx("span",{className:"font-medium truncate max-w-[120px] hover:underline cursor-help",title:`Brand: ${e.merchants?.name||e.brands?.name||"Unknown"}`,children:e.merchants?.name||e.brands?.name||"Brand"}),e.verified&&s.jsxs("span",{className:"c-verified-badge verified-badge ml-2",children:[s.jsx("img",{src:"/Vapehybrid light icon.svg",alt:"Verified",className:"dark:hidden",width:16,height:16}),s.jsx("img",{src:"/Vapehybrid dark icon.svg",alt:"Verified",className:"hidden dark:block",width:16,height:16}),s.jsx("span",{className:"text-design-primary dark:text-design-primary",children:"Verified"})]}),void 0!==e.success_rate&&s.jsxs("span",{className:"ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full "+(e.success_rate>=90?"bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground":e.success_rate>=70?"bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground":"bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"),title:`${Math.round(e.success_rate||85)}% success rate`,children:[s.jsx(fe,{size:10,className:"mr-1"}),Math.round(e.success_rate||85),"%"]})]}),s.jsx("h3",{className:"deal-card-title line-clamp-2 mb-2 text-base font-semibold overflow-hidden",title:e.cleaned_title&&"null"!==e.cleaned_title?e.cleaned_title:e.title,children:e.cleaned_title&&"null"!==e.cleaned_title?e.cleaned_title:e.title}),s.jsx("div",{className:"flex flex-1",children:s.jsxs("div",{className:"flex-1",children:[e.description&&s.jsx("p",{className:"text-sm text-design-muted-foreground mb-3 line-clamp-2",title:e.description,children:e.description}),s.jsxs("div",{className:"flex items-center text-xs text-design-muted-foreground mb-3",children:[s.jsxs("div",{className:"c-avatar-group user-avatar-group flex relative",style:{marginRight:"0.35rem"},children:[b.slice(0,Math.min(3,v||3)).map(((e,t)=>s.jsxs("div",{className:"c-avatar c-avatar--xs user-avatar relative transition-transform hover:scale-110 hover:z-10 overflow-hidden",style:{width:"20px",height:"20px",marginLeft:t>0?"-8px":"0",borderRadius:"50%",border:"1.5px solid white",boxShadow:"0 1px 2px rgba(0,0,0,0.1)",zIndex:3-t,backgroundColor:e.color},title:`Staff member: ${e.name}`,children:[e.webpPath&&e.imagePath?s.jsxs("picture",{children:[s.jsx("source",{srcSet:e.webpPath,type:"image/webp"}),s.jsx("img",{src:e.imagePath,alt:e.initials,className:"w-full h-full object-cover",onError:e=>{const t=e.target;t.style.display="none";const s=t.parentElement?.parentElement;s&&s.classList.add("fallback-active")}})]}):s.jsx("div",{className:"w-full h-full flex items-center justify-center text-white font-bold",children:s.jsx("span",{style:{fontSize:"8px"},children:e.initials})}),s.jsx("div",{className:"fallback-content absolute inset-0 flex items-center justify-center text-white font-bold opacity-0",children:s.jsx("span",{style:{fontSize:"8px"},children:e.initials})})]},t))),v>3&&s.jsxs("div",{className:"c-avatar c-avatar--xs user-avatar flex items-center justify-center bg-design-muted-foreground/20",style:{width:"20px",height:"20px",marginLeft:"-8px",borderRadius:"50%",border:"1.5px solid white",boxShadow:"0 1px 2px rgba(0,0,0,0.1)",fontSize:"8px"},title:v-3+" more staff members",children:["+",v-3]})]}),s.jsxs("span",{children:["Verified ",s.jsx("time",{dateTime:e.last_verified_at,title:new Date(e.last_verified_at||"").toLocaleString(),children:w})," by ",v||3," staffer",(v||3)>1?"s":""]})]}),null!==j&&j>0&&s.jsxs("div",{className:"mb-3 text-xs text-design-warning px-2 py-1 rounded flex items-center",children:[s.jsx("span",{className:"animate-pulse mr-1",children:"⏱"}),s.jsxs("span",{children:["Limited time offer! ",j>365?`${Math.floor(j/365)}+ ${1===Math.floor(j/365)?"year":"years"} left`:j>30?`${Math.floor(j/30)} ${1===Math.floor(j/30)?"month":"months"} left`:1===j?"Ends today":`Ends in ${j} days`]})]})]})})]})}),s.jsxs("div",{className:"flex flex-col items-center justify-center px-4 sm:pr-4 sm:pl-0 gap-2 w-full sm:min-w-[150px] sm:w-auto py-3 sm:py-0",children:[s.jsx("div",{className:"deal-coupon-code relative cursor-pointer px-3 py-1 w-full text-center bg-design-muted rounded-full border border-design-muted-foreground/10 overflow-hidden",onClick:e=>{e.stopPropagation(),S(e)},title:l?"Click to copy":"Click to reveal code","aria-label":l?`Copy coupon code: ${e.coupon_code||"NO CODE"}`:"Click to reveal coupon code",children:e.coupon_code?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"text-base font-bold transition-all duration-300 "+(l?"blur-none opacity-100":"blur-[4px] select-none"),style:{color:"var(--design-foreground)"},"aria-hidden":!l,children:e.coupon_code}),l&&s.jsx("span",{className:"absolute inset-0 bg-design-primary/10 animate-reveal-sweep","aria-hidden":"true"}),s.jsx("span",{className:"sr-only",children:l?`Coupon code: ${e.coupon_code}`:"Click to reveal coupon code"})]}):"NO CODE"}),s.jsx("button",{className:"copy-code-button px-4 py-1 rounded-full border border-design-muted-foreground/20 hover:border-design-primary/50 dark:hover:border-design-primary/50 transition-colors",onClick:e=>{e.stopPropagation(),S(e)},"aria-label":`Copy coupon code ${e.coupon_code||"for this deal"}`,children:"Copy Code"}),s.jsxs("div",{className:"deal-card-actions flex items-center mt-1",children:[s.jsx(_e,{dealId:e.id.toString(),className:"bookmark-button p-2 rounded-full hover:bg-design-muted transition-colors"}),s.jsx("button",{className:"eye-button p-2 rounded-full hover:bg-design-muted transition-colors relative z-10",onClick:t=>{t.stopPropagation(),window.open(Pe(e),"_blank")},title:"View coupon details","aria-label":"View coupon details",children:s.jsx(te,{size:18,className:"text-design-muted-foreground hover:text-design-foreground transition-colors"})})]})]})]})]})},At=Object.freeze(Object.defineProperty({__proto__:null,OptimizedDealListCard:Ft,default:Ft},Symbol.toStringTag,{value:"Module"})),Jt=Object.freeze(Object.defineProperty({__proto__:null,CompactDealCard:({deal:e,isRevealed:r=!1,dealUrl:n,className:i,...o})=>{const[l,d]=t.useState(!0),[c,u]=t.useState(r),m=e.deal_end_date?Math.ceil((new Date(e.deal_end_date).getTime()-(new Date).getTime())/864e5):null,g=null!==m&&m>0&&m<=7,h=null!==m&&m<=0,p=(()=>{if(e.brands?.logo_url){const t=e.brands.logo_url.trim();if(""!==t)return t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:`/${t}`}if(e.merchants?.logo_url){const t=e.merchants.logo_url.trim();if(""!==t)return t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:`/${t}`}if(e.categories?.category_logo){const t=e.categories.category_logo.trim();if(""!==t)return t.startsWith("http://")||t.startsWith("https://")||t.startsWith("/")?t:`/${t}`}return e.imagebig_url&&""!==e.imagebig_url.trim()?e.imagebig_url:e.image_url&&""!==e.image_url.trim()?e.image_url:e.imagesmall_url&&""!==e.imagesmall_url.trim()?e.imagesmall_url:"/placeholder-image.svg"})(),x=()=>{if(u(!0),"undefined"!=typeof window){const t=JSON.parse(localStorage.getItem("revealedCodes")||"{}");t[e.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(t))}if(e.coupon_code)if(navigator.clipboard&&navigator.clipboard.writeText)navigator.clipboard.writeText(e.coupon_code),a.success("Coupon code copied to clipboard!");else{const s=document.createElement("textarea");s.value=e.coupon_code,document.body.appendChild(s),s.select();try{document.execCommand("copy"),a.success("Coupon code copied to clipboard!")}catch(t){a.error("Failed to copy code")}document.body.removeChild(s)}e.tracking_url&&window.open(e.tracking_url,"_blank")},f=n||(e.slug?`/deal/${e.slug}`:`/deal/${e.id}`);return s.jsx("div",{className:"flex justify-center w-full",children:s.jsxs(Ce,{variant:"default",interactive:!0,onClick:()=>{"undefined"!=typeof window&&window.open(f,"_blank")},className:V("compact-deal-card relative overflow-hidden cursor-pointer w-full h-full max-w-[200px] min-h-[280px]","flex flex-col justify-between",i),style:{borderRadius:"12px"},...o,children:[g&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-warning text-black dark:text-black text-xs font-bold px-2 py-1 rounded-full",children:1===m?"Expires today":`Expires in ${m} days`}),h&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-destructive text-white text-xs font-bold px-2 py-1 rounded-full",children:"Expired"}),e.discount&&s.jsx("div",{className:"absolute top-2 left-2 z-10 bg-design-primary text-white dark:text-black text-xs font-bold px-2 py-1 rounded-full",children:e.discount}),s.jsxs("div",{className:"p-3 flex flex-col h-full",children:[s.jsx("div",{className:"flex justify-center mb-2",children:s.jsx("div",{className:"w-12 h-12 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center overflow-hidden border border-design-border/20 shadow-sm",children:s.jsx("img",{src:p,alt:e.brands?.name||e.merchants?.name||"Brand",className:"w-10 h-10 object-contain",loading:"lazy",onLoad:()=>d(!1),onError:t=>{const s=t.target;if(!s.src.includes("placeholder-image.svg")){if(e.brands?.logo_url&&!s.src.includes(e.brands.logo_url)){const t=e.brands.logo_url.trim();if(""!==t)return void(t.startsWith("http://")||t.startsWith("https://")?s.src=t:s.src=t.startsWith("/")?t:`/${t}`)}if(e.merchants?.logo_url&&!s.src.includes(e.merchants.logo_url)){const t=e.merchants.logo_url.trim();if(""!==t)return void(t.startsWith("http://")||t.startsWith("https://")?s.src=t:s.src=t.startsWith("/")?t:`/${t}`)}s.src="/placeholder-image.svg"}d(!1)}})})}),s.jsxs("div",{className:"flex justify-center items-center gap-2 mb-2",children:[e.price&&s.jsxs("span",{className:"text-design-foreground font-bold text-sm",children:[e.currency||"$",e.price]}),e.discount&&s.jsx("span",{className:"text-design-primary font-bold text-sm",children:e.discount})]}),s.jsx("h3",{className:"text-xs font-semibold text-design-foreground text-center line-clamp-2 mb-2 leading-tight min-h-[2.5rem] flex items-center justify-center",children:e.cleaned_title&&"null"!==e.cleaned_title?e.cleaned_title:e.title}),s.jsxs("div",{className:"hidden",children:["Title: ",e.title,", Cleaned: ",e.cleaned_title||"null",", Price: ",e.price||"null",", Discount: ",e.discount||"null"]}),s.jsx("div",{className:"text-xs text-design-muted-foreground text-center mb-2",children:e.brands?.name||e.merchants?.name||"Brand"}),s.jsxs("div",{className:"flex items-center justify-center gap-2 mb-3",children:[e.verified&&s.jsxs("span",{className:"verified-badge text-[10px] px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full flex items-center gap-1",children:[s.jsx("img",{src:"/Vapehybrid light icon.svg",alt:"Verified",className:"dark:hidden w-2 h-2"}),s.jsx("img",{src:"/Vapehybrid dark icon.svg",alt:"Verified",className:"hidden dark:block w-2 h-2"}),s.jsx("span",{children:"Verified"})]}),void 0!==e.success_rate&&s.jsxs("span",{className:"success-rate-badge text-[10px] px-2 py-1 rounded-full flex items-center gap-1 "+(e.success_rate>=80?"bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200":e.success_rate>=50?"bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200":"bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"),children:[s.jsx(fe,{size:8}),s.jsxs("span",{children:[Math.round(e.success_rate),"%"]})]})]}),e.coupon_code&&s.jsxs("div",{className:"mt-auto mb-3",children:[s.jsx("div",{className:"relative cursor-pointer px-2 py-2 w-full text-center bg-design-foreground text-design-background rounded-lg mb-2 text-xs shadow-sm",onClick:e=>{e.stopPropagation(),x()},title:c?"Click to copy":"Click to reveal code",children:s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"font-mono font-bold "+(c?"":"blur-[2px] select-none"),children:e.coupon_code}),s.jsx("span",{className:"sr-only",children:c?`Coupon code: ${e.coupon_code}`:"Click to reveal coupon code"})]})}),s.jsx("div",{className:"flex justify-center mb-2",children:s.jsxs("button",{onClick:e=>{e.stopPropagation(),x()},className:"bg-design-primary hover:bg-design-primary/90 text-white dark:text-black px-3 py-1.5 rounded-lg text-xs font-medium transition-colors flex items-center gap-1","aria-label":c?`Copy coupon code ${e.coupon_code}`:"Reveal coupon code",children:[s.jsx(ee,{size:12,"aria-hidden":"true"}),c?"Copy":"Reveal"]})})]}),s.jsxs("div",{className:"deal-card-actions flex items-center justify-between px-2 mt-auto",children:[s.jsx(_e,{dealId:e.id.toString(),className:"heart-bookmark-button w-8 h-8 flex items-center justify-center rounded-full hover:bg-design-muted/20 transition-colors"}),s.jsx("button",{onClick:e=>{e.stopPropagation(),"undefined"!=typeof window&&window.open(f,"_blank")},className:"eye-button w-8 h-8 flex items-center justify-center rounded-full hover:bg-design-muted/20 transition-colors relative z-10",title:"View deal details",children:s.jsx(te,{size:16,className:"text-design-muted-foreground hover:text-design-foreground transition-colors"})})]})]})]})})}},Symbol.toStringTag,{value:"Module"}));export{Jt as $,H as A,_e as B,Ce as C,Be as D,te as E,ot as F,U as G,re as H,Fe as I,tt as J,it as K,ie as L,oe as M,Wt as N,Ue as O,lt as P,ft as Q,Nt as R,he as S,Dt as T,be as U,yt as V,jt as W,St as X,Lt as Y,Mt as Z,At as _,fe as a,xe as b,ke as c,Te as d,Re as e,V as f,$e as g,De as h,Pe as i,Oe as j,Y as k,ce as l,Z as m,Me as n,ee as o,je as p,me as q,q as r,G as s,bt as t,Ie as u,wt as v,vt as w,Ut as x,ve as y,We as z};
