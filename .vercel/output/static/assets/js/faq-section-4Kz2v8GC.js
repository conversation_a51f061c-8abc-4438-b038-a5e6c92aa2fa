import{r as e,j as r}from"./vendor/react-bq1VYqFi.js";import{m as s}from"./proxy-BAs1uqPX.js";import{A as i}from"./index-tFD8qxOu.js";const t=({title:s,subtitle:i,faqs:t})=>{const[n,d]=e.useState(!1),[l,o]=e.useState(null);if(e.useEffect((()=>{d(!0)}),[]),!n)return r.jsx("section",{className:"py-16 bg-design-background/50 relative overflow-hidden",children:r.jsx("div",{className:"max-w-4xl mx-auto px-4 text-center",children:r.jsx("p",{className:"text-design-muted-foreground",children:"Loading FAQs..."})})});const c=e=>{o(l===e?null:e)},x=t.slice(0,Math.ceil(t.length/2)),m=t.slice(Math.ceil(t.length/2));return r.jsxs("section",{className:"py-16 bg-design-background/50 relative overflow-hidden",children:[r.jsx("div",{className:"absolute inset-0 bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-20 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-10 z-0"}),r.jsxs("div",{className:"max-w-4xl mx-auto px-4 relative z-10 flex flex-col items-center",children:[r.jsxs("div",{className:"text-center mb-10",children:[r.jsxs("div",{className:"inline-flex items-center bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm px-4 py-2 rounded-full mb-4 border border-design-primary/20",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-design-primary mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{fillRule:"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-3a1 1 0 00-.867.5 1 1 0 11-1.731-1A3 3 0 0113 8a3.001 3.001 0 01-2 2.83V11a1 1 0 11-2 0v-1a1 1 0 011-1 1 1 0 100-2zm0 8a1 1 0 100-2 1 1 0 000 2z",clipRule:"evenodd"})}),r.jsx("span",{className:"text-design-primary font-medium text-xs",children:"Expert Answers"})]}),r.jsx("h2",{className:"text-3xl md:text-[34px] font-normal text-design-foreground mb-4",children:s}),r.jsx("p",{className:"text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto",children:i})]}),r.jsxs("div",{className:"hidden md:flex gap-8",children:[r.jsx("div",{className:"w-1/2 space-y-4",children:x.map(((e,s)=>r.jsx(a,{faq:e,index:s,isOpen:l===s,toggleFAQ:()=>c(s)},s)))}),r.jsx("div",{className:"w-1/2 space-y-4",children:m.map(((e,s)=>r.jsx(a,{faq:e,index:s+x.length,isOpen:l===s+x.length,toggleFAQ:()=>c(s+x.length)},s+x.length)))})]}),r.jsx("div",{className:"md:hidden space-y-4",children:t.map(((e,s)=>r.jsx(a,{faq:e,index:s,isOpen:l===s,toggleFAQ:()=>c(s)},s)))}),r.jsx("div",{className:"mt-12 mb-6 text-center",children:r.jsx("a",{href:"/faq",className:"inline-block text-sm font-medium transition-all duration-300 px-6 py-2.5 rounded-full\n            bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] text-white hover:text-white\n            shadow-glow hover:shadow-glow-light hover:animate-glow-border\n            hover:bg-animated-gradient bg-[length:400%_400%] hover:animate-gradient-move\n            dark:bg-gradient-to-b dark:from-[#1df292] dark:to-[#0db875] dark:text-black\n            dark:shadow-glow-dark dark:hover:shadow-glow-dark\n            dark:hover:bg-animated-gradient-dark",style:{minWidth:"180px",textAlign:"center"},children:"View All FAQs"})})]})]})},a=({faq:e,index:t,isOpen:a,toggleFAQ:n})=>r.jsxs(s.div,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5,delay:.05*t},viewport:{once:!0},className:`bg-design-card/80 backdrop-blur-sm border ${a?"border-design-primary/30":"border-design-border"} rounded-xl overflow-hidden shadow-sm hover:shadow-md transition-all`,children:[r.jsxs("button",{className:"w-full text-left p-4 flex justify-between items-center focus:outline-none focus:ring-2 focus:ring-design-primary/30 focus:ring-inset",onClick:n,"aria-expanded":a,children:[r.jsx("h3",{className:"text-sm font-medium text-design-foreground pr-8",children:e.question}),r.jsx("div",{className:"flex-shrink-0 bg-design-primary/10 rounded-full p-2 transform transition-transform duration-200 "+(a?"rotate-180 bg-design-primary/20":""),children:r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-5 w-5 text-design-primary",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M19 9l-7 7-7-7"})})})]}),r.jsx(i,{children:a&&r.jsx(s.div,{initial:{height:0,opacity:0},animate:{height:"auto",opacity:1},exit:{height:0,opacity:0},transition:{duration:.3},children:r.jsxs("div",{className:"px-4 pb-4 text-design-muted-foreground border-t border-design-border pt-3",children:[r.jsx("p",{className:"leading-relaxed text-xs",children:e.answer}),r.jsxs("div",{className:"mt-4 pt-4 border-t border-design-border/30",children:[r.jsx("p",{className:"text-sm font-medium text-design-foreground mb-2",children:"Related Questions:"}),r.jsxs("div",{className:"flex flex-wrap gap-2",children:[r.jsx("a",{href:"/faq/#coupon-codes",className:"text-[14px] bg-design-primary/10 text-design-primary px-2 py-1 rounded-full hover:bg-design-primary/20 transition-colors",children:"Vape coupon codes"}),r.jsx("a",{href:"/faq/#best-deals",className:"text-[14px] bg-design-primary/10 text-design-primary px-2 py-1 rounded-full hover:bg-design-primary/20 transition-colors",children:"Best vape deals"}),r.jsx("a",{href:"/faq/#discount-juice",className:"text-[14px] bg-design-primary/10 text-design-primary px-2 py-1 rounded-full hover:bg-design-primary/20 transition-colors",children:"Discount vape juice"})]})]})]})})})]});export{t as FAQSection};
