import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$InfoPageLayout } from '../assets/js/InfoPageLayout-Bg861p5k.js';
import { $ as $$InfoPageHeader, a as $$InfoPageSection } from '../assets/js/InfoPageSection-CrV_q83G.js';
import { jsx, jsxs } from 'react/jsx-runtime';
import { useState, useCallback, useMemo, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import confetti from 'canvas-confetti';
import { ArrowLeft, Send, ArrowRight, Check, User, Mail, Loader2, Clock, HelpCircle, BookOpen, Users } from 'lucide-react';
import { u as useToast, G as GlowingButton } from '../assets/js/app/design-system-CUssmfny.js';
import { c as createServerSupabaseClient } from '../assets/js/server-e_5TR1Eu.js';
export { renderers } from '../renderers.mjs';

const contactCategories = [
  {
    value: "general",
    label: "General Inquiry",
    icon: "💬",
    subcategories: [
      { value: "services", label: "Services Question", description: "Ask about our services" },
      { value: "navigation", label: "Website Help", description: "Need help using the site" },
      { value: "account", label: "Account Issues", description: "Login or account problems" },
      { value: "other", label: "Other", description: "Something else" }
    ]
  },
  {
    value: "bug",
    label: "Report a Bug",
    icon: "🐛",
    subcategories: [
      { value: "not-loading", label: "Site Not Loading", description: "Website loading issues" },
      { value: "broken-link", label: "Broken Link", description: "Missing or broken pages" },
      { value: "payment", label: "Payment Problem", description: "Checkout or payment issues" },
      { value: "coupon", label: "Coupon Not Working", description: "Coupon code problems" },
      { value: "other-technical", label: "Other Technical", description: "Other technical issues" }
    ]
  },
  {
    value: "feedback",
    label: "Feedback",
    icon: "💡",
    subcategories: [
      { value: "feature-suggestion", label: "Feature Request", description: "Suggest new features" },
      { value: "improvement", label: "Improvement Idea", description: "Improve existing features" },
      { value: "negative", label: "Report Issue", description: "Report negative experience" },
      { value: "positive", label: "Share Success", description: "Share positive experience" },
      { value: "other-feedback", label: "Other Feedback", description: "Other feedback" }
    ]
  },
  {
    value: "partnership",
    label: "Partnership",
    icon: "🤝",
    subcategories: [
      { value: "business-collab", label: "Business Partnership", description: "Business collaboration" },
      { value: "affiliate", label: "Affiliate Program", description: "Join our affiliate program" },
      { value: "merchant", label: "Merchant Listing", description: "List your business" },
      { value: "sponsorship", label: "Sponsorship", description: "Advertising opportunities" },
      { value: "other-partnership", label: "Other Partnership", description: "Other partnership requests" }
    ]
  }
];
if (typeof window !== "undefined") {
  console.warn("reCAPTCHA Site Key not configured");
}
const FloatingLabelInput = ({ id, label, type = "text", value, onChange, error, icon, required, placeholder }) => {
  const [isFocused, setIsFocused] = useState(false);
  const hasValue = value.length > 0;
  const shouldFloat = isFocused || hasValue;
  return /* @__PURE__ */ jsxs("div", { className: "relative mb-6", children: [
    /* @__PURE__ */ jsxs("div", { className: "relative", children: [
      icon && /* @__PURE__ */ jsx("div", { className: "absolute left-3 sm:left-4 top-1/2 transform -translate-y-1/2 text-design-muted-foreground z-10", children: /* @__PURE__ */ jsx("div", { className: "w-4 h-4 sm:w-5 sm:h-5", children: icon }) }),
      /* @__PURE__ */ jsx(
        "input",
        {
          id,
          type,
          value,
          onChange: (e) => onChange(e.target.value),
          onFocus: () => setIsFocused(true),
          onBlur: () => setIsFocused(false),
          placeholder: isFocused ? placeholder : "",
          className: `
            w-full h-10 sm:h-14 px-2 sm:px-4 ${icon ? "pl-8 sm:pl-12" : "pl-2 sm:pl-4"} pr-2 sm:pr-4
            bg-design-background/50 backdrop-blur-sm
            border-2 rounded-lg sm:rounded-2xl
            text-design-foreground text-sm sm:text-base
            transition-all duration-300 ease-out
            focus:outline-none focus:ring-0
            ${error ? "border-red-500 focus:border-red-500" : "border-design-border focus:border-design-primary"}
            ${shouldFloat ? "pt-5 sm:pt-6 pb-1 sm:pb-2" : "pt-3 sm:pt-4 pb-3 sm:pb-4"}
          `
        }
      ),
      /* @__PURE__ */ jsxs(
        "label",
        {
          htmlFor: id,
          className: `
            absolute left-2 sm:left-4 ${icon ? "left-8 sm:left-12" : "left-2 sm:left-4"}
            transition-all duration-300 ease-out
            pointer-events-none
            text-design-muted-foreground
            ${shouldFloat ? "top-1.5 sm:top-2 text-xs font-medium" : "top-1/2 transform -translate-y-1/2 text-sm sm:text-base"}
          `,
          children: [
            label,
            " ",
            required && /* @__PURE__ */ jsx("span", { className: "text-red-500", children: "*" })
          ]
        }
      )
    ] }),
    error && /* @__PURE__ */ jsx("p", { className: "text-red-500 text-sm mt-2 ml-1 animate-in slide-in-from-left-2 duration-200", children: error })
  ] });
};
const FloatingLabelTextarea = ({ id, label, value, onChange, error, required, placeholder, rows = 4 }) => {
  const [isFocused, setIsFocused] = useState(false);
  const hasValue = value.length > 0;
  const shouldFloat = isFocused || hasValue;
  return /* @__PURE__ */ jsxs("div", { className: "relative mb-6", children: [
    /* @__PURE__ */ jsxs("div", { className: "relative", children: [
      /* @__PURE__ */ jsx(
        "textarea",
        {
          id,
          value,
          onChange: (e) => onChange(e.target.value),
          onFocus: () => setIsFocused(true),
          onBlur: () => setIsFocused(false),
          placeholder: isFocused ? placeholder : "",
          rows,
          className: `
            w-full px-2 sm:px-4 pr-2 sm:pr-4
            bg-design-background/50 backdrop-blur-sm
            border-2 rounded-lg sm:rounded-2xl
            text-design-foreground text-sm sm:text-base
            transition-all duration-300 ease-out
            focus:outline-none focus:ring-0
            resize-none
            ${error ? "border-red-500 focus:border-red-500" : "border-design-border focus:border-design-primary"}
            ${shouldFloat ? "pt-7 sm:pt-8 pb-3 sm:pb-4" : "pt-5 sm:pt-6 pb-3 sm:pb-4"}
          `
        }
      ),
      /* @__PURE__ */ jsxs(
        "label",
        {
          htmlFor: id,
          className: `
            absolute left-2 sm:left-4
            transition-all duration-300 ease-out
            pointer-events-none
            text-design-muted-foreground
            ${shouldFloat ? "top-1.5 sm:top-2 text-xs font-medium" : "top-5 sm:top-6 text-sm sm:text-base"}
          `,
          children: [
            label,
            " ",
            required && /* @__PURE__ */ jsx("span", { className: "text-red-500", children: "*" })
          ]
        }
      )
    ] }),
    error && /* @__PURE__ */ jsx("p", { className: "text-red-500 text-sm mt-2 ml-1 animate-in slide-in-from-left-2 duration-200", children: error })
  ] });
};
const Button = ({
  children,
  onClick,
  type = "button",
  variant = "primary",
  size = "md",
  disabled,
  loading,
  icon,
  iconPosition = "left",
  fullWidth,
  className = ""
}) => {
  const baseClasses = `
    relative inline-flex items-center justify-center
    font-medium rounded-2xl
    transition-all duration-300 ease-out
    focus:outline-none focus:ring-2 focus:ring-offset-2
    disabled:opacity-50 disabled:cursor-not-allowed
    transform active:scale-95
    ${fullWidth ? "w-full" : ""}
  `;
  const variantClasses = {
    primary: `
      bg-gradient-to-r from-design-primary to-design-primary/90
      hover:from-design-primary/90 hover:to-design-primary
      text-white shadow-lg hover:shadow-xl
      focus:ring-design-primary/50
    `,
    secondary: `
      bg-design-muted hover:bg-design-muted/80
      text-design-foreground
      border border-design-border
      focus:ring-design-primary/50
    `,
    ghost: `
      bg-transparent hover:bg-design-muted/50
      text-design-foreground
      focus:ring-design-primary/50
    `
  };
  const sizeClasses = {
    sm: "h-8 px-3 text-sm",
    md: "h-10 px-4 text-sm sm:h-12 sm:px-6 sm:text-base",
    lg: "h-12 px-6 text-base sm:h-14 sm:px-8 sm:text-lg"
  };
  return /* @__PURE__ */ jsxs(
    "button",
    {
      type,
      onClick,
      disabled: disabled || loading,
      className: `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`,
      children: [
        loading && /* @__PURE__ */ jsx(Loader2, { className: "w-4 h-4 mr-2 animate-spin" }),
        !loading && icon && iconPosition === "left" && /* @__PURE__ */ jsx("span", { className: "mr-2", children: icon }),
        children,
        !loading && icon && iconPosition === "right" && /* @__PURE__ */ jsx("span", { className: "ml-2", children: icon })
      ]
    }
  );
};
const ContactFormMobileOptimized = () => {
  const [currentStep, setCurrentStep] = useState("category");
  const [isLoading, setIsLoading] = useState(false);
  const [isSuccess, setIsSuccess] = useState(false);
  const [errorMessage, setErrorMessage] = useState(null);
  const [touchStart, setTouchStart] = useState(null);
  const [touchEnd, setTouchEnd] = useState(null);
  const { toast } = useToast();
  const executeRecaptcha = useCallback(async () => {
    return new Promise((resolve, reject) => {
      {
        console.warn("reCAPTCHA site key not configured, using development mode");
        resolve("development_mode_dummy_token");
        return;
      }
    });
  }, []);
  const [formValues, setFormValues] = useState({
    category: "",
    subcategory: "",
    description: "",
    name: "",
    email: ""
  });
  const {
    handleSubmit,
    setValue,
    formState: { errors },
    reset
  } = useForm({
    defaultValues: formValues
  });
  const stepConfig = useMemo(() => ({
    category: { title: "How can we help?", subtitle: "Choose a category", progress: 20 },
    subcategory: { title: "Be more specific", subtitle: "Select a subcategory", progress: 40 },
    description: { title: "Tell us more", subtitle: "Describe your inquiry", progress: 60 },
    user: { title: "Contact details", subtitle: "How can we reach you?", progress: 80 },
    submit: { title: "Review & submit", subtitle: "Check your information", progress: 100 }
  }), []);
  const handleFieldChange = useCallback((field, value) => {
    setFormValues((prev) => ({ ...prev, [field]: value }));
    setValue(field, value);
    setErrorMessage(null);
  }, [setValue]);
  const getCurrentSubcategories = useCallback(() => {
    const category = contactCategories.find((cat) => cat.value === formValues.category);
    return category?.subcategories || [];
  }, [formValues.category]);
  const handleTouchStart = useCallback((e) => {
    setTouchEnd(null);
    setTouchStart(e.targetTouches[0].clientX);
  }, []);
  const handleTouchMove = useCallback((e) => {
    setTouchEnd(e.targetTouches[0].clientX);
  }, []);
  const handleTouchEnd = useCallback(() => {
    if (!touchStart || !touchEnd) return;
    const distance = touchStart - touchEnd;
    const isLeftSwipe = distance > 50;
    const isRightSwipe = distance < -50;
    if (isLeftSwipe && canGoNext()) {
      goToNextStep();
    }
    if (isRightSwipe && canGoBack()) {
      goToPrevStep();
    }
  }, [touchStart, touchEnd]);
  const canGoNext = useCallback(() => {
    switch (currentStep) {
      case "category":
        return !!formValues.category;
      case "subcategory":
        return !!formValues.subcategory;
      case "description":
        return !!formValues.description;
      case "user":
        return !!formValues.name && !!formValues.email && /^\S+@\S+\.\S+$/.test(formValues.email);
      case "submit":
        return true;
      // reCAPTCHA v3 will be executed on submit
      default:
        return false;
    }
  }, [currentStep, formValues]);
  const canGoBack = useCallback(() => {
    return currentStep !== "category" && !isSuccess;
  }, [currentStep, isSuccess]);
  const goToNextStep = useCallback(() => {
    if (!canGoNext()) {
      setErrorMessage(getValidationMessage());
      return;
    }
    const steps = ["category", "subcategory", "description", "user", "submit"];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex < steps.length - 1) {
      setCurrentStep(steps[currentIndex + 1]);
      setErrorMessage(null);
    }
  }, [currentStep, canGoNext]);
  const goToPrevStep = useCallback(() => {
    if (!canGoBack()) return;
    const steps = ["category", "subcategory", "description", "user", "submit"];
    const currentIndex = steps.indexOf(currentStep);
    if (currentIndex > 0) {
      setCurrentStep(steps[currentIndex - 1]);
      setErrorMessage(null);
    }
  }, [currentStep, canGoBack]);
  const getValidationMessage = useCallback(() => {
    switch (currentStep) {
      case "category":
        return "Please select a category";
      case "subcategory":
        return "Please select a subcategory";
      case "description":
        return "Please describe your inquiry";
      case "user":
        if (!formValues.name) return "Please enter your name";
        if (!formValues.email) return "Please enter your email";
        if (!/^\S+@\S+\.\S+$/.test(formValues.email)) return "Please enter a valid email";
        return "Please fill out all fields";
      case "submit":
        return "Please complete the verification";
      default:
        return "Please complete this step";
    }
  }, [currentStep, formValues]);
  const onSubmit = useCallback(async (data) => {
    setIsLoading(true);
    setErrorMessage(null);
    try {
      const token = await executeRecaptcha();
      const formData = {
        name: data.name.trim(),
        email: data.email.trim(),
        subject: `${contactCategories.find((c) => c.value === data.category)?.label || data.category} - ${getCurrentSubcategories().find((s) => s.value === data.subcategory)?.label || data.subcategory}`,
        message: data.description.trim(),
        token
      };
      const response = await fetch("/api/contact", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(formData)
      });
      if (!response.ok) {
        const errorData = await response.json().catch(() => ({ error: "Unknown error" }));
        throw new Error(errorData.error || "Failed to send message");
      }
      setIsSuccess(true);
      confetti({
        particleCount: 100,
        spread: 70,
        origin: { y: 0.6 }
      });
      if (typeof window !== "undefined" && window.gtag) {
        window.gtag("event", "form_submission", {
          event_category: "engagement",
          event_label: `${data.category} - ${data.subcategory}`,
          value: 1
        });
      }
      toast("Success", {
        description: "Your message has been sent successfully!",
        variant: "success"
      });
    } catch (error) {
      setErrorMessage(error.message || "Failed to send message. Please try again.");
      toast("Error", {
        description: error.message || "Failed to send message. Please try again.",
        variant: "destructive"
      });
    } finally {
      setIsLoading(false);
    }
  }, [executeRecaptcha, getCurrentSubcategories, toast]);
  useEffect(() => {
    const handleKeyDown = (e) => {
      if (e.key === "Enter" && canGoNext()) {
        e.preventDefault();
        goToNextStep();
      }
      if (e.key === "Escape" && canGoBack()) {
        e.preventDefault();
        goToPrevStep();
      }
    };
    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [canGoNext, canGoBack, goToNextStep, goToPrevStep]);
  useEffect(() => {
    Object.entries(formValues).forEach(([key, value]) => {
      setValue(key, value);
    });
  }, [formValues, setValue]);
  const renderStepContent = () => {
    if (isSuccess) {
      return /* @__PURE__ */ jsxs("div", { className: "text-center py-6 sm:py-8 animate-in fade-in duration-500", children: [
        /* @__PURE__ */ jsx("div", { className: "w-12 h-12 sm:w-16 sm:h-16 mx-auto mb-4 sm:mb-6 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center", children: /* @__PURE__ */ jsx(Check, { className: "w-6 h-6 sm:w-8 sm:h-8 text-green-600 dark:text-green-400" }) }),
        /* @__PURE__ */ jsx("h2", { className: "text-xl sm:text-2xl font-bold text-design-foreground mb-3 sm:mb-4", children: "Thank You!" }),
        /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground mb-6 sm:mb-8 max-w-md mx-auto text-sm sm:text-base px-4", children: "Your message has been received. We'll get back to you within 1-3 business days." }),
        /* @__PURE__ */ jsxs("div", { className: "bg-design-card/50 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 mb-6 sm:mb-8 border border-design-border", children: [
          /* @__PURE__ */ jsx("h3", { className: "font-semibold mb-3 sm:mb-4 text-design-foreground text-sm sm:text-base", children: "What happens next?" }),
          /* @__PURE__ */ jsxs("div", { className: "space-y-2 sm:space-y-3 text-xs sm:text-sm text-design-muted-foreground text-left", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex items-start", children: [
              /* @__PURE__ */ jsx(Check, { className: "w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" }),
              /* @__PURE__ */ jsxs("span", { children: [
                "Confirmation email sent to ",
                /* @__PURE__ */ jsx("strong", { className: "text-design-foreground break-all", children: formValues.email })
              ] })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-start", children: [
              /* @__PURE__ */ jsx(Check, { className: "w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" }),
              /* @__PURE__ */ jsx("span", { children: "Our team will review your message" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-start", children: [
              /* @__PURE__ */ jsx(Check, { className: "w-3 h-3 sm:w-4 sm:h-4 text-green-500 mr-2 sm:mr-3 mt-0.5 flex-shrink-0" }),
              /* @__PURE__ */ jsx("span", { children: "You'll receive a reply within 1-3 business days" })
            ] })
          ] })
        ] }),
        /* @__PURE__ */ jsx(
          Button,
          {
            onClick: () => {
              setIsSuccess(false);
              reset();
              setFormValues({
                category: "",
                subcategory: "",
                description: "",
                name: "",
                email: ""
              });
              setCurrentStep("category");
            },
            variant: "primary",
            size: "lg",
            fullWidth: true,
            children: "Send Another Message"
          }
        )
      ] });
    }
    switch (currentStep) {
      case "category":
        return /* @__PURE__ */ jsx("div", { className: "space-y-6", children: /* @__PURE__ */ jsx("div", { className: "grid gap-4", children: contactCategories.map((category) => /* @__PURE__ */ jsx(
          "div",
          {
            onClick: () => handleFieldChange("category", category.value),
            className: `
                    p-3 sm:p-4 rounded-xl sm:rounded-2xl border-2 cursor-pointer
                    transition-all duration-300 ease-out
                    transform hover:scale-[1.02] active:scale-[0.98]
                    touch-manipulation
                    ${formValues.category === category.value ? "border-design-primary bg-design-primary/10 shadow-lg" : "border-design-border bg-design-card/50 hover:border-design-primary/50"}
                  `,
            children: /* @__PURE__ */ jsxs("div", { className: "flex items-center", children: [
              /* @__PURE__ */ jsx("div", { className: "text-xl sm:text-2xl mr-3 sm:mr-4 flex-shrink-0", children: category.icon }),
              /* @__PURE__ */ jsxs("div", { className: "flex-1 min-w-0", children: [
                /* @__PURE__ */ jsx("h3", { className: "font-semibold text-design-foreground text-sm sm:text-base", children: category.label }),
                /* @__PURE__ */ jsxs("p", { className: "text-xs sm:text-sm text-design-muted-foreground mt-1 truncate", children: [
                  category.subcategories.length,
                  " options available"
                ] })
              ] }),
              /* @__PURE__ */ jsx("div", { className: `
                      w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 flex items-center justify-center flex-shrink-0
                      ${formValues.category === category.value ? "border-design-primary bg-design-primary" : "border-design-border"}
                    `, children: formValues.category === category.value && /* @__PURE__ */ jsx("div", { className: "w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-white" }) })
            ] })
          },
          category.value
        )) }) });
      case "subcategory":
        const subcategories = getCurrentSubcategories();
        return /* @__PURE__ */ jsx("div", { className: "space-y-4", children: subcategories.map((subcategory) => /* @__PURE__ */ jsx(
          "div",
          {
            onClick: () => handleFieldChange("subcategory", subcategory.value),
            className: `
                  p-3 sm:p-4 rounded-xl sm:rounded-2xl border-2 cursor-pointer
                  transition-all duration-300 ease-out
                  transform hover:scale-[1.02] active:scale-[0.98]
                  touch-manipulation
                  ${formValues.subcategory === subcategory.value ? "border-design-primary bg-design-primary/10 shadow-lg" : "border-design-border bg-design-card/50 hover:border-design-primary/50"}
                `,
            children: /* @__PURE__ */ jsxs("div", { className: "flex items-center", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex-1 min-w-0", children: [
                /* @__PURE__ */ jsx("h3", { className: "font-semibold text-design-foreground text-sm sm:text-base", children: subcategory.label }),
                subcategory.description && /* @__PURE__ */ jsx("p", { className: "text-xs sm:text-sm text-design-muted-foreground mt-1", children: subcategory.description })
              ] }),
              /* @__PURE__ */ jsx("div", { className: `
                    w-5 h-5 sm:w-6 sm:h-6 rounded-full border-2 flex items-center justify-center flex-shrink-0 ml-3
                    ${formValues.subcategory === subcategory.value ? "border-design-primary bg-design-primary" : "border-design-border"}
                  `, children: formValues.subcategory === subcategory.value && /* @__PURE__ */ jsx("div", { className: "w-1.5 h-1.5 sm:w-2 sm:h-2 rounded-full bg-white" }) })
            ] })
          },
          subcategory.value
        )) });
      case "description":
        return /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsx(
          FloatingLabelTextarea,
          {
            id: "description",
            label: "Message",
            value: formValues.description,
            onChange: (value) => handleFieldChange("description", value),
            error: errors.description?.message,
            required: true,
            placeholder: "Please describe your issue or question in detail...",
            rows: 6
          }
        ) });
      case "user":
        return /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
          /* @__PURE__ */ jsx(
            FloatingLabelInput,
            {
              id: "name",
              label: "Full Name",
              value: formValues.name,
              onChange: (value) => handleFieldChange("name", value),
              error: errors.name?.message,
              icon: /* @__PURE__ */ jsx(User, { className: "w-5 h-5" }),
              required: true,
              placeholder: "Enter your full name"
            }
          ),
          /* @__PURE__ */ jsx(
            FloatingLabelInput,
            {
              id: "email",
              label: "Email Address",
              type: "email",
              value: formValues.email,
              onChange: (value) => handleFieldChange("email", value),
              error: errors.email?.message,
              icon: /* @__PURE__ */ jsx(Mail, { className: "w-5 h-5" }),
              required: true,
              placeholder: "Enter your email address"
            }
          )
        ] });
      case "submit":
        return /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
          /* @__PURE__ */ jsxs("div", { className: "bg-design-card/50 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 border border-design-border", children: [
            /* @__PURE__ */ jsx("h3", { className: "font-semibold mb-4 text-design-foreground text-sm sm:text-base", children: "Review Your Information" }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-3 sm:space-y-4 text-sm", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "font-medium text-design-foreground text-xs sm:text-sm", children: "Category:" }),
                /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground text-xs sm:text-sm mt-1", children: contactCategories.find((c) => c.value === formValues.category)?.label })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "font-medium text-design-foreground text-xs sm:text-sm", children: "Subcategory:" }),
                /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground text-xs sm:text-sm mt-1", children: getCurrentSubcategories().find((s) => s.value === formValues.subcategory)?.label })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "font-medium text-design-foreground text-xs sm:text-sm", children: "Message:" }),
                /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground text-xs sm:text-sm mt-1 line-clamp-3", children: formValues.description })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("span", { className: "font-medium text-design-foreground text-xs sm:text-sm", children: "Contact:" }),
                /* @__PURE__ */ jsxs("p", { className: "text-design-muted-foreground text-xs sm:text-sm mt-1 break-all", children: [
                  formValues.name,
                  " (",
                  formValues.email,
                  ")"
                ] })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "text-center text-sm text-gray-600 dark:text-gray-400", children: /* @__PURE__ */ jsx("p", { children: "Protected by reCAPTCHA v3" }) })
        ] });
      default:
        return null;
    }
  };
  return /* @__PURE__ */ jsx(
    "div",
    {
      className: "w-full max-w-2xl mx-auto px-2 sm:px-6 lg:px-8",
      onTouchStart: handleTouchStart,
      onTouchMove: handleTouchMove,
      onTouchEnd: handleTouchEnd,
      children: /* @__PURE__ */ jsxs("div", { className: "bg-design-background/80 backdrop-blur-xl border border-design-border/50 rounded-xl sm:rounded-3xl shadow-2xl overflow-hidden", children: [
        !isSuccess && /* @__PURE__ */ jsxs("div", { className: "bg-gradient-to-r from-design-primary/10 to-design-primary/5 p-3 sm:p-6 border-b border-design-border/50", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between mb-3 sm:mb-4 space-y-2 sm:space-y-0", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
              /* @__PURE__ */ jsx("h1", { className: "text-base sm:text-xl font-bold text-design-foreground", children: stepConfig[currentStep].title }),
              /* @__PURE__ */ jsx("p", { className: "text-xs sm:text-sm text-design-muted-foreground mt-1", children: stepConfig[currentStep].subtitle })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "text-left sm:text-right", children: /* @__PURE__ */ jsxs("div", { className: "text-xs font-medium text-design-primary bg-design-primary/10 px-2 py-1 rounded-full", children: [
              "Step ",
              ["category", "subcategory", "description", "user", "submit"].indexOf(currentStep) + 1,
              " of 5"
            ] }) })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "w-full bg-design-muted/30 rounded-full h-2", children: /* @__PURE__ */ jsx(
            "div",
            {
              className: "bg-gradient-to-r from-design-primary to-design-primary/80 h-2 rounded-full transition-all duration-500 ease-out",
              style: { width: `${stepConfig[currentStep].progress}%` }
            }
          ) }),
          /* @__PURE__ */ jsx("div", { className: "flex justify-center mt-3", children: /* @__PURE__ */ jsxs("p", { className: "text-xs text-design-muted-foreground text-center", children: [
            /* @__PURE__ */ jsx("span", { className: "hidden sm:inline", children: "💡 Swipe left/right or use arrow keys to navigate" }),
            /* @__PURE__ */ jsx("span", { className: "sm:hidden", children: "💡 Swipe to navigate" })
          ] }) })
        ] }),
        errorMessage && /* @__PURE__ */ jsx("div", { className: "mx-4 sm:mx-6 mt-4 sm:mt-6 p-3 sm:p-4 bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-xl sm:rounded-2xl", children: /* @__PURE__ */ jsx("p", { className: "text-red-600 dark:text-red-400 text-sm font-medium text-center sm:text-left", children: errorMessage }) }),
        /* @__PURE__ */ jsx("div", { className: "p-3 sm:p-6 min-h-[300px] sm:min-h-[400px]", children: renderStepContent() }),
        !isSuccess && /* @__PURE__ */ jsxs("div", { className: "bg-design-background/50 backdrop-blur-sm p-3 sm:p-6 border-t border-design-border/50", children: [
          /* @__PURE__ */ jsxs("div", { className: "sm:hidden", children: [
            /* @__PURE__ */ jsx("div", { className: "flex justify-center space-x-2 mb-3", children: ["category", "subcategory", "description", "user", "submit"].map((step, index) => /* @__PURE__ */ jsx(
              "div",
              {
                className: `w-2 h-2 rounded-full transition-all duration-300 ${step === currentStep ? "bg-design-primary scale-125" : ["category", "subcategory", "description", "user", "submit"].indexOf(currentStep) > index ? "bg-design-primary/60" : "bg-design-muted"}`
              },
              step
            )) }),
            /* @__PURE__ */ jsxs("div", { className: "flex gap-2", children: [
              /* @__PURE__ */ jsx(
                Button,
                {
                  onClick: goToPrevStep,
                  disabled: !canGoBack(),
                  variant: "ghost",
                  size: "sm",
                  icon: /* @__PURE__ */ jsx(ArrowLeft, { className: "w-3 h-3" }),
                  className: `flex-1 ${!canGoBack() ? "invisible" : ""}`,
                  children: "Back"
                }
              ),
              currentStep === "submit" ? /* @__PURE__ */ jsx(
                Button,
                {
                  onClick: handleSubmit(onSubmit),
                  disabled: !canGoNext() || isLoading,
                  loading: isLoading,
                  variant: "primary",
                  size: "sm",
                  icon: /* @__PURE__ */ jsx(Send, { className: "w-3 h-3" }),
                  iconPosition: "right",
                  className: "flex-[2]",
                  children: isLoading ? "Sending..." : "Submit"
                }
              ) : /* @__PURE__ */ jsx(
                Button,
                {
                  onClick: goToNextStep,
                  disabled: !canGoNext(),
                  variant: "primary",
                  size: "sm",
                  icon: /* @__PURE__ */ jsx(ArrowRight, { className: "w-3 h-3" }),
                  iconPosition: "right",
                  className: "flex-[2]",
                  children: "Continue"
                }
              )
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "hidden sm:flex justify-between items-center gap-4", children: [
            /* @__PURE__ */ jsx(
              Button,
              {
                onClick: goToPrevStep,
                disabled: !canGoBack(),
                variant: "ghost",
                size: "lg",
                icon: /* @__PURE__ */ jsx(ArrowLeft, { className: "w-4 h-4" }),
                className: `${!canGoBack() ? "invisible" : ""}`,
                children: "Back"
              }
            ),
            /* @__PURE__ */ jsx("div", { className: "flex space-x-2", children: ["category", "subcategory", "description", "user", "submit"].map((step, index) => /* @__PURE__ */ jsx(
              "div",
              {
                className: `w-2 h-2 rounded-full transition-all duration-300 ${step === currentStep ? "bg-design-primary scale-125" : ["category", "subcategory", "description", "user", "submit"].indexOf(currentStep) > index ? "bg-design-primary/60" : "bg-design-muted"}`
              },
              step
            )) }),
            currentStep === "submit" ? /* @__PURE__ */ jsx(
              Button,
              {
                onClick: handleSubmit(onSubmit),
                disabled: !canGoNext() || isLoading,
                loading: isLoading,
                variant: "primary",
                size: "lg",
                icon: /* @__PURE__ */ jsx(Send, { className: "w-4 h-4" }),
                iconPosition: "right",
                children: isLoading ? "Sending..." : "Submit"
              }
            ) : /* @__PURE__ */ jsx(
              Button,
              {
                onClick: goToNextStep,
                disabled: !canGoNext(),
                variant: "primary",
                size: "lg",
                icon: /* @__PURE__ */ jsx(ArrowRight, { className: "w-4 h-4" }),
                iconPosition: "right",
                children: "Continue"
              }
            )
          ] })
        ] })
      ] })
    }
  );
};

const $$Astro = createAstro("http://localhost:4321");
const $$Contact = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Contact;
  const title = "Contact Us | VapeHybrid - Get in Touch Today";
  const description = "Have questions about our vape products or services? Contact VapeHybrid's friendly team for support, partnership inquiries, or merchant listings. We're here to help!";
  const canonicalUrl = new URL(Astro2.url.pathname, "https://vapehybrid.com").toString();
  const obfuscateEmail = (email) => {
    return email.replace("@", " [at] ").replace(".", " [dot] ");
  };
  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      {
        "@type": "WebPage",
        "@id": `${canonicalUrl}#webpage`,
        "url": canonicalUrl,
        "name": title,
        "description": description,
        "inLanguage": "en-US",
        "isPartOf": {
          "@id": "https://vapehybrid.com/#website"
        },
        "about": {
          "@id": "https://vapehybrid.com/#organization"
        },
        "datePublished": "2024-01-01T00:00:00+00:00",
        "dateModified": (/* @__PURE__ */ new Date()).toISOString()
      },
      {
        "@type": "ContactPage",
        "name": title,
        "description": description,
        "publisher": {
          "@type": "Organization",
          "@id": "https://vapehybrid.com/#organization",
          "name": "VapeHybrid",
          "url": "https://vapehybrid.com",
          "logo": {
            "@type": "ImageObject",
            "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`,
            "width": 200,
            "height": 50
          },
          "contactPoint": {
            "@type": "ContactPoint",
            "telephone": "+1-XXX-XXX-XXXX",
            "contactType": "customer service",
            "availableLanguage": ["en"],
            "areaServed": ["US"],
            "contactOption": "TollFree"
          },
          "sameAs": [
            "https://twitter.com/vapehybrid",
            "https://facebook.com/vapehybrid",
            "https://instagram.com/vapehybrid"
          ]
        },
        "mainEntityOfPage": {
          "@id": `${canonicalUrl}#webpage`
        }
      }
    ]
  };
  const supabase = createServerSupabaseClient();
  const { data: merchantsData, error } = await supabase.from("merchants").select("name, logo_url").eq("status", "active").order("name");
  if (error) {
    console.error("Error fetching merchants:", error);
  }
  return renderTemplate`${renderComponent($$result, "InfoPageLayout", $$InfoPageLayout, { "title": title, "description": description, "structuredData": structuredData, "pattern": "dots" }, { "default": async ($$result2) => renderTemplate` ${renderComponent($$result2, "InfoPageHeader", $$InfoPageHeader, { "title": "Contact Us", "subtitle": "Have a question or interested in partnering with VapeHybrid? We'd love to hear from you.", "pattern": "grid", "glass": true })}  ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Partnership Opportunities", "iconName": "building", "centerTitle": true, "background": "transparent", "pattern": "wave", "glass": true }, { "default": async ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="max-w-3xl mx-auto mb-12"> <div class="flex flex-col items-center justify-center mb-8 text-center"> <div class="max-w-2xl mx-auto"> <p class="text-design-muted-foreground">
We partner with vape merchants and brands to bring the best deals to our users. If you're interested in listing your products or services on VapeHybrid, please get in touch.
</p> </div> </div> <div class="grid md:grid-cols-2 gap-8 mb-12 max-w-2xl mx-auto"> <div class="flex flex-col items-center text-center"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full mb-4"> ${renderComponent($$result3, "Mail", Mail, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> <div> <h3 class="text-lg font-bold text-design-foreground mb-2">Email Us</h3> <p class="text-design-muted-foreground">partnerships${obfuscateEmail("@vapehybrid.com")}</p> </div> </div> <div class="flex flex-col items-center text-center"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full mb-4"> ${renderComponent($$result3, "Clock", Clock, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> <div> <h3 class="text-lg font-bold text-design-foreground mb-2">Response Time</h3> <p class="text-design-muted-foreground">We reply within 1–3 business days</p> </div> </div> </div> </div>  ${renderComponent($$result3, "InfoPageSection", $$InfoPageSection, { "title": "Send Us a Message", "iconName": "messageSquare", "centerTitle": true, "background": "transparent", "pattern": "dots", "glass": true }, { "default": async ($$result4) => renderTemplate` <div class="flex justify-center w-full px-0"> ${renderComponent($$result4, "ContactFormFixed", ContactFormMobileOptimized, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ContactFormFixed", "client:component-export": "default" })} </div> ` })}  ${renderComponent($$result3, "InfoPageSection", $$InfoPageSection, { "title": "Helpful Resources", "centerTitle": true, "background": "transparent", "pattern": "circles", "glass": true }, { "default": async ($$result4) => renderTemplate` <div class="max-w-4xl mx-auto"> <div class="grid grid-cols-1 md:grid-cols-3 gap-12"> <div class="text-center flex flex-col h-full bg-design-card/30 backdrop-blur-sm border border-design-border rounded-xl p-6"> <div class="flex justify-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full"> ${renderComponent($$result4, "HelpCircle", HelpCircle, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> </div> <h3 class="text-xl font-bold text-design-foreground mb-2">Frequently Asked Questions</h3> <p class="text-design-muted-foreground mb-4 flex-grow">Find answers to common questions about VapeHybrid and our services.</p> <div class="mt-auto pt-4"> ${renderComponent($$result4, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/faq", "className": "text-sm px-4 py-2", "aria-label": "Visit the FAQ page for answers to common questions", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result5) => renderTemplate`
FAQ: VapeHybrid Help
` })} </div> </div> <div class="text-center flex flex-col h-full bg-design-card/30 backdrop-blur-sm border border-design-border rounded-xl p-6"> <div class="flex justify-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full"> ${renderComponent($$result4, "BookOpen", BookOpen, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> </div> <h3 class="text-xl font-bold text-design-foreground mb-2">How It Works</h3> <p class="text-design-muted-foreground mb-4 flex-grow">Learn how VapeHybrid helps you find the best vaping deals and coupons.</p> <div class="mt-auto pt-4"> ${renderComponent($$result4, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/how-it-works", "className": "text-sm px-4 py-2", "aria-label": "Learn how VapeHybrid works", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result5) => renderTemplate`
How VapeHybrid Works
` })} </div> </div> <div class="text-center flex flex-col h-full bg-design-card/30 backdrop-blur-sm border border-design-border rounded-xl p-6"> <div class="flex justify-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full"> ${renderComponent($$result4, "Users", Users, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> </div> <h3 class="text-xl font-bold text-design-foreground mb-2">About Our Team</h3> <p class="text-design-muted-foreground mb-4 flex-grow">Meet the team behind VapeHybrid and learn about our mission.</p> <div class="mt-auto pt-4"> ${renderComponent($$result4, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/about", "className": "text-sm px-4 py-2", "aria-label": "Learn about the VapeHybrid team", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result5) => renderTemplate`
About VapeHybrid
` })} </div> </div> </div> </div> ` })} ${renderComponent($$result3, "InfoPageSection", $$InfoPageSection, { "title": "Our Global Reach", "iconName": "globe", "centerTitle": true, "background": "transparent", "pattern": "hexagon", "glass": true }, { "default": async ($$result4) => renderTemplate` <div class="max-w-3xl mx-auto text-center"> <p class="text-design-muted-foreground mx-auto mb-8">
VapeHybrid connects vapers with the best deals worldwide. Our platform features merchants and brands from across the globe, ensuring you find quality products at great prices no matter where you are.
</p> <div class="flex justify-center"> ${renderComponent($$result4, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/coupons", "className": "text-base", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result5) => renderTemplate`
Explore Global Coupons
<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg> ` })} </div> </div> ` })} ` })}` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/contact.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/contact.astro";
const $$url = "/contact";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Contact,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
