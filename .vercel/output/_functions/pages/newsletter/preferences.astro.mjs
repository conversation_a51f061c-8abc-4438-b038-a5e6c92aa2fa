import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../assets/js/MainLayout-BVJnMCp3.js';
import { s as supabaseAdmin } from '../../assets/js/supabase-admin-CJafpzMW.js';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Preferences = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Preferences;
  const token = Astro2.url.searchParams.get("token");
  const success = Astro2.url.searchParams.get("success") === "true";
  let subscriber = null;
  let error = null;
  let preferences = null;
  if (token) {
    try {
      const { data, error: queryError } = await supabaseAdmin.from("newsletter_subscribers").select("*").eq("management_token", token).single();
      if (queryError) {
        error = "Invalid or expired token. Please check your email for the correct link.";
      } else if (data) {
        subscriber = data;
        const { data: preferencesData, error: preferencesError } = await supabaseAdmin.from("newsletter_preferences").select("*").eq("subscriber_id", data.id).single();
        if (!preferencesError) {
          preferences = preferencesData;
        }
      } else {
        error = "Subscriber not found. Please check your email for the correct link.";
      }
    } catch (e) {
      console.error("Error fetching subscriber:", e);
      error = "An unexpected error occurred. Please try again later.";
    }
  } else {
    error = "No token provided. Please check your email for the correct link.";
  }
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Newsletter Preferences - VapeHybrid" }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-12"> <div class="max-w-2xl mx-auto"> <h1 class="text-3xl font-bold text-design-foreground mb-6">Newsletter Preferences</h1> ${error ? renderTemplate`<div class="bg-design-destructive/10 border border-design-destructive/30 rounded-lg p-6 mb-8"> <h2 class="text-xl font-semibold text-design-destructive mb-2">Error</h2> <p class="text-design-foreground">${error}</p> <div class="mt-4"> <a href="/" class="text-design-primary hover:underline">Return to Homepage</a> </div> </div>` : renderTemplate`<div class="bg-design-card border border-design-border rounded-lg p-6 mb-8"> <h2 class="text-xl font-semibold text-design-foreground mb-4">Your Subscription</h2> ${success && renderTemplate`<div class="mb-6 p-4 bg-design-success/10 border border-design-success/30 rounded-lg"> <p class="text-design-success font-medium">Your preferences have been updated successfully!</p> </div>`} <div class="mb-6"> <p class="text-design-muted-foreground mb-2">Email Address:</p> <p class="font-medium text-design-foreground">${subscriber.email}</p> </div> <div class="mb-6"> <p class="text-design-muted-foreground mb-2">Subscription Status:</p> <div class="flex items-center"> ${subscriber.confirmed ? renderTemplate`<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-design-success/10 text-design-success">
Active
</span>` : renderTemplate`<span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-design-warning/10 text-design-warning">
Pending Confirmation
</span>`} </div> </div> <form action="/api/newsletter/update-preferences" method="POST" class="space-y-6"> <input type="hidden" name="token"${addAttribute(token, "value")}> <div> <h3 class="text-lg font-medium text-design-foreground mb-3">Email Preferences</h3> <div class="space-y-3"> <label class="flex items-center space-x-3"> <input type="checkbox" name="weekly_digest" value="true"${addAttribute(preferences?.weekly_digest !== false, "checked")} class="h-5 w-5 rounded border-design-border text-design-primary focus:ring-design-primary"> <span class="text-design-foreground">Weekly Deal Digest</span> </label> <label class="flex items-center space-x-3"> <input type="checkbox" name="flash_deals" value="true"${addAttribute(preferences?.flash_deals !== false, "checked")} class="h-5 w-5 rounded border-design-border text-design-primary focus:ring-design-primary"> <span class="text-design-foreground">Flash Deals & Limited Time Offers</span> </label> <label class="flex items-center space-x-3"> <input type="checkbox" name="product_launches" value="true"${addAttribute(preferences?.product_launches !== false, "checked")} class="h-5 w-5 rounded border-design-border text-design-primary focus:ring-design-primary"> <span class="text-design-foreground">New Product Launches</span> </label> </div> </div> <div> <h3 class="text-lg font-medium text-design-foreground mb-3">Deal Categories</h3> <div class="grid grid-cols-2 gap-3"> <label class="flex items-center space-x-3"> <input type="checkbox" name="category_devices" value="true"${addAttribute(preferences?.category_devices !== false, "checked")} class="h-5 w-5 rounded border-design-border text-design-primary focus:ring-design-primary"> <span class="text-design-foreground">Vape Devices</span> </label> <label class="flex items-center space-x-3"> <input type="checkbox" name="category_ejuice" value="true"${addAttribute(preferences?.category_ejuice !== false, "checked")} class="h-5 w-5 rounded border-design-border text-design-primary focus:ring-design-primary"> <span class="text-design-foreground">E-Juice</span> </label> <label class="flex items-center space-x-3"> <input type="checkbox" name="category_pods" value="true"${addAttribute(preferences?.category_pods !== false, "checked")} class="h-5 w-5 rounded border-design-border text-design-primary focus:ring-design-primary"> <span class="text-design-foreground">Pods & Cartridges</span> </label> <label class="flex items-center space-x-3"> <input type="checkbox" name="category_accessories" value="true"${addAttribute(preferences?.category_accessories !== false, "checked")} class="h-5 w-5 rounded border-design-border text-design-primary focus:ring-design-primary"> <span class="text-design-foreground">Accessories</span> </label> </div> </div> <div class="mb-6"> <h3 class="text-lg font-medium text-design-foreground mb-3">Email Frequency</h3> <p class="text-design-muted-foreground mb-4">
How often would you like to receive emails? This setting applies to all selected email types.
</p> <div class="space-y-3"> <label class="flex items-center space-x-3"> <input type="radio" name="email_frequency" value="weekly"${addAttribute(!preferences?.email_frequency || preferences.email_frequency === "weekly", "checked")} class="h-5 w-5 rounded-full border-design-border text-design-primary focus:ring-design-primary"> <div> <span class="text-design-foreground font-medium">Weekly</span> <p class="text-sm text-design-muted-foreground">Receive emails once per week</p> </div> </label> <label class="flex items-center space-x-3"> <input type="radio" name="email_frequency" value="twice_weekly"${addAttribute(preferences?.email_frequency === "twice_weekly", "checked")} class="h-5 w-5 rounded-full border-design-border text-design-primary focus:ring-design-primary"> <div> <span class="text-design-foreground font-medium">Twice a Week</span> <p class="text-sm text-design-muted-foreground">Receive emails twice per week</p> </div> </label> <label class="flex items-center space-x-3"> <input type="radio" name="email_frequency" value="biweekly"${addAttribute(preferences?.email_frequency === "biweekly", "checked")} class="h-5 w-5 rounded-full border-design-border text-design-primary focus:ring-design-primary"> <div> <span class="text-design-foreground font-medium">Every Two Weeks</span> <p class="text-sm text-design-muted-foreground">Receive emails once every two weeks</p> </div> </label> <label class="flex items-center space-x-3"> <input type="radio" name="email_frequency" value="monthly"${addAttribute(preferences?.email_frequency === "monthly", "checked")} class="h-5 w-5 rounded-full border-design-border text-design-primary focus:ring-design-primary"> <div> <span class="text-design-foreground font-medium">Monthly</span> <p class="text-sm text-design-muted-foreground">Receive emails once per month</p> </div> </label> </div> <div class="mt-4 p-4 bg-design-muted/20 border border-design-border rounded-lg"> <p class="text-sm text-design-muted-foreground"> <strong>Note:</strong> Your selected frequency applies to all email types. For example, if you select "Weekly" and have checked all three email types (Weekly Digest, Flash Deals, and Product Launches), you'll still receive only one email per week containing all relevant content.
</p> </div> </div> <div class="pt-4 flex flex-col sm:flex-row gap-4 justify-between"> <button type="submit" class="px-4 py-2 bg-design-primary text-white dark:text-black font-medium rounded-lg hover:bg-design-primary/90 transition-colors">
Save Preferences
</button> <a${addAttribute(`/api/newsletter/unsubscribe?token=${token}`, "href")} class="px-4 py-2 border border-design-border text-design-muted-foreground font-medium rounded-lg hover:bg-design-muted/10 transition-colors text-center">
Unsubscribe
</a> </div> </form> </div>`} <div class="text-center text-design-muted-foreground text-sm"> <p>If you have any questions about your subscription, please <a href="/contact" class="text-design-primary hover:underline">contact us</a>.</p> </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/preferences.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/preferences.astro";
const $$url = "/newsletter/preferences";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Preferences,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
