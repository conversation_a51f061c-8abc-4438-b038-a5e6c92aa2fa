import{r as e,j as r}from"./vendor/react-bq1VYqFi.js";import{a as o,b as s}from"./app/design-system-lJ5ci6hi.js";import"./vendor/ui-components-_FzKGR3X.js";import"./vendor/utils-hwhCH1Yb.js";const n=({dealId:n})=>{const[t,d]=e.useState(null),a=e.useCallback((e=>{if(!t&&(d(e),"undefined"!=typeof window&&"function"==typeof navigator.sendBeacon))try{navigator.sendBeacon("/api/track-click",JSON.stringify({dealId:n.toString(),action:`vote_${e}`,timestamp:Date.now()}))}catch(r){}}),[n,t]);return r.jsxs("div",{className:"flex items-center gap-2",children:[r.jsxs("button",{onClick:()=>a("up"),disabled:!!t,className:"flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors "+("up"===t?"bg-green-100 text-green-600 border-green-300 cursor-not-allowed":"down"===t?"opacity-50 cursor-not-allowed border-design-border":"border-design-border hover:bg-design-muted/10"),children:[r.jsx(o,{size:16,className:"up"===t?"fill-current":""}),r.jsx("span",{className:"text-sm",children:"up"===t?"Thanks!":"Helpful"})]}),r.jsxs("button",{onClick:()=>a("down"),disabled:!!t,className:"flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors "+("down"===t?"bg-red-100 text-red-600 border-red-300 cursor-not-allowed":"up"===t?"opacity-50 cursor-not-allowed border-design-border":"border-design-border hover:bg-design-muted/10"),children:[r.jsx(s,{size:16,className:"down"===t?"fill-current":""}),r.jsx("span",{className:"text-sm",children:"Not Helpful"})]})]})};export{n as CouponVotingButton};
