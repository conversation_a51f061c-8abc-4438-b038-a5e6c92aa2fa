/**
 * Test script for the CSS system
 * 
 * This script tests the entire CSS system to ensure that it works correctly.
 * It runs the following tests:
 * 1. Compiles design tokens from TypeScript to JavaScript
 * 2. Validates color contrast ratios
 * 3. Generates CSS variables
 * 4. Checks that the generated CSS files exist
 */

import { exec } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Testing CSS system...');

// Function to run a command and return a promise
function runCommand(command) {
  return new Promise((resolve, reject) => {
    exec(command, (error, stdout, stderr) => {
      if (error) {
        console.error(`Error running command: ${command}`);
        console.error(stderr);
        reject(error);
      } else {
        console.log(stdout);
        resolve(stdout);
      }
    });
  });
}

// Function to check if a file exists
function checkFileExists(filePath) {
  return new Promise((resolve, reject) => {
    fs.access(filePath, fs.constants.F_OK, (error) => {
      if (error) {
        reject(new Error(`File does not exist: ${filePath}`));
      } else {
        resolve(true);
      }
    });
  });
}

// Main function
async function main() {
  try {
    // Step 1: Compile design tokens
    console.log('\n1. Compiling design tokens...');
    await runCommand('npm run build-tokens');
    
    // Check if the compiled file exists
    const compiledTokensPath = path.join(__dirname, '../src/styles/design-tokens.js');
    await checkFileExists(compiledTokensPath);
    console.log('✅ Design tokens compiled successfully!');
    
    // Step 2: Validate color contrast
    console.log('\n2. Validating color contrast...');
    await runCommand('npm run validate-contrast');
    
    // Step 3: Generate CSS variables
    console.log('\n3. Generating CSS variables...');
    await runCommand('npm run generate-css');
    
    // Check if the generated CSS file exists
    const generatedCssPath = path.join(__dirname, '../src/styles/generated-variables.css');
    await checkFileExists(generatedCssPath);
    console.log('✅ CSS variables generated successfully!');
    
    // Step 4: Check that all CSS files exist
    console.log('\n4. Checking CSS files...');
    const cssFiles = [
      path.join(__dirname, '../src/styles/typography.css'),
      path.join(__dirname, '../src/styles/components.css'),
      path.join(__dirname, '../src/styles/global.css'),
    ];
    
    for (const cssFile of cssFiles) {
      await checkFileExists(cssFile);
      console.log(`✅ ${path.basename(cssFile)} exists!`);
    }
    
    console.log('\n✅ CSS system test passed!');
    return true;
  } catch (error) {
    console.error('\n❌ CSS system test failed!', error);
    process.exit(1);
  }
}

// Run the main function
main();
