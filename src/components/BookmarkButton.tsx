import React, { useState, useEffect } from 'react';
import { Heart } from 'lucide-react';
import Button from './DesignSystem/Button/Button';
import { toast } from 'sonner';

interface BookmarkButtonProps {
  dealId: string | number;
  className?: string;
  showText?: boolean;
}

export default function BookmarkButton({
  dealId,
  className,
  showText = false
}: BookmarkButtonProps) {
  const [bookmarked, setBookmarked] = useState(false);

  // Convert dealId to string for consistent handling
  const dealIdString = dealId.toString();

  // Check if the deal is bookmarked on component mount
  useEffect(() => {
    if (typeof window !== 'undefined') {
      try {
        const storedBookmarkIds = localStorage.getItem('bookmarkIds');
        if (storedBookmarkIds) {
          const bookmarkIds: string[] = JSON.parse(storedBookmarkIds);
          setBookmarked(bookmarkIds.includes(dealIdString));
        }
      } catch (error) {
        console.error('Error checking bookmark status:', error);
      }

      // Listen for storage events to update bookmark status
      const handleStorageChange = (e: StorageEvent) => {
        if (e.key === 'bookmarkIds') {
          try {
            const newBookmarkIds: string[] = e.newValue ? JSON.parse(e.newValue) : [];
            setBookmarked(newBookmarkIds.includes(dealIdString));
          } catch (error) {
            console.error('Error parsing bookmarks from storage event:', error);
          }
        }
      };

      // Listen for custom bookmark change events
      const handleCustomBookmarkChange = (e: CustomEvent) => {
        try {
          const newBookmarkIds = e.detail ? e.detail : [];
          setBookmarked(newBookmarkIds.includes(dealIdString));
        } catch (error) {
          console.error('Error handling custom bookmark change event:', error);
        }
      };

      window.addEventListener('storage', handleStorageChange);
      window.addEventListener('bookmarkChange' as any, handleCustomBookmarkChange);

      return () => {
        window.removeEventListener('storage', handleStorageChange);
        window.removeEventListener('bookmarkChange' as any, handleCustomBookmarkChange);
      };
    }
  }, [dealIdString]);

  const handleBookmarkToggle = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    if (typeof window !== 'undefined') {
      try {
        // Get current bookmark IDs
        const storedBookmarkIds = localStorage.getItem('bookmarkIds');
        let bookmarkIds: string[] = storedBookmarkIds ? JSON.parse(storedBookmarkIds) : [];

        if (bookmarked) {
          // Remove bookmark
          bookmarkIds = bookmarkIds.filter(id => id !== dealIdString);
          toast.success('Removed from bookmarks');
        } else {
          // Add bookmark
          if (!bookmarkIds.includes(dealIdString)) {
            bookmarkIds.push(dealIdString);
            toast.success('Added to bookmarks');
          }
        }

        // Save updated bookmark IDs
        localStorage.setItem('bookmarkIds', JSON.stringify(bookmarkIds));

        // Update local state
        setBookmarked(!bookmarked);

        // Dispatch storage event to update other components
        window.dispatchEvent(new StorageEvent('storage', {
          key: 'bookmarkIds',
          newValue: JSON.stringify(bookmarkIds)
        }));

        // Dispatch custom event for direct component communication
        window.dispatchEvent(new CustomEvent('bookmarkChange', {
          detail: bookmarkIds
        }));
      } catch (error) {
        console.error('Error toggling bookmark:', error);
        toast.error('Error saving bookmark');
      }
    }
  };

  return (
    <Button
      variant="ghost"
      size="sm"
      onClick={handleBookmarkToggle}
      className={`${showText ? 'flex items-center gap-2' : ''} ${className || ''}`}
      aria-label={bookmarked ? 'Remove from bookmarks' : 'Add to bookmarks'}
      data-bookmarked={bookmarked}
    >
      <Heart size={showText ? 16 : 20} fill={bookmarked ? 'red' : 'none'} />
      {showText && (
        <span className="text-sm">{bookmarked ? 'Saved' : 'Save'}</span>
      )}
    </Button>
  );
}
