name = "vapehybrid"
main = "dist/_worker.js"
compatibility_date = "2024-01-15"
compatibility_flags = ["nodejs_compat"]

[site]
bucket = "dist"
include = ["**/*"]
exclude = ["_worker.js", "_routes.json"]

# Custom domains will be configured in Cloudflare dashboard
# [[routes]]
# pattern = "vapehybrid.com/*"
# zone_name = "vapehybrid.com"

# [[routes]]
# pattern = "www.vapehybrid.com/*"
# zone_name = "vapehybrid.com"

# Environment variables (secrets will be set via CLI)
[vars]
NODE_ENV = "production"
ASTRO_SITE_URL = "https://vapehybrid.com"
PUBLIC_SITE_URL = "https://vapehybrid.com"
AFFILIATE_CLOAK_BASE_URL = "/go"
REVEAL_COOKIE_PREFIX = "revealed_deal_"
ADMIN_EMAIL = "<EMAIL>"
JWT_EXPIRATION = "86400"
SUPABASE_URL = "https://zlnvivfgzgcjuspktadj.supabase.co"
PUBLIC_SUPABASE_URL = "https://zlnvivfgzgcjuspktadj.supabase.co"

# Build configuration - skip build since we already have dist folder
# [build]
# command = "npm run build"
# cwd = "."

# Deployment settings - using default environment
# [env.production]
# name = "vapehybrid"
# route = "vapehybrid.com/*"
