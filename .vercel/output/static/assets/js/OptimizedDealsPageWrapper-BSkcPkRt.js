import{r as e,j as o}from"./vendor/react-bq1VYqFi.js";import{t as a}from"./vendor/ui-components-_FzKGR3X.js";import{S as t,L as i,a as s,b as r}from"./app/deals-3jyzwLVb.js";const n=({deals:n,initialViewMode:d,onViewModeChange:l})=>{const c=e.useMemo((()=>n.slice(0,24)),[n]),[g,m]=e.useState(d),[p,u]=e.useState(!1),[w,f]=e.useState(null),[h,S]=e.useState({});e.useEffect((()=>{m(d)}),[d]),e.useEffect((()=>{if("undefined"!=typeof window){const e=new URLSearchParams(window.location.search),o=e.get("dealId"),a=e.get("showPopup"),t=e.get("view");if("grid"!==t&&"list"!==t||m(t),o&&"true"===a){const e=n.find((e=>e.id.toString()===o));e&&v(e)}const i=JSON.parse(localStorage.getItem("revealedCodes")||"{}");S(i)}}),[n]);const v=e=>{p||("undefined"!=typeof window&&localStorage.setItem("activeModal",e.id.toString()),f(e),u(!0),S((o=>{const a={...o,[e.id]:!0};return"undefined"!=typeof window&&localStorage.setItem("revealedCodes",JSON.stringify(a)),a})))};return o.jsxs("div",{className:"deals-page-wrapper",children:[o.jsx("div",{className:"deals-container px-2 sm:px-3 md:px-4 "+("grid"===g?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 optimized-grid":"flex flex-col gap-8 optimized-list"),style:{contentVisibility:"auto",containIntrinsicSize:"grid"===g?"320px":"160px"},children:c.map(((e,a)=>{let r=e.merchants?.name||e.merchant_name||"Brand";"Vapesourcing Electronics Co.,Ltd."===r&&(r="Vapesourcing");const n={...e,merchants:e.merchants||{name:r,website_url:"",logo_url:e.merchant_logo_url},brands:e.brands||{name:e.brand_name||"",logo_url:e.brand_logo_url}},d=a<6;return"grid"===g?o.jsx(t,{fallback:o.jsx("div",{className:"animate-pulse bg-design-muted rounded-lg h-[320px] w-full"}),children:o.jsx(i,{deal:n,onShowCouponModal:v,isRevealed:h[e.id]||!1,view:"grid",priority:d})},e.id):o.jsx(t,{fallback:o.jsx("div",{className:"animate-pulse bg-design-muted rounded-lg h-[160px] w-full"}),children:o.jsx(s,{deal:n,onShowCouponModal:v,isRevealed:h[e.id]||!1,priority:d})},e.id)}))}),p&&w&&o.jsx(r,{deal:w,onClose:()=>{if(u(!1),f(null),"undefined"!=typeof window){localStorage.removeItem("modalShowing"),localStorage.removeItem("activeModal");const e=new URL(window.location.href);e.searchParams.delete("dealId"),e.searchParams.delete("showPopup"),e.searchParams.delete("solidBg");const o=e.searchParams.get("view"),a=new URL(window.location.pathname,window.location.origin);o&&a.searchParams.set("view",o),window.history.replaceState({},"",a.toString())}},onVote:(e,o)=>{fetch("/api/vote",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({deal_id:e,vote_good:o})}).then((e=>{if(e.ok){const e=o?"Thanks for your positive feedback!":"Thanks for your feedback!";a.success(e)}else a.error("Failed to record your vote. Please try again.")})).catch((e=>{a.error("Failed to record your vote. Please try again.")}))}})]})};export{n as default};
