import{r as e,j as s}from"./vendor/react-bq1VYqFi.js";import{u as a,d as t,g as r,e as i,n,c as l,C as o,f as d,h as c,a as m,B as u,E as g,i as p,j as x,D as h}from"./app/design-system-lJ5ci6hi.js";import"./vendor/ui-components-_FzKGR3X.js";import"./vendor/utils-hwhCH1Yb.js";const f=({deal:a,imageUrl:t})=>{const[r,i]=e.useState(!1);return e.useEffect((()=>{const e=(window.requestIdleCallback||(e=>setTimeout(e,1)))((()=>{i(!0)}));return()=>{(window.cancelIdleCallback||(e=>clearTimeout(e)))(e)}}),[]),r?s.jsx(x,{deal:a,imageUrl:t}):null},b=({deal:x,className:h,isRevealed:b=!1,priority:v=!1,onShowCouponModal:w,...y})=>{const[j,_]=e.useState(b),N=e.useRef(null),k=`/deal/${x.id}`,{onMouseEnter:S,onMouseLeave:C}=a(k,{delay:150}),{setIsVisible:M,handleMouseEnter:E,handleMouseLeave:I,handleClick:P}=t(x.id,"deal",{minDwellTime:2e3}),$=e.useMemo((()=>r(5,x.id)),[x.id]),D=e.useMemo((()=>i(x.id)),[x.id]),O=e.useMemo((()=>D.timeAgo),[D]),z=e.useMemo((()=>x.usage_count||D.count),[x.usage_count,D.count]),L=e.useMemo((()=>x.deal_end_date?Math.ceil((new Date(x.deal_end_date).getTime()-(new Date).getTime())/864e5):null),[x.deal_end_date]),V=e.useMemo((()=>null!==L&&L>0&&L<=7),[L]),T=e.useMemo((()=>null!==L&&L<=0),[L]),B=e.useMemo((()=>{const e=n(x.imagebig_url)||n(x.image_url)||n(x.imagesmall_url);if(e)return e;const s=n(x.brands?.logo_url)||n(x.brand_logo_url);if(s)return s;const a=n(x.merchants?.logo_url)||n(x.merchant_logo_url);return a||"/placeholder-image.svg"}),[x.imagebig_url,x.image_url,x.imagesmall_url,x.brands?.logo_url,x.brand_logo_url,x.merchants?.logo_url,x.merchant_logo_url]);e.useEffect((()=>{if(!N.current)return;const e=new IntersectionObserver((e=>{e.forEach((e=>{if(M(e.isIntersecting),e.isIntersecting)try{let e=localStorage.getItem("vh_session_id");if(e||(e="session_"+Math.random().toString(36).substring(2,15),localStorage.setItem("vh_session_id",e)),"function"==typeof navigator.sendBeacon){const s=new FormData;s.append("deal_id",x.id.toString()),s.append("session_id",e),navigator.sendBeacon("/api/track-impression?deal_id="+x.id+"&session_id="+e,s)}else fetch("/api/track-impression?deal_id="+x.id+"&session_id="+e,{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({deal_id:x.id,session_id:e}),keepalive:!0})}catch(s){}}))}),{threshold:.5});return e.observe(N.current),()=>{e.disconnect()}}),[x.id,N,M]);const R=e.useCallback((e=>{e.stopPropagation(),P(),requestAnimationFrame((()=>{if(_(!0),"undefined"!=typeof window){if("function"==typeof navigator.sendBeacon)try{const e=new FormData;e.append("deal_id",x.id.toString()),e.append("fallback","true"),navigator.sendBeacon("/api/track-click",e)}catch(e){}const s=JSON.parse(localStorage.getItem("clickedDeals")||"{}"),a=JSON.parse(localStorage.getItem("revealedCodes")||"{}");a[x.id]=!0,localStorage.setItem("revealedCodes",JSON.stringify(a)),s[x.id]=!0,localStorage.setItem("clickedDeals",JSON.stringify(s)),x.coupon_code&&l(x.coupon_code);const t=new URL(window.location.href).searchParams.get("view")||"list",r=new URL(window.location.href);r.searchParams.set("dealId",x.id.toString()),r.searchParams.set("showPopup","true"),r.searchParams.set("solidBg","true"),r.searchParams.set("view",t),w&&w(x),window.open(r.toString(),"_blank"),x.tracking_url?window.location.href=x.tracking_url:window.location.href=`/go/${x.id}`}}))}),[x,P,w]);return s.jsxs(s.Fragment,{children:["undefined"!=typeof window&&s.jsx(f,{deal:x,imageUrl:B}),s.jsxs(o,{ref:N,variant:"default",interactive:!0,glowEffect:!0,className:d("brand-deal-list-card relative cursor-pointer w-full flex flex-col sm:flex-row items-center justify-between mb-6","transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.005]","hover:border-design-primary/50 dark:hover:border-design-primary/50","border-black/15 dark:border-white/15",h),style:{padding:"0.5rem",borderRadius:"25px",borderWidth:"1.5px",minHeight:"175px",maxHeight:"175px"},onClick:e=>{e.stopPropagation(),R(e)},onMouseEnter:()=>{S(),E()},onMouseLeave:()=>{C(),I()},"aria-label":`Deal: ${x.title}`,role:"button",tabIndex:0,...y,children:[V&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full",children:1===L?"Expires today":`Expires in ${L} days`}),T&&s.jsx("div",{className:"absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full",children:"Expired"}),s.jsx("div",{className:"w-full sm:w-[160px] py-2 sm:py-0",children:s.jsx("div",{className:"flex justify-center h-full",children:s.jsx("div",{className:"relative flex items-center justify-center w-full h-full aspect-square max-w-[140px] sm:min-w-[140px] sm:max-w-[150px] sm:max-h-[150px] bg-white dark:bg-white rounded-lg overflow-hidden",style:{boxShadow:"inset 0 0 0 1px rgba(0,0,0,0.05)",padding:"6px"},children:s.jsx(c,{src:B,alt:x.title||"Deal image",width:150,height:150,priority:v,fetchpriority:v?"high":"auto",className:"",fallbackSrc:"/placeholder-image.svg",index:0})})})}),s.jsx("div",{className:"flex-1 p-2 w-full",children:s.jsxs("div",{className:"h-full flex flex-col justify-between",children:[s.jsx("div",{className:"mb-1",children:s.jsxs("div",{className:"text-2xl font-bold text-design-foreground",children:[x.discount?s.jsxs("span",{className:"text-green-600 dark:text-green-500",children:[x.discount,"% ",s.jsx("span",{className:"text-sm font-normal text-gray-500 dark:text-gray-400",children:"off "}),"🔥"]}):x.price?`${x.currency||"$"}${x.price}`:"20% Off",V&&s.jsx("span",{className:"ml-2 text-xs font-normal text-design-warning animate-pulse",children:1===L?"Ends today!":`Ends in ${L} days!`})]})}),s.jsxs("div",{className:"text-sm text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1",children:[s.jsx("span",{className:"font-medium truncate max-w-[120px] hover:underline cursor-help",title:`Brand: ${x.merchants?.name||x.brands?.name||"Unknown"}`,children:x.merchants?.name||x.brands?.name||"Brand"}),x.verified&&s.jsxs("span",{className:"c-verified-badge verified-badge ml-2",children:[s.jsx("img",{src:"/Vapehybrid light icon.svg",alt:"Verified",className:"dark:hidden",width:16,height:16}),s.jsx("img",{src:"/Vapehybrid dark icon.svg",alt:"Verified",className:"hidden dark:block",width:16,height:16}),s.jsx("span",{className:"text-design-primary dark:text-design-primary",children:"Verified"})]}),void 0!==x.success_rate&&s.jsxs("span",{className:"ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full "+(x.success_rate>=90?"bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground":x.success_rate>=70?"bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground":"bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"),title:`${Math.round(x.success_rate||85)}% success rate`,children:[s.jsx(m,{size:10,className:"mr-1"}),Math.round(x.success_rate||85),"%"]})]}),s.jsx("h3",{className:"deal-card-title line-clamp-2 mb-1 text-base font-semibold overflow-hidden",title:x.cleaned_title&&"null"!==x.cleaned_title?x.cleaned_title:x.title,children:x.cleaned_title&&"null"!==x.cleaned_title?x.cleaned_title:x.title}),s.jsx("div",{className:"flex flex-1",children:s.jsxs("div",{className:"flex-1",children:[s.jsxs("div",{className:"flex items-center text-xs text-design-muted-foreground mb-2",children:[s.jsxs("div",{className:"c-avatar-group user-avatar-group flex relative",style:{marginRight:"0.35rem"},children:[$.slice(0,Math.min(3,z||3)).map(((e,a)=>s.jsxs("div",{className:"c-avatar c-avatar--xs user-avatar relative transition-transform hover:scale-110 hover:z-10 overflow-hidden",style:{width:"20px",height:"20px",marginLeft:a>0?"-8px":"0",borderRadius:"50%",border:"1.5px solid white",boxShadow:"0 1px 2px rgba(0,0,0,0.1)",zIndex:3-a,backgroundColor:e.color},title:`Staff member: ${e.name}`,children:[e.webpPath&&e.imagePath?s.jsxs("picture",{children:[s.jsx("source",{srcSet:e.webpPath,type:"image/webp"}),s.jsx("img",{src:e.imagePath,alt:e.initials,className:"w-full h-full object-cover",onError:e=>{const s=e.target;s.style.display="none";const a=s.parentElement?.parentElement;a&&a.classList.add("fallback-active")}})]}):s.jsx("div",{className:"w-full h-full flex items-center justify-center text-white font-bold",children:s.jsx("span",{style:{fontSize:"8px"},children:e.initials})}),s.jsx("div",{className:"fallback-content absolute inset-0 flex items-center justify-center text-white font-bold opacity-0",children:s.jsx("span",{style:{fontSize:"8px"},children:e.initials})})]},a))),z>3&&s.jsxs("div",{className:"c-avatar c-avatar--xs user-avatar flex items-center justify-center bg-design-muted-foreground/20",style:{width:"20px",height:"20px",marginLeft:"-8px",borderRadius:"50%",border:"1.5px solid white",boxShadow:"0 1px 2px rgba(0,0,0,0.1)",fontSize:"8px"},title:z-3+" more staff members",children:["+",z-3]})]}),s.jsxs("span",{children:["Verified ",s.jsx("time",{dateTime:x.last_verified_at,title:new Date(x.last_verified_at||"").toLocaleString(),children:O})," by ",z||3," staffer",(z||3)>1?"s":""]})]}),V&&s.jsxs("div",{className:"mb-2 text-xs text-design-warning px-2 py-1 rounded flex items-center",children:[s.jsx("span",{className:"animate-pulse mr-1",children:"⏱"}),s.jsxs("span",{children:["Limited time offer! ",1===L?"Ends today":`Ends in ${L} days`]})]})]})})]})}),s.jsxs("div",{className:"flex flex-col items-center justify-center px-4 sm:pr-4 sm:pl-0 gap-2 w-full sm:min-w-[150px] sm:w-auto py-3 sm:py-0",children:[s.jsx("div",{className:"deal-coupon-code relative cursor-pointer px-3 py-1 w-full text-center bg-design-muted rounded-full border border-design-muted-foreground/10 overflow-hidden",onClick:e=>{e.stopPropagation(),R(e)},title:j?"Click to copy":"Click to reveal code","aria-label":j?`Copy coupon code: ${x.coupon_code||"NO CODE"}`:"Click to reveal coupon code",children:x.coupon_code?s.jsxs(s.Fragment,{children:[s.jsx("span",{className:"text-base font-bold transition-all duration-300 "+(j?"blur-none opacity-100":"blur-[4px] select-none"),style:{color:"var(--design-foreground)"},"aria-hidden":!j,children:x.coupon_code}),j&&s.jsx("span",{className:"absolute inset-0 bg-design-primary/10 animate-reveal-sweep","aria-hidden":"true"}),s.jsx("span",{className:"sr-only",children:j?`Coupon code: ${x.coupon_code}`:"Click to reveal coupon code"})]}):"NO CODE"}),s.jsx("button",{className:"copy-code-button px-4 py-1 rounded-full border border-design-muted-foreground/20 hover:border-design-primary/50 dark:hover:border-design-primary/50 transition-colors",onClick:e=>{e.stopPropagation(),R(e)},"aria-label":`Copy coupon code ${x.coupon_code||"for this deal"}`,children:"Copy Code"}),s.jsxs("div",{className:"deal-card-actions flex items-center justify-center gap-2 mt-1",children:[s.jsx(u,{dealId:x.id.toString(),className:"bookmark-button p-1.5 rounded-full hover:bg-design-muted transition-colors"}),s.jsx("button",{className:"eye-button p-1.5 rounded-full hover:bg-design-muted transition-colors relative z-10",onClick:e=>{e.stopPropagation(),window.open(p(x),"_blank")},title:"View coupon details","aria-label":"View coupon details",children:s.jsx(g,{size:16,className:"text-design-muted-foreground hover:text-design-foreground transition-colors"})})]})]})]})]})},v=({deals:a,initialViewMode:t="list",className:r=""})=>{const[i,n]=e.useState(null),[l,o]=e.useState(!1),d=e=>{n(e),o(!0)};return a&&0!==a.length?s.jsxs("div",{className:`brand-deals-wrapper ${r}`,children:[s.jsx("div",{className:"space-y-6",style:{maxWidth:"730px"},children:a.map(((e,a)=>s.jsx(b,{deal:e,priority:a<3,onShowCouponModal:d,className:"w-full"},e.id)))}),l&&i&&s.jsx(h,{deal:i,onClose:()=>{o(!1),n(null)}})]}):s.jsxs("div",{className:"text-center py-12 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl",children:[s.jsx("p",{className:"text-gray-600 dark:text-gray-300",children:"No active coupons available at the moment."}),s.jsx("p",{className:"text-sm text-gray-500 dark:text-gray-400 mt-2",children:"Check back soon for new deals!"})]})};export{v as default};
