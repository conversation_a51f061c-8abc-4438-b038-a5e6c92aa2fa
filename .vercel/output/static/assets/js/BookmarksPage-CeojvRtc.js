import{j as e,r as s}from"./vendor/react-bq1VYqFi.js";import{H as r,l as t,S as a,m as n,E as o,o as i}from"./app/design-system-lJ5ci6hi.js";import{t as d}from"./vendor/ui-components-_FzKGR3X.js";import"./vendor/utils-hwhCH1Yb.js";function l({reason:s="no-bookmarks"}){return e.jsxs("div",{className:"text-center py-12 bg-design-card/80 backdrop-blur-sm border border-design-border rounded-lg max-w-md mx-auto",children:[e.jsx("div",{className:"flex justify-center mb-4",children:e.jsxs("div",{className:"relative",children:[e.jsx("div",{className:"absolute -inset-1 rounded-full bg-design-accent/20 animate-pulse"}),e.jsx(r,{className:"h-12 w-12 text-[#f21d6b] relative"})]})}),e.jsx("h3",{className:"text-xl font-semibold text-design-foreground mb-2",children:(()=>{switch(s){case"no-bookmarks":default:return"Your Bookmarks List is Empty";case"no-deals-found":return"Could Not Find Your Bookmarked Deals";case"error":return"Error Loading Your Bookmarks"}})()}),e.jsx("p",{className:"text-design-muted-foreground mb-6 max-w-xs mx-auto text-sm",children:(()=>{switch(s){case"no-bookmarks":default:return"Save your favorite deals by clicking the heart icon on any product card. Your bookmarks will be stored here for easy access.";case"no-deals-found":return"We found your bookmarked IDs but could not retrieve the deals. The deals may have expired or been removed.";case"error":return"There was an issue loading your bookmarked deals. Please try refreshing the page."}})()}),e.jsxs("div",{className:"flex justify-center gap-4",children:["no-bookmarks"!==s&&e.jsxs("button",{onClick:()=>window.location.reload(),className:"inline-flex items-center justify-center gap-2 rounded-md border border-design-border bg-design-background text-design-foreground hover:bg-design-accent/10 px-4 py-2 text-sm font-medium transition-colors",children:[e.jsx(t,{className:"h-4 w-4"}),e.jsx("span",{children:"Refresh"})]}),e.jsxs("a",{href:"/deals",className:"inline-flex items-center justify-center gap-2 rounded-md bg-design-primary text-design-primary-foreground hover:bg-design-primary/90 px-4 py-2 text-sm font-medium transition-colors hover:text-white hover:bg-design-secondary",children:[e.jsx(a,{className:"h-4 w-4"}),e.jsx("span",{children:"Discover Deals"})]})]})]},"empty-bookmarks")}function c(){const[r,t]=s.useState([]),[a,c]=s.useState([]),[m,x]=s.useState(!0);s.useEffect((()=>{if("undefined"!=typeof window){try{const e=localStorage.getItem("bookmarkIds");if(e){const s=JSON.parse(e);Array.isArray(s)?t(s):t([])}}catch(e){localStorage.setItem("bookmarkIds",JSON.stringify([]))}const s=s=>{if("bookmarkIds"===s.key)try{const e=s.newValue?JSON.parse(s.newValue):[];t(e)}catch(e){}},r=s=>{try{const e=s.detail?s.detail:[];t(e)}catch(e){}};return window.addEventListener("storage",s),window.addEventListener("bookmarkChange",r),()=>{window.removeEventListener("storage",s),window.removeEventListener("bookmarkChange",r)}}}),[]),s.useEffect((()=>{(async()=>{if(0===r.length)return c([]),void x(!1);x(!0);try{const e=r.join(","),s="",t=await fetch(`${s}/api/deals?ids=${e}`);if(!t.ok){await t.text();throw new Error(`Failed to fetch deals: ${t.status} ${t.statusText}`)}const a=await t.json();if(!Array.isArray(a))throw new Error("Invalid response format");const n=a.filter((e=>{const s=String(e.id);return r.some((e=>String(e)===s))}));c(n)}catch(e){d.error("Error loading bookmarked deals"),c([]),window.bookmarksError=!0}finally{x(!1)}})()}),[r]);const g=e=>e.imagebig_url&&"YOUR_LOGO_URL"!==e.imagebig_url?e.imagebig_url:e.image_url&&"YOUR_LOGO_URL"!==e.image_url?e.image_url:e.imagesmall_url&&"YOUR_LOGO_URL"!==e.imagesmall_url?e.imagesmall_url:"/placeholder-image.svg",u=(e,s)=>{if(!e||!s)return e;const r=parseInt(s);return isNaN(r)?e:e*(1-r/100)},h=s.useMemo((()=>{let e=0,s=0;a.forEach((r=>{if(r.price){const t=r.price,a=u(t,r.discount);e+=t,s+=a}}));const r=e-s,t=e>0?r/e*100:0;return{totalOriginal:e.toFixed(2),totalDiscounted:s.toFixed(2),totalSavings:r.toFixed(2),savingsPercentage:Math.round(t)}}),[a]);return m?e.jsx("div",{className:"flex justify-center items-center py-12",children:e.jsx("div",{className:"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-design-primary"})}):0===r.length?e.jsx(l,{reason:"no-bookmarks"}):0===a.length?window.bookmarksError?e.jsx(l,{reason:"error"}):e.jsx(l,{reason:"no-deals-found"}):e.jsxs("div",{className:"max-w-6xl mx-auto",children:[e.jsx("div",{className:"flex justify-end items-center mb-4",children:e.jsxs("p",{className:"text-sm text-design-muted-foreground",children:[a.length," item",1!==a.length?"s":""]})}),e.jsx("div",{className:"bg-design-card/80 backdrop-blur-sm rounded-lg border border-design-border overflow-hidden mb-6",children:e.jsx("div",{className:"overflow-x-auto",children:e.jsxs("table",{className:"w-full border-collapse text-sm",children:[e.jsx("thead",{children:e.jsxs("tr",{className:"border-b border-design-border bg-design-card/50",children:[e.jsx("th",{className:"py-2 px-3 text-left w-8 text-design-muted-foreground",children:"#"}),e.jsx("th",{className:"py-2 px-3 text-left w-8"}),e.jsx("th",{className:"py-2 px-3 text-left w-20",children:"Image"}),e.jsx("th",{className:"py-2 px-3 text-left",children:"Product"}),e.jsx("th",{className:"py-2 px-3 text-right",children:"Discount"}),e.jsx("th",{className:"py-2 px-3 text-right",children:"Original"}),e.jsx("th",{className:"py-2 px-3 text-right",children:"Price"}),e.jsx("th",{className:"py-2 px-3 text-center",children:"Actions"})]})}),e.jsx("tbody",{children:a.map(((s,r)=>{const a=s.price||0,l=u(a,s.discount),m=a-l;return e.jsxs("tr",{className:"border-b border-design-border hover:bg-design-accent/10 transition-colors duration-design-normal",children:[e.jsx("td",{className:"py-3 px-3 text-design-muted-foreground text-xs",children:r+1}),e.jsx("td",{className:"py-3 px-3",children:e.jsx("button",{onClick:()=>(e=>{if("undefined"!=typeof window)try{const s=localStorage.getItem("bookmarkIds");let r=s?JSON.parse(s):[];const a=String(e);r=r.filter((e=>String(e)!==a)),localStorage.setItem("bookmarkIds",JSON.stringify(r)),t(r),c((e=>e.filter((e=>String(e.id)!==a)))),c((e=>[...e])),d.success("Deal removed from bookmarks"),window.dispatchEvent(new StorageEvent("storage",{key:"bookmarkIds",newValue:JSON.stringify(r)})),window.dispatchEvent(new CustomEvent("bookmarkChange",{detail:r}))}catch(s){d.error("Error removing bookmark")}})(s.id),className:"text-design-muted-foreground hover:text-[#f21d6b] transition-colors","aria-label":"Remove from bookmarks",children:e.jsx(n,{className:"h-4 w-4"})})}),e.jsx("td",{className:"py-3 px-3",children:e.jsx("div",{className:"w-14 h-14 rounded overflow-hidden bg-design-muted/20 flex items-center justify-center",children:e.jsx("img",{src:g(s),className:"max-w-full max-h-full object-contain",alt:s.cleaned_title||s.title,onError:e=>{const s=e.target;"/placeholder-image.svg"!==s.src&&(s.src="/placeholder-image.svg")}})})}),e.jsx("td",{className:"py-3 px-3 font-medium text-design-foreground",children:s.cleaned_title||s.title}),e.jsx("td",{className:"py-3 px-3 text-right",children:e.jsxs("span",{className:"text-design-foreground font-medium",children:[s.discount||"0","%"]})}),e.jsx("td",{className:"py-3 px-3 text-right",children:e.jsxs("span",{className:"line-through text-design-muted-foreground",children:["$",a.toFixed(2)]})}),e.jsxs("td",{className:"py-3 px-3 text-right",children:[e.jsxs("span",{className:"font-semibold text-design-foreground",children:["$",l.toFixed(2)]}),m>0&&e.jsxs("div",{className:"text-[11px] leading-2 text-design-primary font-medium",children:["Save $",m.toFixed(2)]})]}),e.jsx("td",{className:"py-3 px-3 text-center",children:e.jsxs("div",{className:"flex gap-2 justify-center",children:[e.jsxs("a",{href:`/deal/${s.id}`,className:"h-7 w-16 text-xs inline-flex items-center justify-center gap-1 rounded-md border border-design-border bg-design-background text-design-muted-foreground hover:bg-design-accent/20 hover:border-design-primary/50 transition-colors",children:[e.jsx(o,{className:"h-3 w-3"}),e.jsx("span",{children:"View"})]}),e.jsxs("a",{href:`/go/${s.id}`,className:"h-7 w-16 text-xs inline-flex items-center justify-center gap-1 rounded-md bg-design-primary text-black hover:bg-design-secondary  shadow-sm transition-colors",children:[e.jsx(i,{className:"h-3 w-3"}),e.jsx("span",{children:"Get"})]})]})})]},`deal-${s.id}-${r}`)}))},`deals-tbody-${a.length}-${Date.now()}`)]})})}),a.length>0&&e.jsxs("div",{className:"bg-design-card/80 backdrop-blur-sm rounded-lg border border-design-border p-4 max-w-md ml-auto",children:[e.jsx("h3",{className:"text-sm font-medium text-design-foreground mb-3",children:"Summary"}),e.jsxs("div",{className:"space-y-2 text-sm",children:[e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-design-muted-foreground",children:"Total Original Price:"}),e.jsxs("span",{className:"text-design-foreground",children:["$",h.totalOriginal]})]}),e.jsxs("div",{className:"flex justify-between",children:[e.jsx("span",{className:"text-design-muted-foreground",children:"Total Discounted Price:"}),e.jsxs("span",{className:"text-design-foreground",children:["$",h.totalDiscounted]})]}),e.jsxs("div",{className:"border-t border-design-border my-2 pt-2 flex justify-between font-medium",children:[e.jsx("span",{className:"text-design-foreground",children:"Total Savings:"}),e.jsxs("div",{className:"text-right",children:[e.jsxs("span",{className:"text-design-primary font-semibold",children:["$",h.totalSavings]}),e.jsxs("span",{className:"text-design-primary text-xs ml-1",children:["(",h.savingsPercentage,"% off)"]})]})]})]})]})]})}export{c as default};
