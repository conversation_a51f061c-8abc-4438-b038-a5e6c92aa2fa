/**
 * IconButton Component
 *
 * A button component specifically designed for icons.
 */

import React from 'react';
import { cn } from '../../../lib/utils';
import type { ButtonProps } from './Button';

export interface IconButtonProps extends Omit<ButtonProps, 'leftIcon' | 'rightIcon'> {
  /**
   * The icon to display in the button
   */
  icon: React.ReactNode;

  /**
   * Whether the button is round
   */
  round?: boolean;
}

/**
 * IconButton component for displaying icon-only buttons
 *
 * @example
 * ```tsx
 * <IconButton
 *   icon={<svg>...</svg>}
 *   variant="primary"
 *   size="md"
 *   onClick={() => console.log('Clicked')}
 * />
 * ```
 */
const IconButton = React.forwardRef<HTMLButtonElement, IconButtonProps>(
  ({
    icon,
    variant = 'primary',
    size = 'md',
    round = false,
    loading = false,
    disabled = false,
    className,
    ...props
  }, ref) => {
    // Map variant to design system class
    const variantClass = {
      primary: 'design-button-primary',
      secondary: 'design-button-secondary',
      outline: 'design-button-outline',
      ghost: 'design-button-ghost',
      destructive: 'design-button-destructive',
      accent: 'design-button-accent',
      link: 'design-button-link',
    }[variant];

    // Map size to design system class
    const sizeClass = {
      xs: 'design-button-sm p-1',
      sm: 'design-button-sm p-1',
      md: 'p-2',
      lg: 'design-button-lg p-3',
      xl: 'design-button-lg p-3',
      icon: 'p-2',
    }[size];

    return (
      <button
        ref={ref}
        className={cn(
          'design-button',
          'design-icon-button',
          variantClass,
          sizeClass,
          round && 'rounded-full',
          loading && 'design-button-loading',
          disabled && 'opacity-50 cursor-not-allowed',
          className
        )}
        disabled={disabled || loading}
        {...props}
      >
        {loading ? (
          <svg className="animate-spin h-5 w-5" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
        ) : icon}
      </button>
    );
  }
);

IconButton.displayName = 'IconButton';

export default IconButton;
