/**
 * Supabase client for server-side and client-side usage
 *
 * This file provides two Supabase clients:
 * 1. createSupabaseServerClient - For server-side usage with cookie handling
 * 2. createSupabaseClient - For client-side usage
 */

import { createClient } from '@supabase/supabase-js';
import { createServerClient, type CookieOptions } from '@supabase/ssr';
import type { AstroCookies } from 'astro';

// Database type definition for Supabase
export interface Database {
  public: {
    Tables: {
      deals: {
        Row: {
          id: number;
          title: string;
          description?: string;
          coupon_code?: string;
          image_url?: string;
          imagebig_url?: string;
          imagesmall_url?: string;
          merchant_id?: number;
          brand_id?: number;
          category_id?: number;
          discount?: string;
          price?: number;
          currency?: string;
          deal_start_date?: string;
          deal_end_date?: string;
          tracking_url?: string;
          click_count?: number;
          success_rate?: number;
          verified?: boolean;
          last_verified_at?: string;
          usage_count?: number;
          last_used_date?: string;
          created_at?: string;
          updated_at?: string;
          slug?: string;
          normalized_title?: string;
          cleaned_title?: string;
          discount_percentage?: number;
        };
        Insert: {
          title: string;
          description?: string;
          coupon_code?: string;
          image_url?: string;
          imagebig_url?: string;
          imagesmall_url?: string;
          merchant_id?: number;
          brand_id?: number;
          category_id?: number;
          discount?: string;
          price?: number;
          currency?: string;
          deal_start_date?: string;
          deal_end_date?: string;
          tracking_url?: string;
          click_count?: number;
          success_rate?: number;
          verified?: boolean;
          last_verified_at?: string;
          usage_count?: number;
          last_used_date?: string;
          slug?: string;
          normalized_title?: string;
          cleaned_title?: string;
          discount_percentage?: number;
        };
        Update: {
          title?: string;
          description?: string;
          coupon_code?: string;
          image_url?: string;
          imagebig_url?: string;
          imagesmall_url?: string;
          merchant_id?: number;
          brand_id?: number;
          category_id?: number;
          discount?: string;
          price?: number;
          currency?: string;
          deal_start_date?: string;
          deal_end_date?: string;
          tracking_url?: string;
          click_count?: number;
          success_rate?: number;
          verified?: boolean;
          last_verified_at?: string;
          usage_count?: number;
          last_used_date?: string;
          slug?: string;
          normalized_title?: string;
          cleaned_title?: string;
          discount_percentage?: number;
        };
      };
      merchants: {
        Row: {
          id: number;
          name: string;
          website_url?: string;
          logo_url?: string;
          slug?: string;
          created_at?: string;
          updated_at?: string;
        };
        Insert: {
          name: string;
          website_url?: string;
          logo_url?: string;
          slug?: string;
        };
        Update: {
          name?: string;
          website_url?: string;
          logo_url?: string;
          slug?: string;
        };
      };
      brands: {
        Row: {
          id: number;
          name: string;
          logo_url?: string;
          slug?: string;
          created_at?: string;
          updated_at?: string;
        };
        Insert: {
          name: string;
          logo_url?: string;
          slug?: string;
        };
        Update: {
          name?: string;
          logo_url?: string;
          slug?: string;
        };
      };
      categories: {
        Row: {
          id: number;
          name: string;
          category_logo?: string;
          logo_url?: string;
          slug?: string;
          created_at?: string;
          updated_at?: string;
        };
        Insert: {
          name: string;
          category_logo?: string;
          logo_url?: string;
          slug?: string;
        };
        Update: {
          name?: string;
          category_logo?: string;
          logo_url?: string;
          slug?: string;
        };
      };
    };
    Views: {
      [_ in never]: never;
    };
    Functions: {
      [_ in never]: never;
    };
    Enums: {
      [_ in never]: never;
    };
  };
}

// Environment variables - Try both import.meta.env (Vite) and process.env (Node.js)
const supabaseUrl =
  (typeof import.meta !== 'undefined' ? import.meta.env.SUPABASE_URL : undefined) ||
  (typeof process !== 'undefined' ? process.env.SUPABASE_URL : undefined) ||
  'https://zlnvivfgzgcjuspktadj.supabase.co';

const supabaseAnonKey =
  (typeof import.meta !== 'undefined' ? import.meta.env.SUPABASE_ANON_KEY : undefined) ||
  (typeof process !== 'undefined' ? process.env.SUPABASE_ANON_KEY : undefined) ||
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbnZpdmZnemdjamVzcGt0YWRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODM1NDcyNTAsImV4cCI6MTk5OTEyMzI1MH0.Rl3ZH_ZZS-XbQnrYYKI-Rl-ck-1hqvXQnKYop3iGP68';

// Cookie options for server-side client
export const cookieOptions: CookieOptions = {
  path: '/',
  secure: true,
  httpOnly: true,
  sameSite: 'lax',
};

/**
 * Create a Supabase client for server-side usage
 */
export function createSupabaseServerClient(cookies: AstroCookies) {
  return createServerClient(
    supabaseUrl,
    supabaseAnonKey,
    {
      cookies: {
        get(key) {
          return cookies.get(key)?.value;
        },
        set(key, value, options) {
          cookies.set(key, value, {
            ...options,
            path: options?.path || '/',
          });
        },
        remove(key, options) {
          cookies.delete(key, {
            ...options,
            path: options?.path || '/',
          });
        },
      },
    }
  );
}

/**
 * Create a Supabase client for client-side usage
 */
export function createSupabaseClient() {
  return createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      flowType: 'pkce',
      autoRefreshToken: true,
      persistSession: true,
      detectSessionInUrl: true,
    },
  });
}

// Admin client has been removed for security reasons
