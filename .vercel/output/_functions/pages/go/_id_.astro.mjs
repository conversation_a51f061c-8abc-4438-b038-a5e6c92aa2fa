import { c as createAstro, a as createComponent } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import 'clsx';
import { c as createServerSupabaseClient } from '../../assets/js/server-e_5TR1Eu.js';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$id = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const { id } = Astro2.params;
  if (!id) {
    return Astro2.redirect("/deals");
  }
  const supabase = createServerSupabaseClient();
  const { data, error } = await supabase.from("deals").select("tracking_url, coupon_code").eq("id", id).single();
  if (error || !data) {
    return Astro2.redirect("/deals");
  }
  const deal = data;
  await supabase.from("clicks").insert([
    {
      deal_id: parseInt(id),
      user_agent: Astro2.request.headers.get("user-agent") || "",
      ip_address: Astro2.request.headers.get("x-forwarded-for") || "127.0.0.1",
      timestamp: (/* @__PURE__ */ new Date()).toISOString()
    }
  ]);
  const { data: dealData, error: getError } = await supabase.from("deals").select("click_count").eq("id", parseInt(id)).single();
  if (!getError && dealData) {
    const currentCount = dealData.click_count || 0;
    const { error: updateError } = await supabase.from("deals").update({ click_count: currentCount + 1 }).eq("id", parseInt(id));
    if (updateError) {
      console.error("Error updating click count:", updateError);
    }
  }
  const cookieName = `revealed_deal_${id}`;
  Astro2.cookies.set(cookieName, "true", {
    path: "/",
    maxAge: 60 * 60 * 24 * 30
    // 30 days
  });
  const redirectUrl = deal.tracking_url || "/deals";
  return Astro2.redirect(redirectUrl);
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/go/[id].astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/go/[id].astro";
const $$url = "/go/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
