import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../assets/js/MainLayout-BVJnMCp3.js';
import { jsxs, jsx } from 'react/jsx-runtime';
import { useState, useEffect, useMemo } from 'react';
import { Heart, RefreshCw, ShoppingBag, XCircle, Eye, ExternalLink } from 'lucide-react';
import '../assets/js/app/design-system-CUssmfny.js';
import { toast } from 'sonner';
export { renderers } from '../renderers.mjs';

function EmptyBookmarks({ reason = "no-bookmarks" }) {
  const getTitle = () => {
    switch (reason) {
      case "no-bookmarks":
        return "Your Bookmarks List is Empty";
      case "no-deals-found":
        return "Could Not Find Your Bookmarked Deals";
      case "error":
        return "Error Loading Your Bookmarks";
      default:
        return "Your Bookmarks List is Empty";
    }
  };
  const getMessage = () => {
    switch (reason) {
      case "no-bookmarks":
        return "Save your favorite deals by clicking the heart icon on any product card. Your bookmarks will be stored here for easy access.";
      case "no-deals-found":
        return "We found your bookmarked IDs but could not retrieve the deals. The deals may have expired or been removed.";
      case "error":
        return "There was an issue loading your bookmarked deals. Please try refreshing the page.";
      default:
        return "Save your favorite deals by clicking the heart icon on any product card. Your bookmarks will be stored here for easy access.";
    }
  };
  return /* @__PURE__ */ jsxs("div", { className: "text-center py-12 bg-design-card/80 backdrop-blur-sm border border-design-border rounded-lg max-w-md mx-auto", children: [
    /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-4", children: /* @__PURE__ */ jsxs("div", { className: "relative", children: [
      /* @__PURE__ */ jsx("div", { className: "absolute -inset-1 rounded-full bg-design-accent/20 animate-pulse" }),
      /* @__PURE__ */ jsx(Heart, { className: "h-12 w-12 text-[#f21d6b] relative" })
    ] }) }),
    /* @__PURE__ */ jsx("h3", { className: "text-xl font-semibold text-design-foreground mb-2", children: getTitle() }),
    /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground mb-6 max-w-xs mx-auto text-sm", children: getMessage() }),
    /* @__PURE__ */ jsxs("div", { className: "flex justify-center gap-4", children: [
      reason !== "no-bookmarks" && /* @__PURE__ */ jsxs(
        "button",
        {
          onClick: () => window.location.reload(),
          className: "inline-flex items-center justify-center gap-2 rounded-md border border-design-border bg-design-background text-design-foreground hover:bg-design-accent/10 px-4 py-2 text-sm font-medium transition-colors",
          children: [
            /* @__PURE__ */ jsx(RefreshCw, { className: "h-4 w-4" }),
            /* @__PURE__ */ jsx("span", { children: "Refresh" })
          ]
        }
      ),
      /* @__PURE__ */ jsxs("a", { href: "/deals", className: "inline-flex items-center justify-center gap-2 rounded-md bg-design-primary text-design-primary-foreground hover:bg-design-primary/90 px-4 py-2 text-sm font-medium transition-colors hover:text-white hover:bg-design-secondary", children: [
        /* @__PURE__ */ jsx(ShoppingBag, { className: "h-4 w-4" }),
        /* @__PURE__ */ jsx("span", { children: "Discover Deals" })
      ] })
    ] })
  ] }, "empty-bookmarks");
}

function BookmarksPage() {
  const [bookmarkIds, setBookmarkIds] = useState([]);
  const [deals, setDeals] = useState([]);
  const [loading, setLoading] = useState(true);
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const storedBookmarkIds = localStorage.getItem("bookmarkIds");
        console.log("Stored Bookmark IDs:", storedBookmarkIds);
        if (storedBookmarkIds) {
          const parsedIds = JSON.parse(storedBookmarkIds);
          console.log("Parsed Bookmark IDs:", parsedIds);
          if (Array.isArray(parsedIds)) {
            setBookmarkIds(parsedIds);
          } else {
            console.error("Stored bookmarkIds is not an array:", parsedIds);
            setBookmarkIds([]);
          }
        }
      } catch (error) {
        console.error("Error loading bookmark IDs:", error);
        localStorage.setItem("bookmarkIds", JSON.stringify([]));
      }
      const handleStorageChange = (e) => {
        if (e.key === "bookmarkIds") {
          try {
            const newBookmarkIds = e.newValue ? JSON.parse(e.newValue) : [];
            setBookmarkIds(newBookmarkIds);
          } catch (error) {
            console.error("Error parsing bookmark IDs from storage event:", error);
          }
        }
      };
      const handleCustomBookmarkChange = (e) => {
        try {
          const newBookmarkIds = e.detail ? e.detail : [];
          setBookmarkIds(newBookmarkIds);
        } catch (error) {
          console.error("Error handling custom bookmark change event:", error);
        }
      };
      window.addEventListener("storage", handleStorageChange);
      window.addEventListener("bookmarkChange", handleCustomBookmarkChange);
      return () => {
        window.removeEventListener("storage", handleStorageChange);
        window.removeEventListener("bookmarkChange", handleCustomBookmarkChange);
      };
    }
  }, []);
  useEffect(() => {
    const fetchDeals = async () => {
      if (bookmarkIds.length === 0) {
        setDeals([]);
        setLoading(false);
        return;
      }
      setLoading(true);
      try {
        const idList = bookmarkIds.join(",");
        console.log("Fetching deals with IDs:", idList);
        const baseUrl = "";
        const response = await fetch(`${baseUrl}/api/deals?ids=${idList}`);
        console.log("API Response Status:", response.status, response.statusText);
        if (!response.ok) {
          const errorText = await response.text();
          console.error("API Error Response:", errorText);
          console.error("Response Status:", response.status, response.statusText);
          throw new Error(`Failed to fetch deals: ${response.status} ${response.statusText}`);
        }
        const data = await response.json();
        console.log("API Response Data:", data);
        if (!Array.isArray(data)) {
          console.error("API did not return an array:", data);
          throw new Error("Invalid response format");
        }
        const filteredDeals = data.filter((deal) => {
          const dealIdStr = String(deal.id);
          const isIncluded = bookmarkIds.some((id) => String(id) === dealIdStr);
          console.log(`Checking if deal ${dealIdStr} is in bookmarks: ${isIncluded}`);
          return isIncluded;
        });
        console.log("Filtered Deals:", filteredDeals);
        setDeals(filteredDeals);
      } catch (error) {
        console.error("Error fetching deals:", error);
        toast.error("Error loading bookmarked deals");
        setDeals([]);
        window.bookmarksError = true;
      } finally {
        setLoading(false);
      }
    };
    fetchDeals();
  }, [bookmarkIds]);
  const getImageUrl = (deal) => {
    if (deal.imagebig_url && deal.imagebig_url !== "YOUR_LOGO_URL") return deal.imagebig_url;
    if (deal.image_url && deal.image_url !== "YOUR_LOGO_URL") return deal.image_url;
    if (deal.imagesmall_url && deal.imagesmall_url !== "YOUR_LOGO_URL") return deal.imagesmall_url;
    return "/placeholder-image.svg";
  };
  const calculateDiscountedPrice = (price, discount) => {
    if (!price || !discount) return price;
    const discountValue = parseInt(discount);
    if (isNaN(discountValue)) return price;
    return price * (1 - discountValue / 100);
  };
  const totals = useMemo(() => {
    let totalOriginal = 0;
    let totalDiscounted = 0;
    deals.forEach((deal) => {
      if (deal.price) {
        const originalPrice = deal.price;
        const discountedPrice = calculateDiscountedPrice(originalPrice, deal.discount);
        totalOriginal += originalPrice;
        totalDiscounted += discountedPrice;
      }
    });
    const totalSavings = totalOriginal - totalDiscounted;
    const savingsPercentage = totalOriginal > 0 ? totalSavings / totalOriginal * 100 : 0;
    return {
      totalOriginal: totalOriginal.toFixed(2),
      totalDiscounted: totalDiscounted.toFixed(2),
      totalSavings: totalSavings.toFixed(2),
      savingsPercentage: Math.round(savingsPercentage)
    };
  }, [deals]);
  const removeBookmark = (id) => {
    if (typeof window !== "undefined") {
      try {
        console.log("Removing bookmark with ID:", id);
        console.log("Current deals:", deals);
        const storedBookmarkIds = localStorage.getItem("bookmarkIds");
        console.log("Stored bookmark IDs before removal:", storedBookmarkIds);
        let ids = storedBookmarkIds ? JSON.parse(storedBookmarkIds) : [];
        console.log("Parsed bookmark IDs before removal:", ids);
        const idStr = String(id);
        ids = ids.filter((item) => {
          const itemStr = String(item);
          const matches = itemStr !== idStr;
          console.log(`Comparing ${itemStr} with ${idStr}: ${matches ? "keep" : "remove"}`);
          return matches;
        });
        console.log("Bookmark IDs after removal:", ids);
        localStorage.setItem("bookmarkIds", JSON.stringify(ids));
        setBookmarkIds(ids);
        console.log("Updated bookmarkIds state");
        setDeals((prevDeals) => {
          const newDeals = prevDeals.filter((deal) => {
            const keepDeal = String(deal.id) !== idStr;
            console.log(`Filtering deal ${deal.id}: ${keepDeal ? "keep" : "remove"}`);
            return keepDeal;
          });
          console.log("New deals after removal:", newDeals);
          return newDeals;
        });
        setDeals((prevDeals) => [...prevDeals]);
        toast.success("Deal removed from bookmarks");
        window.dispatchEvent(new StorageEvent("storage", {
          key: "bookmarkIds",
          newValue: JSON.stringify(ids)
        }));
        window.dispatchEvent(new CustomEvent("bookmarkChange", {
          detail: ids
        }));
        console.log("Bookmark removal complete");
      } catch (error) {
        console.error("Error removing bookmark:", error);
        toast.error("Error removing bookmark");
      }
    }
  };
  if (loading) {
    return /* @__PURE__ */ jsx("div", { className: "flex justify-center items-center py-12", children: /* @__PURE__ */ jsx("div", { className: "animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-design-primary" }) });
  }
  if (bookmarkIds.length === 0) {
    return /* @__PURE__ */ jsx(EmptyBookmarks, { reason: "no-bookmarks" });
  }
  if (deals.length === 0) {
    if (window.bookmarksError) {
      return /* @__PURE__ */ jsx(EmptyBookmarks, { reason: "error" });
    }
    return /* @__PURE__ */ jsx(EmptyBookmarks, { reason: "no-deals-found" });
  }
  return /* @__PURE__ */ jsxs("div", { className: "max-w-6xl mx-auto", children: [
    /* @__PURE__ */ jsx("div", { className: "flex justify-end items-center mb-4", children: /* @__PURE__ */ jsxs("p", { className: "text-sm text-design-muted-foreground", children: [
      deals.length,
      " item",
      deals.length !== 1 ? "s" : ""
    ] }) }),
    /* @__PURE__ */ jsx("div", { className: "bg-design-card/80 backdrop-blur-sm rounded-lg border border-design-border overflow-hidden mb-6", children: /* @__PURE__ */ jsx("div", { className: "overflow-x-auto", children: /* @__PURE__ */ jsxs("table", { className: "w-full border-collapse text-sm", children: [
      /* @__PURE__ */ jsx("thead", { children: /* @__PURE__ */ jsxs("tr", { className: "border-b border-design-border bg-design-card/50", children: [
        /* @__PURE__ */ jsx("th", { className: "py-2 px-3 text-left w-8 text-design-muted-foreground", children: "#" }),
        /* @__PURE__ */ jsx("th", { className: "py-2 px-3 text-left w-8" }),
        /* @__PURE__ */ jsx("th", { className: "py-2 px-3 text-left w-20", children: "Image" }),
        /* @__PURE__ */ jsx("th", { className: "py-2 px-3 text-left", children: "Product" }),
        /* @__PURE__ */ jsx("th", { className: "py-2 px-3 text-right", children: "Discount" }),
        /* @__PURE__ */ jsx("th", { className: "py-2 px-3 text-right", children: "Original" }),
        /* @__PURE__ */ jsx("th", { className: "py-2 px-3 text-right", children: "Price" }),
        /* @__PURE__ */ jsx("th", { className: "py-2 px-3 text-center", children: "Actions" })
      ] }) }),
      /* @__PURE__ */ jsx("tbody", { children: deals.map((deal, index) => {
        const originalPrice = deal.price || 0;
        const discountedPrice = calculateDiscountedPrice(originalPrice, deal.discount);
        const savings = originalPrice - discountedPrice;
        return /* @__PURE__ */ jsxs(
          "tr",
          {
            className: "border-b border-design-border hover:bg-design-accent/10 transition-colors duration-design-normal",
            children: [
              /* @__PURE__ */ jsx("td", { className: "py-3 px-3 text-design-muted-foreground text-xs", children: index + 1 }),
              /* @__PURE__ */ jsx("td", { className: "py-3 px-3", children: /* @__PURE__ */ jsx(
                "button",
                {
                  onClick: () => removeBookmark(deal.id),
                  className: "text-design-muted-foreground hover:text-[#f21d6b] transition-colors",
                  "aria-label": "Remove from bookmarks",
                  children: /* @__PURE__ */ jsx(XCircle, { className: "h-4 w-4" })
                }
              ) }),
              /* @__PURE__ */ jsx("td", { className: "py-3 px-3", children: /* @__PURE__ */ jsx("div", { className: "w-14 h-14 rounded overflow-hidden bg-design-muted/20 flex items-center justify-center", children: /* @__PURE__ */ jsx(
                "img",
                {
                  src: getImageUrl(deal),
                  className: "max-w-full max-h-full object-contain",
                  alt: deal.cleaned_title || deal.title,
                  onError: (e) => {
                    const target = e.target;
                    if (target.src !== "/placeholder-image.svg") {
                      target.src = "/placeholder-image.svg";
                    }
                  }
                }
              ) }) }),
              /* @__PURE__ */ jsx("td", { className: "py-3 px-3 font-medium text-design-foreground", children: deal.cleaned_title || deal.title }),
              /* @__PURE__ */ jsx("td", { className: "py-3 px-3 text-right", children: /* @__PURE__ */ jsxs("span", { className: "text-design-foreground font-medium", children: [
                deal.discount || "0",
                "%"
              ] }) }),
              /* @__PURE__ */ jsx("td", { className: "py-3 px-3 text-right", children: /* @__PURE__ */ jsxs("span", { className: "line-through text-design-muted-foreground", children: [
                "$",
                originalPrice.toFixed(2)
              ] }) }),
              /* @__PURE__ */ jsxs("td", { className: "py-3 px-3 text-right", children: [
                /* @__PURE__ */ jsxs("span", { className: "font-semibold text-design-foreground", children: [
                  "$",
                  discountedPrice.toFixed(2)
                ] }),
                savings > 0 && /* @__PURE__ */ jsxs("div", { className: "text-[11px] leading-2 text-design-primary font-medium", children: [
                  "Save $",
                  savings.toFixed(2)
                ] })
              ] }),
              /* @__PURE__ */ jsx("td", { className: "py-3 px-3 text-center", children: /* @__PURE__ */ jsxs("div", { className: "flex gap-2 justify-center", children: [
                /* @__PURE__ */ jsxs(
                  "a",
                  {
                    href: `/deal/${deal.id}`,
                    className: "h-7 w-16 text-xs inline-flex items-center justify-center gap-1 rounded-md border border-design-border bg-design-background text-design-muted-foreground hover:bg-design-accent/20 hover:border-design-primary/50 transition-colors",
                    children: [
                      /* @__PURE__ */ jsx(Eye, { className: "h-3 w-3" }),
                      /* @__PURE__ */ jsx("span", { children: "View" })
                    ]
                  }
                ),
                /* @__PURE__ */ jsxs(
                  "a",
                  {
                    href: `/go/${deal.id}`,
                    className: "h-7 w-16 text-xs inline-flex items-center justify-center gap-1 rounded-md bg-design-primary text-black hover:bg-design-secondary  shadow-sm transition-colors",
                    children: [
                      /* @__PURE__ */ jsx(ExternalLink, { className: "h-3 w-3" }),
                      /* @__PURE__ */ jsx("span", { children: "Get" })
                    ]
                  }
                )
              ] }) })
            ]
          },
          `deal-${deal.id}-${index}`
        );
      }) }, `deals-tbody-${deals.length}-${Date.now()}`)
    ] }) }) }),
    deals.length > 0 && /* @__PURE__ */ jsxs("div", { className: "bg-design-card/80 backdrop-blur-sm rounded-lg border border-design-border p-4 max-w-md ml-auto", children: [
      /* @__PURE__ */ jsx("h3", { className: "text-sm font-medium text-design-foreground mb-3", children: "Summary" }),
      /* @__PURE__ */ jsxs("div", { className: "space-y-2 text-sm", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsx("span", { className: "text-design-muted-foreground", children: "Total Original Price:" }),
          /* @__PURE__ */ jsxs("span", { className: "text-design-foreground", children: [
            "$",
            totals.totalOriginal
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex justify-between", children: [
          /* @__PURE__ */ jsx("span", { className: "text-design-muted-foreground", children: "Total Discounted Price:" }),
          /* @__PURE__ */ jsxs("span", { className: "text-design-foreground", children: [
            "$",
            totals.totalDiscounted
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "border-t border-design-border my-2 pt-2 flex justify-between font-medium", children: [
          /* @__PURE__ */ jsx("span", { className: "text-design-foreground", children: "Total Savings:" }),
          /* @__PURE__ */ jsxs("div", { className: "text-right", children: [
            /* @__PURE__ */ jsxs("span", { className: "text-design-primary font-semibold", children: [
              "$",
              totals.totalSavings
            ] }),
            /* @__PURE__ */ jsxs("span", { className: "text-design-primary text-xs ml-1", children: [
              "(",
              totals.savingsPercentage,
              "% off)"
            ] })
          ] })
        ] })
      ] })
    ] })
  ] });
}

const $$Bookmarks = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Saved Deals - VapeHybrid Coupons", "description": "Your saved deals and coupons" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-8"> <div class="mb-6 text-center"> <nav class="flex justify-center mb-2 text-sm"> <ol class="flex items-center space-x-1"> <li> <a href="/" class="text-design-muted-foreground hover:text-design-foreground transition-colors">Home</a> </li> <li> <span class="text-design-muted-foreground mx-1">/</span> </li> <li> <span class="text-design-foreground font-medium">Saved Deals</span> </li> </ol> </nav> <h1 class="text-4xl md:text-5xl font-normal mb-3 text-design-foreground">
Saved Deals
</h1> <p class="text-xs text-design-muted-foreground max-w-2xl mx-auto">
Your bookmarked deals and coupons in one place
</p> </div> ${renderComponent($$result2, "BookmarksPage", BookmarksPage, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/BookmarksPage", "client:component-export": "default" })} </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/bookmarks.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/bookmarks.astro";
const $$url = "/bookmarks";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Bookmarks,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
