/**
 * <PERSON><PERSON><PERSON> to install the required dependencies for the design system
 *
 * This script installs the required dependencies for the design system,
 * including the Wix Madefor Display font and the chroma-js library.
 *
 * Usage: node scripts/install-design-system.js
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Installing design system dependencies...');

// Install chroma-js for color manipulation
try {
  console.log('Installing chroma-js...');
  execSync('npm install chroma-js', { stdio: 'inherit' });
  console.log('chroma-js installed successfully.');
} catch (error) {
  console.error('Error installing chroma-js:', error);
  process.exit(1);
}

// Install class-variance-authority if not already installed
try {
  console.log('Checking for class-variance-authority...');
  const packageJson = JSON.parse(fs.readFileSync(path.join(__dirname, '../package.json'), 'utf8'));

  if (!packageJson.dependencies['class-variance-authority']) {
    console.log('Installing class-variance-authority...');
    execSync('npm install class-variance-authority', { stdio: 'inherit' });
    console.log('class-variance-authority installed successfully.');
  } else {
    console.log('class-variance-authority is already installed.');
  }
} catch (error) {
  console.error('Error checking/installing class-variance-authority:', error);
  process.exit(1);
}

// Create a fonts directory if it doesn't exist
const fontsDir = path.join(__dirname, '../public/fonts');
if (!fs.existsSync(fontsDir)) {
  console.log('Creating fonts directory...');
  fs.mkdirSync(fontsDir, { recursive: true });
}

// Generate CSS variables
try {
  console.log('Generating CSS variables...');
  execSync('node scripts/generate-css-variables.js', { stdio: 'inherit' });
  console.log('CSS variables generated successfully.');
} catch (error) {
  console.error('Error generating CSS variables:', error);
  process.exit(1);
}

console.log('Design system dependencies installed successfully.');
console.log('');
console.log('Next steps:');
console.log('1. Add the Wix Madefor Display font to your project:');
console.log('   - Add the following to your <head> in the layout component:');
console.log('     <link rel="preconnect" href="https://fonts.googleapis.com">');
console.log('     <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>');
console.log('     <link href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@400;500;600;700&display=swap" rel="stylesheet">');
console.log('');
console.log('2. Visit /design-system to see the design system in action.');
console.log('');
console.log('3. Use the design system components in your project:');
console.log('   - Import components from src/components/DesignSystem');
console.log('   - Use the design tokens from src/styles/design-tokens.ts');
console.log('   - Use the utility functions from src/lib/design-system.ts');
console.log('');
console.log('4. Run `npm run generate-css` to update the CSS variables when you change the design tokens.');
console.log('');
