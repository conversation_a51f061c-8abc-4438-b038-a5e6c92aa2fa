/**
 * <PERSON><PERSON><PERSON> to analyze CSS usage
 * 
 * This script analyzes CSS usage in the application and generates a report.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';
import { execSync } from 'child_process';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);
const distDir = path.join(__dirname, '../dist');
const reportDir = path.join(__dirname, '../reports');

/**
 * Analyze CSS usage
 */
async function analyzeCss() {
  console.log('Analyzing CSS usage...');
  
  try {
    // Check if dist directory exists
    if (!fs.existsSync(distDir)) {
      console.error(`Error: dist directory does not exist at ${distDir}`);
      console.error('Please run "npm run build" first.');
      process.exit(1);
    }
    
    // Create reports directory if it doesn't exist
    if (!fs.existsSync(reportDir)) {
      fs.mkdirSync(reportDir, { recursive: true });
    }
    
    // Run css-analyzer
    const output = execSync(`npx css-analyzer ${distDir}/**/*.css --stats`, { encoding: 'utf8' });
    
    // Write report to file
    const reportPath = path.join(reportDir, 'css-analysis.txt');
    fs.writeFileSync(reportPath, output);
    
    console.log(`✅ CSS analysis report saved to ${reportPath}`);
    
    // Log summary
    console.log('\nCSS Analysis Summary:');
    console.log(output.split('\n').slice(0, 20).join('\n'));
    console.log(`\nSee ${reportPath} for full report.`);
    
    return true;
  } catch (error) {
    console.error('Error analyzing CSS:', error);
    return false;
  }
}

// Run the main function
analyzeCss();
