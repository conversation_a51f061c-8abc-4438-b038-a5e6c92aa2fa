import React, { useState, useCallback, useRef } from 'react';
import { Slider } from '../ui/slider';
import Button from './Button/Button';
// Badge component is not used in this file
import { Checkbox } from '../ui/checkbox';
import Label from './Form/Label';

interface Category {
  id: number;
  name: string;
}

interface Merchant {
  id: number;
  name: string;
}

interface Brand {
  id: number;
  name: string;
}

interface FilterFormProps {
  categories: Category[];
  merchants: Merchant[];
  brands: Brand[];
  initialFilters?: FilterState;
  isMobile?: boolean;
  currentUrl?: string;
}

export interface FilterState {
  categories: number[];
  merchants: number[];
  brands: number[];
  priceRange: [number, number];
  discountRange: [number, number];
  expiringSoon: boolean;
  validLongTerm: boolean;
}

export default function FilterForm({
  categories,
  merchants,
  brands,
  initialFilters,
  // isMobile parameter is not used
  // isMobile = false,
  currentUrl = '/coupons'
}: FilterFormProps) {
  const [filters, setFilters] = useState<FilterState>(initialFilters || {
    categories: [],
    merchants: [],
    brands: [],
    priceRange: [0, 200],
    discountRange: [0, 100],
    expiringSoon: false,
    validLongTerm: false
  });

  const [minPrice, maxPrice] = filters.priceRange;
  const [minDiscount, maxDiscount] = filters.discountRange;

  // For search functionality
  const [categorySearch, setCategorySearch] = useState('');
  const [merchantSearch, setMerchantSearch] = useState('');
  const [brandSearch, setBrandSearch] = useState('');

  // Filter categories, merchants, and brands based on search
  const filteredCategories = categories.filter(category =>
    category.name.toLowerCase().includes(categorySearch.toLowerCase())
  );

  const filteredMerchants = merchants.filter(merchant =>
    merchant.name.toLowerCase().includes(merchantSearch.toLowerCase())
  );

  const filteredBrands = brands.filter(brand =>
    brand.name.toLowerCase().includes(brandSearch.toLowerCase())
  );

  const handleCheckboxChange = (
    value: number,
    checked: boolean,
    type: 'categories' | 'merchants' | 'brands'
  ) => {
    setFilters(prev => {
      if (checked) {
        return {
          ...prev,
          [type]: [...prev[type], value]
        };
      } else {
        return {
          ...prev,
          [type]: prev[type].filter(id => id !== value)
        };
      }
    });
  };

  const handleExpiryChange = (name: string, checked: boolean) => {
    setFilters(prev => ({
      ...prev,
      [name]: checked
    }));
  };

  // Use debouncing for slider changes to prevent forced reflow
  const priceRangeTimeoutRef = useRef<number | null>(null);
  const discountRangeTimeoutRef = useRef<number | null>(null);

  const handlePriceRangeChange = useCallback((value: number[]) => {
    // Clear any existing timeout
    if (priceRangeTimeoutRef.current) {
      window.clearTimeout(priceRangeTimeoutRef.current);
    }

    // Set a timeout to update the state after a delay
    priceRangeTimeoutRef.current = window.setTimeout(() => {
      // Use requestAnimationFrame to batch DOM updates
      requestAnimationFrame(() => {
        setFilters(prev => ({
          ...prev,
          priceRange: [value[0], value[1]] as [number, number]
        }));
      });
    }, 50); // 50ms delay
  }, []);

  const handleDiscountRangeChange = useCallback((value: number[]) => {
    // Clear any existing timeout
    if (discountRangeTimeoutRef.current) {
      window.clearTimeout(discountRangeTimeoutRef.current);
    }

    // Set a timeout to update the state after a delay
    discountRangeTimeoutRef.current = window.setTimeout(() => {
      // Use requestAnimationFrame to batch DOM updates
      requestAnimationFrame(() => {
        setFilters(prev => ({
          ...prev,
          discountRange: [value[0], value[1]] as [number, number]
        }));
      });
    }, 50); // 50ms delay
  }, []);

  const handleReset = () => {
    setFilters({
      categories: [],
      merchants: [],
      brands: [],
      priceRange: [0, 200],
      discountRange: [0, 100],
      expiringSoon: false,
      validLongTerm: false
    });
    setCategorySearch('');
    setMerchantSearch('');
    setBrandSearch('');
  };

  // Count active filters
  const activeFilterCount =
    filters.categories.length +
    filters.merchants.length +
    filters.brands.length +
    (filters.expiringSoon ? 1 : 0) +
    (filters.validLongTerm ? 1 : 0) +
    ((minPrice > 0 || maxPrice < 200) ? 1 : 0) +
    ((minDiscount > 0 || maxDiscount < 100) ? 1 : 0);

  // Safely get URL search params
  const getSearchParams = () => {
    try {
      // Check if we're in a browser environment
      if (typeof window === 'undefined') {
        return new URLSearchParams();
      }

      // If currentUrl is a full URL, use it directly
      if (currentUrl && currentUrl.startsWith('http')) {
        return new URL(currentUrl).searchParams;
      }

      // If it's a pathname, create a full URL for parsing
      if (currentUrl && currentUrl.startsWith('/')) {
        const fullUrl = new URL(currentUrl, window.location.origin);
        return fullUrl.searchParams;
      }

      // Fallback: try to get from current window location
      return new URL(window.location.href).searchParams;
    } catch (error) {
      console.warn('Error parsing URL in FilterForm:', error);
      // Ultimate fallback: return empty URLSearchParams
      return new URLSearchParams();
    }
  };

  const searchParams = getSearchParams();

  return (
    <form action={currentUrl} method="get" className="space-y-4">
      {/* Preserve existing query parameters */}
      <input type="hidden" name="view" value={searchParams.get('view') || 'grid'} />
      <input type="hidden" name="sort" value={searchParams.get('sort') || 'newest'} />

      {/* Price range hidden fields */}
      <input type="hidden" name="minPrice" value={minPrice} />
      <input type="hidden" name="maxPrice" value={maxPrice} />

      {/* Discount range hidden fields */}
      <input type="hidden" name="minDiscount" value={minDiscount} />
      <input type="hidden" name="maxDiscount" value={maxDiscount} />

      {/* Expiry hidden fields */}
      {filters.expiringSoon && <input type="hidden" name="expiringSoon" value="true" />}
      {filters.validLongTerm && <input type="hidden" name="validLongTerm" value="true" />}

      {/* Filter categories */}
      {filters.categories.map(id => (
        <input key={id} type="hidden" name="categories" value={id} />
      ))}

      {/* Filter merchants */}
      {filters.merchants.map(id => (
        <input key={id} type="hidden" name="merchants" value={id} />
      ))}

      {/* Filter brands */}
      {filters.brands.map(id => (
        <input key={id} type="hidden" name="brands" value={id} />
      ))}

      {/* Reset button only shown when filters are active */}
      {activeFilterCount > 0 && (
        <div className="mb-2 -mt-1">
          <Button
            type="button"
            variant="ghost"
            size="sm"
            onClick={handleReset}
            className="text-design-destructive hover:text-design-destructive/90 font-medium w-full justify-center border border-design-border rounded-sm text-xs py-1"
          >
            Reset ({activeFilterCount})
          </Button>
        </div>
      )}

      {/* Price Range */}
      <div className="space-y-2 bg-design-muted/10 p-2 rounded-md">
        <div className="flex items-center justify-between">
          <h4 className="text-xs font-medium text-design-foreground">Price Range</h4>
          <div className="text-xs text-design-muted-foreground">
            ${minPrice} - ${maxPrice}
          </div>
        </div>
        <Slider
          defaultValue={[minPrice, maxPrice]}
          min={0}
          max={200}
          step={5}
          value={[minPrice, maxPrice]}
          onValueChange={handlePriceRangeChange}
          className="py-1"
        />
      </div>

      {/* Discount Range */}
      <div className="space-y-2 bg-design-muted/10 p-2 rounded-md">
        <div className="flex items-center justify-between">
          <h4 className="text-xs font-medium text-design-foreground">Discount Range</h4>
          <div className="text-xs text-design-muted-foreground">
            {minDiscount}% - {maxDiscount}%
          </div>
        </div>
        <Slider
          defaultValue={[minDiscount, maxDiscount]}
          min={0}
          max={100}
          step={5}
          value={[minDiscount, maxDiscount]}
          onValueChange={handleDiscountRangeChange}
          className="py-1"
        />
      </div>

      {/* Expiry Options */}
      <div className="space-y-2 bg-design-muted/10 p-2 rounded-md">
        <h4 className="text-xs font-medium text-design-foreground mb-1">Deal Expiry</h4>
        <div className="space-y-1.5">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="expiringSoon"
              checked={filters.expiringSoon}
              onCheckedChange={(checked) => handleExpiryChange('expiringSoon', checked as boolean)}
              className="h-3.5 w-3.5"
            />
            <Label htmlFor="expiringSoon" className="text-xs font-normal cursor-pointer">
              Expiring Soon (15 days or less)
            </Label>
          </div>
          <div className="flex items-center space-x-2">
            <Checkbox
              id="validLongTerm"
              checked={filters.validLongTerm}
              onCheckedChange={(checked) => handleExpiryChange('validLongTerm', checked as boolean)}
              className="h-3.5 w-3.5"
            />
            <Label htmlFor="validLongTerm" className="text-xs font-normal cursor-pointer">
              Valid Long Term (30+ days)
            </Label>
          </div>
        </div>
      </div>

      {/* Categories */}
      <div className="space-y-2 bg-design-muted/10 p-2 rounded-md">
        <h4 className="text-xs font-medium text-design-foreground mb-1">Categories</h4>
        <div className="relative">
          <input
            type="text"
            placeholder="Search categories..."
            value={categorySearch}
            onChange={(e) => setCategorySearch(e.target.value)}
            className="w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"
          />
          {categorySearch && (
            <button
              type="button"
              onClick={() => setCategorySearch('')}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              <span className="sr-only">Clear search</span>
            </button>
          )}
        </div>
        <div className="space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1">
          {filteredCategories?.length > 0 ? (
            filteredCategories.map(category => (
              <div key={category.id} className="flex items-center space-x-1.5">
                <Checkbox
                  id={`category-${category.id}`}
                  checked={filters.categories.includes(category.id)}
                  onCheckedChange={(checked) => handleCheckboxChange(category.id, checked as boolean, 'categories')}
                  className="h-3.5 w-3.5"
                />
                <Label htmlFor={`category-${category.id}`} className="text-xs font-normal cursor-pointer">
                  {category.name}
                </Label>
              </div>
            ))
          ) : (
            <p className="text-xs text-design-muted-foreground py-1">
              {categories.length > 0 ? 'No matching categories' : 'No categories found'}
            </p>
          )}
        </div>
      </div>

      {/* Merchants */}
      <div className="space-y-2 bg-design-muted/10 p-2 rounded-md">
        <h4 className="text-xs font-medium text-design-foreground mb-1">Merchants</h4>
        <div className="relative">
          <input
            type="text"
            placeholder="Search merchants..."
            value={merchantSearch}
            onChange={(e) => setMerchantSearch(e.target.value)}
            className="w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"
          />
          {merchantSearch && (
            <button
              type="button"
              onClick={() => setMerchantSearch('')}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              <span className="sr-only">Clear search</span>
            </button>
          )}
        </div>
        <div className="space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1">
          {filteredMerchants?.length > 0 ? (
            filteredMerchants.map(merchant => (
              <div key={merchant.id} className="flex items-center space-x-1.5">
                <Checkbox
                  id={`merchant-${merchant.id}`}
                  checked={filters.merchants.includes(merchant.id)}
                  onCheckedChange={(checked) => handleCheckboxChange(merchant.id, checked as boolean, 'merchants')}
                  className="h-3.5 w-3.5"
                />
                <Label htmlFor={`merchant-${merchant.id}`} className="text-xs font-normal cursor-pointer">
                  {merchant.name}
                </Label>
              </div>
            ))
          ) : (
            <p className="text-xs text-design-muted-foreground py-1">
              {merchants.length > 0 ? 'No matching merchants' : 'No merchants found'}
            </p>
          )}
        </div>
      </div>

      {/* Brands */}
      <div className="space-y-2 bg-design-muted/10 p-2 rounded-md">
        <h4 className="text-xs font-medium text-design-foreground mb-1">Brands</h4>
        <div className="relative">
          <input
            type="text"
            placeholder="Search brands..."
            value={brandSearch}
            onChange={(e) => setBrandSearch(e.target.value)}
            className="w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"
          />
          {brandSearch && (
            <button
              type="button"
              onClick={() => setBrandSearch('')}
              className="absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground"
            >
              <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
              <span className="sr-only">Clear search</span>
            </button>
          )}
        </div>
        <div className="space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1">
          {filteredBrands?.length > 0 ? (
            filteredBrands.map(brand => (
              <div key={brand.id} className="flex items-center space-x-1.5">
                <Checkbox
                  id={`brand-${brand.id}`}
                  checked={filters.brands.includes(brand.id)}
                  onCheckedChange={(checked) => handleCheckboxChange(brand.id, checked as boolean, 'brands')}
                  className="h-3.5 w-3.5"
                />
                <Label htmlFor={`brand-${brand.id}`} className="text-xs font-normal cursor-pointer">
                  {brand.name}
                </Label>
              </div>
            ))
          ) : (
            <p className="text-xs text-design-muted-foreground py-1">
              {brands.length > 0 ? 'No matching brands' : 'No brands found'}
            </p>
          )}
        </div>
      </div>

      {/* Apply Filters Button */}
      <div className="sticky bottom-0 pt-2 pb-2 bg-design-background border-t border-design-border z-40 shadow-md">
        <Button
          type="submit"
          variant="primary"
          className="w-full text-xs font-medium bg-design-primary text-white dark:text-black cursor-pointer rounded-sm px-3 py-2 text-center transition-colors hover:bg-design-primary/90 flex items-center justify-center"
        >
          <div className="flex items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" className="h-3.5 w-3.5 mr-1 inline-block" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <path d="M22 3H2l8 9.46V19l4 2v-8.54L22 3z" />
            </svg>
            <span>Apply {activeFilterCount > 0 && `(${activeFilterCount})`}</span>
          </div>
        </Button>
      </div>
    </form>
  );
}
