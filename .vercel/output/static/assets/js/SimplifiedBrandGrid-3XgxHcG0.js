import{r as e,j as r}from"./vendor/react-bq1VYqFi.js";import{m as a}from"./proxy-BAs1uqPX.js";const t=({brands:t=[],title:s="Premium Vape Brands We Partner With",subtitle:l="Exclusive deals from the most trusted names in the industry",viewAllLink:i="/brands",maxBrands:n=9})=>{const d=e.useMemo((()=>{const e=t.filter((e=>e.logo_url));return e.length>=3?e:t.slice(0,n)}),[t,n]);return r.jsxs("div",{className:"py-16 md:py-20 lg:py-24 bg-transparent relative overflow-hidden",children:[r.jsx("div",{className:"absolute top-20 left-10 w-32 h-32 bg-[#262cbd]/10 dark:bg-[#1df292]/10 rounded-full blur-3xl"}),r.jsx("div",{className:"absolute bottom-20 right-10 w-40 h-40 bg-[#f21d6b]/10 dark:bg-[#f2d91d]/10 rounded-full blur-3xl"}),r.jsxs("div",{className:"max-w-4xl mx-auto px-4 relative z-10 flex flex-col items-center",children:[r.jsxs("div",{className:"text-center mb-12",children:[r.jsxs("div",{className:"inline-flex items-center bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm px-4 py-2 rounded-full mb-4 border border-design-primary/20",children:[r.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-4 w-4 text-design-primary mr-2",viewBox:"0 0 20 20",fill:"currentColor",children:r.jsx("path",{fillRule:"evenodd",d:"M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",clipRule:"evenodd"})}),r.jsx("span",{className:"text-design-primary font-medium text-xs",children:"Trusted Partners"})]}),r.jsx(a.h2,{initial:{opacity:0,y:20},whileInView:{opacity:1,y:0},transition:{duration:.5},viewport:{once:!0},className:"text-3xl md:text-[34px] font-normal text-design-foreground mb-4",children:s}),r.jsx("p",{className:"text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto mb-8",children:l})]}),r.jsx("div",{className:"grid grid-cols-2 sm:grid-cols-3 gap-6 md:gap-8 max-w-4xl mx-auto mb-12",children:d.slice(0,9).map(((e,a)=>r.jsxs("a",{href:`/brands/${e.slug||e.name.toLowerCase().replace(/\s+/g,"-")}`,className:"group flex flex-col items-center justify-center p-4 md:p-6 bg-design-card/50 dark:bg-design-card/30 backdrop-blur-sm rounded-xl border border-design-border hover:border-design-primary/40 hover:shadow-lg transition-all duration-300 hover:scale-[1.02]","aria-label":`View deals from ${e.name}`,children:[r.jsx("div",{className:"h-20 w-full flex items-center justify-center p-2 mb-3 transition-transform duration-300 group-hover:scale-105",children:e.logo_url?r.jsx("img",{src:e.logo_url,alt:e.name,className:"h-full w-full object-contain max-h-16",loading:"lazy",width:120,height:60}):r.jsx("div",{className:"w-16 h-16 flex items-center justify-center bg-design-primary/10 rounded-full",children:r.jsx("span",{className:"text-xl font-bold text-design-primary",children:e.name.charAt(0).toUpperCase()})})}),r.jsx("span",{className:"text-sm font-medium text-center text-design-foreground/90 group-hover:text-design-primary transition-colors duration-300",children:e.name})]},e.id||a)))}),r.jsx("div",{className:"text-center mt-8",children:r.jsxs("a",{href:i,className:"inline-flex items-center justify-center px-6 py-3 text-sm font-medium rounded-full transition-all duration-300\n              bg-gradient-to-b from-design-primary to-design-primary/90 text-white hover:text-white\n              shadow-glow hover:shadow-glow-light hover:animate-glow-border\n              hover:bg-animated-gradient bg-[length:400%_400%] hover:animate-gradient-move\n              dark:from-[#1df292] dark:to-[#0db875] dark:text-black dark:shadow-glow-dark\n              dark:hover:shadow-glow-dark dark:hover:bg-animated-gradient-dark",children:["View All Brand Partners",r.jsx("svg",{className:"ml-2 w-4 h-4",fill:"none",stroke:"currentColor",viewBox:"0 0 24 24",xmlns:"http://www.w3.org/2000/svg",children:r.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M9 5l7 7-7 7"})})]})})]})]})};export{t as SimplifiedBrandGrid};
