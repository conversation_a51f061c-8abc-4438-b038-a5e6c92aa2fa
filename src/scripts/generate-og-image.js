// <PERSON>ript to generate OG image from the template
import puppeteer from 'puppeteer';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

async function generateOGImage() {
  console.log('Generating OG image...');
  
  // Launch a headless browser
  const browser = await puppeteer.launch({
    headless: 'new',
    args: ['--no-sandbox', '--disable-setuid-sandbox']
  });
  
  try {
    const page = await browser.newPage();
    
    // Set viewport to OG image dimensions
    await page.setViewport({
      width: 1200,
      height: 630,
      deviceScaleFactor: 1
    });
    
    // Navigate to the template page
    console.log('Navigating to OG image template...');
    await page.goto('http://localhost:4321/og-image-template', {
      waitUntil: 'networkidle2'
    });
    
    // Wait for the page to be fully rendered
    await page.waitForSelector('.og-container', { visible: true });
    
    // Take a screenshot
    console.log('Taking screenshot...');
    const screenshotBuffer = await page.screenshot({
      type: 'jpeg',
      quality: 90
    });
    
    // Save the screenshot to the public directory
    const outputPath = path.resolve(__dirname, '../../public/og-image.jpg');
    fs.writeFileSync(outputPath, screenshotBuffer);
    
    console.log(`OG image saved to ${outputPath}`);
    
    // Also create a WebP version for better performance
    console.log('Creating WebP version...');
    const webpBuffer = await page.screenshot({
      type: 'webp',
      quality: 80
    });
    
    const webpOutputPath = path.resolve(__dirname, '../../public/og-image.webp');
    fs.writeFileSync(webpOutputPath, webpBuffer);
    
    console.log(`WebP version saved to ${webpOutputPath}`);
  } catch (error) {
    console.error('Error generating OG image:', error);
  } finally {
    await browser.close();
  }
}

// Run the function
generateOGImage().catch(console.error);
