import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$InfoPageLayout } from '../assets/js/InfoPageLayout-Bg861p5k.js';
import { $ as $$InfoPageHeader, a as $$InfoPageSection } from '../assets/js/InfoPageSection-CrV_q83G.js';
import { ShieldCheck, Search, ThumbsUp, TrendingUp } from 'lucide-react';
import { G as GlowingButton } from '../assets/js/app/design-system-CUssmfny.js';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$About = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$About;
  const title = "About Us | VapeHybrid";
  const description = "Learn more about VapeHybrid's mission, team, and commitment to providing the best vape deals.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "AboutPage",
    "name": title,
    "description": description,
    "publisher": {
      "@type": "Organization",
      "name": "VapeHybrid",
      "logo": {
        "@type": "ImageObject",
        "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`
      },
      "foundingDate": "2018-12-01",
      "email": "<EMAIL>",
      "employee": [
        {
          "@type": "Person",
          "name": "Geoffrey J. Koehler",
          "jobTitle": "Head of Operations",
          "image": `${Astro2.url.origin}/staff/Geoffrey_J._Koehler-removebg.webp`,
          "description": "Geoffrey brings over 10 years of experience in the vaping industry and leads our operations with a passion for helping smokers find healthier alternatives."
        },
        {
          "@type": "Person",
          "name": "Victoria F. Ingram",
          "jobTitle": "Head of Deal Curation",
          "image": `${Astro2.url.origin}/staff/Victoria_F._Ingram-removebg.webp`,
          "description": "Victoria leads our deal hunting team, ensuring that every coupon and offer on VapeHybrid is verified and provides genuine value to our users."
        },
        {
          "@type": "Person",
          "name": "Harvey B. Green",
          "jobTitle": "Product Specialist",
          "image": `${Astro2.url.origin}/staff/Harvey_B._Green-removebg-preview.webp`,
          "description": "With extensive knowledge of vaping devices and e-liquids, Harvey ensures we feature only the highest quality products from reputable brands."
        },
        {
          "@type": "Person",
          "name": "Cynthia S. Garcia",
          "jobTitle": "Community Manager",
          "image": `${Astro2.url.origin}/staff/Cynthia_S._Garcia-removebg.webp`,
          "description": "Cynthia builds and nurtures our community of vaping enthusiasts, ensuring that user feedback is incorporated into our platform improvements."
        },
        {
          "@type": "Person",
          "name": "Chyou Shen",
          "jobTitle": "Technology Lead",
          "image": `${Astro2.url.origin}/staff/Chyou_Shen-removebg.webp`,
          "description": "Chyou oversees our technical infrastructure, ensuring that our platform is fast, secure, and provides a seamless experience for all users."
        },
        {
          "@type": "Person",
          "name": "Dannielle E. Benn",
          "jobTitle": "Content Director",
          "image": `${Astro2.url.origin}/staff/Dannielle_E._Benn-removebg.webp`,
          "description": "Dannielle leads our content strategy, creating informative and engaging resources that help users make informed decisions about vaping products."
        }
      ]
    }
  };
  return renderTemplate`${renderComponent($$result, "InfoPageLayout", $$InfoPageLayout, { "title": title, "description": description, "structuredData": structuredData, "pattern": "dots" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "InfoPageHeader", $$InfoPageHeader, { "title": "About VapeHybrid", "subtitle": "Learn about our mission, team, and commitment to providing the best vape deals.", "pattern": "grid", "glass": true })}  ${maybeRenderHead()}<div class="flex justify-center items-center my-12"> <div class="relative w-40 h-40"> <!-- Light mode logo --> <img src="/Vapehybrid light icon.svg" alt="VapeHybrid Logo" class="w-full h-full absolute inset-0 block dark:hidden" width="160" height="160"> <!-- Dark mode logo --> <img src="/Vapehybrid dark icon.svg" alt="VapeHybrid Logo" class="w-full h-full absolute inset-0 hidden dark:block" width="160" height="160"> </div> </div> ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Our Mission", "centerTitle": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-3xl mx-auto text-center"> <p class="text-xl font-medium text-design-foreground mb-4">Helping vape users save money via vetted deals.</p> <p class="text-design-muted-foreground">At VapeHybrid, we're dedicated to finding and sharing the best deals on vaping products. We understand that vaping can be an expensive hobby, and we're here to make it more affordable for enthusiasts everywhere.</p> </div> ` })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "What Sets Us Apart", "centerTitle": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-4xl mx-auto"> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <div class="text-center"> <div class="flex justify-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full"> ${renderComponent($$result3, "ShieldCheck", ShieldCheck, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> </div> <h3 class="text-xl font-bold mb-2">Verified Deals</h3> <p class="text-design-muted-foreground">Every deal on our platform is manually verified by our team to ensure it's valid and offers genuine value.</p> </div> <div class="text-center"> <div class="flex justify-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full"> ${renderComponent($$result3, "Search", Search, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> </div> <h3 class="text-xl font-bold mb-2">Curated Selection</h3> <p class="text-design-muted-foreground">We carefully select deals from reputable merchants to ensure you get quality products at the best prices.</p> </div> <div class="text-center"> <div class="flex justify-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full"> ${renderComponent($$result3, "ThumbsUp", ThumbsUp, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> </div> <h3 class="text-xl font-bold mb-2">User-Focused</h3> <p class="text-design-muted-foreground">Our platform is designed with you in mind, making it easy to find and use the deals that matter to you.</p> </div> <div class="text-center"> <div class="flex justify-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full"> ${renderComponent($$result3, "TrendingUp", TrendingUp, { "className": "w-8 h-8 text-primary dark:text-design-secondary" })} </div> </div> <h3 class="text-xl font-bold mb-2">Always Up-to-Date</h3> <p class="text-design-muted-foreground">We constantly update our deals to ensure you never miss out on the latest savings opportunities.</p> </div> </div> </div> ` })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Our Team & Expertise", "centerTitle": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-3xl mx-auto text-center"> <p class="text-design-muted-foreground mb-4">VapeHybrid is powered by a team of vaping enthusiasts and deal-hunting experts. Our curators and reviewers have extensive knowledge of vaping products and the industry, allowing us to identify truly valuable deals.</p> <p class="text-design-muted-foreground">We combine our expertise with a passion for helping others save money, creating a platform that serves both newcomers and experienced vapers alike.</p> </div> ` })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Meet Our Team", "centerTitle": true, "background": "transparent", "pattern": "circles", "glass": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-5xl mx-auto"> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8"> <!-- Team Member 1 --> <div class="bg-white/70 dark:bg-black/80 backdrop-blur-md p-6 rounded-lg shadow-sm border border-design-border/10 hover:border-primary/20 transition-all duration-300 card-glow"> <div class="flex flex-col items-center text-center"> <div class="w-32 h-32 mb-4 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-design-secondary/20 p-1"> <img src="/staff/Geoffrey_J._Koehler-removebg.webp" alt="Geoffrey J. Koehler - Head of Operations" class="w-full h-full object-cover rounded-full" width="128" height="128" loading="lazy"> </div> <h3 class="text-xl font-bold mb-1 text-design-foreground">Geoffrey J. Koehler</h3> <p class="text-primary font-medium mb-3">Head of Operations</p> <p class="text-design-muted-foreground text-sm">Geoffrey brings over 10 years of experience in the vaping industry and leads our operations with a passion for helping smokers find healthier alternatives.</p> </div> </div> <!-- Team Member 2 --> <div class="bg-white/70 dark:bg-black/80 backdrop-blur-md p-6 rounded-lg shadow-sm border border-design-border/10 hover:border-primary/20 transition-all duration-300 card-glow"> <div class="flex flex-col items-center text-center"> <div class="w-32 h-32 mb-4 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-design-secondary/20 p-1"> <img src="/staff/Victoria_F._Ingram-removebg.webp" alt="Victoria F. Ingram - Head of Deal Curation" class="w-full h-full object-cover rounded-full" width="128" height="128" loading="lazy"> </div> <h3 class="text-xl font-bold mb-1 text-design-foreground">Victoria F. Ingram</h3> <p class="text-primary font-medium mb-3">Head of Deal Curation</p> <p class="text-design-muted-foreground text-sm">Victoria leads our deal hunting team, ensuring that every coupon and offer on VapeHybrid is verified and provides genuine value to our users.</p> </div> </div> <!-- Team Member 3 --> <div class="bg-white/70 dark:bg-black/80 backdrop-blur-md p-6 rounded-lg shadow-sm border border-design-border/10 hover:border-primary/20 transition-all duration-300 card-glow"> <div class="flex flex-col items-center text-center"> <div class="w-32 h-32 mb-4 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-design-secondary/20 p-1"> <img src="/staff/Harvey_B._Green-removebg-preview.webp" alt="Harvey B. Green - Product Specialist" class="w-full h-full object-cover rounded-full" width="128" height="128" loading="lazy"> </div> <h3 class="text-xl font-bold mb-1 text-design-foreground">Harvey B. Green</h3> <p class="text-primary font-medium mb-3">Product Specialist</p> <p class="text-design-muted-foreground text-sm">With extensive knowledge of vaping devices and e-liquids, Harvey ensures we feature only the highest quality products from reputable brands.</p> </div> </div> <!-- Team Member 4 --> <div class="bg-white/70 dark:bg-black/80 backdrop-blur-md p-6 rounded-lg shadow-sm border border-design-border/10 hover:border-primary/20 transition-all duration-300 card-glow"> <div class="flex flex-col items-center text-center"> <div class="w-32 h-32 mb-4 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-design-secondary/20 p-1"> <img src="/staff/Cynthia_S._Garcia-removebg.webp" alt="Cynthia S. Garcia - Community Manager" class="w-full h-full object-cover rounded-full" width="128" height="128" loading="lazy"> </div> <h3 class="text-xl font-bold mb-1 text-design-foreground">Cynthia S. Garcia</h3> <p class="text-primary font-medium mb-3">Community Manager</p> <p class="text-design-muted-foreground text-sm">Cynthia builds and nurtures our community of vaping enthusiasts, ensuring that user feedback is incorporated into our platform improvements.</p> </div> </div> <!-- Team Member 5 --> <div class="bg-white/70 dark:bg-black/80 backdrop-blur-md p-6 rounded-lg shadow-sm border border-design-border/10 hover:border-primary/20 transition-all duration-300 card-glow"> <div class="flex flex-col items-center text-center"> <div class="w-32 h-32 mb-4 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-design-secondary/20 p-1"> <img src="/staff/Chyou_Shen-removebg.webp" alt="Chyou Shen - Technology Lead" class="w-full h-full object-cover rounded-full" width="128" height="128" loading="lazy"> </div> <h3 class="text-xl font-bold mb-1 text-design-foreground">Chyou Shen</h3> <p class="text-primary font-medium mb-3">Technology Lead</p> <p class="text-design-muted-foreground text-sm">Chyou oversees our technical infrastructure, ensuring that our platform is fast, secure, and provides a seamless experience for all users.</p> </div> </div> <!-- Team Member 6 --> <div class="bg-white/70 dark:bg-black/80 backdrop-blur-md p-6 rounded-lg shadow-sm border border-design-border/10 hover:border-primary/20 transition-all duration-300 card-glow"> <div class="flex flex-col items-center text-center"> <div class="w-32 h-32 mb-4 rounded-full overflow-hidden bg-gradient-to-br from-primary/20 to-design-secondary/20 p-1"> <img src="/staff/Dannielle_E._Benn-removebg.webp" alt="Dannielle E. Benn - Content Director" class="w-full h-full object-cover rounded-full" width="128" height="128" loading="lazy"> </div> <h3 class="text-xl font-bold mb-1 text-design-foreground">Dannielle E. Benn</h3> <p class="text-primary font-medium mb-3">Content Director</p> <p class="text-design-muted-foreground text-sm">Dannielle leads our content strategy, creating informative and engaging resources that help users make informed decisions about vaping products.</p> </div> </div> </div> </div> ` })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Join Our Team", "centerTitle": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-3xl mx-auto text-center"> <p class="text-design-muted-foreground mb-6">We're always looking for passionate individuals who share our mission of helping vapers save money and find quality products. If you're enthusiastic about vaping and want to make a difference, we'd love to hear from you.</p> ${renderComponent($$result3, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/contact", "className": "text-base", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": ($$result4) => renderTemplate`
Get in Touch
<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg> ` })} </div> ` })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Our Curation Process", "centerTitle": true, "background": "transparent", "pattern": "dots", "glass": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-4xl mx-auto"> <p class="text-design-muted-foreground mb-8 text-center max-w-3xl mx-auto">We take deal verification seriously to ensure you never waste time on expired or invalid offers.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-10"> <div class="bg-design-card/80 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="relative z-10"> <div class="flex items-center mb-4"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-3 rounded-full mr-4"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path><path d="m9 12 2 2 4-4"></path></svg> </div> <h3 class="text-xl font-bold text-design-foreground">Manual Verification</h3> </div> <p class="text-design-muted-foreground pl-14">Our team personally tests each coupon code before it's published, ensuring it works at checkout.</p> </div> </div> <div class="bg-design-card/80 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="relative z-10"> <div class="flex items-center mb-4"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-3 rounded-full mr-4"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><rect width="20" height="14" x="2" y="3" rx="2"></rect><line x1="8" x2="16" y1="21" y2="21"></line><line x1="12" x2="12" y1="17" y2="21"></line><path d="M2 7h20"></path><path d="M16 14h.01"></path><path d="M12 14h.01"></path><path d="M8 14h.01"></path></svg> </div> <h3 class="text-xl font-bold text-design-foreground">Automated Monitoring</h3> </div> <p class="text-design-muted-foreground pl-14">We use technology to continuously check deal validity and expiration dates in real-time.</p> </div> </div> <div class="bg-design-card/80 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="relative z-10"> <div class="flex items-center mb-4"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-3 rounded-full mr-4"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><path d="M17 18a2 2 0 0 0-2-2H9a2 2 0 0 0-2 2"></path><rect width="18" height="18" x="3" y="4" rx="2"></rect><circle cx="12" cy="10" r="2"></circle><line x1="8" x2="8" y1="2" y2="4"></line><line x1="16" x2="16" y1="2" y2="4"></line></svg> </div> <h3 class="text-xl font-bold text-design-foreground">User Feedback</h3> </div> <p class="text-design-muted-foreground pl-14">We incorporate user reports to quickly identify and remove expired or problematic deals.</p> </div> </div> <div class="bg-design-card/80 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="relative z-10"> <div class="flex items-center mb-4"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-3 rounded-full mr-4"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg> </div> <h3 class="text-xl font-bold text-design-foreground">Merchant Relationships</h3> </div> <p class="text-design-muted-foreground pl-14">We work directly with brands and retailers to get exclusive deals for our users.</p> </div> </div> </div> <div class="bg-primary/10 dark:bg-primary/20 border border-primary/20 dark:border-primary/30 rounded-lg p-6 flex items-center justify-center max-w-xl mx-auto"> <div class="flex items-center gap-4"> ${renderComponent($$result3, "ShieldCheck", ShieldCheck, { "className": "w-12 h-12 text-primary dark:text-design-secondary" })} <div> <p class="text-xl font-medium text-design-foreground">Verified daily by our team</p> <p class="text-sm text-design-muted-foreground">We update our deals database every 24 hours</p> </div> </div> </div> </div> ` })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Our Story & Milestones", "centerTitle": true, "background": "transparent", "pattern": "hexagon", "glass": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-4xl mx-auto"> <p class="text-design-muted-foreground mb-10 text-center max-w-3xl mx-auto">
VapeHybrid was founded in 2018 with a simple goal: to create a reliable platform where vapers could find genuine savings on their favorite products. From our humble beginnings, we've grown into a comprehensive deals platform serving thousands of users worldwide.
</p> <div class="relative">  <div class="absolute left-1/2 transform -translate-x-1/2 h-full w-0.5 bg-transparent"></div>  <div class="relative z-10 mb-16">  <div class="absolute left-1/2 transform -translate-x-1/2 top-16 h-[calc(100%+4rem)] w-1 overflow-hidden"> <div class="w-full h-full bg-primary dark:bg-design-secondary opacity-70 relative"> <div class="absolute top-0 left-0 right-0 h-40 animate-pulse-slow bg-gradient-to-b from-primary via-primary to-transparent dark:from-design-secondary dark:via-design-secondary dark:to-transparent"></div> </div> </div> <div class="flex flex-col items-center">  <div class="flex items-center justify-center mb-8"> <div class="w-20 h-20 bg-white/80 dark:bg-black/80 backdrop-blur-md rounded-full border-2 border-primary dark:border-design-secondary flex items-center justify-center z-10 shadow-glow-light dark:shadow-glow-dark"> <span class="text-2xl font-bold text-primary dark:text-design-secondary">2018</span> </div> </div>  <div class="w-full max-w-2xl"> <div class="bg-white/70 dark:bg-black/70 backdrop-blur-md p-6 rounded-2xl shadow-sm border border-design-border/10 hover:border-primary/30 dark:hover:border-design-secondary/30 transition-all duration-300"> <h3 class="text-xl font-bold text-design-foreground mb-3">Platform Launch</h3> <p class="text-design-muted-foreground">
VapeHybrid was launched with a focus on curating the best vape deals for our community. We started with just 50 deals from 10 merchants, but our vision was clear: to become the most trusted source for vape deals online.
</p> </div> </div> </div> </div>  <div class="relative z-10 mb-16">  <div class="absolute left-1/2 transform -translate-x-1/2 top-16 h-[calc(100%+4rem)] w-1 overflow-hidden"> <div class="w-full h-full bg-primary dark:bg-design-secondary opacity-70 relative"> <div class="absolute top-0 left-0 right-0 h-40 animate-pulse-slow bg-gradient-to-b from-primary via-primary to-transparent dark:from-design-secondary dark:via-design-secondary dark:to-transparent"></div> </div> </div> <div class="flex flex-col items-center">  <div class="flex items-center justify-center mb-8"> <div class="w-20 h-20 bg-white/80 dark:bg-black/80 backdrop-blur-md rounded-full border-2 border-primary dark:border-design-secondary flex items-center justify-center z-10 shadow-glow-light dark:shadow-glow-dark"> <span class="text-2xl font-bold text-primary dark:text-design-secondary">2019</span> </div> </div>  <div class="w-full max-w-2xl"> <div class="bg-white/70 dark:bg-black/70 backdrop-blur-md p-6 rounded-2xl shadow-sm border border-design-border/10 hover:border-primary/30 dark:hover:border-design-secondary/30 transition-all duration-300"> <h3 class="text-xl font-bold text-design-foreground mb-3">Key Partnerships</h3> <p class="text-design-muted-foreground">
We established partnerships with major vape brands and retailers, expanding our deal offerings to over 200 active deals and introducing exclusive coupon codes for our users. This was a pivotal year for building our network of trusted merchants.
</p> </div> </div> </div> </div>  <div class="relative z-10 mb-16">  <div class="absolute left-1/2 transform -translate-x-1/2 top-16 h-[calc(100%+4rem)] w-1 overflow-hidden"> <div class="w-full h-full bg-primary dark:bg-design-secondary opacity-70 relative"> <div class="absolute top-0 left-0 right-0 h-40 animate-pulse-slow bg-gradient-to-b from-primary via-primary to-transparent dark:from-design-secondary dark:via-design-secondary dark:to-transparent"></div> </div> </div> <div class="flex flex-col items-center">  <div class="flex items-center justify-center mb-8"> <div class="w-20 h-20 bg-white/80 dark:bg-black/80 backdrop-blur-md rounded-full border-2 border-primary dark:border-design-secondary flex items-center justify-center z-10 shadow-glow-light dark:shadow-glow-dark"> <span class="text-2xl font-bold text-primary dark:text-design-secondary">2020</span> </div> </div>  <div class="w-full max-w-2xl"> <div class="bg-white/70 dark:bg-black/70 backdrop-blur-md p-6 rounded-2xl shadow-sm border border-design-border/10 hover:border-primary/30 dark:hover:border-design-secondary/30 transition-all duration-300"> <h3 class="text-xl font-bold text-design-foreground mb-3">Verification System</h3> <p class="text-design-muted-foreground">
Introduced our automated deal verification system to ensure up-to-date and valid offers. This innovation reduced invalid coupon reports by 87% and significantly improved user satisfaction. Our commitment to quality and reliability became our hallmark.
</p> </div> </div> </div> </div>  <div class="relative z-10 mb-16">  <div class="absolute left-1/2 transform -translate-x-1/2 top-16 h-[calc(100%+4rem)] w-1 overflow-hidden"> <div class="w-full h-full bg-primary dark:bg-design-secondary opacity-70 relative"> <div class="absolute top-0 left-0 right-0 h-40 animate-pulse-slow bg-gradient-to-b from-primary via-primary to-transparent dark:from-design-secondary dark:via-design-secondary dark:to-transparent"></div> </div> </div> <div class="flex flex-col items-center">  <div class="flex items-center justify-center mb-8"> <div class="w-20 h-20 bg-white/80 dark:bg-black/80 backdrop-blur-md rounded-full border-2 border-primary dark:border-design-secondary flex items-center justify-center z-10 shadow-glow-light dark:shadow-glow-dark"> <span class="text-2xl font-bold text-primary dark:text-design-secondary">2021</span> </div> </div>  <div class="w-full max-w-2xl"> <div class="bg-white/70 dark:bg-black/70 backdrop-blur-md p-6 rounded-2xl shadow-sm border border-design-border/10 hover:border-primary/30 dark:hover:border-design-secondary/30 transition-all duration-300"> <h3 class="text-xl font-bold text-design-foreground mb-3">Community Growth</h3> <p class="text-design-muted-foreground">
Our user community reached 10,000 members, and we launched our newsletter service to keep users informed about the latest deals and industry news. The VapeHybrid community became a valuable resource for vapers seeking advice and recommendations.
</p> </div> </div> </div> </div>  <div class="relative z-10 mb-16">  <div class="absolute left-1/2 transform -translate-x-1/2 top-16 h-[calc(100%+4rem)] w-1 overflow-hidden"> <div class="w-full h-full bg-primary dark:bg-design-secondary opacity-70 relative"> <div class="absolute top-0 left-0 right-0 h-40 animate-pulse-slow bg-gradient-to-b from-primary via-primary to-transparent dark:from-design-secondary dark:via-design-secondary dark:to-transparent"></div> </div> </div> <div class="flex flex-col items-center">  <div class="flex items-center justify-center mb-8"> <div class="w-20 h-20 bg-white/80 dark:bg-black/80 backdrop-blur-md rounded-full border-2 border-primary dark:border-design-secondary flex items-center justify-center z-10 shadow-glow-light dark:shadow-glow-dark"> <span class="text-2xl font-bold text-primary dark:text-design-secondary">2022</span> </div> </div>  <div class="w-full max-w-2xl"> <div class="bg-white/70 dark:bg-black/70 backdrop-blur-md p-6 rounded-2xl shadow-sm border border-design-border/10 hover:border-primary/30 dark:hover:border-design-secondary/30 transition-all duration-300"> <h3 class="text-xl font-bold text-design-foreground mb-3">Responsive Redesign</h3> <p class="text-design-muted-foreground">
Implemented a fully responsive design, making it easier for users to access deals on mobile devices. Mobile traffic increased by 65% following this update, and our user experience ratings improved significantly across all platforms.
</p> </div> </div> </div> </div>  <div class="relative z-10 mb-16">  <div class="absolute left-1/2 transform -translate-x-1/2 top-16 h-[calc(100%+4rem)] w-1 overflow-hidden"> <div class="w-full h-full bg-primary dark:bg-design-secondary opacity-70 relative"> <div class="absolute top-0 left-0 right-0 h-40 animate-pulse-slow bg-gradient-to-b from-primary via-primary to-transparent dark:from-design-secondary dark:via-design-secondary dark:to-transparent"></div> </div> </div> <div class="flex flex-col items-center">  <div class="flex items-center justify-center mb-8"> <div class="w-20 h-20 bg-white/80 dark:bg-black/80 backdrop-blur-md rounded-full border-2 border-primary dark:border-design-secondary flex items-center justify-center z-10 shadow-glow-light dark:shadow-glow-dark"> <span class="text-2xl font-bold text-primary dark:text-design-secondary">2023</span> </div> </div>  <div class="w-full max-w-2xl"> <div class="bg-white/70 dark:bg-black/70 backdrop-blur-md p-6 rounded-2xl shadow-sm border border-design-border/10 hover:border-primary/30 dark:hover:border-design-secondary/30 transition-all duration-300"> <h3 class="text-xl font-bold text-design-foreground mb-3">Educational Content</h3> <p class="text-design-muted-foreground">
Expanded our platform with comprehensive educational resources, helping newcomers navigate the vaping world with confidence and knowledge. Our guides and tutorials became trusted resources for both beginners and experienced vapers alike.
</p> </div> </div> </div> </div>  <div class="relative z-10"> <div class="flex flex-col items-center">  <div class="flex items-center justify-center mb-8"> <div class="w-20 h-20 bg-white/80 dark:bg-black/80 backdrop-blur-md rounded-full border-2 border-primary dark:border-design-secondary flex items-center justify-center z-10 shadow-glow-light dark:shadow-glow-dark"> <span class="text-2xl font-bold text-primary dark:text-design-secondary">2024</span> </div> </div>  <div class="w-full max-w-2xl"> <div class="bg-gradient-to-br from-primary/5 to-design-secondary/5 dark:from-primary/10 dark:to-design-secondary/10 backdrop-blur-md p-6 rounded-2xl shadow-sm border border-primary/20 dark:border-design-secondary/30 transition-all duration-300"> <h3 class="text-xl font-bold text-design-foreground mb-3">Global Expansion</h3> <p class="text-design-muted-foreground">
Expanded our platform with enhanced features, improved user experience, and a growing international presence serving users across 25+ countries. As we continue to grow, our commitment to providing the best vape deals remains unwavering.
</p> </div> </div> </div> </div> </div> <div class="mt-16 text-center"> <p class="text-design-muted-foreground italic max-w-2xl mx-auto">
As we look to the future, we remain committed to our mission of helping vapers find the best deals while providing education and support for those transitioning from smoking.
</p> <div class="mt-8"> ${renderComponent($$result3, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/contact", "className": "text-base", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": ($$result4) => renderTemplate`
Join Our Journey
<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg> ` })} </div> </div> </div> ` })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Our Vision and Commitment", "centerTitle": true, "background": "transparent", "pattern": "wave", "glass": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-4xl mx-auto"> <p class="text-design-muted-foreground mb-8 text-center max-w-3xl mx-auto">At VapeHybrid, our mission extends beyond just providing deals. We're committed to making a positive impact in the vaping community.</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-8 mb-12"> <div class="bg-design-card/80 backdrop-blur-sm p-8 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="relative"> <h3 class="text-2xl font-bold text-design-foreground mb-6 flex items-center"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary mr-3"><path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path></svg>
Our Mission
</h3> <ul class="space-y-4"> <li class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <p class="text-design-muted-foreground">Helping people transition away from traditional smoking to healthier alternatives</p> </li> <li class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <p class="text-design-muted-foreground">Advocating for vaping as a potentially life-saving option for smokers</p> </li> <li class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <p class="text-design-muted-foreground">Educating the public with comprehensive information about vaping products</p> </li> <li class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <p class="text-design-muted-foreground">Empowering consumers to make informed decisions about their vaping journey</p> </li> </ul> </div> </div> <div class="bg-design-card/80 backdrop-blur-sm p-8 rounded-lg shadow-sm border border-design-border/20 transition-all duration-300 hover:shadow-md hover:border-design-primary/20"> <div class="relative"> <h3 class="text-2xl font-bold text-design-foreground mb-6 flex items-center"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary mr-3"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10"></path></svg>
Our Commitment
</h3> <ul class="space-y-4"> <li class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <p class="text-design-muted-foreground">Providing extensive, accessible information about vaping products and their benefits</p> </li> <li class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <p class="text-design-muted-foreground">Offering in-depth product reviews to guide users in selecting the best vaping devices</p> </li> <li class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <p class="text-design-muted-foreground">Curating deals and coupons to make quality vaping products more affordable</p> </li> <li class="flex items-start"> <div class="bg-design-primary/10 dark:bg-design-primary/20 p-1 rounded-full mr-3 mt-1"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-design-primary"><polyline points="20 6 9 17 4 12"></polyline></svg> </div> <p class="text-design-muted-foreground">Building a supportive community for those transitioning from smoking to vaping</p> </li> </ul> </div> </div> </div> <div class="text-center"> <p class="text-design-muted-foreground italic">Since recognizing the potential of vaping to help smokers quit in 2015, we've been dedicated to making vaping more accessible, affordable, and understandable for everyone.</p> </div> </div> ` })} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": "Contact Us", "centerTitle": true }, { "default": ($$result3) => renderTemplate` <div class="max-w-3xl mx-auto text-center"> <p class="text-design-muted-foreground">We'd love to hear from you! If you have questions, feedback, or suggestions, please <a href="/contact" class="text-design-primary hover:underline">contact us</a>.</p> </div> ` })} ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/about.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/about.astro";
const $$url = "/about";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$About,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
