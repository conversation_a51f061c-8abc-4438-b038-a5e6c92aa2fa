/**
 * Color utility functions for working with design tokens
 */

/**
 * Converts a hex color to RGB
 * @param {string} hex - Hex color code (with or without #)
 * @returns {Object} RGB color object with r, g, b properties (0-255)
 */
function hexToRgb(hex) {
  // Remove the # if present
  hex = hex.replace('#', '');

  // Parse the hex values
  const r = parseInt(hex.substring(0, 2), 16);
  const g = parseInt(hex.substring(2, 4), 16);
  const b = parseInt(hex.substring(4, 6), 16);

  return { r, g, b };
}

/**
 * Converts a hex color to HSL
 * @param {string} hex - Hex color code (with or without #)
 * @returns {string} HSL color string in format "h s% l%"
 */
function hexToHSL(hex) {
  // Remove the # if present
  hex = hex.replace('#', '');

  // Convert hex to RGB
  let r = parseInt(hex.substring(0, 2), 16) / 255;
  let g = parseInt(hex.substring(2, 4), 16) / 255;
  let b = parseInt(hex.substring(4, 6), 16) / 255;

  // Find the maximum and minimum values to calculate the lightness
  const max = Math.max(r, g, b);
  const min = Math.min(r, g, b);

  // Calculate the lightness
  let l = (max + min) / 2;

  let h = 0;
  let s = 0;

  if (max !== min) {
    // Calculate the saturation
    s = l > 0.5 ? (max - min) / (2 - max - min) : (max - min) / (max + min);

    // Calculate the hue
    if (max === r) {
      h = (g - b) / (max - min) + (g < b ? 6 : 0);
    } else if (max === g) {
      h = (b - r) / (max - min) + 2;
    } else {
      h = (r - g) / (max - min) + 4;
    }

    h = Math.round(h * 60);
  }

  // Convert saturation and lightness to percentages
  s = Math.round(s * 100);
  l = Math.round(l * 100);

  return `${h} ${s}% ${l}%`;
}

/**
 * Converts an RGB color to its relative luminance
 * Based on WCAG 2.0 formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html
 * @param {Object} rgb - RGB color object with r, g, b properties (0-255)
 * @returns {number} Relative luminance (0-1)
 */
function rgbToLuminance({ r, g, b }) {
  // Convert RGB to sRGB
  const sR = r / 255;
  const sG = g / 255;
  const sB = b / 255;

  // Convert sRGB to linear RGB
  const R = sR <= 0.03928 ? sR / 12.92 : Math.pow((sR + 0.055) / 1.055, 2.4);
  const G = sG <= 0.03928 ? sG / 12.92 : Math.pow((sG + 0.055) / 1.055, 2.4);
  const B = sB <= 0.03928 ? sB / 12.92 : Math.pow((sB + 0.055) / 1.055, 2.4);

  // Calculate luminance
  return 0.2126 * R + 0.7152 * G + 0.0722 * B;
}

/**
 * Calculates the contrast ratio between two colors
 * Based on WCAG 2.0 formula: https://www.w3.org/TR/WCAG20-TECHS/G17.html
 * @param {string} color1 - First color in hex format
 * @param {string} color2 - Second color in hex format
 * @returns {number} Contrast ratio (1-21)
 */
function calculateContrastRatio(color1, color2) {
  // Convert colors to RGB
  const rgb1 = hexToRgb(color1);
  const rgb2 = hexToRgb(color2);

  // Calculate luminance
  const luminance1 = rgbToLuminance(rgb1);
  const luminance2 = rgbToLuminance(rgb2);

  // Calculate contrast ratio
  const lighter = Math.max(luminance1, luminance2);
  const darker = Math.min(luminance1, luminance2);

  return (lighter + 0.05) / (darker + 0.05);
}

/**
 * Adjusts a color's lightness to meet a target contrast ratio with another color
 * @param {string} baseColor - Base color in hex format
 * @param {string} contrastColor - Color to contrast with in hex format
 * @param {number} targetRatio - Target contrast ratio (typically 4.5 for AA, 7 for AAA)
 * @returns {string} Suggested color in hex format
 */
function suggestColorWithBetterContrast(baseColor, contrastColor, targetRatio) {
  // Convert colors to RGB
  const baseRgb = hexToRgb(baseColor);
  const contrastRgb = hexToRgb(contrastColor);

  // Calculate current luminance
  const baseLuminance = rgbToLuminance(baseRgb);
  const contrastLuminance = rgbToLuminance(contrastRgb);

  // Determine if we need to lighten or darken
  const shouldLighten = baseLuminance < contrastLuminance;

  // Start with the base color's HSL
  let hsl = hexToHSL(baseColor);
  let [h, s, l] = hsl.split(' ').map(v => parseInt(v));
  l = parseInt(l.toString().replace('%', ''));
  s = parseInt(s.toString().replace('%', ''));

  // Adjust lightness until we meet the target ratio
  const step = shouldLighten ? 5 : -5;
  let newL = l;
  let iterations = 0;
  const maxIterations = 20; // Prevent infinite loops

  while (iterations < maxIterations) {
    newL = Math.max(0, Math.min(100, newL + step));

    // Convert back to hex
    const newHex = hslToHex(h, s, newL);
    const newRatio = calculateContrastRatio(newHex, contrastColor);

    if ((shouldLighten && newRatio >= targetRatio) ||
        (!shouldLighten && newRatio >= targetRatio) ||
        newL === 0 || newL === 100) {
      return newHex;
    }

    iterations++;
  }

  // If we couldn't find a good match, suggest a safe fallback
  return shouldLighten ? '#ffffff' : '#000000';
}

/**
 * Converts HSL values to a hex color
 * @param {number} h - Hue (0-360)
 * @param {number} s - Saturation (0-100)
 * @param {number} l - Lightness (0-100)
 * @returns {string} Hex color code
 */
function hslToHex(h, s, l) {
  s /= 100;
  l /= 100;

  const c = (1 - Math.abs(2 * l - 1)) * s;
  const x = c * (1 - Math.abs((h / 60) % 2 - 1));
  const m = l - c / 2;

  let r, g, b;

  if (h >= 0 && h < 60) {
    [r, g, b] = [c, x, 0];
  } else if (h >= 60 && h < 120) {
    [r, g, b] = [x, c, 0];
  } else if (h >= 120 && h < 180) {
    [r, g, b] = [0, c, x];
  } else if (h >= 180 && h < 240) {
    [r, g, b] = [0, x, c];
  } else if (h >= 240 && h < 300) {
    [r, g, b] = [x, 0, c];
  } else {
    [r, g, b] = [c, 0, x];
  }

  r = Math.round((r + m) * 255).toString(16).padStart(2, '0');
  g = Math.round((g + m) * 255).toString(16).padStart(2, '0');
  b = Math.round((b + m) * 255).toString(16).padStart(2, '0');

  return `#${r}${g}${b}`;
}

export {
  hexToRgb,
  hexToHSL,
  rgbToLuminance,
  calculateContrastRatio,
  suggestColorWithBetterContrast,
  hslToHex
};
