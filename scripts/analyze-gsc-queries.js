/**
 * GSC Query Analysis Script
 *
 * This script analyzes Google Search Console queries to identify content gaps
 * and opportunities for new pages. It reads CSV files and creates recommendations.
 */

import fs from 'fs';
import path from 'path';

// Function to read and parse CSV files
function parseCSV(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf-8');
    const lines = content.split('\n').filter(line => line.trim());
    const headers = lines[0].split(',');

    return lines.slice(1).map(line => {
      const values = line.split(',');
      const obj = {};
      headers.forEach((header, index) => {
        obj[header.trim()] = values[index]?.trim() || '';
      });
      return obj;
    });
  } catch (error) {
    console.error(`Error reading CSV file ${filePath}:`, error.message);
    return [];
  }
}

// Read GSC data from CSV files
function loadGSCData() {
  const gscDataDir = path.join(process.cwd(), 'docs', 'performance', 'vapehybrid.com-Performance-on-Search-2025-06-08');
  const queriesPath = path.join(gscDataDir, 'Queries.csv');
  const pagesPath = path.join(gscDataDir, 'Pages.csv');

  console.log(`📂 Looking for GSC data in: ${gscDataDir}`);

  const queries = parseCSV(queriesPath).map(row => ({
    query: row['Top queries'] || '',
    clicks: parseInt(row['Clicks']) || 0,
    impressions: parseInt(row['Impressions']) || 0,
    ctr: row['CTR'] || '0%',
    position: parseFloat(row['Position']) || 0
  })).filter(q => q.query.trim() !== ''); // Filter out empty queries

  const pages = parseCSV(pagesPath).map(row => ({
    url: row['Top pages'] || '',
    clicks: parseInt(row['Clicks']) || 0,
    impressions: parseInt(row['Impressions']) || 0,
    ctr: row['CTR'] || '0%',
    position: parseFloat(row['Position']) || 0
  })).filter(p => p.url.trim() !== ''); // Filter out empty URLs

  return { queries, pages };
}

// Mock database data for analysis (we'll enhance this later with real DB connection)
function getMockDatabaseData() {
  return {
    brands: [
      { name: 'Geek Bar', slug: 'geek-bar' },
      { name: 'Lost Mary', slug: 'lost-mary' },
      { name: 'Voopoo', slug: 'voopoo' },
      { name: 'Vaporesso', slug: 'vaporesso' },
      { name: 'Kado Bar', slug: 'kado-bar' },
      { name: 'Flum', slug: 'flum' },
      { name: 'Kylinbar', slug: 'kylinbar' },
      { name: 'Airmez', slug: 'airmez' },
      { name: 'Olit', slug: 'olit' },
      { name: 'Ijoy', slug: 'ijoy' },
      { name: 'Vozol', slug: 'vozol' },
      { name: 'Pyne', slug: 'pyne' },
      { name: 'Wynn', slug: 'wynn' },
      { name: 'Ria', slug: 'ria' },
      { name: 'Viscore', slug: 'viscore' },
      { name: 'Bounce', slug: 'bounce' },
      { name: 'Artery', slug: 'artery' },
      { name: 'Fumi', slug: 'fumi' },
      { name: 'Off Stamp', slug: 'off-stamp' },
      { name: 'MTRX', slug: 'mtrx' },
      { name: 'Foger', slug: 'foger' },
      { name: 'Spaceman', slug: 'spaceman' },
      { name: 'Pillow Talk', slug: 'pillow-talk' },
      { name: 'Oxbar', slug: 'oxbar' },
      { name: 'Ploox', slug: 'ploox' },
      { name: 'Starx', slug: 'starx' },
      { name: 'Dead Rabbit', slug: 'dead-rabbit' },
      { name: 'Taijizen', slug: 'taijizen' },
      { name: 'Fzzy', slug: 'fzzy' },
      { name: 'VRK', slug: 'vrk' }
    ],
    categories: [
      { name: 'Disposable Vapes', slug: 'disposable-vapes' },
      { name: 'Box Mods', slug: 'box-mods' },
      { name: 'Pod Systems', slug: 'pod-systems' },
      { name: 'Vape Kits', slug: 'vape-kits' },
      { name: 'E-Juice', slug: 'e-juice' },
      { name: 'Coils', slug: 'coils' },
      { name: 'Accessories', slug: 'accessories' },
      { name: 'Sale', slug: 'sale' },
      { name: 'Giveaway', slug: 'giveaway' }
    ],
    merchants: [
      { name: 'EightVape', slug: 'eightvape' },
      { name: 'VapeDeal', slug: 'vapedeal' },
      { name: 'EJuice Connect', slug: 'ejuice-connect' },
      { name: 'VaporDNA', slug: 'vapordna' },
      { name: 'VapeSourcing', slug: 'vapesourcing' },
      { name: 'DirectVapor', slug: 'directvapor' },
      { name: 'Sweet Vapes', slug: 'sweet-vapes' },
      { name: 'VapeMood', slug: 'vapemood' },
      { name: 'Local Vape', slug: 'local-vape' }
    ],
    deals: [] // We'll populate this with current page analysis
  };
}

function categorizeSearchIntent(query) {
  const lowerQuery = query.toLowerCase();
  
  // Coupon/Deal intent keywords
  const couponKeywords = ['coupon', 'code', 'discount', 'promo', 'deal', 'sale', 'offer'];
  const isCouponIntent = couponKeywords.some(keyword => lowerQuery.includes(keyword));
  
  // Product/Brand intent keywords
  const productKeywords = ['disposable', 'vape', 'mod', 'kit', 'juice', 'pod'];
  const isProductIntent = productKeywords.some(keyword => lowerQuery.includes(keyword));
  
  // Brand-specific queries
  const brandKeywords = ['geek bar', 'lost mary', 'voopoo', 'vaporesso', 'kado bar', 'flum', 'kylinbar'];
  const isBrandIntent = brandKeywords.some(keyword => lowerQuery.includes(keyword));
  
  // Competitor intent
  const competitorKeywords = ['vapedeal', 'eightvape', 'ejuice connect', 'vapesourcing'];
  const isCompetitorIntent = competitorKeywords.some(keyword => lowerQuery.includes(keyword));
  
  if (isCompetitorIntent) return 'competitor';
  if (isCouponIntent) return 'coupon';
  if (isBrandIntent) return 'brand';
  if (isProductIntent) return 'product';
  
  return 'informational';
}

function findMatchingContent(query, databaseData, existingPages) {
  const { brands, categories, merchants } = databaseData;
  const lowerQuery = query.toLowerCase();

  const matches = {
    brands: [],
    categories: [],
    merchants: [],
    existingPages: [],
    exactMatches: [],
    partialMatches: []
  };

  // Check brands
  brands?.forEach(brand => {
    if (lowerQuery.includes(brand.name.toLowerCase()) ||
        (brand.slug && lowerQuery.includes(brand.slug.toLowerCase()))) {
      matches.brands.push(brand);
      matches.exactMatches.push({ type: 'brand', item: brand });
    }
  });

  // Check categories
  categories?.forEach(category => {
    if (lowerQuery.includes(category.name.toLowerCase()) ||
        (category.slug && lowerQuery.includes(category.slug.toLowerCase()))) {
      matches.categories.push(category);
      matches.exactMatches.push({ type: 'category', item: category });
    }
  });

  // Check merchants
  merchants?.forEach(merchant => {
    if (lowerQuery.includes(merchant.name.toLowerCase())) {
      matches.merchants.push(merchant);
      matches.exactMatches.push({ type: 'merchant', item: merchant });
    }
  });

  // Check existing pages from GSC data
  existingPages?.forEach(page => {
    const url = page.url.toLowerCase();
    if (url.includes('coupon') || url.includes('deal') || url.includes('discount')) {
      // Extract relevant parts from URL for matching
      const urlParts = url.split('/');
      const relevantParts = urlParts.filter(part =>
        part.length > 2 && !['https:', '', 'vapehybrid.com', 'www'].includes(part)
      );

      if (relevantParts.some(part => lowerQuery.includes(part) || part.includes(lowerQuery.split(' ')[0]))) {
        matches.existingPages.push(page);
        matches.exactMatches.push({ type: 'page', item: page });
      }
    }
  });

  return matches;
}

function generateRecommendations(query, intent, matches, impressions, position) {
  const recommendations = [];
  
  // High-priority recommendations based on impressions and intent
  if (impressions >= 10 && intent === 'coupon') {
    if (matches.exactMatches.length === 0) {
      recommendations.push({
        priority: 'HIGH',
        action: 'CREATE_COUPON_PAGE',
        description: `Create dedicated coupon page for "${query}" - ${impressions} impressions, no matching content`,
        suggestedUrl: `/coupons/${query.replace(/\s+/g, '-').toLowerCase()}`,
        reason: 'High search volume with coupon intent but no matching content'
      });
    }
  }
  
  if (intent === 'competitor' && impressions >= 5) {
    recommendations.push({
      priority: 'HIGH',
      action: 'CREATE_COMPETITOR_ALTERNATIVE',
      description: `Create alternative page targeting "${query}" users`,
      suggestedUrl: `/alternatives/${query.replace(/\s+/g, '-').toLowerCase()}`,
      reason: 'Competitor search with decent volume - opportunity to capture users'
    });
  }
  
  if (intent === 'brand' && matches.brands.length > 0) {
    recommendations.push({
      priority: 'MEDIUM',
      action: 'OPTIMIZE_BRAND_PAGE',
      description: `Optimize existing brand page for "${query}"`,
      suggestedUrl: `/coupons/brands/${matches.brands[0].slug}`,
      reason: 'Brand match found but may need SEO optimization'
    });
  }
  
  return recommendations;
}

function analyzeQueries() {
  console.log('🚀 Starting GSC Query Analysis...\n');

  // Load GSC data from CSV files
  const { queries: gscQueries, pages: gscPages } = loadGSCData();
  const databaseData = getMockDatabaseData();

  console.log(`📊 Loaded ${gscQueries.length} queries and ${gscPages.length} pages from GSC data\n`);

  const analysis = {
    totalQueries: gscQueries.length,
    totalPages: gscPages.length,
    byIntent: {},
    recommendations: [],
    contentGaps: [],
    opportunities: [],
    existingContent: []
  };

  console.log('📊 Analyzing queries...\n');

  gscQueries.forEach((queryData, index) => {
    const { query, impressions, position } = queryData;
    if (!query || query.trim() === '') return; // Skip empty queries

    const intent = categorizeSearchIntent(query);
    const matches = findMatchingContent(query, databaseData, gscPages);
    const recommendations = generateRecommendations(query, intent, matches, impressions, position);

    // Track by intent
    if (!analysis.byIntent[intent]) {
      analysis.byIntent[intent] = [];
    }
    analysis.byIntent[intent].push({
      query,
      impressions,
      position,
      matches: matches.exactMatches.length,
      hasContent: matches.exactMatches.length > 0,
      matchTypes: matches.exactMatches.map(m => m.type)
    });

    // Add recommendations
    analysis.recommendations.push(...recommendations);

    // Identify content gaps
    if (matches.exactMatches.length === 0 && impressions >= 2) {
      analysis.contentGaps.push({
        query,
        intent,
        impressions,
        position,
        priority: impressions >= 10 ? 'HIGH' : impressions >= 5 ? 'MEDIUM' : 'LOW'
      });
    }

    // Track existing content that's performing
    if (matches.exactMatches.length > 0) {
      analysis.existingContent.push({
        query,
        intent,
        impressions,
        position,
        matches: matches.exactMatches
      });
    }

    console.log(`${index + 1}. "${query}" (${impressions} imp, pos ${position})`);
    console.log(`   Intent: ${intent}`);
    console.log(`   Matches: ${matches.exactMatches.length} (${matches.exactMatches.map(m => m.type).join(', ')})`);
    if (recommendations.length > 0) {
      console.log(`   Recommendations: ${recommendations.length}`);
    }
    console.log('');
  });

  return analysis;
}

function generateReport(analysis) {
  console.log('📝 Generating comprehensive report...\n');

  // Calculate totals from analysis data
  const totalImpressions = Object.values(analysis.byIntent)
    .flat()
    .reduce((sum, item) => sum + item.impressions, 0);

  const allQueries = Object.values(analysis.byIntent).flat();
  const averagePosition = allQueries.length > 0
    ? allQueries.reduce((sum, item) => sum + item.position, 0) / allQueries.length
    : 0;

  const report = {
    summary: {
      totalQueries: analysis.totalQueries,
      totalPages: analysis.totalPages,
      totalImpressions,
      averagePosition: Math.round(averagePosition * 100) / 100,
      contentGaps: analysis.contentGaps.length,
      highPriorityGaps: analysis.contentGaps.filter(gap => gap.priority === 'HIGH').length,
      mediumPriorityGaps: analysis.contentGaps.filter(gap => gap.priority === 'MEDIUM').length,
      existingContentMatches: analysis.existingContent.length
    },
    intentBreakdown: analysis.byIntent,
    contentGaps: analysis.contentGaps.sort((a, b) => b.impressions - a.impressions),
    recommendations: analysis.recommendations,
    existingContent: analysis.existingContent,
    actionPlan: []
  };

  // Generate action plan
  const highPriorityActions = analysis.recommendations.filter(r => r.priority === 'HIGH');
  const mediumPriorityActions = analysis.recommendations.filter(r => r.priority === 'MEDIUM');

  report.actionPlan = [
    {
      phase: 'Phase 1 - High Priority Content Gaps',
      description: 'Address queries with highest search volume and clear coupon intent',
      actions: highPriorityActions.slice(0, 10)
    },
    {
      phase: 'Phase 2 - Medium Priority Opportunities',
      description: 'Optimize existing content and create targeted landing pages',
      actions: mediumPriorityActions.slice(0, 15)
    },
    {
      phase: 'Phase 3 - Long-tail Optimization',
      description: 'Address remaining content gaps and optimize for specific product queries',
      actions: analysis.contentGaps
        .filter(gap => gap.priority === 'LOW' && gap.impressions >= 2)
        .slice(0, 20)
        .map(gap => ({
          priority: 'LOW',
          action: 'CREATE_TARGETED_CONTENT',
          description: `Create content for "${gap.query}"`,
          suggestedUrl: `/coupons/${gap.query.replace(/\s+/g, '-').toLowerCase()}`,
          reason: `${gap.impressions} impressions, position ${gap.position}`
        }))
    }
  ];

  return report;
}

// Main execution
function main() {
  try {
    const analysis = analyzeQueries();
    const report = generateReport(analysis);

    // Create documents folder if it doesn't exist
    const documentsDir = path.join(process.cwd(), 'documents');
    if (!fs.existsSync(documentsDir)) {
      fs.mkdirSync(documentsDir, { recursive: true });
    }

    // Save detailed JSON report
    const jsonPath = path.join(documentsDir, 'gsc-query-analysis.json');
    fs.writeFileSync(jsonPath, JSON.stringify(report, null, 2));

    // Save markdown report for easy reading
    const markdownReport = generateMarkdownReport(report);
    const mdPath = path.join(documentsDir, 'gsc-query-analysis.md');
    fs.writeFileSync(mdPath, markdownReport);

    console.log(`✅ Analysis complete! Reports saved:`);
    console.log(`   📄 JSON: ${jsonPath}`);
    console.log(`   📝 Markdown: ${mdPath}`);
    console.log('\n📊 Summary:');
    console.log(`   Total Queries: ${report.summary.totalQueries}`);
    console.log(`   Total Impressions: ${report.summary.totalImpressions}`);
    console.log(`   Average Position: ${report.summary.averagePosition}`);
    console.log(`   Content Gaps: ${report.summary.contentGaps}`);
    console.log(`   High Priority Gaps: ${report.summary.highPriorityGaps}`);
    console.log(`   Medium Priority Gaps: ${report.summary.mediumPriorityGaps}`);
    console.log(`   Existing Content Matches: ${report.summary.existingContentMatches}`);

  } catch (error) {
    console.error('❌ Analysis failed:', error.message);
    console.error(error.stack);
  }
}

function generateMarkdownReport(report) {
  return `# GSC Query Analysis Report

## Executive Summary

- **Total Queries Analyzed**: ${report.summary.totalQueries}
- **Total Impressions**: ${report.summary.totalImpressions}
- **Average Position**: ${report.summary.averagePosition}
- **Content Gaps Identified**: ${report.summary.contentGaps}
- **High Priority Gaps**: ${report.summary.highPriorityGaps}
- **Medium Priority Gaps**: ${report.summary.mediumPriorityGaps}
- **Existing Content Matches**: ${report.summary.existingContentMatches}

## Search Intent Breakdown

${Object.entries(report.intentBreakdown).map(([intent, queries]) => `
### ${intent.toUpperCase()} Intent (${queries.length} queries)

${queries.slice(0, 10).map(q => `- "${q.query}" - ${q.impressions} impressions, position ${q.position}, ${q.matches} matches`).join('\n')}
${queries.length > 10 ? `\n... and ${queries.length - 10} more queries` : ''}
`).join('\n')}

## High Priority Content Gaps

${report.contentGaps.filter(gap => gap.priority === 'HIGH').map(gap => `
### "${gap.query}"
- **Impressions**: ${gap.impressions}
- **Position**: ${gap.position}
- **Intent**: ${gap.intent}
- **Priority**: ${gap.priority}
`).join('\n')}

## Action Plan

${report.actionPlan.map(phase => `
### ${phase.phase}
${phase.description ? `*${phase.description}*` : ''}

${phase.actions.slice(0, 10).map((action, index) => `
${index + 1}. **${action.action}**: ${action.description}
   - Priority: ${action.priority}
   - Suggested URL: ${action.suggestedUrl || 'TBD'}
   - Reason: ${action.reason}
`).join('\n')}
${phase.actions.length > 10 ? `\n... and ${phase.actions.length - 10} more actions` : ''}
`).join('\n')}

## Recommendations

1. **Immediate Actions** (High Priority):
   - Create coupon landing pages for high-volume queries with no existing content
   - Optimize existing brand and category pages for better keyword targeting
   - Implement competitor alternative pages to capture competitor searches

2. **Medium-term Actions**:
   - Expand product-specific coupon pages
   - Create seasonal and event-based coupon pages
   - Improve internal linking between related coupon pages

3. **Long-term Strategy**:
   - Monitor and analyze new query patterns
   - Continuously optimize existing content based on performance
   - Expand into related product categories and brands

---
*Report generated on ${new Date().toISOString()}*
`;
}

// Run the analysis
main();
