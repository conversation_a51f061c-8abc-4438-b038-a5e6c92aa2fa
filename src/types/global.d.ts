/**
 * Global TypeScript definitions
 */

declare global {
  // Add requestIdleCallback and cancelIdleCallback to Window interface
  interface Window {
    requestIdleCallback: (
      callback: (deadline: {
        didTimeout: boolean;
        timeRemaining: () => number;
      }) => void,
      options?: { timeout: number }
    ) => number;
    cancelIdleCallback: (handle: number) => void;
    gtag?: (command: string, action: string, params?: Record<string, any>) => void;
    dataLayer?: any[];
  }

  // Global gtag function
  function gtag(command: string, action: string, params?: Record<string, any>): void;

  // Browser detection
  const isBrowser: boolean;
}

// Extend existing interfaces
declare module '*.astro' {
  const Component: any;
  export default Component;
}

// Vitest globals
declare module 'vitest' {
  export const vi: any;
  export const describe: any;
  export const it: any;
  export const expect: any;
  export const beforeEach: any;
  export const afterEach: any;
}

export {};
