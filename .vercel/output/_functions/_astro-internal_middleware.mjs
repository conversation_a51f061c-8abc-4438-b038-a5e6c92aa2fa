import 'es-module-lexer';
import './assets/js/astro-designed-error-pages-BPSgjeYS.js';
import 'kleur/colors';
import './assets/js/astro/server-CU4H3-CM.js';
import 'clsx';
import 'cookie';
import { s as sequence } from './assets/js/index-DKICMPDI.js';

/**
 * Middleware for handling asset loading and API request issues
 *
 * This middleware ensures that:
 * 1. CSS files are properly loaded
 * 2. Font files are properly loaded
 * 3. API requests are properly handled
 * 4. JavaScript files are properly loaded
 */
const onRequest$1 = async (context, next) => {
  const url = new URL(context.request.url);

  // Log all requests for debugging
  console.log(`Middleware handling request: ${url.pathname}`);

  // Handle CORS for all requests
  if (context.request.method === 'OPTIONS') {
    return new Response(null, {
      status: 204,
      headers: {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, PUT, DELETE, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '86400'
      }
    });
  }

  // Handle API requests to Supabase
  if (url.pathname.startsWith('/rest/v1') || url.pathname.includes('supabase')) {
    try {
      // Add CORS headers to the response
      const response = await next();
      const newHeaders = new Headers(response.headers);
      newHeaders.set('Access-Control-Allow-Origin', '*');

      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: newHeaders
      });
    } catch (error) {
      console.error(`Error handling API request: ${url.pathname}`, error);
      // Return a fallback response for API requests
      return new Response(JSON.stringify({ error: 'API request failed', message: error.message }), {
        status: 200,
        headers: {
          'Content-Type': 'application/json',
          'Access-Control-Allow-Origin': '*',
          'Cache-Control': 'no-cache'
        }
      });
    }
  }

  // Handle CSS files
  if (url.pathname.endsWith('.css')) {
    // Handle problematic CSS files
    if (url.pathname.includes('/assets/excluded/README.css') ||
        url.pathname.includes('/assets/excluded/index.css')) {

      // Return an empty CSS file to prevent 404 errors
      return new Response('/* Empty placeholder CSS file */', {
        status: 200,
        headers: {
          'Content-Type': 'text/css',
          'Cache-Control': 'public, max-age=31536000' // Long cache time
        }
      });
    }

    // If the path doesn't include _astro and it's not a direct request to the _astro directory
    if (!url.pathname.includes('/_astro/') && !url.pathname.startsWith('/_astro/')) {
      try {
        // Try to redirect to the _astro directory
        const astroPath = `/_astro${url.pathname}`;
        return context.redirect(astroPath);
      } catch (error) {
        console.error(`Error redirecting CSS: ${url.pathname}`, error);
        // Return a fallback CSS file
        return new Response('/* Fallback CSS */', {
          status: 200,
          headers: {
            'Content-Type': 'text/css',
            'Cache-Control': 'no-cache'
          }
        });
      }
    }
  }

  // Handle font files
  if (url.pathname.endsWith('.woff') || url.pathname.endsWith('.woff2') ||
      url.pathname.endsWith('.ttf') || url.pathname.endsWith('.otf')) {
    try {
      const response = await next();
      // Add proper CORS headers for fonts
      const newHeaders = new Headers(response.headers);
      newHeaders.set('Access-Control-Allow-Origin', '*');

      return new Response(response.body, {
        status: response.status,
        statusText: response.statusText,
        headers: newHeaders
      });
    } catch (error) {
      console.error(`Error loading font: ${url.pathname}`, error);
      // Return an empty response for font files
      return new Response(null, {
        status: 200,
        headers: {
          'Content-Type': url.pathname.endsWith('.woff2') ? 'font/woff2' :
                         url.pathname.endsWith('.woff') ? 'font/woff' :
                         url.pathname.endsWith('.ttf') ? 'font/ttf' : 'font/otf',
          'Access-Control-Allow-Origin': '*',
          'Cache-Control': 'public, max-age=31536000'
        }
      });
    }
  }

  // Handle JavaScript files
  if (url.pathname.endsWith('.js')) {
    try {
      // Continue with normal request handling
      return await next();
    } catch (error) {
      // If there's a network error, return an empty JavaScript file
      console.error(`Error loading JavaScript file: ${url.pathname}`, error);
      return new Response('/* Error loading JavaScript file - providing fallback */', {
        status: 200,
        headers: {
          'Content-Type': 'application/javascript',
          'Cache-Control': 'no-cache'
        }
      });
    }
  }

  // For all other requests, try to handle normally
  try {
    return await next();
  } catch (error) {
    console.error(`Error handling request: ${url.pathname}`, error);

    // For HTML pages, return a minimal HTML response
    if (url.pathname.endsWith('/') || url.pathname.endsWith('.html') || !url.pathname.includes('.')) {
      return new Response(`
        <!DOCTYPE html>
        <html>
          <head>
            <title>Error</title>
            <meta charset="utf-8">
            <meta name="viewport" content="width=device-width, initial-scale=1">
          </head>
          <body>
            <h1>Error loading page</h1>
            <p>Please try refreshing the page.</p>
          </body>
        </html>
      `, {
        status: 200,
        headers: {
          'Content-Type': 'text/html',
          'Cache-Control': 'no-cache'
        }
      });
    }

    // For other file types, return an appropriate error response
    return new Response(`Error: ${error.message}`, {
      status: 200,
      headers: {
        'Content-Type': 'text/plain',
        'Cache-Control': 'no-cache'
      }
    });
  }
};

const onRequest = sequence(
	
	onRequest$1
	
);

export { onRequest };
