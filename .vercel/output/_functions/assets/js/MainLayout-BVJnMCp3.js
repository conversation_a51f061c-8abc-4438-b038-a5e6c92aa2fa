import { a as createComponent, d as renderComponent, r as renderTemplate, F as Fragment, m as maybeRenderHead, c as createAstro, b as addAttribute, s as spreadAttributes, u as unescapeHTML, i as renderHead, e as renderSlot } from './astro/server-CU4H3-CM.js';
import 'kleur/colors';
/* empty css                        */
import { T as ToastProvider, o as Navbar } from './app/design-system-CUssmfny.js';
import { jsxs, Fragment as Fragment$1, jsx } from 'react/jsx-runtime';
import 'react';
import 'clsx';
import 'sonner';
import { R as ReactProviders, B as BackToTop, E as ExitIntentPopup, F as FooterNewsletter } from './FooterNewsletter-CELCN2dO.js';

var __freeze$1 = Object.freeze;
var __defProp$1 = Object.defineProperty;
var __template$1 = (cooked, raw) => __freeze$1(__defProp$1(cooked, "raw", { value: __freeze$1(cooked.slice()) }));
var _a;
const $$FontLoader = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "Fragment", Fragment, {}, { "default": ($$result2) => renderTemplate(_a || (_a = __template$1([`<link rel="preconnect" href="https://fonts.googleapis.com" crossorigin><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@400;500;600;700&display=swap" onload="this.onload=null;this.rel='stylesheet'"><link rel="preload" as="style" href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Text:wght@400;500;600;700&display=swap" onload="this.onload=null;this.rel='stylesheet'"><link rel="stylesheet" href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@800&display=swap" media="print" onload="this.media='all'" fetchpriority="low">`, `<noscript><link href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@400;500;600;700&display=swap" rel="stylesheet"><link href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Text:wght@400;500;600;700&display=swap" rel="stylesheet"></noscript><style>
    /* System font stack for initial render */
    :root {
      --font-sans: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      --font-display: 'Wix Madefor Display', var(--font-sans);
      --font-text: 'Wix Madefor Text', var(--font-sans);
    }
    
    /* Base font settings */
    html {
      font-family: var(--font-sans);
      -webkit-font-smoothing: antialiased;
      -moz-osx-font-smoothing: grayscale;
      text-rendering: optimizeLegibility;
    }

    /* Font classes with optimized fallbacks */
    .font-sans {
      font-family: var(--font-display);
      font-display: swap;
    }

    .font-body {
      font-family: 'Wix Madefor Text', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif;
      font-display: swap;
    }

    .font-mono {
      font-family: ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, 'Liberation Mono', 'Courier New', monospace;
      font-display: swap;
    }

    /* Add specific font-weight classes for better control */
    .font-normal { font-weight: 400; }
    .font-medium { font-weight: 500; }
    .font-semibold { font-weight: 600; }
    .font-bold { font-weight: 700; }
    .font-extrabold { font-weight: 800; }
  </style><script>
    // Font loading optimization
    if ("fonts" in document) {
      // Check if the variable font is already loaded
      if (sessionStorage.fontsLoaded) {
        document.documentElement.classList.add('fonts-loaded');
      } else {
        // Use requestAnimationFrame to defer off main thread
        requestAnimationFrame(() => {
          // Use the Font Loading API to load fonts
          Promise.all([
            document.fonts.load('400 1em "Wix Madefor Display"'),
            document.fonts.load('700 1em "Wix Madefor Display"'),
            document.fonts.load('400 1em "Wix Madefor Text"'),
            document.fonts.load('700 1em "Wix Madefor Text"')
          ]).then(() => {
            document.documentElement.classList.add('fonts-loaded');
            // Save in session storage so we don't repeat this on page navigation
            sessionStorage.fontsLoaded = true;
          }).catch(err => {
            console.warn('Font loading failed:', err);
            // Add the class anyway to ensure fallback fonts are used
            document.documentElement.classList.add('fonts-loaded');
            sessionStorage.fontsLoaded = true;
          });
        });
      }
    }
  <\/script><noscript><link href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Display:wght@400;500;600;700;800&display=swap" rel="stylesheet"><link href="https://fonts.googleapis.com/css2?family=Wix+Madefor+Text:wght@400;500;600;700&display=swap" rel="stylesheet"></noscript>`])), maybeRenderHead()) })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/FontLoader.astro", void 0);

const $$Astro$8 = createAstro("http://localhost:4321");
const $$OpenGraphArticleTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$8, $$props, $$slots);
  Astro2.self = $$OpenGraphArticleTags;
  const { publishedTime, modifiedTime, expirationTime, authors, section, tags } = Astro2.props.openGraph.article;
  return renderTemplate`${publishedTime ? renderTemplate`<meta property="article:published_time"${addAttribute(publishedTime, "content")}>` : null}${modifiedTime ? renderTemplate`<meta property="article:modified_time"${addAttribute(modifiedTime, "content")}>` : null}${expirationTime ? renderTemplate`<meta property="article:expiration_time"${addAttribute(expirationTime, "content")}>` : null}${authors ? authors.map((author) => renderTemplate`<meta property="article:author"${addAttribute(author, "content")}>`) : null}${section ? renderTemplate`<meta property="article:section"${addAttribute(section, "content")}>` : null}${tags ? tags.map((tag) => renderTemplate`<meta property="article:tag"${addAttribute(tag, "content")}>`) : null}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro-seo/src/components/OpenGraphArticleTags.astro", void 0);

const $$Astro$7 = createAstro("http://localhost:4321");
const $$OpenGraphBasicTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$7, $$props, $$slots);
  Astro2.self = $$OpenGraphBasicTags;
  const { openGraph } = Astro2.props;
  return renderTemplate`<meta property="og:title"${addAttribute(openGraph.basic.title, "content")}><meta property="og:type"${addAttribute(openGraph.basic.type, "content")}><meta property="og:image"${addAttribute(openGraph.basic.image, "content")}><meta property="og:url"${addAttribute(openGraph.basic.url || Astro2.url.href, "content")}>`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro-seo/src/components/OpenGraphBasicTags.astro", void 0);

const $$Astro$6 = createAstro("http://localhost:4321");
const $$OpenGraphImageTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$6, $$props, $$slots);
  Astro2.self = $$OpenGraphImageTags;
  const { image } = Astro2.props.openGraph.basic;
  const { secureUrl, type, width, height, alt } = Astro2.props.openGraph.image;
  return renderTemplate`<meta property="og:image:url"${addAttribute(image, "content")}>${secureUrl ? renderTemplate`<meta property="og:image:secure_url"${addAttribute(secureUrl, "content")}>` : null}${type ? renderTemplate`<meta property="og:image:type"${addAttribute(type, "content")}>` : null}${width ? renderTemplate`<meta property="og:image:width"${addAttribute(width, "content")}>` : null}${height ? renderTemplate`<meta property="og:image:height"${addAttribute(height, "content")}>` : null}${alt ? renderTemplate`<meta property="og:image:alt"${addAttribute(alt, "content")}>` : null}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro-seo/src/components/OpenGraphImageTags.astro", void 0);

const $$Astro$5 = createAstro("http://localhost:4321");
const $$OpenGraphOptionalTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$5, $$props, $$slots);
  Astro2.self = $$OpenGraphOptionalTags;
  const { optional } = Astro2.props.openGraph;
  return renderTemplate`${optional.audio ? renderTemplate`<meta property="og:audio"${addAttribute(optional.audio, "content")}>` : null}${optional.description ? renderTemplate`<meta property="og:description"${addAttribute(optional.description, "content")}>` : null}${optional.determiner ? renderTemplate`<meta property="og:determiner"${addAttribute(optional.determiner, "content")}>` : null}${optional.locale ? renderTemplate`<meta property="og:locale"${addAttribute(optional.locale, "content")}>` : null}${optional.localeAlternate?.map((locale) => renderTemplate`<meta property="og:locale:alternate"${addAttribute(locale, "content")}>`)}${optional.siteName ? renderTemplate`<meta property="og:site_name"${addAttribute(optional.siteName, "content")}>` : null}${optional.video ? renderTemplate`<meta property="og:video"${addAttribute(optional.video, "content")}>` : null}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro-seo/src/components/OpenGraphOptionalTags.astro", void 0);

const $$Astro$4 = createAstro("http://localhost:4321");
const $$ExtendedTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$4, $$props, $$slots);
  Astro2.self = $$ExtendedTags;
  const { props } = Astro2;
  return renderTemplate`${props.extend.link?.map((attributes) => renderTemplate`<link${spreadAttributes(attributes)}>`)}${props.extend.meta?.map(({ content, httpEquiv, media, name, property }) => renderTemplate`<meta${addAttribute(name, "name")}${addAttribute(property, "property")}${addAttribute(content, "content")}${addAttribute(httpEquiv, "http-equiv")}${addAttribute(media, "media")}>`)}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro-seo/src/components/ExtendedTags.astro", void 0);

const $$Astro$3 = createAstro("http://localhost:4321");
const $$TwitterTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$3, $$props, $$slots);
  Astro2.self = $$TwitterTags;
  const { card, site, title, creator, description, image, imageAlt } = Astro2.props.twitter;
  return renderTemplate`${card ? renderTemplate`<meta name="twitter:card"${addAttribute(card, "content")}>` : null}${site ? renderTemplate`<meta name="twitter:site"${addAttribute(site, "content")}>` : null}${title ? renderTemplate`<meta name="twitter:title"${addAttribute(title, "content")}>` : null}${image ? renderTemplate`<meta name="twitter:image"${addAttribute(image, "content")}>` : null}${imageAlt ? renderTemplate`<meta name="twitter:image:alt"${addAttribute(imageAlt, "content")}>` : null}${description ? renderTemplate`<meta name="twitter:description"${addAttribute(description, "content")}>` : null}${creator ? renderTemplate`<meta name="twitter:creator"${addAttribute(creator, "content")}>` : null}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro-seo/src/components/TwitterTags.astro", void 0);

const $$Astro$2 = createAstro("http://localhost:4321");
const $$LanguageAlternatesTags = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$LanguageAlternatesTags;
  const { languageAlternates } = Astro2.props;
  return renderTemplate`${languageAlternates.map((alternate) => renderTemplate`<link rel="alternate"${addAttribute(alternate.hrefLang, "hreflang")}${addAttribute(alternate.href, "href")}>`)}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro-seo/src/components/LanguageAlternatesTags.astro", void 0);

const $$Astro$1 = createAstro("http://localhost:4321");
const $$SEO = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$SEO;
  Astro2.props.surpressWarnings = true;
  function validateProps(props) {
    if (props.openGraph) {
      if (!props.openGraph.basic || (props.openGraph.basic.title ?? void 0) == void 0 || (props.openGraph.basic.type ?? void 0) == void 0 || (props.openGraph.basic.image ?? void 0) == void 0) {
        throw new Error(
          "If you pass the openGraph prop, you have to at least define the title, type, and image basic properties!"
        );
      }
    }
    if (props.title && props.openGraph?.basic.title) {
      if (props.title == props.openGraph.basic.title && !props.surpressWarnings) {
        console.warn(
          "WARNING(astro-seo): You passed the same value to `title` and `openGraph.optional.title`. This is most likely not what you want. See docs for more."
        );
      }
    }
    if (props.openGraph?.basic?.image && !props.openGraph?.image?.alt && !props.surpressWarnings) {
      console.warn(
        "WARNING(astro-seo): You defined `openGraph.basic.image`, but didn't define `openGraph.image.alt`. This is strongly discouraged.'"
      );
    }
  }
  validateProps(Astro2.props);
  let updatedTitle = "";
  if (Astro2.props.title) {
    updatedTitle = Astro2.props.title;
    if (Astro2.props.titleTemplate) {
      updatedTitle = Astro2.props.titleTemplate.replace(/%s/g, updatedTitle);
    }
  } else if (Astro2.props.titleDefault) {
    updatedTitle = Astro2.props.titleDefault;
  }
  const baseUrl = Astro2.site ?? Astro2.url;
  const defaultCanonicalUrl = new URL(Astro2.url.pathname + Astro2.url.search, baseUrl);
  return renderTemplate`${updatedTitle ? renderTemplate`<title>${unescapeHTML(updatedTitle)}</title>` : null}${Astro2.props.charset ? renderTemplate`<meta${addAttribute(Astro2.props.charset, "charset")}>` : null}<link rel="canonical"${addAttribute(Astro2.props.canonical || defaultCanonicalUrl.href, "href")}>${Astro2.props.description ? renderTemplate`<meta name="description"${addAttribute(Astro2.props.description, "content")}>` : null}<meta name="robots"${addAttribute(`${Astro2.props.noindex ? "noindex" : "index"}, ${Astro2.props.nofollow ? "nofollow" : "follow"}`, "content")}>${Astro2.props.openGraph && renderTemplate`${renderComponent($$result, "OpenGraphBasicTags", $$OpenGraphBasicTags, { ...Astro2.props })}`}${Astro2.props.openGraph?.optional && renderTemplate`${renderComponent($$result, "OpenGraphOptionalTags", $$OpenGraphOptionalTags, { ...Astro2.props })}`}${Astro2.props.openGraph?.image && renderTemplate`${renderComponent($$result, "OpenGraphImageTags", $$OpenGraphImageTags, { ...Astro2.props })}`}${Astro2.props.openGraph?.article && renderTemplate`${renderComponent($$result, "OpenGraphArticleTags", $$OpenGraphArticleTags, { ...Astro2.props })}`}${Astro2.props.twitter && renderTemplate`${renderComponent($$result, "TwitterTags", $$TwitterTags, { ...Astro2.props })}`}${Astro2.props.extend && renderTemplate`${renderComponent($$result, "ExtendedTags", $$ExtendedTags, { ...Astro2.props })}`}${Astro2.props.languageAlternates && renderTemplate`${renderComponent($$result, "LanguageAlternatesTags", $$LanguageAlternatesTags, { ...Astro2.props })}`}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro-seo/src/SEO.astro", void 0);

function NavigationStructuredData({
  currentPath = "/",
  siteName = "VapeHybrid",
  baseUrl = "https://vapehybrid.com"
}) {
  const navigationItems = [
    { name: "Home", url: `${baseUrl}/`, position: 1 },
    { name: "Coupons", url: `${baseUrl}/coupons`, position: 2 },
    { name: "Brands", url: `${baseUrl}/coupons/brands`, position: 3 },
    { name: "Merchants", url: `${baseUrl}/merchants`, position: 4 },
    { name: "Categories", url: `${baseUrl}/coupons/categories`, position: 5 },
    { name: "Flash Deals", url: `${baseUrl}/flash-deals`, position: 6 }
  ];
  const generateBreadcrumbs = (path) => {
    const breadcrumbs2 = [
      { name: "Home", url: `${baseUrl}/`, position: 1 }
    ];
    if (path === "/") return breadcrumbs2;
    const pathSegments = path.split("/").filter(Boolean);
    let currentUrl = baseUrl;
    pathSegments.forEach((segment, index) => {
      currentUrl += `/${segment}`;
      let name = segment.charAt(0).toUpperCase() + segment.slice(1);
      switch (segment) {
        case "flash-deals":
          name = "Flash Deals";
          break;
        case "how-it-works":
          name = "How It Works";
          break;
        default:
          name = segment.split("-").map(
            (word) => word.charAt(0).toUpperCase() + word.slice(1)
          ).join(" ");
      }
      breadcrumbs2.push({
        name,
        url: currentUrl,
        position: index + 2
      });
    });
    return breadcrumbs2;
  };
  const breadcrumbs = generateBreadcrumbs(currentPath);
  const siteNavigationSchema = {
    "@context": "https://schema.org",
    "@type": "SiteNavigationElement",
    "name": `${siteName} Navigation`,
    "url": baseUrl,
    "hasPart": navigationItems.map((item) => ({
      "@type": "WebPage",
      "name": item.name,
      "url": item.url,
      "position": item.position
    }))
  };
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((item) => ({
      "@type": "ListItem",
      "position": item.position,
      "name": item.name,
      "item": item.url
    }))
  };
  const websiteSchema = {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": siteName,
    "url": baseUrl,
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/search?q={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "mainEntity": {
      "@type": "Organization",
      "name": siteName,
      "url": baseUrl,
      "sameAs": [
        "https://twitter.com/vapehybrid",
        "https://facebook.com/vapehybrid"
      ]
    }
  };
  const organizationSchema = {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": siteName,
    "url": baseUrl,
    "logo": `${baseUrl}/logo-light.svg`,
    "description": "Find the best vape deals and coupons all in one place",
    "contactPoint": {
      "@type": "ContactPoint",
      "contactType": "customer service",
      "url": `${baseUrl}/contact`
    },
    "sameAs": [
      "https://twitter.com/vapehybrid",
      "https://facebook.com/vapehybrid"
    ]
  };
  return /* @__PURE__ */ jsxs(Fragment$1, { children: [
    /* @__PURE__ */ jsx(
      "script",
      {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
          __html: JSON.stringify(siteNavigationSchema)
        }
      }
    ),
    breadcrumbs.length > 1 && /* @__PURE__ */ jsx(
      "script",
      {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
          __html: JSON.stringify(breadcrumbSchema)
        }
      }
    ),
    /* @__PURE__ */ jsx(
      "script",
      {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
          __html: JSON.stringify(websiteSchema)
        }
      }
    ),
    /* @__PURE__ */ jsx(
      "script",
      {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
          __html: JSON.stringify(organizationSchema)
        }
      }
    )
  ] });
}
function generateCanonicalUrl(currentPath, baseUrl = "https://vapehybrid.com", searchParams) {
  let canonicalUrl = `${baseUrl}${currentPath}`;
  if (canonicalUrl !== `${baseUrl}/` && canonicalUrl.endsWith("/")) {
    canonicalUrl = canonicalUrl.slice(0, -1);
  }
  if (currentPath === "/search" && searchParams?.get("q")) {
    canonicalUrl += `?q=${encodeURIComponent(searchParams.get("q"))}`;
  }
  if (searchParams?.get("page") && searchParams.get("page") !== "1") {
    const separator = canonicalUrl.includes("?") ? "&" : "?";
    canonicalUrl += `${separator}page=${searchParams.get("page")}`;
  }
  return canonicalUrl;
}
function generateSocialMetaTags(title, description, canonicalUrl, imageUrl, siteName = "VapeHybrid") {
  const defaultImage = "https://vapehybrid.com/og-image.svg";
  return {
    // Open Graph
    "og:title": title,
    "og:description": description,
    "og:url": canonicalUrl,
    "og:type": "website",
    "og:site_name": siteName,
    "og:image": imageUrl || defaultImage,
    "og:image:width": "1200",
    "og:image:height": "630",
    "og:image:alt": `${siteName} - ${title}`,
    // Twitter Card
    "twitter:card": "summary_large_image",
    "twitter:site": "@vapehybrid",
    "twitter:creator": "@vapehybrid",
    "twitter:title": title,
    "twitter:description": description,
    "twitter:image": imageUrl || defaultImage,
    "twitter:image:alt": `${siteName} - ${title}`,
    // Additional meta tags
    "canonical": canonicalUrl,
    "robots": "index, follow, max-snippet:-1, max-image-preview:large, max-video-preview:-1",
    "viewport": "width=device-width, initial-scale=1",
    "theme-color": "#3b82f6"
    // Primary blue color
  };
}

var __freeze = Object.freeze;
var __defProp = Object.defineProperty;
var __template = (cooked, raw) => __freeze(__defProp(cooked, "raw", { value: __freeze(cooked.slice()) }));
var _b, _c;
const $$Astro = createAstro("http://localhost:4321");
const $$MainLayout = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$MainLayout;
  const {
    title = "VapeHybrid Coupons",
    description = "Find the best vape deals and coupons",
    structuredData
  } = Astro2.props;
  const currentPath = Astro2.url.pathname;
  const searchParams = Astro2.url.searchParams;
  const canonical = generateCanonicalUrl(currentPath, "https://vapehybrid.com", searchParams);
  generateSocialMetaTags(
    title,
    description,
    canonical,
    `${Astro2.url.origin}/og-image.svg`,
    "VapeHybrid"
  );
  const darkModePreference = Astro2.cookies.get("darkMode")?.value === "true" || false;
  return renderTemplate(_c || (_c = __template(['<html lang="en"', '> <head><meta charset="utf-8"><meta name="viewport" content="width=device-width, initial-scale=1.0"><meta name="theme-color" content="#ffffff"><link rel="icon" type="image/svg+xml" href="/favicon.svg"><!-- Preload critical resources --><!-- Removed preload for FaviconLogo.svg to fix console warnings --><!-- Inline critical CSS --><style>{criticalCSS}</style><!-- Load fonts early -->', "<!-- SEO Component -->", '<!-- Preconnect to critical third-party domains --><link rel="preconnect" href="https://fonts.gstatic.com" crossorigin><link rel="preconnect" href="https://fonts.googleapis.com" crossorigin><link rel="preconnect" href="https://fonts.cdnfonts.com" crossorigin><link rel="preconnect" href="https://zlnvivfgzgcjuspktadj.supabase.co" crossorigin><link rel="preconnect" href="https://static.shareasale.com" crossorigin><link rel="preconnect" href="https://res.cloudinary.com" crossorigin><link rel="preconnect" href="https://cdn.jsdelivr.net" crossorigin><link rel="preconnect" href="https://www.google.com" crossorigin><link rel="preconnect" href="https://www.gstatic.com" crossorigin><!-- Google reCAPTCHA v3 - Only load on contact page -->', '<!-- Preload critical navigation assets --><link rel="preload" href="/logo-light.svg" as="image" type="image/svg+xml"><link rel="preload" href="/logo-dark.svg" as="image" type="image/svg+xml"><link rel="preload" href="/Vapehybrid light icon.svg" as="image" type="image/svg+xml"><link rel="preload" href="/Vapehybrid dark icon.svg" as="image" type="image/svg+xml">', "<!-- Enhanced Navigation Structured Data -->", "<!-- Optimized Font Loading -->", `<!-- Initialize theme (light is default) --><script>
      // Check for dark mode preference, but default to light mode
      const darkModePreference = localStorage.getItem('darkMode') === 'true';
      // Ignore system preference to ensure light mode is default

      // Apply dark mode class if explicitly set, otherwise use light mode
      if (darkModePreference) {
        document.documentElement.classList.add('dark');
      } else {
        document.documentElement.classList.remove('dark');
        // Ensure light mode is set in localStorage
        localStorage.setItem('darkMode', 'false');
      }
    </script><!-- Error handler script to catch and handle JavaScript errors --><script src="/js/error-handler.js"></script>`, '</head> <body class="min-h-screen bg-design-background text-design-foreground font-sans antialiased"> ', " </body></html>"])), addAttribute(darkModePreference ? "dark" : "", "class"), renderComponent($$result, "FontLoader", $$FontLoader, {}), renderComponent($$result, "SEO", $$SEO, { "title": title, "description": description, "canonical": canonical, "openGraph": {
    basic: {
      title,
      type: "website",
      image: `${Astro2.url.origin}/og-image.svg`
    },
    optional: {
      description,
      siteName: "VapeHybrid"
    },
    image: {
      alt: "VapeHybrid - Find the best vape deals and coupons all in one place",
      width: 1200,
      height: 630
    }
  }, "twitter": {
    card: "summary_large_image",
    site: "@vapehybrid",
    creator: "@vapehybrid",
    title,
    description,
    image: `${Astro2.url.origin}/og-image.svg`,
    imageAlt: "VapeHybrid - Find the best vape deals and coupons all in one place"
  }, "extend": {
    // Additional meta tags
    meta: [
      { name: "theme-color", content: "#262cbd" }
    ],
    // Additional link tags
    link: [
      { rel: "icon", href: "/favicon.ico", sizes: "any" },
      { rel: "icon", href: "/favicon.svg", type: "image/svg+xml" }
    ]
  } }), undefined, structuredData && renderTemplate(_b || (_b = __template(['<script type="application/ld+json">', "</script>"])), unescapeHTML(JSON.stringify(structuredData))), renderComponent($$result, "NavigationStructuredData", NavigationStructuredData, { "currentPath": currentPath, "siteName": "VapeHybrid", "baseUrl": "https://vapehybrid.com" }), renderComponent($$result, "FontLoader", $$FontLoader, {}), renderHead(), renderComponent($$result, "ToastProvider", ToastProvider, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem", "client:component-export": "ToastProvider" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "ReactProviders", ReactProviders, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/react-providers", "client:component-export": "ReactProviders" }, { "default": ($$result3) => renderTemplate` <div class="relative flex min-h-screen flex-col"> ${renderComponent($$result3, "Navbar", Navbar, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Navbar", "client:component-export": "Navbar" })} <main id="main-content" class="flex-1" tabindex="-1"> ${renderSlot($$result3, $$slots["default"])} </main> ${renderComponent($$result3, "BackToTop", BackToTop, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/back-to-top", "client:component-export": "BackToTop" })} ${renderComponent($$result3, "ExitIntentPopup", ExitIntentPopup, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ExitIntentPopup", "client:component-export": "default" })} <footer class="border-t border-design-border pt-10 pb-2 bg-design-background"> <div class="container mx-auto px-4"> <div class="grid grid-cols-1 md:grid-cols-4 gap-8 mb-10"> <div class="md:col-span-1"> <div class="flex items-center gap-2 mb-4"> <img src="/footer-logo-light.svg" alt="VapeHybrid Logo" class="w-8 h-8 dark:hidden" width="32" height="32"> <img src="/footer-logo-dark.svg" alt="VapeHybrid Logo" class="w-8 h-8 hidden dark:block" width="32" height="32"> <h3 class="text-lg font-semibold text-design-foreground">VapeHybrid</h3> </div> <p class="text-sm text-design-muted-foreground mb-4">
Helping vape users save money via vetted deals. We independently review everything we recommend.
</p> <div class="flex items-center"> <div class="flex items-center text-design-primary"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1"><path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path></svg> <span class="text-xs font-medium">Verified daily by our team</span> </div> </div> </div> <div class="md:col-span-1"> <ul class="space-y-2"> <li> <a href="/coupons" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">All Coupons</a> </li> <li> <a href="/coupons/brands" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Brands</a> </li> <li> <a href="/merchants" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Merchants</a> </li> <li> <a href="/coupons/categories" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Categories</a> </li> </ul> </div> <div class="md:col-span-1"> <ul class="space-y-2"> <li> <a href="/about" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">About Us</a> </li> <li> <a href="/contact" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Contact Us</a> </li> <li> <a href="/faq" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">FAQ</a> </li> <li> <a href="/blog" class="text-sm text-design-muted-foreground hover:text-design-foreground transition-colors">Blog</a> </li> </ul> </div> <div class="md:col-span-1"> ${renderComponent($$result3, "FooterNewsletter", FooterNewsletter, { "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/FooterNewsletter", "client:component-export": "FooterNewsletter" })} </div> </div> <div class="w-full h-px bg-design-border mb-6"></div> <div class="flex flex-col md:flex-row items-center justify-between gap-4"> <div class="flex flex-col items-center md:items-start gap-2"> <p class="text-center text-[12px] text-design-muted-foreground md:text-left">
We may earn a commission on the items you choose to buy.
</p> <p class="text-center text-[10px] text-design-muted-foreground/70 md:text-left">
© 2024 VapeHybrid. All rights reserved.
</p> </div> <div class="flex flex-wrap justify-end gap-4"> <a href="/legal" class="text-[12px] text-design-muted-foreground hover:text-design-foreground transition-colors">Legal</a> <a href="/terms" class="text-[12px] text-design-muted-foreground hover:text-design-foreground transition-colors">Terms</a> <a href="/privacy" class="text-[12px] text-design-muted-foreground hover:text-design-foreground transition-colors">Privacy</a> </div> </div> </div> </footer> </div> ` })} ` }));
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/layouts/MainLayout.astro", void 0);

export { $$MainLayout as $ };
