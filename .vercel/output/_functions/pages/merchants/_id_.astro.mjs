import { c as createAstro, a as createComponent, r as renderTemplate } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import 'clsx';
import { c as createServerSupabaseClient } from '../../assets/js/server-e_5TR1Eu.js';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$id = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const { id } = Astro2.params;
  const supabase = createServerSupabaseClient();
  const isNumericId = /^\d+$/.test(id);
  let merchant = null;
  let error = null;
  if (isNumericId) {
    const { data: merchantData, error: merchantError } = await supabase.from("merchants").select("*").eq("id", id).single();
    if (merchantData?.slug) {
      return Astro2.redirect(`/coupons/merchants/${merchantData.slug}`, 301);
    } else if (merchantData) {
      return Astro2.redirect(`/coupons/merchants/${merchantData.id}`, 301);
    }
    merchant = merchantData;
    error = merchantError;
  } else {
    const { data: merchantData, error: merchantError } = await supabase.from("merchants").select("*").eq("slug", id).single();
    if (merchantData) {
      return Astro2.redirect(`/coupons/merchants/${merchantData.slug || merchantData.id}`, 301);
    }
    merchant = merchantData;
    error = merchantError;
  }
  if (error || !merchant) {
    console.error("Error fetching merchant for redirect:", error);
    return Astro2.redirect("/merchants", 302);
  }
  return renderTemplate``;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/merchants/[id].astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/merchants/[id].astro";
const $$url = "/merchants/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
