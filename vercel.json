{"buildCommand": "npm run build", "outputDirectory": "dist", "framework": "astro", "installCommand": "npm install", "devCommand": "npm run dev", "regions": ["iad1"], "functions": {"src/pages/**/*.astro": {"maxDuration": 30}, "src/pages/api/**/*.ts": {"maxDuration": 30}}, "headers": [{"source": "/_astro/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000, immutable"}]}, {"source": "/images/(.*)", "headers": [{"key": "Cache-Control", "value": "public, max-age=31536000"}]}], "rewrites": [{"source": "/sitemap.xml", "destination": "/sitemap.xml"}]}