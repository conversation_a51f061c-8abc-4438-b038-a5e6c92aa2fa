import{r as e,j as t}from"./vendor/react-bq1VYqFi.js";import{M as n,i as s,u as o,P as r,a as i,b as c,L as u}from"./proxy-BAs1uqPX.js";class a extends e.Component{getSnapshotBeforeUpdate(e){const t=this.props.childRef.current;if(t&&e.isPresent&&!this.props.isPresent){const e=t.offsetParent,n=s(e)&&e.offsetWidth||0,o=this.props.sizeRef.current;o.height=t.offsetHeight||0,o.width=t.offsetWidth||0,o.top=t.offsetTop,o.left=t.offsetLeft,o.right=n-o.width-o.left}return null}componentDidUpdate(){}render(){return this.props.children}}function l({children:s,isPresent:o,anchorX:r}){const i=e.useId(),c=e.useRef(null),u=e.useRef({width:0,height:0,top:0,left:0,right:0}),{nonce:l}=e.useContext(n);return e.useInsertionEffect((()=>{const{width:e,height:t,top:n,left:s,right:a}=u.current;if(o||!c.current||!e||!t)return;const h="left"===r?`left: ${s}`:`right: ${a}`;c.current.dataset.motionPopId=i;const f=document.createElement("style");return l&&(f.nonce=l),document.head.appendChild(f),f.sheet&&f.sheet.insertRule(`\n          [data-motion-pop-id="${i}"] {\n            position: absolute !important;\n            width: ${e}px !important;\n            height: ${t}px !important;\n            ${h}px !important;\n            top: ${n}px !important;\n          }\n        `),()=>{document.head.contains(f)&&document.head.removeChild(f)}}),[o]),t.jsx(a,{isPresent:o,childRef:c,sizeRef:u,children:e.cloneElement(s,{ref:c})})}const h=({children:n,initial:s,isPresent:i,onExitComplete:c,custom:u,presenceAffectsLayout:a,mode:h,anchorX:d})=>{const p=o(f),m=e.useId();let g=!0,x=e.useMemo((()=>(g=!1,{id:m,initial:s,isPresent:i,custom:u,onExitComplete:e=>{p.set(e,!0);for(const t of p.values())if(!t)return;c&&c()},register:e=>(p.set(e,!1),()=>p.delete(e))})),[i,p,c]);return a&&g&&(x={...x}),e.useMemo((()=>{p.forEach(((e,t)=>p.set(t,!1)))}),[i]),e.useEffect((()=>{!i&&!p.size&&c&&c()}),[i]),"popLayout"===h&&(n=t.jsx(l,{isPresent:i,anchorX:d,children:n})),t.jsx(r.Provider,{value:x,children:n})};function f(){return new Map}const d=e=>e.key||"";function p(t){const n=[];return e.Children.forEach(t,(t=>{e.isValidElement(t)&&n.push(t)})),n}const m=({children:n,custom:s,initial:r=!0,onExitComplete:a,presenceAffectsLayout:l=!0,mode:f="sync",propagate:m=!1,anchorX:g="left"})=>{const[x,E]=i(m),P=e.useMemo((()=>p(n)),[n]),C=m&&!x?[]:P.map(d),R=e.useRef(!0),j=e.useRef(P),w=o((()=>new Map)),[y,$]=e.useState(P),[v,L]=e.useState(P);c((()=>{R.current=!1,j.current=P;for(let e=0;e<v.length;e++){const t=d(v[e]);C.includes(t)?w.delete(t):!0!==w.get(t)&&w.set(t,!1)}}),[v,C.length,C.join("-")]);const M=[];if(P!==y){let e=[...P];for(let t=0;t<v.length;t++){const n=v[t],s=d(n);C.includes(s)||(e.splice(t,0,n),M.push(n))}return"wait"===f&&M.length&&(e=M),L(p(e)),$(P),null}const{forceRender:X}=e.useContext(u);return t.jsx(t.Fragment,{children:v.map((e=>{const n=d(e),o=!(m&&!x)&&(P===v||C.includes(n));return t.jsx(h,{isPresent:o,initial:!(R.current&&!r)&&void 0,custom:s,presenceAffectsLayout:l,mode:f,onExitComplete:o?void 0:()=>{if(!w.has(n))return;w.set(n,!0);let e=!0;w.forEach((t=>{t||(e=!1)})),e&&(X?.(),L(j.current),m&&E?.(),a&&a())},anchorX:g,children:e},n)}))})};export{m as A};
