/**
 * Error handling utilities for TypeScript
 */

export function getErrorMessage(error: unknown): string {
  if (error instanceof Error) {
    return error.message;
  }
  if (typeof error === 'string') {
    return error;
  }
  if (error && typeof error === 'object' && 'message' in error) {
    return String(error.message);
  }
  return 'An unknown error occurred';
}

export function getErrorStack(error: unknown): string | undefined {
  if (error instanceof Error) {
    return error.stack;
  }
  if (error && typeof error === 'object' && 'stack' in error) {
    return String(error.stack);
  }
  return undefined;
}

export function isError(error: unknown): error is Error {
  return error instanceof Error;
}

export function createErrorResponse(error: unknown, defaultMessage = 'An error occurred') {
  return {
    success: false,
    error: true,
    message: getErrorMessage(error) || defaultMessage,
    details: getErrorMessage(error),
    stack: getErrorStack(error)
  };
}
