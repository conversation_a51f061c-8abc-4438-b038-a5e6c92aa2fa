import { a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../assets/js/MainLayout-BVJnMCp3.js';
export { renderers } from '../renderers.mjs';

const $$CardComparison = createComponent(($$result, $$props, $$slots) => {
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Card Component Comparison" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-7xl mx-auto px-4 py-8"> <h1 class="text-3xl font-bold mb-4">Card Component Comparison</h1> <p class="mb-8">This page compares existing card components with the new design system card components.</p> <div class="mb-12"> <h2 class="text-2xl font-bold mb-6 pb-2 border-b">Basic Cards</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <div> <h3 class="text-xl font-bold mb-4">Existing Card Component</h3> <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"> <h3 class="text-lg font-bold mb-2">Card Title</h3> <p class="text-gray-600 mb-4">This is a basic card component with some content. It uses the existing styling approach.</p> <button class="bg-blue-600 text-white px-4 py-2 rounded-md">Action</button> </div> </div> <div> <h3 class="text-xl font-bold mb-4">Design System Card Component</h3> <div class="design-card"> <h3 class="text-lg font-bold mb-2">Card Title</h3> <p class="mb-4">This is a basic card component with some content. It uses the new design system styling.</p> <button class="design-button design-button-primary">Action</button> </div> </div> </div> </div> <div class="mb-12"> <h2 class="text-2xl font-bold mb-6 pb-2 border-b">Interactive Cards</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <div> <h3 class="text-xl font-bold mb-4">Existing Interactive Card</h3> <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm hover:shadow-md hover:border-blue-300 transition-all duration-200"> <h3 class="text-lg font-bold mb-2">Interactive Card</h3> <p class="text-gray-600 mb-4">This card has hover effects. Try hovering over it!</p> <button class="bg-blue-600 text-white px-4 py-2 rounded-md">Learn More</button> </div> </div> <div> <h3 class="text-xl font-bold mb-4">Design System Interactive Card</h3> <div class="design-card" data-interactive="true"> <h3 class="text-lg font-bold mb-2">Interactive Card</h3> <p class="mb-4">This card has hover effects. Try hovering over it!</p> <button class="design-button design-button-primary">Learn More</button> </div> </div> </div> </div> <div class="mb-12"> <h2 class="text-2xl font-bold mb-6 pb-2 border-b">Deal Cards</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <div> <h3 class="text-xl font-bold mb-4">Existing Deal Card</h3> <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"> <div class="flex justify-between items-start mb-3"> <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">50% OFF</span> </div> <div> <h3 class="font-bold text-lg mb-2">Premium Subscription Deal</h3> <p class="text-gray-600 text-sm mb-3">Get 50% off on our premium subscription plan.</p> </div> <div class="mt-4 pt-3 border-t border-gray-200 flex justify-between items-center"> <div class="text-xs text-gray-500 flex items-center gap-1"> <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"> <circle cx="12" cy="12" r="10"></circle> <polyline points="12 6 12 12 16 14"></polyline> </svg>
Expires in 10 days
</div> <button class="bg-blue-600 text-white px-3 py-1 text-sm rounded-md">Get Deal</button> </div> </div> </div> <div> <h3 class="text-xl font-bold mb-4">Design System Deal Card</h3> <div class="deal-card"> <div class="deal-card-header"> <span class="design-badge design-badge-primary">50% OFF</span> </div> <div class="deal-card-body"> <h3 class="deal-card-title">Premium Subscription Deal</h3> <p class="deal-card-description">Get 50% off on our premium subscription plan.</p> </div> <div class="deal-card-footer"> <div class="coupon-expiry coupon-expiry-future"> <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"> <circle cx="12" cy="12" r="10"></circle> <polyline points="12 6 12 12 16 14"></polyline> </svg>
Expires in 10 days
</div> <button class="design-button design-button-primary design-button-sm">Get Deal</button> </div> </div> </div> </div> </div> <div class="mb-12"> <h2 class="text-2xl font-bold mb-6 pb-2 border-b">Real Product Comparison: DealCardSimple vs New Design</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <div> <h3 class="text-xl font-bold mb-4">Existing DealCardSimple Component</h3> <div class="deal-card relative overflow-hidden transition-all duration-design-normal flex flex-col bg-design-card text-design-card-foreground hover:shadow-design-md border-design-deal-border hover:border-design-deal-border-hover"> <!-- Discount Badge --> <div class="absolute top-2 left-2 z-10 inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold border-transparent bg-[#262cbd] dark:bg-[#1df292] text-white dark:text-black">
40% OFF
</div> <!-- Expiry Badge --> <div class="absolute top-2 right-8 z-10 inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold border-transparent bg-[#f21d6b] text-white">
Expires in 5 days
</div> <!-- Image --> <div class="w-full p-4 pb-2"> <div class="relative block"> <div class="relative overflow-hidden rounded-lg"> <img src="/hero/vape-1.png" alt="SMOK Nord 4 Pod Kit - 24 Hour Flash Sale" class="object-contain aspect-[4/3] w-full hover:scale-105 transition-transform duration-design-normal" loading="eager" width="400" height="300"> </div> </div> </div> <!-- Content --> <div class="p-4 pt-0 flex-1"> <div class="p-0"> <h3 class="text-lg font-semibold line-clamp-2 text-design-foreground">
SMOK Nord 4 Pod Kit - 24 Hour Flash Sale
</h3> <div class="flex items-center gap-1 text-xs text-design-muted-foreground mt-1"> <span>SMOK</span> </div> <!-- Success Rate and Verified Badge --> <div class="flex items-center gap-2 text-xs mt-1"> <span class="flex items-center gap-1 text-design-success"> <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"> <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path> </svg> <span>85% success</span> </span> <span class="flex items-center gap-1 text-design-success"> <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"> <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"></path> </svg> <span>Verified</span> </span> </div> <!-- Timer --> <div class="text-xs mt-1 text-design-muted-foreground">
5 days remaining
</div> </div> <div class="p-0 mt-4"> <!-- Coupon Code Area --> <div class="relative"> <div class="bg-design-muted p-2 rounded-md text-center font-mono text-sm blur-sm select-none">
NORD40
</div> <button class="absolute inset-0 w-full bg-design-primary/50 rounded-md flex items-center justify-center"> <span class="hover:underline bg-design-primary text-[#fff] dark:text-[#0a0a0a] px-3 py-1 text-sm rounded-md font-medium flex items-center gap-1"> <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"> <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path> <polyline points="15 3 21 3 21 9"></polyline> <line x1="10" y1="14" x2="21" y2="3"></line> </svg>
Reveal Code
</span> </button> </div> </div> </div> <!-- Footer --> <div class="p-4 pt-0 flex justify-between items-center"> <!-- Vote Buttons --> <div class="flex gap-1"> <!-- Upvote Button --> <button class="bg-gray-100 dark:bg-gray-800 hover:bg-green-100 dark:hover:bg-green-900 hover:text-green-600 dark:hover:text-green-400 h-10 w-10 p-1 rounded-full transition-all transform hover:scale-110"> <div class="flex flex-col items-center justify-center"> <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"> <path d="M14 9V5a3 3 0 0 0-3-3l-4 9v11h11.28a2 2 0 0 0 2-1.7l1.38-9a2 2 0 0 0-2-2.3zM7 22H4a2 2 0 0 1-2-2v-7a2 2 0 0 1 2-2h3"></path> </svg> <span class="text-xs font-medium mt-1">24</span> </div> </button> </div> <!-- View Deal Link and Bookmark Button --> <div class="flex gap-1 items-center flex-nowrap"> <button class="h-12 w-12 flex items-center justify-center text-design-muted-foreground hover:text-design-primary transition-colors"> <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"> <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path> </svg> </button> <a href="#" class="text-xs text-design-primary hover:underline px-2 py-1 border border-design-primary/20 rounded-md whitespace-nowrap">
View Deal
</a> </div> </div> </div> </div> <div> <h3 class="text-xl font-bold mb-4">New Design System Card</h3> <!-- First Card --> <div class="bg-[#121212] p-4 rounded-lg mb-6"> <div class="bg-white rounded-lg overflow-hidden mb-3"> <div class="p-4 flex items-center justify-center"> <img src="/hero/vape-1.png" alt="SMOK Nord 4 Pod Kit" class="h-16 object-contain"> </div> </div> <div class="flex items-center justify-between mb-1"> <div class="text-white font-bold text-xl">$135 off</div> <div class="text-green-500 text-sm">LOVE25</div> </div> <div class="text-white font-medium mb-1">Free Stuff Finder</div> <div class="text-gray-400 text-sm mb-3">
$135 Off Extra On SMOK Nord 4 Pod Kit - Lifetime Activation On Groupon
</div> <div class="flex items-center text-gray-400 text-xs mb-3"> <div class="flex items-center"> <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-blue-600 mr-1"></span> <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-blue-600 mr-1"></span> <span class="mr-2">Worked 3w ago for 3 shoppers</span> </div> </div> <button class="w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors">
Copy code
</button> </div> <!-- Second Card --> <div class="bg-[#121212] p-4 rounded-lg mb-6"> <div class="bg-white rounded-lg overflow-hidden mb-3"> <div class="p-4 flex items-center justify-center"> <img src="/hero/vape-1.png" alt="Vaporesso XROS 3" class="h-16 object-contain"> </div> </div> <div class="flex items-center justify-between mb-1"> <div class="text-white font-bold text-xl">35% off</div> <div class="text-green-500 text-sm">351FB13K</div> </div> <div class="text-white font-medium mb-1">The Krazy Coupon Lady</div> <div class="text-gray-400 text-sm mb-3">
35% Off Storewide at The Krazy Coupon Lady
</div> <div class="flex items-center text-gray-400 text-xs mb-3"> <div class="flex items-center"> <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-yellow-600 mr-1"></span> <span class="mr-2">Worked 3d ago for 2 shoppers</span> </div> </div> <button class="w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors">
Copy code
</button> </div> <!-- Third Card --> <div class="bg-[#121212] p-4 rounded-lg"> <div class="bg-white rounded-lg overflow-hidden mb-3"> <div class="p-4 flex items-center justify-center"> <img src="/hero/vape-1.png" alt="Uwell Caliburn G2" class="h-16 object-contain"> </div> </div> <div class="flex items-center justify-between mb-1"> <div class="text-white font-bold text-xl">25% off</div> <div class="text-green-500 text-sm">NYLOCALS</div> </div> <div class="text-white font-medium mb-1">Timeout Offers</div> <div class="text-gray-400 text-sm mb-3">
25% Off You Can Now Go Glamping On The Brooklyn Waterfront at Timeout Offers
</div> <div class="flex items-center text-gray-400 text-xs mb-3"> <div class="flex items-center"> <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-600 mr-1"></span> <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-600 mr-1"></span> <span class="inline-flex items-center justify-center w-5 h-5 rounded-full bg-purple-600 mr-1"></span> <span class="mr-2">Worked 3mo ago for 3 shoppers</span> </div> </div> <button class="w-full py-2 bg-gray-700 hover:bg-gray-600 text-white rounded-md transition-colors">
Copy code
</button> </div> </div> </div> </div> <div class="mb-12"> <h2 class="text-2xl font-bold mb-6 pb-2 border-b">Merchant Cards</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <div> <h3 class="text-xl font-bold mb-4">Existing Merchant Card</h3> <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm"> <div class="flex items-center mb-4"> <img src="https://via.placeholder.com/64" alt="Merchant Logo" class="w-16 h-16 rounded-full mr-4"> <div> <h3 class="font-bold text-lg">Merchant Name</h3> <p class="text-gray-600 text-sm">Electronics & Gadgets</p> </div> </div> <p class="text-gray-600 mb-4">This merchant offers a wide range of electronics and gadgets at competitive prices.</p> <div class="flex justify-between items-center"> <span class="text-sm text-gray-500">15 active deals</span> <button class="bg-blue-600 text-white px-4 py-2 rounded-md">View Merchant</button> </div> </div> </div> <div> <h3 class="text-xl font-bold mb-4">Design System Merchant Card</h3> <div class="merchant-card"> <div class="merchant-card-header"> <img src="https://via.placeholder.com/64" alt="Merchant Logo" class="merchant-card-logo"> <div class="merchant-card-info"> <h3 class="merchant-card-title">Merchant Name</h3> <p class="merchant-card-category">Electronics & Gadgets</p> </div> </div> <div class="merchant-card-body"> <p class="merchant-card-description">This merchant offers a wide range of electronics and gadgets at competitive prices.</p> </div> <div class="merchant-card-footer"> <span class="merchant-card-deals">15 active deals</span> <button class="design-button design-button-primary">View Merchant</button> </div> </div> </div> </div> </div> <div class="mb-12"> <h2 class="text-2xl font-bold mb-6 pb-2 border-b">Category Cards</h2> <div class="grid grid-cols-1 md:grid-cols-2 gap-8"> <div> <h3 class="text-xl font-bold mb-4">Existing Category Card</h3> <div class="bg-white border border-gray-200 rounded-lg overflow-hidden shadow-sm"> <div class="h-40 bg-gray-200 relative"> <img src="https://via.placeholder.com/400x200" alt="Category Image" class="w-full h-full object-cover"> <div class="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent"></div> <h3 class="absolute bottom-4 left-4 text-white font-bold text-xl">Electronics</h3> </div> <div class="p-4"> <p class="text-gray-600 mb-4">Find the best deals on electronics and gadgets.</p> <div class="flex justify-between items-center"> <span class="text-sm text-gray-500">42 deals available</span> <button class="bg-blue-600 text-white px-4 py-2 rounded-md">Browse Category</button> </div> </div> </div> </div> <div> <h3 class="text-xl font-bold mb-4">Design System Category Card</h3> <div class="category-card"> <div class="category-card-image-container"> <img src="https://via.placeholder.com/400x200" alt="Category Image" class="category-card-image"> <div class="category-card-overlay"></div> <h3 class="category-card-title">Electronics</h3> </div> <div class="category-card-body"> <p class="category-card-description">Find the best deals on electronics and gadgets.</p> </div> <div class="category-card-footer"> <span class="category-card-deals">42 deals available</span> <button class="design-button design-button-primary">Browse Category</button> </div> </div> </div> </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/card-comparison.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/card-comparison.astro";
const $$url = "/card-comparison";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$CardComparison,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
