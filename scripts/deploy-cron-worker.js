#!/usr/bin/env node

/**
 * Deploy Cloudflare Worker for Supabase Cron Jobs
 * 
 * This script helps deploy the Cloudflare Worker with proper configuration
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';

const WORKER_DIR = 'workers';
const WORKER_NAME = 'vapehybrid-supabase-cron';

/**
 * Check if wrangler is installed
 */
function checkWrangler() {
  try {
    execSync('wrangler --version', { stdio: 'pipe' });
    console.log('✅ Wrangler CLI is installed');
    return true;
  } catch (error) {
    console.error('❌ Wrangler CLI is not installed');
    console.log('📦 Install with: npm install -g wrangler');
    return false;
  }
}

/**
 * Check if user is logged in to Cloudflare
 */
function checkLogin() {
  try {
    execSync('wrangler whoami', { stdio: 'pipe' });
    console.log('✅ Logged in to Cloudflare');
    return true;
  } catch (error) {
    console.error('❌ Not logged in to Cloudflare');
    console.log('🔑 Login with: wrangler login');
    return false;
  }
}

/**
 * Check if worker files exist
 */
function checkWorkerFiles() {
  const requiredFiles = [
    path.join(WORKER_DIR, 'supabase-cron.js'),
    path.join(WORKER_DIR, 'wrangler.toml')
  ];
  
  for (const file of requiredFiles) {
    if (!fs.existsSync(file)) {
      console.error(`❌ Missing file: ${file}`);
      return false;
    }
  }
  
  console.log('✅ All worker files exist');
  return true;
}

/**
 * Deploy the worker
 */
function deployWorker() {
  try {
    console.log('🚀 Deploying Cloudflare Worker...');
    
    const result = execSync(`cd ${WORKER_DIR} && wrangler deploy`, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    console.log('✅ Worker deployed successfully!');
    console.log(result);
    
    return true;
  } catch (error) {
    console.error('❌ Deployment failed:');
    console.error(error.stdout || error.message);
    return false;
  }
}

/**
 * Test the deployed worker
 */
async function testWorker() {
  try {
    console.log('🧪 Testing deployed worker...');
    
    // Get worker URL from wrangler
    const result = execSync(`cd ${WORKER_DIR} && wrangler subdomain`, { 
      encoding: 'utf8',
      stdio: 'pipe'
    });
    
    // Extract subdomain (this is a simplified approach)
    const workerUrl = `https://${WORKER_NAME}.your-subdomain.workers.dev`;
    
    console.log(`📍 Worker URL: ${workerUrl}`);
    console.log('🔍 Test endpoints:');
    console.log(`   Health check: ${workerUrl}/health`);
    console.log(`   Manual trigger: ${workerUrl}/trigger (POST)`);
    
    return true;
  } catch (error) {
    console.warn('⚠️  Could not determine worker URL automatically');
    console.log('🔍 Check your Cloudflare dashboard for the worker URL');
    return false;
  }
}

/**
 * Main deployment process
 */
async function main() {
  console.log('🔧 VapeHybrid Supabase Cron Worker Deployment');
  console.log('=' .repeat(50));
  
  // Pre-deployment checks
  if (!checkWrangler()) {
    process.exit(1);
  }
  
  if (!checkLogin()) {
    process.exit(1);
  }
  
  if (!checkWorkerFiles()) {
    process.exit(1);
  }
  
  // Deploy
  if (!deployWorker()) {
    process.exit(1);
  }
  
  // Test
  await testWorker();
  
  console.log('\n🎉 Deployment completed successfully!');
  console.log('\n📋 Next steps:');
  console.log('1. Check your Cloudflare dashboard to verify the worker is running');
  console.log('2. The cron trigger will automatically run daily at 4:00 PM UTC');
  console.log('3. Monitor the worker logs with: wrangler tail vapehybrid-supabase-cron');
  console.log('4. Test manually by calling the /trigger endpoint');
}

// Run if called directly
if (import.meta.url === `file://${process.argv[1]}`) {
  main().catch(error => {
    console.error('💥 Deployment script failed:', error);
    process.exit(1);
  });
}
