import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$LegalPageLayout } from '../assets/js/LegalPageLayout-DSHTXlhZ.js';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Disclaimer = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Disclaimer;
  const title = "Disclaimer | VapeHybrid";
  const description = "Important disclaimers regarding the content and services provided by VapeHybrid.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": title,
    "description": description,
    "publisher": {
      "@type": "Organization",
      "name": "VapeHybrid",
      "logo": {
        "@type": "ImageObject",
        "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`
      }
    }
  };
  (/* @__PURE__ */ new Date()).getFullYear();
  return renderTemplate`${renderComponent($$result, "LegalPageLayout", $$LegalPageLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<h1 class="text-3xl font-bold text-design-foreground mb-6">Disclaimer</h1> <p class="text-design-muted-foreground mb-8">Last updated: ${(/* @__PURE__ */ new Date()).toLocaleDateString("en-US", { year: "numeric", month: "long", day: "numeric" })}</p> <div class="prose prose-lg max-w-none text-design-foreground prose-headings:text-design-foreground prose-strong:text-design-foreground prose-a:text-design-primary"> <h2>No Direct Sales</h2> <p>VapeHybrid is not a merchant or retailer. We do not sell vape products or any other merchandise directly. Our platform serves solely as an affiliate marketing service that connects users with third-party merchants who sell vape products. All transactions occur on the merchant's website, not on VapeHybrid.</p> <h2>No Health or Medical Advice</h2> <p>The content on VapeHybrid is for informational purposes only and is not intended to be a substitute for professional medical advice, diagnosis, or treatment. Always seek the advice of a qualified healthcare provider with any questions you may have regarding a medical condition or the use of vaping products.</p> <p>We do not recommend vaping as a smoking cessation method or make any health claims about vaping products. Consult a medical professional for health concerns related to smoking, vaping, or nicotine use.</p> <h2>Price and Availability</h2> <p>All prices, discounts, and availability information displayed on VapeHybrid are subject to change without notice. While we strive to provide accurate and up-to-date information, there may be instances where the information on our site differs from what is displayed on the merchant's site.</p> <p>Always refer to the merchant's website for the most current pricing, availability, and terms before making a purchase. We cannot guarantee that any deal or coupon code will work as described, as merchants may change their offers at any time without informing us.</p> <h2>Age Restriction Reminder</h2> <p>Vaping products are age-restricted and intended for adults only. Users must be of legal vaping age in their jurisdiction (21+ in the United States, 18+ in most other countries) to use our service and to purchase products from the merchants we link to.</p> <p>By using VapeHybrid, you confirm that you meet the minimum age requirements for purchasing and using vaping products in your jurisdiction. We do not knowingly collect information from or market to individuals below the legal vaping age.</p> <h2>Limitation of Liability</h2> <p>As detailed in our <a href="/terms">Terms & Conditions</a>, VapeHybrid's liability is limited. We are not responsible for:</p> <ul> <li>The quality, safety, legality, or delivery of products purchased from merchants we link to</li> <li>Any adverse effects or health consequences related to the use of vaping products</li> <li>The accuracy of product descriptions, images, or specifications provided by merchants</li> <li>Any disputes between users and merchants</li> <li>Any indirect, incidental, special, consequential, or punitive damages arising from the use of our service</li> </ul> <h2>External Links</h2> <p>VapeHybrid contains links to external websites that are not operated by us. We have no control over, and assume no responsibility for, the content, privacy policies, or practices of any third-party websites or services.</p> <h2>Contact Information</h2> <p>If you have any questions about this Disclaimer, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/disclaimer.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/disclaimer.astro";
const $$url = "/disclaimer";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Disclaimer,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
