import { defineConfig } from 'astro/config';
import tailwind from '@astrojs/tailwind';
import react from '@astrojs/react';
import vercel from '@astrojs/vercel/serverless';
import path from 'path';
import dotenv from 'dotenv';

dotenv.config(); // Ensure .env variables are loaded early

// Optional: fall back if env is not set
const supabaseUrl = process.env.SUPABASE_URL || 'https://zlnvivfgzgcjuspktadj.supabase.co';

export default defineConfig({
  site: process.env.ASTRO_SITE_URL || 'https://vapehybrid.com',
  output: 'server',  // Server-side rendering for Vercel
  adapter: vercel({
    webAnalytics: {
      enabled: true
    },
    edgeMiddleware: false,
    functionPerRoute: false
  }),

  // Enable prefetching for better performance
  prefetch: true,

  integrations: [
    tailwind({
      // Ensure Tailwind processes all CSS files
      applyBaseStyles: false, // We're manually importing Tailwind in main.css
    }),
    react()
  ],

  vite: {
    plugins: [],
    resolve: {
      alias: {
        '~': path.resolve('./src'),
      },
    },
    server: {
      proxy: {
        '/rest/v1': {
          target: supabaseUrl,
          changeOrigin: true,
          secure: true,
          rewrite: path => path.replace(/^\/rest\/v1/, '/rest/v1'),
        }
      }
    },
    optimizeDeps: {
      exclude: ['README.md', '*.md']
    },
    // Exclude README.md files and markdown files from being processed at all
    assetsInclude: ['**/*.svg'],

    css: {
      devSourcemap: true,
      // Ensure CSS is properly processed
      preprocessorOptions: {
        // Add any preprocessor options if needed
      },
      postcss: {
        // Use the postcss.config.js file
      },
    },
    build: {
      cssCodeSplit: true,
      cssMinify: 'lightningcss',
      minify: 'terser',
      terserOptions: {
        compress: {
          drop_console: true,
          drop_debugger: true,
          pure_funcs: ['console.log', 'console.debug', 'console.info'],
        },
      },
      rollupOptions: {
        output: {
          // Don't manually chunk CSS files to avoid 404 errors
          // Process them with Vite's standard code splitting instead
          manualChunks: id => {
            if (id.includes('node_modules/react')) return 'vendor/react';
            if (id.includes('node_modules/@radix-ui') || id.includes('node_modules/cmdk') || id.includes('node_modules/sonner')) {
              return 'vendor/ui-components';
            }
            if (id.includes('node_modules/clsx') || id.includes('node_modules/tailwind-merge') || id.includes('node_modules/class-variance-authority')) {
              return 'vendor/utils';
            }

            if (id.includes('/src/components/DesignSystem/')) return 'app/design-system';
            if (id.includes('/src/components/Admin/')) return 'app/admin';
            if (id.includes('/src/components/Deal')) return 'app/deals';
            if (id.includes('/src/components/Auth')) return 'app/auth';
          },
          chunkFileNames: 'assets/js/[name]-[hash].js',
          entryFileNames: 'assets/js/[name]-[hash].js',
        },
      },
    },
  },
});
