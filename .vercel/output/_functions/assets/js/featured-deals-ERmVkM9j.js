import { jsx, jsxs } from 'react/jsx-runtime';
import { useState, useEffect } from 'react';
import { R as ResponsiveImage } from './app/design-system-CUssmfny.js';
import { C as CountdownTimer } from './countdown-timer-DOGdzfkT.js';

const FeaturedDeals = ({
  deals,
  flashDeals = [],
  viewAllLink,
  viewAllText
}) => {
  const [isMounted, setIsMounted] = useState(false);
  const [activeTab, setActiveTab] = useState("featured");
  useEffect(() => {
    setIsMounted(true);
  }, []);
  if (!isMounted) {
    return /* @__PURE__ */ jsx("section", { className: "py-20 bg-transparent relative overflow-hidden", children: /* @__PURE__ */ jsx("div", { className: "max-w-4xl mx-auto px-4 text-center", children: /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground", children: "Loading deals..." }) }) });
  }
  const formatTodayDate = () => {
    const today = /* @__PURE__ */ new Date();
    return today.toLocaleDateString("en-US", {
      month: "long",
      day: "numeric",
      year: "numeric"
    });
  };
  const getImageUrl = (deal) => {
    if (deal.imagebig_url && deal.imagebig_url.trim() !== "")
      return deal.imagebig_url;
    if (deal.brand_logo_url && deal.brand_logo_url.trim() !== "")
      return deal.brand_logo_url;
    if (deal.image_url && deal.image_url.trim() !== "")
      return deal.image_url;
    return "/placeholder-image.svg";
  };
  const formatDiscount = (discount) => {
    if (!discount) return "Save";
    if (typeof discount === "string" && discount.includes("%")) {
      return discount;
    }
    return `${discount}%`;
  };
  const featuredDeals = deals.filter((deal) => deal.is_featured).length > 0 ? deals.filter((deal) => deal.is_featured).slice(0, 9) : deals.slice(0, 9);
  const processedFlashDeals = flashDeals && flashDeals.length > 0 ? flashDeals.slice(0, 9) : [];
  const finalFlashDeals = processedFlashDeals;
  return /* @__PURE__ */ jsxs("section", { className: "py-24 bg-transparent relative overflow-hidden", children: [
    /* @__PURE__ */ jsx("div", { className: "absolute top-20 left-10 w-32 h-32 bg-[#262cbd]/10 dark:bg-[#1df292]/10 rounded-full blur-3xl" }),
    /* @__PURE__ */ jsx("div", { className: "absolute bottom-20 right-10 w-40 h-40 bg-[#f21d6b]/10 dark:bg-[#f2d91d]/10 rounded-full blur-3xl" }),
    /* @__PURE__ */ jsxs("div", { className: "max-w-4xl mx-auto px-4 relative z-10 flex flex-col items-center", children: [
      /* @__PURE__ */ jsxs("div", { className: "text-center mb-12", children: [
        /* @__PURE__ */ jsxs("div", { className: "inline-flex items-center bg-design-primary/10 dark:bg-design-primary/20 backdrop-blur-sm px-4 py-2 rounded-full mb-4 border border-design-primary/20", children: [
          /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-4 w-4 text-design-primary mr-2", viewBox: "0 0 20 20", fill: "currentColor", children: /* @__PURE__ */ jsx("path", { fillRule: "evenodd", d: "M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z", clipRule: "evenodd" }) }),
          /* @__PURE__ */ jsxs("span", { className: "text-design-primary font-medium text-xs", children: [
            "Updated Today - ",
            /* @__PURE__ */ jsx("span", { className: "font-bold", children: formatTodayDate() })
          ] })
        ] }),
        /* @__PURE__ */ jsx("h2", { className: "text-3xl md:text-[34px] font-normal text-design-foreground mb-4", children: "Vape Deals & Coupons" }),
        /* @__PURE__ */ jsx("p", { className: "text-xs md:text-sm text-design-muted-foreground max-w-2xl mx-auto mb-8", children: "Handpicked offers from top vape brands, verified daily by our team" }),
        /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-10", children: /* @__PURE__ */ jsxs("div", { className: "inline-flex bg-design-card/80 backdrop-blur-sm rounded-full p-1 border border-design-border/20 shadow-sm", children: [
          /* @__PURE__ */ jsx(
            "button",
            {
              onClick: () => setActiveTab("featured"),
              "aria-label": "Show featured deals",
              "aria-pressed": activeTab === "featured",
              className: `px-6 py-2.5 text-sm font-medium rounded-full transition-all duration-300 ${activeTab === "featured" ? "bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] text-white dark:bg-gradient-to-b from-[#111111] to-[#f1d1fd] text-white dark:text-black shadow-sm transform scale-[1.02]" : "text-design-muted-foreground hover:text-design-foreground hover:bg-design-card/90"}`,
              children: "Featured Deals"
            }
          ),
          /* @__PURE__ */ jsxs(
            "button",
            {
              onClick: () => setActiveTab("flash"),
              "aria-label": "Show flash deals",
              "aria-pressed": activeTab === "flash",
              className: `px-6 py-2.5 text-sm font-medium rounded-full transition-all duration-300 flex items-center ${activeTab === "flash" ? "bg-gradient-to-b from-[#f21d6b] to-[#d91d4a] text-white shadow-sm transform scale-[1.02]" : "text-design-muted-foreground hover:text-design-foreground hover:bg-design-card/90"}`,
              children: [
                "Flash Deals",
                /* @__PURE__ */ jsx("span", { className: `ml-1.5 inline-flex items-center justify-center bg-white text-[#f21d6b] text-xs font-bold w-5 h-5 rounded-full ${activeTab === "flash" ? "animate-pulse" : ""}`, "aria-label": `${finalFlashDeals.length} flash deals available`, children: finalFlashDeals.length })
              ]
            }
          )
        ] }) })
      ] }),
      activeTab === "featured" ? /* @__PURE__ */ jsx("div", { className: "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 gap-6 mx-auto max-w-3xl", children: featuredDeals.length > 0 ? featuredDeals.map((deal, index) => /* @__PURE__ */ jsx(
        "div",
        {
          className: "group",
          children: /* @__PURE__ */ jsxs(
            "a",
            {
              href: `/go/${deal.id}`,
              className: "block h-full bg-design-card/95 backdrop-blur-sm rounded-xl overflow-hidden hover:shadow-md transition-all duration-300 hover:border-design-primary/20 border border-design-border/10 transform hover:-translate-y-1",
              target: "_blank",
              rel: "sponsored",
              "aria-label": `Get deal for ${deal.cleaned_title || deal.title}`,
              children: [
                /* @__PURE__ */ jsxs("div", { className: "relative overflow-hidden", children: [
                  /* @__PURE__ */ jsx(
                    ResponsiveImage,
                    {
                      src: getImageUrl(deal),
                      alt: `${deal.brand_name || ""} ${deal.title} - Save up to ${deal.discount || "50"}% on premium vape products`,
                      className: "w-full h-36 object-contain bg-gray-50 dark:bg-gray-900 p-2 transition-transform duration-500 group-hover:scale-110",
                      loading: index < 3 ? "eager" : "lazy",
                      width: 200,
                      height: 150,
                      fallbackSrc: "/placeholder-image.svg"
                    }
                  ),
                  deal.discount && /* @__PURE__ */ jsx("div", { className: "absolute top-2 left-2 bg-design-tetradic1 text-white text-xs font-bold px-2 py-1 rounded-full shadow-sm", children: formatDiscount(deal.discount) }),
                  /* @__PURE__ */ jsxs("div", { className: "absolute top-2 right-2 bg-design-primary text-white dark:text-black text-xs font-bold px-2 py-0.5 rounded-full shadow-sm flex items-center", children: [
                    /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-3 w-3 mr-0.5", viewBox: "0 0 20 20", fill: "currentColor", children: /* @__PURE__ */ jsx("path", { fillRule: "evenodd", d: "M6.267 3.455a3.066 3.066 0 001.745-.723 3.066 3.066 0 013.976 0 3.066 3.066 0 001.745.723 3.066 3.066 0 012.812 2.812c.051.643.304 1.254.723 1.745a3.066 3.066 0 010 3.976 3.066 3.066 0 00-.723 1.745 3.066 3.066 0 01-2.812 2.812 3.066 3.066 0 00-1.745.723 3.066 3.066 0 01-3.976 0 3.066 3.066 0 00-1.745-.723 3.066 3.066 0 01-2.812-2.812 3.066 3.066 0 00-.723-1.745 3.066 3.066 0 010-3.976 3.066 3.066 0 00.723-1.745 3.066 3.066 0 012.812-2.812zm7.44 5.252a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z", clipRule: "evenodd" }) }),
                    "Verified"
                  ] })
                ] }),
                /* @__PURE__ */ jsxs("div", { className: "p-3", children: [
                  /* @__PURE__ */ jsx("h3", { className: "font-medium text-sm text-design-foreground line-clamp-2 mb-2 group-hover:text-design-primary transition-colors", children: deal.cleaned_title || deal.title }),
                  /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-3", children: [
                    deal.brand_name && /* @__PURE__ */ jsx("span", { className: "font-medium", children: deal.brand_name }),
                    deal.brand_name && deal.merchant_name && /* @__PURE__ */ jsx("span", { className: "mx-1", children: "•" }),
                    deal.merchant_name && /* @__PURE__ */ jsx("span", { className: "truncate", children: deal.merchant_name })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center", children: [
                    /* @__PURE__ */ jsx("span", { className: "inline-block px-3 py-1.5 bg-gradient-to-b from-white to-[#d6e6f6] text-[#262cbd] text-xs font-medium rounded-full transition-all duration-300 shadow-sm flex items-center group-hover:shadow", children: "Get Deal" }),
                    /* @__PURE__ */ jsxs("div", { className: "text-design-muted-foreground text-xs flex items-center hover:text-design-primary", children: [
                      /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-3.5 w-3.5 mr-0.5", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor", children: [
                        /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z" }),
                        /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" })
                      ] }),
                      Math.floor(Math.random() * 200) + 50
                    ] })
                  ] })
                ] })
              ]
            }
          )
        },
        deal.id
      )) : /* @__PURE__ */ jsx("div", { className: "col-span-3 text-center py-8", children: /* @__PURE__ */ jsx("div", { className: "text-design-muted-foreground text-sm", children: "No featured deals available at the moment. Check back soon!" }) }) }) : /* @__PURE__ */ jsx("div", { className: "grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 gap-6 mx-auto max-w-3xl", children: finalFlashDeals && finalFlashDeals.length > 0 ? finalFlashDeals.map((deal, index) => /* @__PURE__ */ jsx(
        "div",
        {
          className: "group",
          children: /* @__PURE__ */ jsxs(
            "a",
            {
              href: `/go/${deal.id}`,
              className: "block h-full bg-design-card/95 backdrop-blur-sm rounded-xl overflow-hidden hover:shadow-md transition-all duration-300 hover:border-[#f21d6b]/20 border border-design-border/10 transform hover:-translate-y-1",
              target: "_blank",
              rel: "sponsored",
              "aria-label": `Get flash deal for ${deal.cleaned_title || deal.title}`,
              children: [
                /* @__PURE__ */ jsxs("div", { className: "relative overflow-hidden", children: [
                  /* @__PURE__ */ jsx(
                    ResponsiveImage,
                    {
                      src: getImageUrl(deal),
                      alt: `${deal.brand_name || ""} ${deal.title} - Save up to ${deal.discount || "50"}% on premium vape products`,
                      className: "w-full h-36 object-contain bg-gray-50 dark:bg-gray-900 p-2 transition-transform duration-500 group-hover:scale-110",
                      loading: index < 3 ? "eager" : "lazy",
                      width: 200,
                      height: 150,
                      fallbackSrc: "/placeholder-image.svg"
                    }
                  ),
                  deal.discount && /* @__PURE__ */ jsx("div", { className: "absolute top-2 left-2 bg-[#f21d6b] text-white text-xs font-bold px-2 py-1 rounded-full shadow-sm", children: formatDiscount(deal.discount) }),
                  /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 bg-black/70 backdrop-blur-sm text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-sm", children: /* @__PURE__ */ jsx(
                    CountdownTimer,
                    {
                      expiryDate: deal.deal_end_date || (/* @__PURE__ */ new Date()).toISOString(),
                      className: "text-white"
                    }
                  ) })
                ] }),
                /* @__PURE__ */ jsxs("div", { className: "p-3", children: [
                  /* @__PURE__ */ jsx("h3", { className: "font-medium text-sm text-design-foreground line-clamp-2 mb-2 group-hover:text-[#f21d6b] transition-colors", children: deal.cleaned_title || deal.title }),
                  /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-3", children: [
                    deal.brand_name && /* @__PURE__ */ jsx("span", { className: "font-medium", children: deal.brand_name }),
                    deal.brand_name && deal.merchant_name && /* @__PURE__ */ jsx("span", { className: "mx-1", children: "•" }),
                    deal.merchant_name && /* @__PURE__ */ jsx("span", { className: "truncate", children: deal.merchant_name })
                  ] }),
                  /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center", children: [
                    /* @__PURE__ */ jsx("span", { className: "inline-block px-3 py-1.5 bg-gradient-to-b from-[#f21d6b] to-[#d91d4a] text-white text-xs font-medium rounded-full transition-all duration-300 shadow-sm flex items-center group-hover:shadow", children: "Flash Deal" }),
                    /* @__PURE__ */ jsxs("div", { className: "text-design-muted-foreground text-xs flex items-center hover:text-[#f21d6b]", children: [
                      /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-3.5 w-3.5 mr-0.5", fill: "none", viewBox: "0 0 24 24", stroke: "currentColor", children: [
                        /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M15 12a3 3 0 11-6 0 3 3 0 016 0z" }),
                        /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" })
                      ] }),
                      Math.floor(Math.random() * 100) + 20
                    ] })
                  ] })
                ] })
              ]
            }
          )
        },
        deal.id
      )) : /* @__PURE__ */ jsx("div", { className: "col-span-3 text-center py-8", children: /* @__PURE__ */ jsx("div", { className: "text-design-muted-foreground text-sm", children: "No flash deals available at the moment. Check back soon!" }) }) }),
      /* @__PURE__ */ jsxs("div", { className: "mt-16 mb-8 text-center", children: [
        /* @__PURE__ */ jsxs("div", { className: "inline-block relative", children: [
          /* @__PURE__ */ jsx(
            "a",
            {
              href: viewAllLink,
              className: "inline-block text-sm font-medium transition-all duration-300 px-6 py-2.5 rounded-full\r\n              bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] text-white hover:text-white\r\n              shadow-glow hover:shadow-glow-light hover:animate-glow-border\r\n              hover:bg-animated-gradient bg-[length:400%_400%] hover:animate-gradient-move\r\n              dark:bg-gradient-to-b dark:from-[#1df292] dark:to-[#0db875] dark:text-black\r\n              dark:shadow-glow-dark dark:hover:shadow-glow-dark\r\n              dark:hover:bg-animated-gradient-dark",
              style: {
                minWidth: "180px",
                textAlign: "center"
              },
              "aria-label": "View all available deals",
              children: "View All Coupons"
            }
          ),
          /* @__PURE__ */ jsx("div", { className: "absolute -top-2 -right-2 bg-[#f21d6b] text-white text-xs font-bold px-2 py-0.5 rounded-full shadow-sm animate-pulse", children: "1000+ Deals" })
        ] }),
        /* @__PURE__ */ jsx("p", { className: "mt-3 text-xs text-design-muted-foreground", children: "All deals are manually verified by our team daily" }),
        /* @__PURE__ */ jsxs("div", { className: "mt-6 flex flex-wrap justify-center gap-2", children: [
          /* @__PURE__ */ jsx("a", { href: "/coupons/categories", className: "text-xs bg-design-primary/5 text-design-primary px-3 py-1.5 rounded-full hover:bg-design-primary/10 transition-colors border border-design-primary/10 hover:border-design-primary/20", children: "Browse Categories" }),
          /* @__PURE__ */ jsx("a", { href: "/coupons/brands", className: "text-xs bg-design-primary/5 text-design-primary px-3 py-1.5 rounded-full hover:bg-design-primary/10 transition-colors border border-design-primary/10 hover:border-design-primary/20", children: "Shop by Brand" }),
          /* @__PURE__ */ jsx("a", { href: "/faq", className: "text-xs bg-design-primary/5 text-design-primary px-3 py-1.5 rounded-full hover:bg-design-primary/10 transition-colors border border-design-primary/10 hover:border-design-primary/20", children: "Vaping FAQs" }),
          /* @__PURE__ */ jsx("a", { href: "/blog", className: "text-xs bg-design-primary/5 text-design-primary px-3 py-1.5 rounded-full hover:bg-design-primary/10 transition-colors border border-design-primary/10 hover:border-design-primary/20", children: "Vaping Guides" })
        ] })
      ] })
    ] })
  ] });
};

export { FeaturedDeals };
