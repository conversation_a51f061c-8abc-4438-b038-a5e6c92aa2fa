/**
 * OptimizedDealCard Component
 *
 * An enhanced and optimized version of the ImprovedDealCard component with better performance,
 * accessibility, and user experience.
 */

import React, { useState, useRef, useMemo, useCallback, useEffect } from 'react';
import { cn } from '../../../lib/utils';
import Card from './Card';
import { ThumbsUp, Eye } from 'lucide-react';
import { getRandomStaffImages, generateUsageInfo } from '../../../utils/dealUtils';
import BookmarkButton from '../../BookmarkButton';
import { DealImage } from '../DealImage';
import DealStructuredData from '../SEO/DealStructuredData';
import { copyToClipboard } from '../../../utils/clipboardUtils';
import { generateCouponUrl } from '../../../utils/couponUrlUtils';
import { normalizeUrl } from '../../../utils/urlUtils';
import usePrefetch from '../../../hooks/usePrefetch';
import useEngagementTracking from '../../../hooks/useEngagementTracking';
import type { DealCardProps } from '../../../types/deal';

// Extended props interface to include onShowCouponModal
interface ExtendedDealCardProps extends DealCardProps {
  onShowCouponModal?: (deal: any) => void;
  viewMode?: string; // Add viewMode prop to prevent DOM warnings
}

// Create a delayed version of DealStructuredData using requestIdleCallback
const DelayedStructuredData = ({ deal, imageUrl }: { deal: any, imageUrl: string }) => {
  const [shouldRender, setShouldRender] = useState(false);

  useEffect(() => {
    // Use requestIdleCallback to delay rendering until the browser is idle
    const requestIdleCallbackPolyfill =
      window.requestIdleCallback ||
      ((cb) => setTimeout(cb, 1));

    const idleCallbackId = requestIdleCallbackPolyfill(() => {
      setShouldRender(true);
    });

    return () => {
      // Use cancelIdleCallback to clean up
      const cancelIdleCallbackPolyfill =
        window.cancelIdleCallback ||
        ((id) => clearTimeout(id));

      cancelIdleCallbackPolyfill(idleCallbackId);
    };
  }, []);

  if (!shouldRender) return null;

  return <DealStructuredData deal={deal} imageUrl={imageUrl} />;
};

/**
 * OptimizedDealCard - A fully optimized version of the DealCard component
 *
 * Features:
 * - Improved performance with memoization and optimized rendering
 * - Enhanced accessibility with proper ARIA attributes
 * - Better image loading with priority hints
 * - Structured data for SEO
 * - Viewport tracking for analytics
 * - Modern clipboard API with fallbacks
 * - Improved visual feedback and animations
 */
export const OptimizedDealCard: React.FC<ExtendedDealCardProps> = ({
  deal,
  className,
  isRevealed = false,
  priority = false,
  onShowCouponModal,
  viewMode, // Extract viewMode to prevent it from being passed to DOM
  ...props
}) => {
  // State for code revealing
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);

  // Create refs for the card element
  const cardRef = useRef<HTMLDivElement>(null);

  // Set up prefetching for deal links
  const dealUrl = `/deal/${deal.id}`;
  const { onMouseEnter: handlePrefetch, onMouseLeave: handleCancelPrefetch } = usePrefetch(dealUrl, { delay: 150 });

  // Set up engagement tracking
  const {
    setIsVisible,
    handleMouseEnter: trackMouseEnter,
    handleMouseLeave: trackMouseLeave,
    handleClick: trackClick
  } = useEngagementTracking(deal.id, 'deal', { minDwellTime: 2000 });

  // Memoize computed values to prevent recalculation on every render
  const staffAvatars = useMemo(() => getRandomStaffImages(5, deal.id), [deal.id]);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = useMemo(() => usageInfo.timeAgo, [usageInfo]);
  const count = useMemo(() => deal.usage_count || usageInfo.count, [deal.usage_count, usageInfo.count]);

  // Calculate days remaining until expiration
  const daysRemaining = useMemo(() => {
    if (!deal.deal_end_date) return null;
    return Math.ceil((new Date(deal.deal_end_date).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24));
  }, [deal.deal_end_date]);

  // Format time remaining in a human-readable way
  const formattedTimeRemaining = useMemo(() => {
    if (daysRemaining === null || daysRemaining <= 0) return null;
    
    // Convert to years for large day counts
    if (daysRemaining > 365) {
      const years = Math.floor(daysRemaining / 365);
      return `${years}+ ${years === 1 ? 'year' : 'years'} left`;
    }
    
    // Convert to months for medium day counts
    if (daysRemaining > 30) {
      const months = Math.floor(daysRemaining / 30);
      return `${months} ${months === 1 ? 'month' : 'months'} left`;
    }
    
    // Show days for small counts
    if (daysRemaining > 1) {
      return `${daysRemaining}d left`;
    }
    
    return 'Ends today';
  }, [daysRemaining]);

  // Determine if the deal is expiring soon
  const isExpiringSoon = useMemo(() => {
    return daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  }, [daysRemaining]);

  // Get the appropriate image URL with fallbacks
  const imageUrl = useMemo(() => {
    // 1. First try to get the deal image
    const dealImage = normalizeUrl(deal.imagebig_url) ||
                      normalizeUrl(deal.image_url) ||
                      normalizeUrl(deal.imagesmall_url);

    if (dealImage) return dealImage;

    // 2. Try brand logos next
    const brandLogo = normalizeUrl(deal.brands?.logo_url) ||
                      normalizeUrl(deal.brand_logo_url);

    if (brandLogo) return brandLogo;

    // 3. Try merchant logos next
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) ||
                         normalizeUrl(deal.merchant_logo_url);

    if (merchantLogo) return merchantLogo;

    // 4. Use SVG as the default fallback
    return '/placeholder-image.svg';
  }, [
    deal.imagebig_url,
    deal.image_url,
    deal.imagesmall_url,
    deal.brands?.logo_url,
    deal.brand_logo_url,
    deal.merchants?.logo_url,
    deal.merchant_logo_url
  ]);

  // Track when the card becomes visible in the viewport
  useEffect(() => {
    if (!cardRef.current) return;

    // Create an observer to track when the card becomes visible
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach(entry => {
          // Update visibility state for engagement tracking
          setIsVisible(entry.isIntersecting);

          if (entry.isIntersecting) {
            // Only track in production
            if (process.env.NODE_ENV !== 'production') {
              console.log('Deal impression tracked:', deal.id);
            } else {
              // Track the impression using Beacon API if available
              try {
                // Generate or get a session ID
                let sessionId = localStorage.getItem('vh_session_id');
                if (!sessionId) {
                  sessionId = 'session_' + Math.random().toString(36).substring(2, 15);
                  localStorage.setItem('vh_session_id', sessionId);
                }

                if (typeof navigator.sendBeacon === 'function') {
                  // Use the Beacon API for more reliable tracking
                  const trackingData = new FormData();
                  trackingData.append('deal_id', deal.id.toString());
                  trackingData.append('session_id', sessionId);
                  navigator.sendBeacon('/api/track-impression?deal_id=' + deal.id + '&session_id=' + sessionId, trackingData);
                } else {
                  // Fallback to fetch with keepalive
                  fetch('/api/track-impression?deal_id=' + deal.id + '&session_id=' + sessionId, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ deal_id: deal.id, session_id: sessionId }),
                    keepalive: true
                  });
                }
              } catch (error) {
                console.error('Error tracking impression:', error);
              }
            }
          }
        });
      },
      { threshold: 0.5 } // Card is considered visible when 50% is in view
    );

    // Start observing the card
    observer.observe(cardRef.current);

    // Clean up the observer when the component unmounts
    return () => {
      observer.disconnect();
    };
  }, [deal.id, cardRef, setIsVisible]);

  // Handle card click - reveals code, opens popup, and navigates to affiliate link
  const handleCardClick = useCallback(() => {
    // Mark this card as revealed
    setIsCodeRevealed(true);

    if (typeof window !== 'undefined') {
      // Check if this deal has been clicked before
      const clickedDeals = JSON.parse(localStorage.getItem('clickedDeals') || '{}');
      const hasBeenClickedBefore = clickedDeals[deal.id];

      // Save to localStorage that this code has been revealed
      const revealedCodes = JSON.parse(localStorage.getItem('revealedCodes') || '{}');
      revealedCodes[deal.id] = true;
      localStorage.setItem('revealedCodes', JSON.stringify(revealedCodes));

      // Mark this deal as clicked for future reference
      clickedDeals[deal.id] = true;
      localStorage.setItem('clickedDeals', JSON.stringify(clickedDeals));

      // Copy the code to clipboard if available
      if (deal.coupon_code) {
        copyToClipboard(deal.coupon_code);
      }

      // Preserve the current view mode in the URL
      const currentUrl = new URL(window.location.href);
      const viewMode = currentUrl.searchParams.get('view') || 'grid';

      // Create a new URL for the popup
      const popupUrl = new URL(window.location.href);
      popupUrl.searchParams.set('dealId', deal.id.toString());
      popupUrl.searchParams.set('showPopup', 'true');
      popupUrl.searchParams.set('view', viewMode); // Preserve the view mode

      // Always show the popup
      window.open(popupUrl.toString(), '_blank');

      // Call the parent component's showCouponModal function to show the popup
      if (onShowCouponModal) {
        onShowCouponModal(deal);
      }

      // Only navigate to the tracking URL if this is the first click
      if (!hasBeenClickedBefore && deal.tracking_url) {
        window.location.href = deal.tracking_url;
      } else if (!hasBeenClickedBefore) {
        // If no tracking URL, use the /go/id format
        window.location.href = `/go/${deal.id}`;
      }
    }
  }, [deal, onShowCouponModal]);
  
  // Alias for consistency with other components
  const handleDealClick = handleCardClick;

  // Handle copy button click - reveals code, copies to clipboard, opens popup, and navigates
  const handleCopy = useCallback((e: React.MouseEvent) => {
    e.stopPropagation(); // Prevent default card click behavior

    // Track the click for engagement metrics
    trackClick();

    // Use requestAnimationFrame to batch DOM updates and prevent forced reflow
    requestAnimationFrame(() => {
      // Reveal the code
      setIsCodeRevealed(true);

      if (typeof window !== 'undefined') {
        // Send tracking beacon for this specific deal only
        if (typeof navigator.sendBeacon === 'function') {
          try {
            const trackingData = new FormData();
            trackingData.append('deal_id', deal.id.toString());
            trackingData.append('fallback', 'true');
            navigator.sendBeacon('/api/track-click', trackingData);
            console.log(`Tracking copy for deal_id=${deal.id} only`);
          } catch (error) {
            console.error('Error sending beacon:', error);
          }
        }

        // Mark this deal as clicked
        const clickedDeals = JSON.parse(localStorage.getItem('clickedDeals') || '{}');

        // Save to localStorage that this code has been revealed
        const revealedCodes = JSON.parse(localStorage.getItem('revealedCodes') || '{}');
        revealedCodes[deal.id] = true;
        localStorage.setItem('revealedCodes', JSON.stringify(revealedCodes));

        // Mark this deal as clicked for future reference
        clickedDeals[deal.id] = true;
        localStorage.setItem('clickedDeals', JSON.stringify(clickedDeals));

        // Copy the code to clipboard
        if (deal.coupon_code) {
          copyToClipboard(deal.coupon_code);
        }

        // Preserve the current view mode in the URL
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get('view') || 'grid';

        // Create a new URL for the popup - using the current page URL
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set('dealId', deal.id.toString());
        popupUrl.searchParams.set('showPopup', 'true');
        popupUrl.searchParams.set('solidBg', 'true'); // Add solid background
        popupUrl.searchParams.set('view', viewMode); // Preserve the view mode

        // IMPORTANT: First open a new tab with the popup URL
        window.open(popupUrl.toString(), '_blank');

        // THEN navigate to the tracking URL in the current window
        if (deal.tracking_url) {
          window.location.href = deal.tracking_url;
        } else {
          // If no tracking URL, use the /go/id format
          window.location.href = `/go/${deal.id}`;
        }
      }
    });
  }, [deal, trackClick]);

  return (
    <>
      {/* Add structured data for SEO - Delayed with requestIdleCallback for better FCP */}
      {typeof window !== 'undefined' && (
        <DelayedStructuredData deal={deal} imageUrl={imageUrl} />
      )}

      <Card
        ref={cardRef}
        variant="default"
        interactive={true}
        glowEffect={true}
        className={cn(
          'optimized-deal-card deal-card relative overflow-hidden cursor-pointer w-full flex flex-col max-h-[550px]',
          'transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.01]',
          'hover:border-design-primary/50 dark:hover:border-design-primary/50',
          'border-black/15 dark:border-white/15', // Distinguishable border in both modes
          'optimized-card', // Add content-visibility optimization
          className
        )}
        style={{
          borderRadius: '25px',
          borderWidth: '1.5px'
        }}
        data-theme-border="true"
        onClick={() => {
          handleDealClick();
          trackClick();
        }}
        onMouseEnter={() => {
          handlePrefetch();
          trackMouseEnter();
        }}
        onMouseLeave={() => {
          handleCancelPrefetch();
          trackMouseLeave();
        }}
        aria-label={`Deal: ${deal.title}`}
        role="button"
        tabIndex={0}
        {...props}
      >
        {/* Anti-Bounce Link Fallback - We'll handle this in the click handler instead */}
        <div
          id={`tracking-pixel-${deal.id}`}
          style={{ position: 'absolute', opacity: 0, pointerEvents: 'none' }}
          aria-hidden="true"
        />
        {/* Image Section - 52% of card height */}
        <div className="w-full p-3" style={{ height: '52%' }}>
          <div className="flex justify-center h-full">
            <div
              className="relative overflow-hidden rounded-lg flex items-center justify-center w-full h-full image-container bg-white dark:bg-white"
              style={{
                maxHeight: 'calc(550px * 0.52 - 24px)', // 52% of 550px minus padding
                aspectRatio: '1/1',
                boxShadow: 'inset 0 0 0 1px rgba(0,0,0,0.05)',
                padding: '8px' // Add padding to ensure image doesn't touch the edges
              }}
            >
              {/* Enhanced Deal Image Component */}
              <DealImage
                src={imageUrl}
                alt={deal.title || 'Deal image'}
                width={400}
                height={400}
                priority={priority}
                fetchpriority={priority ? 'high' : 'auto'}
                className=""
                fallbackSrc="/placeholder-image.svg"
                index={0}
              />
            </div>
          </div>
        </div>

        {/* Content Section - 48% of card height */}
        <div className="px-3 pb-0 flex-1 flex flex-col" style={{ height: '48%', maxHeight: 'calc(550px * 0.48 - 24px)' }}>
          {/* Discount and Coupon Code */}
          <div className="flex justify-between items-center mb-2">
            <div className="relative">
              <div className="text-xl font-bold text-design-foreground">
                {deal.discount ?
                  <span className="font-bold text-primary dark:text-primary">
                    {deal.discount}% <span className='text-xs font-bold text-gray-900 dark:text-gray-400'>off </span>
                  </span> :
                  deal.price ?
                    `${deal.currency || '$'}${deal.price}` :
                    '20% Off'
                }
              </div>
            </div>
            <div className="flex items-center ml-1">
              <div
                className="deal-coupon-code text-xs bg-design-muted px-2 py-1 rounded-full border border-design-muted-foreground/10 relative overflow-hidden"
              >
                {deal.coupon_code ? (
                  <>
                    <span
                      className={`transition-all duration-300 ${isCodeRevealed ? 'blur-none opacity-100' : 'blur-[3px] select-none'}`}
                      aria-hidden={!isCodeRevealed}
                    >
                      {deal.coupon_code}
                    </span>
                    {isCodeRevealed && (
                      <span
                        className="absolute inset-0 bg-design-primary/10 animate-reveal-sweep"
                        aria-hidden="true"
                      ></span>
                    )}
                  </>
                ) : (
                  'NO CODE'
                )}
              </div>
            </div>
          </div>

          {/* Brand/Merchant Name with Success Rate and Limited Time */}
        <div className="text-xs text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1">
          <span
            className="font-medium truncate max-w-[120px] hover:underline cursor-help"
            title={`Brand: ${deal.merchants?.name || deal.brands?.name || 'Unknown'}`}
          >
            {deal.merchants?.name || deal.brands?.name || 'Brand'}
          </span>

          {/* Verified Badge */}
          {deal.verified && (
            <span className="c-verified-badge verified-badge ml-2 inline-flex items-center">
              <img
                src="/Vapehybrid light icon.svg"
                alt="Verified"
                className="dark:hidden inline-block"
                width="10"
                height="10"
              />
              <img
                src="/Vapehybrid dark icon.svg"
                alt="Verified"
                className="hidden dark:inline-block"
                width="10"
                height="10"
              />
              <span className="text-design-primary dark:text-design-primary text-[10px] inline-block">Verified</span>
            </span>
          )}

          {/* Success Rate Badge */}
          {deal.success_rate !== undefined && (
            <span
              className={`ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full ${
                deal.success_rate >= 90 ? 'bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground' :
                deal.success_rate >= 70 ? 'bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground' :
                'bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground'
              }`}
              title={`${Math.round(deal.success_rate || 85)}% success rate`}
            >
              <ThumbsUp size={10} className="mr-1" />
              {Math.round(deal.success_rate || 85)}%
            </span>
          )}

          {/* Limited Time Indicator */}
          {daysRemaining !== null && daysRemaining > 0 && (
            <span className="ml-2 text-xs flex items-center text-design-muted-foreground">
              <svg className="w-3 h-3 mr-1" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
              </svg>
              {daysRemaining > 365 ? 
                `${Math.floor(daysRemaining / 365)}+ ${Math.floor(daysRemaining / 365) === 1 ? 'year' : 'years'} left` : 
                daysRemaining > 30 ? 
                  `${Math.floor(daysRemaining / 30)} ${Math.floor(daysRemaining / 30) === 1 ? 'month' : 'months'} left` : 
                  daysRemaining === 1 ? 'Ends today' : `${daysRemaining}d left`
              }
            </span>
          )}
        </div>

        {/* Title with Truncation and Tooltip */}
        <h3
          className="deal-card-title line-clamp-2 mb-2 text-sm font-semibold overflow-hidden"
          title={deal.cleaned_title && deal.cleaned_title !== 'null' ? deal.cleaned_title : deal.title}
        >
          {deal.cleaned_title && deal.cleaned_title !== 'null' ? deal.cleaned_title : deal.title}
        </h3>

        {/* Usage Info - Enhanced Staff Avatars */}
        <div className="flex items-center text-xs text-design-muted-foreground mb-1">
          <div className="flex -space-x-2">
            {staffAvatars.slice(0, Math.min(3, count || 3)).map((avatar, index) => (
              <div
                key={index}
                className="relative inline-flex items-center justify-center w-5 h-5 rounded-full border-2 border-white bg-white dark:border-gray-800 dark:bg-gray-800 overflow-hidden"
                style={{
                  zIndex: 3 - index,
                  backgroundColor: avatar.color || '#6b7280' // Fallback background color
                }}
                title={`Staff member: ${avatar.name}`}
              >
                {avatar.webpPath || avatar.imagePath ? (
                  <img
                    src={avatar.webpPath || avatar.imagePath || ''}
                    alt={avatar.initials}
                    className="w-full h-full object-cover"
                    onError={(e) => {
                      // If image fails to load, show initials instead
                      const target = e.target as HTMLImageElement;
                      target.style.display = 'none';
                      // Show the fallback content
                      const fallback = target.nextElementSibling as HTMLElement;
                      if (fallback) {
                        fallback.style.display = 'flex';
                      }
                    }}
                    loading="lazy"
                  />
                ) : null}
                <div
                  className="absolute inset-0 flex items-center justify-center text-white font-bold"
                  style={{
                    display: (!avatar.webpPath && !avatar.imagePath) ? 'flex' : 'none',
                    fontSize: '8px',
                    textShadow: '0 1px 1px rgba(0,0,0,0.3)'
                  }}
                >
                  {avatar.initials}
                </div>
              </div>
            ))}
            {count > 3 && (
              <div
                className="relative flex items-center justify-center w-5 h-5 rounded-full border-2 border-white bg-gray-100 dark:border-gray-800 dark:bg-gray-700 text-gray-600 dark:text-gray-300"
                style={{
                  zIndex: 0,
                  fontSize: '8px',
                  fontWeight: 'bold',
                  boxShadow: '0 1px 2px rgba(0,0,0,0.1)'
                }}
                title={`${count - 3} more staff members`}
              >
                +{count - 3}
              </div>
            )}
          </div>
          <span className="ml-2">
            Verified <time dateTime={deal.last_verified_at} title={new Date(deal.last_verified_at || '').toLocaleString()}>{usageTimeAgo}</time> by {count || 3} staffer{(count || 3) > 1 ? 's' : ''}
          </span>
        </div>

        {/* Spacer to push action buttons to bottom */}
        <div className="flex-grow"></div>

        {/* Action Buttons Row - Improved for mobile */}
        <div
          className="flex justify-between items-center w-full mt-auto mb-2 px-2 pt-2 border-t border-design-muted border-opacity-20"
          role="group"
          aria-label="Deal actions"
          style={{ touchAction: 'manipulation' }} // Improves touch response on mobile
        >
          {/* Eye Button with Direct Navigation */}
          <button
            className="eye-button p-2 rounded-full hover:bg-design-muted transition-colors relative z-10"
            onClick={(e) => {
              e.stopPropagation();
              window.open(generateCouponUrl(deal), '_blank');
            }}
            aria-label="View coupon details"
            title="View coupon details"
            tabIndex={0}
            style={{ minWidth: '36px', minHeight: '36px' }} // Larger touch target for mobile
          >
            <Eye size={18} className="text-design-muted-foreground hover:text-design-foreground transition-colors" />
            <span className="sr-only">View deal details</span>
          </button>

          {/* Copy Code Button */}
          <button
            className="copy-code-button h-8 px-4 text-sm mx-2 transition-colors hover:bg-design-primary hover:text-white dark:hover:text-black focus:outline-none focus:ring-2 focus:ring-design-primary focus:ring-offset-2 rounded-[25px]"
            onClick={handleCopy}
            aria-label="Copy Code"
            title="Copy coupon code"
            tabIndex={0}
          >
            Copy Code
          </button>

          {/* Bookmark Button */}
          <BookmarkButton
            dealId={deal.id.toString()}
            className="bookmark-button p-2 rounded-full hover:bg-design-muted transition-colors"
          />
        </div>
        </div>
      </Card>
    </>
  );
};

// Export both named and default for compatibility
export default OptimizedDealCard;
