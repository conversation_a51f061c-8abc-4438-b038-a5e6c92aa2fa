const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["assets/js/app/design-system-lJ5ci6hi.js","assets/js/vendor/react-bq1VYqFi.js","assets/js/vendor/ui-components-_FzKGR3X.js","assets/js/vendor/utils-hwhCH1Yb.js","assets/js/featured-deals-Nf14Q-mO.js","assets/js/countdown-timer-on56VBNl.js","assets/js/flash-deals-CLVvCCUD.js","assets/js/newsletter-section-FtJve_5R.js","assets/js/proxy-BAs1uqPX.js","assets/js/faq-section-4Kz2v8GC.js","assets/js/index-tFD8qxOu.js"])))=>i.map(i=>d[i]);
import{R as e,j as t,r as s}from"../vendor/react-bq1VYqFi.js";import{t as n}from"../vendor/ui-components-_FzKGR3X.js";const o={},a=function(e,t,s){let n=Promise.resolve();if(t&&t.length>0){let e=function(e){return Promise.all(e.map((e=>Promise.resolve(e).then((e=>({status:"fulfilled",value:e})),(e=>({status:"rejected",reason:e}))))))};document.getElementsByTagName("link");const s=document.querySelector("meta[property=csp-nonce]"),a=s?.nonce||s?.getAttribute("nonce");n=e(t.map((e=>{if((e=function(e){return"/"+e}(e))in o)return;o[e]=!0;const t=e.endsWith(".css"),s=t?'[rel="stylesheet"]':"";if(document.querySelector(`link[href="${e}"]${s}`))return;const n=document.createElement("link");return n.rel=t?"stylesheet":"modulepreload",t||(n.as="script"),n.crossOrigin="",n.href=e,a&&n.setAttribute("nonce",a),document.head.appendChild(n),t?new Promise(((t,s)=>{n.addEventListener("load",t),n.addEventListener("error",(()=>s(new Error(`Unable to preload CSS for ${e}`))))})):void 0})))}function a(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return n.then((t=>{for(const e of t||[])"rejected"===e.status&&a(e.reason);return e().catch(a)}))},r=e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.X))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.DealCard})))));e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e._))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.OptimizedDealListCard})))));const i=e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e._))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.OptimizedDealListCard})))));e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.Y))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.DealListCard}))))),e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.$))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.CompactDealCard}))))),e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.Z))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.ImprovedDealCard})))));const d=e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.z))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.OptimizedDealCard}))))),l=e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.I))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.default})))));e.lazy((()=>a((()=>import("../featured-deals-Nf14Q-mO.js")),__vite__mapDeps([4,1,0,2,3,5])).then((e=>({default:e.FeaturedDeals}))))),e.lazy((()=>a((()=>import("../flash-deals-CLVvCCUD.js")),__vite__mapDeps([6,1,0,2,3,5])).then((e=>({default:e.FlashDeals}))))),e.lazy((()=>a((()=>import("../newsletter-section-FtJve_5R.js")),__vite__mapDeps([7,1,8,2])).then((e=>({default:e.NewsletterSection}))))),e.lazy((()=>a((()=>import("../faq-section-4Kz2v8GC.js")),__vite__mapDeps([9,1,8,10])).then((e=>({default:e.FAQSection}))))),e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.P))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.default}))))),e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.K))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.default}))))),e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.y))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.default}))))),e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.Q))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.Select}))))),e.lazy((()=>a((()=>import("./design-system-lJ5ci6hi.js").then((e=>e.J))),__vite__mapDeps([0,1,2,3])).then((e=>({default:e.Sheet})))));const c=({children:s,fallback:n=t.jsx("div",{className:"animate-pulse bg-design-muted rounded-lg h-40 w-full"})})=>{const[,o]=e.useTransition(),[a,r]=e.useState(!1);return e.useEffect((()=>{const e=setTimeout((()=>{o((()=>{r(!0)}))}),10);return()=>clearTimeout(e)}),[]),a?t.jsx(e.Suspense,{fallback:n,children:s}):t.jsx("div",{className:"min-h-[40px]",children:n})},u=[{question:"How do I use a coupon code?",answer:"To use a coupon code, click on the 'Reveal Code' button on any deal. This will copy the code to your clipboard and open the merchant's website in a new tab. Paste the code at checkout to apply your discount."},{question:"Why didn't my code work?",answer:"Coupon codes may not work for several reasons: the code might have expired, it could be limited to specific products, or it might not be combinable with other offers. We verify our codes regularly, but merchant policies can change without notice."},{question:"How often are deals updated?",answer:"We update our deals daily. Our team verifies each deal to ensure it's still valid and offers the best possible savings for our users."},{question:"Do I have to pay extra for using these links?",answer:"No, using our links is completely free for you. When you make a purchase through our links, we may earn a commission from the merchant at no additional cost to you. This helps us maintain and improve our service."}];function m({faqs:e=u}){const[n,o]=s.useState(null);return t.jsxs("div",{className:"w-full",children:[t.jsx("script",{type:"application/ld+json",dangerouslySetInnerHTML:{__html:(()=>{const t={"@context":"https://schema.org","@type":"FAQPage",mainEntity:e.map((e=>({"@type":"Question",name:e.question,acceptedAnswer:{"@type":"Answer",text:e.answer}})))};return JSON.stringify(t)})()}}),t.jsxs("div",{className:"mb-8 text-center",children:[t.jsx("h2",{className:"text-2xl font-bold text-design-foreground mb-3",children:"Frequently Asked Questions"}),t.jsx("p",{className:"text-design-muted-foreground max-w-2xl mx-auto",children:"Find answers to common questions about using our deals and coupons."})]}),t.jsx("div",{className:"space-y-4 max-w-3xl mx-auto",children:e.map(((e,s)=>t.jsxs("div",{className:"bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 rounded-lg overflow-hidden transition-all duration-300 "+(n===s?"shadow-md":"shadow-sm"),children:[t.jsxs("button",{onClick:()=>(e=>{o(n===e?null:e)})(s),className:"flex justify-between items-center w-full p-5 text-left font-medium text-design-foreground focus:outline-none",children:[t.jsxs("span",{className:"flex items-center",children:[t.jsx("span",{className:"inline-flex items-center justify-center w-8 h-8 rounded-full mr-3 transition-colors duration-300 "+(n===s?"bg-primary/20 text-primary":"bg-primary/10 text-design-muted-foreground"),children:t.jsxs("svg",{xmlns:"http://www.w3.org/2000/svg",width:"18",height:"18",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",children:[t.jsx("path",{d:"M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"}),t.jsx("circle",{cx:"12",cy:"12",r:"10"}),t.jsx("line",{x1:"12",y1:"17",x2:"12",y2:"17"})]})}),e.question]}),t.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",width:"20",height:"20",viewBox:"0 0 24 24",fill:"none",stroke:"currentColor",strokeWidth:"2",strokeLinecap:"round",strokeLinejoin:"round",className:"transition-transform duration-300 "+(n===s?"rotate-180 text-primary":"text-design-muted-foreground"),children:t.jsx("polyline",{points:"6 9 12 15 18 9"})})]}),t.jsx("div",{className:"overflow-hidden transition-all duration-300 "+(n===s?"max-h-96":"max-h-0"),children:t.jsx("div",{className:"p-5 pt-0 text-design-muted-foreground border-t border-design-border/10",children:e.answer})})]},s)))})]})}const h=({deals:e,initialViewMode:o,onViewModeChange:a})=>{const d=s.useMemo((()=>e.slice(0,24)),[e]),[u,m]=s.useState(o),[h,p]=s.useState(!1),[f,g]=s.useState(null),[_,y]=s.useState({});s.useEffect((()=>{if("undefined"!=typeof window){const t=new URLSearchParams(window.location.search),s=t.get("dealId"),n=t.get("showPopup"),o=t.get("view");if("grid"!==o&&"list"!==o||m(o),s&&"true"===n){const t=e.find((e=>e.id.toString()===s));t&&w(t)}const a=JSON.parse(localStorage.getItem("revealedCodes")||"{}");y(a)}}),[e]);const w=e=>{g(e),p(!0)};return t.jsxs("div",{children:[t.jsx("div",{className:"deals-container "+("grid"===u?"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8":"flex flex-col gap-8"),children:d.map(((e,n)=>{let o=e.merchants?.name||e.merchant_name||"Brand";"Vapesourcing Electronics Co.,Ltd."===o&&(o="Vapesourcing");const a=s.useMemo((()=>({...e,merchants:e.merchants?{...e.merchants,name:o}:{name:o},brands:e.brands||{name:e.brand_name||""}})),[e,o]),d=n<6;return"grid"===u?t.jsx(c,{fallback:t.jsx("div",{className:"animate-pulse bg-design-muted rounded-lg h-[320px] w-full"}),children:t.jsx(r,{deal:a,onShowCouponModal:w,isRevealed:_[e.id]||!1,view:"grid",priority:d})},e.id):t.jsx(c,{fallback:t.jsx("div",{className:"animate-pulse bg-design-muted rounded-lg h-[160px] w-full"}),children:t.jsx(i,{deal:a,onShowCouponModal:w,isRevealed:_[e.id]||!1,priority:d})},e.id)}))}),h&&f&&t.jsx(c,{fallback:t.jsx("div",{className:"fixed inset-0 z-50 flex items-center justify-center bg-black/50",children:t.jsx("div",{className:"animate-pulse bg-design-card rounded-lg h-[400px] w-[90%] max-w-md"})}),children:t.jsx(l,{deal:f,onClose:()=>{if(p(!1),g(null),"undefined"!=typeof window){const e=new URL(window.location.href);e.searchParams.delete("dealId"),e.searchParams.delete("showPopup");const t=e.searchParams.get("view"),s=new URL(window.location.pathname,window.location.origin);t&&s.searchParams.set("view",t),window.history.replaceState({},"",s.toString())}},onVote:(e,t)=>{fetch("/api/vote",{method:"POST",headers:{"Content-Type":"application/json"},body:JSON.stringify({deal_id:e,vote_good:t})}).then((e=>{if(e.ok){const e=t?"Thanks for your positive feedback!":"Thanks for your feedback!";n.success(e)}else n.error("Failed to record your vote. Please try again.")})).catch((e=>{n.error("Failed to record your vote. Please try again.")}))}})})]})};export{m as D,d as L,c as S,a as _,i as a,l as b,h as c};
