/**
 * JavaScript Optimization Script
 * 
 * This script:
 * 1. Analyzes JavaScript files for unused code
 * 2. Suggests code splitting opportunities
 * 3. Helps optimize JavaScript bundles
 * 
 * Usage:
 * node scripts/optimize-js.js
 */

import fs from 'fs';
import path from 'path';
import { glob } from 'glob';
import { execSync } from 'child_process';

// Configuration
const config = {
  // Source directories to scan for JavaScript/TypeScript files
  sourceDirs: ['src'],
  // File extensions to process
  extensions: ['js', 'jsx', 'ts', 'tsx'],
  // Directories to exclude
  excludeDirs: ['node_modules', 'dist', 'build', '.astro'],
  // Maximum bundle size in KB before suggesting code splitting
  maxBundleSizeKB: 200
};

// Find all JavaScript/TypeScript files
const findJsFiles = async () => {
  let allFiles = [];
  
  for (const dir of config.sourceDirs) {
    for (const ext of config.extensions) {
      const pattern = `${dir}/**/*.${ext}`;
      const files = await glob(pattern, {
        ignore: config.excludeDirs.map(d => `**/${d}/**`)
      });
      allFiles = [...allFiles, ...files];
    }
  }
  
  return allFiles;
};

// Analyze imports in a file
const analyzeImports = (filePath) => {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const imports = [];
    
    // Match ES6 imports
    const es6ImportRegex = /import\s+(?:{([^}]+)}|\*\s+as\s+([^\s]+)|([^\s,{]+))\s+from\s+['"]([^'"]+)['"]/g;
    let match;
    
    while ((match = es6ImportRegex.exec(content)) !== null) {
      const namedImports = match[1] ? match[1].split(',').map(s => s.trim()) : [];
      const namespaceImport = match[2] || null;
      const defaultImport = match[3] || null;
      const source = match[4];
      
      imports.push({
        type: 'es6',
        source,
        namedImports,
        namespaceImport,
        defaultImport
      });
    }
    
    // Match require imports
    const requireRegex = /(?:const|let|var)\s+(?:{([^}]+)}|([^\s,{]+))\s+=\s+require\s*\(\s*['"]([^'"]+)['"]\s*\)/g;
    
    while ((match = requireRegex.exec(content)) !== null) {
      const namedImports = match[1] ? match[1].split(',').map(s => s.trim()) : [];
      const defaultImport = match[2] || null;
      const source = match[3];
      
      imports.push({
        type: 'require',
        source,
        namedImports,
        defaultImport
      });
    }
    
    return imports;
  } catch (error) {
    console.error(`Error analyzing imports in ${filePath}:`, error);
    return [];
  }
};

// Analyze file size and complexity
const analyzeFileSize = (filePath) => {
  try {
    const stats = fs.statSync(filePath);
    const sizeKB = stats.size / 1024;
    
    return {
      path: filePath,
      sizeKB: sizeKB.toFixed(2),
      isLarge: sizeKB > config.maxBundleSizeKB
    };
  } catch (error) {
    console.error(`Error analyzing file size for ${filePath}:`, error);
    return {
      path: filePath,
      sizeKB: 0,
      isLarge: false
    };
  }
};

// Find unused dependencies
const findUnusedDependencies = () => {
  try {
    // Run npm-check to find unused dependencies
    const output = execSync('npx npm-check --skip-unused', { encoding: 'utf8' });
    console.log('Dependency Analysis:');
    console.log(output);
  } catch (error) {
    console.error('Error checking dependencies:', error.message);
  }
};

// Suggest code splitting opportunities
const suggestCodeSplitting = (files) => {
  const largeFiles = files.filter(file => file.isLarge);
  
  if (largeFiles.length === 0) {
    console.log('No large files found that need code splitting.');
    return;
  }
  
  console.log('\nCode Splitting Suggestions:');
  console.log('The following files are large and may benefit from code splitting:');
  
  largeFiles.forEach(file => {
    console.log(`- ${file.path} (${file.sizeKB} KB)`);
  });
  
  console.log('\nConsider implementing dynamic imports for these files:');
  console.log('Example: const Component = React.lazy(() => import(\'./LargeComponent\'));');
};

// Main function
const optimizeJs = async () => {
  console.log('Starting JavaScript optimization analysis...');
  
  // Find all JS/TS files
  const files = await findJsFiles();
  console.log(`Found ${files.length} JavaScript/TypeScript files to analyze`);
  
  // Analyze file sizes
  const fileSizeAnalysis = files.map(analyzeFileSize);
  
  // Analyze imports for each file
  const fileImportAnalysis = files.map(file => ({
    path: file,
    imports: analyzeImports(file)
  }));
  
  // Find unused dependencies
  findUnusedDependencies();
  
  // Suggest code splitting
  suggestCodeSplitting(fileSizeAnalysis);
  
  console.log('\nJavaScript optimization analysis complete!');
  console.log('Run the following commands for further optimization:');
  console.log('1. npx depcheck - to find unused dependencies');
  console.log('2. npx webpack-bundle-analyzer - to visualize bundle sizes');
};

// Run the script
optimizeJs().catch(error => {
  console.error('Error running JavaScript optimization:', error);
  process.exit(1);
});
