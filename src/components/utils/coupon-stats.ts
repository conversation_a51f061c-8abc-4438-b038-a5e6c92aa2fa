/**
 * Coupon statistics and verification utilities
 */

export interface VerificationData {
  timestamp: string;
  type: 'verified' | 'used' | 'saved';
  amount?: number;
}

export function getVerificationTimestamp(): string {
  const now = new Date();
  const hoursAgo = Math.floor(Math.random() * 12) + 1; // 1-12 hours ago
  const verificationTime = new Date(now.getTime() - (hoursAgo * 60 * 60 * 1000));
  
  return `${hoursAgo} hours ago`;
}

export function getSocialProofMessage(): string {
  const messages = [
    "Last verified by <PERSON>",
    "Recently used by <PERSON>",
    "Confirmed working by <PERSON>",
    "Verified by <PERSON>",
    "Used successfully by <PERSON>"
  ];
  
  return messages[Math.floor(Math.random() * messages.length)];
}

export function getUsageStats(): { saved: number; users: number } {
  // Generate realistic usage statistics
  const saved = Math.floor(Math.random() * 500) + 50; // $50-$550
  const users = Math.floor(Math.random() * 100) + 20; // 20-120 users
  
  return { saved, users };
}

export function formatSavingsAmount(amount: number): string {
  return `$${amount.toFixed(2)}`;
}

export function getRecentActivity(): string {
  const activities = [
    "Someone saved $25.99 with this coupon",
    "A user just used this deal successfully",
    "This coupon helped save $18.50 recently",
    "Recently verified as working",
    "Just helped someone save money"
  ];
  
  return activities[Math.floor(Math.random() * activities.length)];
}
