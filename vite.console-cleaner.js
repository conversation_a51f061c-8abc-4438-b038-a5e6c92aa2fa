/**
 * Vite Plugin to Remove Console Logs in Production
 * Automatically strips console.log, console.warn, console.debug statements
 * Keeps console.error for critical error reporting
 */

export function consoleCleanerPlugin() {
  return {
    name: 'console-cleaner',
    enforce: 'pre',
    
    transform(code, id) {
      // Only process JavaScript/TypeScript files
      if (!/\.(js|ts|jsx|tsx)$/.test(id)) {
        return null;
      }
      
      // Skip node_modules
      if (id.includes('node_modules')) {
        return null;
      }
      
      // In production, remove console statements except console.error
      if (process.env.NODE_ENV === 'production') {
        let cleanedCode = code;
        
        // Remove console.log statements
        cleanedCode = cleanedCode.replace(/console\.log\s*\([^)]*\)\s*;?/g, '');
        
        // Remove console.warn statements
        cleanedCode = cleanedCode.replace(/console\.warn\s*\([^)]*\)\s*;?/g, '');
        
        // Remove console.debug statements
        cleanedCode = cleanedCode.replace(/console\.debug\s*\([^)]*\)\s*;?/g, '');
        
        // Remove console.info statements
        cleanedCode = cleanedCode.replace(/console\.info\s*\([^)]*\)\s*;?/g, '');
        
        // Keep console.error for critical error reporting
        
        // Only return if we made changes
        if (cleanedCode !== code) {
          return {
            code: cleanedCode,
            map: null // Skip source maps for console removal
          };
        }
      }
      
      return null;
    },
  };
}

/**
 * Alternative: More aggressive console removal using AST parsing
 * Uncomment this if the regex approach doesn't catch all cases
 */
/*
import { parse } from '@babel/parser';
import traverse from '@babel/traverse';
import generate from '@babel/generator';

export function advancedConsoleCleanerPlugin() {
  return {
    name: 'advanced-console-cleaner',
    enforce: 'pre',
    
    transform(code, id) {
      if (!/\.(js|ts|jsx|tsx)$/.test(id) || id.includes('node_modules')) {
        return null;
      }
      
      if (process.env.NODE_ENV === 'production') {
        try {
          const ast = parse(code, {
            sourceType: 'module',
            plugins: ['typescript', 'jsx']
          });
          
          let hasChanges = false;
          
          traverse(ast, {
            CallExpression(path) {
              const { node } = path;
              
              // Check if it's a console method call
              if (
                node.callee.type === 'MemberExpression' &&
                node.callee.object.name === 'console' &&
                ['log', 'warn', 'debug', 'info'].includes(node.callee.property.name)
              ) {
                path.remove();
                hasChanges = true;
              }
            }
          });
          
          if (hasChanges) {
            const result = generate(ast);
            return {
              code: result.code,
              map: null
            };
          }
        } catch (error) {
          // Fallback to original code if parsing fails
          console.warn('Failed to parse file for console removal:', id);
        }
      }
      
      return null;
    },
  };
}
*/
