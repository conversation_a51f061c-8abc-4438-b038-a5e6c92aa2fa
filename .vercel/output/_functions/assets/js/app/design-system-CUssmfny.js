import { jsx, jsxs, Fragment } from 'react/jsx-runtime';
import * as React from 'react';
import React__default, { useRef, useState, useMemo, useCallback, useEffect, forwardRef, memo } from 'react';
import { Search, Sun, Moon, Heart, X, Menu, ChevronRight, Eye, ThumbsUp, ChevronDown, Check, Copy, ThumbsDown, ExternalLink, ShieldCheck, Grid, List, Filter } from 'lucide-react';
import * as TooltipPrimitive from '@radix-ui/react-tooltip';
import { toast, Toaster } from 'sonner';
import { clsx } from 'clsx';
import * as SelectPrimitive from '@radix-ui/react-select';
import { c as createAstro, a as createComponent, m as maybeRenderHead, b as addAttribute, r as renderTemplate, s as spreadAttributes } from '../astro/server-CU4H3-CM.js';
import 'kleur/colors';
import * as SheetPrimitive from '@radix-ui/react-dialog';
import { cva } from 'class-variance-authority';
import * as SliderPrimitive from '@radix-ui/react-slider';
import * as CheckboxPrimitive from '@radix-ui/react-checkbox';
import { twMerge } from 'tailwind-merge';

function cn(...inputs) {
  return twMerge(clsx(inputs));
}

const TooltipProvider = TooltipPrimitive.Provider;
const TooltipRoot = TooltipPrimitive.Root;
const TooltipTrigger = TooltipPrimitive.Trigger;
const TooltipContent = React.forwardRef(({ className, sideOffset = 4, ...props }, ref) => /* @__PURE__ */ jsx(
  TooltipPrimitive.Content,
  {
    ref,
    sideOffset,
    className: cn(
      "z-50 overflow-hidden rounded-md border border-design-border bg-design-card px-3 py-1.5 text-sm text-design-foreground shadow-md animate-in fade-in-0 zoom-in-95 data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=closed]:zoom-out-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2",
      className
    ),
    ...props
  }
));
TooltipContent.displayName = TooltipPrimitive.Content.displayName;

function toggleDarkMode() {
  if (typeof window !== "undefined") {
    const isDarkMode2 = document.documentElement.classList.contains("dark");
    if (isDarkMode2) {
      document.documentElement.classList.remove("dark");
      localStorage.setItem("darkMode", "false");
    } else {
      document.documentElement.classList.add("dark");
      localStorage.setItem("darkMode", "true");
    }
  }
}
function isDarkMode() {
  if (typeof window !== "undefined") {
    return document.documentElement.classList.contains("dark");
  }
  return false;
}

function trackNavigationEvent(eventName, properties = {}) {
  if (typeof window !== "undefined" && window.gtag) {
    window.gtag("event", eventName, {
      event_category: "Navigation",
      ...properties,
      // Ensure no PII is tracked
      anonymize_ip: true
    });
  }
  if (typeof window !== "undefined" && window.plausible) {
    window.plausible(eventName, { props: properties });
  }
}

function Navbar({ children }) {
  const isMounted = useRef(false);
  const menuButtonRef = useRef(null);
  const navRef = useRef(null);
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isDarkMode$1, setIsDarkMode] = useState(false);
  const [bookmarksCount, setBookmarksCount] = useState(0);
  const [bookmarksLoading, setBookmarksLoading] = useState(true);
  const [activeElement, setActiveElement] = useState(null);
  const [currentPath, setCurrentPath] = useState("");
  const [announcement, setAnnouncement] = useState("");
  const navItems = useMemo(() => [
    { label: "Coupons", href: "/coupons" },
    { label: "Brands", href: "/coupons/brands" },
    { label: "Merchants", href: "/coupons/merchants" },
    { label: "Categories", href: "/coupons/categories" }
  ], []);
  const computedValues = useMemo(() => ({
    headerClasses: cn(
      "relative z-50 w-full transition-all duration-300",
      isScrolled ? "nav-backdrop border-b border-design-border shadow-sm" : "bg-transparent backdrop-blur-0 border-b border-transparent",
      "supports-[backdrop-filter]:bg-design-background/80"
    ),
    mobileMenuClasses: cn(
      "mobile-menu md:hidden fixed top-0 left-0 right-0 bg-white dark:bg-gray-900 border-b border-design-border shadow-lg",
      "transform transition-all duration-300 ease-out",
      "overflow-hidden",
      isMobileMenuOpen ? "opacity-100 translate-y-0 visible" : "opacity-0 -translate-y-full invisible",
      "origin-top z-50"
    ),
    bookmarksAriaLabel: `Saved Deals${bookmarksCount > 0 ? ` (${bookmarksCount} items)` : ""}`,
    themeToggleAriaLabel: isDarkMode$1 ? "Switch to light mode" : "Switch to dark mode",
    menuToggleAriaLabel: isMobileMenuOpen ? "Close menu" : "Open menu"
  }), [isScrolled, isMobileMenuOpen, bookmarksCount, isDarkMode$1]);
  const toggleDarkMode$1 = useCallback(() => {
    toggleDarkMode();
    const newDarkMode = isDarkMode();
    setIsDarkMode(newDarkMode);
    setAnnouncement(newDarkMode ? "Switched to dark mode" : "Switched to light mode");
    return newDarkMode;
  }, []);
  const handleKeyDown = (e, isMobile = false) => {
    if (!navRef.current) return;
    const navElements = Array.from(navRef.current.querySelectorAll("a, button"));
    const currentIndex = navElements.findIndex((el) => el === document.activeElement);
    let nextIndex = currentIndex;
    switch (e.key) {
      case "ArrowRight":
      case "ArrowDown":
        e.preventDefault();
        nextIndex = (currentIndex + 1) % navElements.length;
        break;
      case "ArrowLeft":
      case "ArrowUp":
        e.preventDefault();
        nextIndex = (currentIndex - 1 + navElements.length) % navElements.length;
        break;
      case "Home":
        e.preventDefault();
        nextIndex = 0;
        break;
      case "End":
        e.preventDefault();
        nextIndex = navElements.length - 1;
        break;
      case "Escape":
        if (isMobile) {
          e.preventDefault();
          setIsMobileMenuOpen(false);
          menuButtonRef.current?.focus();
        }
        return;
      default:
        return;
    }
    navElements[nextIndex].focus();
  };
  const toggleMobileMenu = () => {
    const newState = !isMobileMenuOpen;
    setIsMobileMenuOpen(newState);
    setAnnouncement(newState ? "Menu opened" : "Menu closed");
    if (!newState) {
      setTimeout(() => menuButtonRef.current?.focus(), 0);
    }
  };
  useEffect(() => {
    isMounted.current = true;
    setBookmarksLoading(true);
    const loadingTime = Math.random() * 300 + 200;
    const loadStartTime = Date.now();
    const timer = setTimeout(() => {
      if (typeof window !== "undefined" && isMounted.current) {
        try {
          const storedBookmarkIds = localStorage.getItem("bookmarkIds");
          if (storedBookmarkIds && isMounted.current) {
            const bookmarkIds = JSON.parse(storedBookmarkIds);
            setBookmarksCount(bookmarkIds.length);
            if (bookmarkIds.length > 0) {
              trackNavigationEvent("bookmark_count_loaded", {
                count: bookmarkIds.length,
                loading_time: Date.now() - loadStartTime,
                has_bookmarks: true
              });
            }
          }
          setBookmarksLoading(false);
          const handleStorageChange = (e) => {
            if (e.key === "bookmarkIds" && isMounted.current) {
              try {
                const newBookmarkIds = e.newValue ? JSON.parse(e.newValue) : [];
                const oldCount = bookmarksCount;
                setBookmarksCount(newBookmarkIds.length);
                trackNavigationEvent("bookmark_count_changed", {
                  old_count: oldCount,
                  new_count: newBookmarkIds.length,
                  change_source: "storage_event",
                  change_type: newBookmarkIds.length > oldCount ? "added" : "removed"
                });
              } catch (error) {
                console.error("Error parsing bookmarks from storage event:", error);
              }
            }
          };
          window.addEventListener("storage", handleStorageChange);
          const handleCustomUpdate = () => {
            if (isMounted.current) {
              try {
                const storedBookmarkIds2 = localStorage.getItem("bookmarkIds");
                const newCount = storedBookmarkIds2 ? JSON.parse(storedBookmarkIds2).length : 0;
                const oldCount = bookmarksCount;
                setBookmarksCount(newCount);
                trackNavigationEvent("bookmark_count_changed", {
                  old_count: oldCount,
                  new_count: newCount,
                  change_source: "custom_event",
                  change_type: newCount > oldCount ? "added" : "removed"
                });
              } catch (error) {
                setBookmarksCount(0);
              }
            }
          };
          const handleBookmarkChange = (e) => {
            if (isMounted.current) {
              try {
                const newBookmarkIds = e.detail ? e.detail : [];
                const oldCount = bookmarksCount;
                setBookmarksCount(newBookmarkIds.length);
                trackNavigationEvent("bookmark_count_changed", {
                  old_count: oldCount,
                  new_count: newBookmarkIds.length,
                  change_source: "bookmark_change_event",
                  change_type: newBookmarkIds.length > oldCount ? "added" : "removed",
                  event_detail: !!e.detail
                });
              } catch (error) {
                console.error("Error handling custom bookmark change event:", error);
                setBookmarksCount(0);
              }
            }
          };
          window.addEventListener("savedDealsUpdated", handleCustomUpdate);
          window.addEventListener("bookmarkChange", handleBookmarkChange);
          return () => {
            window.removeEventListener("storage", handleStorageChange);
            window.removeEventListener("savedDealsUpdated", handleCustomUpdate);
            window.removeEventListener("bookmarkChange", handleBookmarkChange);
          };
        } catch (error) {
          console.error("Error loading bookmarks count:", error);
          setBookmarksLoading(false);
        }
      }
    }, loadingTime);
    return () => {
      clearTimeout(timer);
      isMounted.current = false;
    };
  }, []);
  const debouncedScrollHandler = useCallback(() => {
    let timeoutId;
    return () => {
      clearTimeout(timeoutId);
      timeoutId = setTimeout(() => {
        setIsScrolled(window.scrollY > 10);
      }, 10);
    };
  }, []);
  useEffect(() => {
    const handleScroll = debouncedScrollHandler();
    window.addEventListener("scroll", handleScroll, { passive: true });
    if (typeof window !== "undefined") {
      setIsDarkMode(isDarkMode());
    }
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [debouncedScrollHandler]);
  const handleGlobalKeyDown = useCallback((e) => {
    if ((e.metaKey || e.ctrlKey) && e.key === "k") {
      e.preventDefault();
      window.location.href = "/search";
      setAnnouncement("Navigating to search page");
    }
    if (e.key === "Escape") {
      if (isMobileMenuOpen) {
        toggleMobileMenu();
        trackNavigationEvent("mobile_menu_closed", {
          method: "escape_key"
        });
      }
    }
    if ((e.metaKey || e.ctrlKey) && e.key === "b") {
      e.preventDefault();
      window.location.href = "/bookmarks";
      trackNavigationEvent("keyboard_shortcut_used", {
        shortcut: "cmd_b",
        action: "open_bookmarks",
        platform: e.metaKey ? "mac" : "windows"
      });
    }
    if ((e.metaKey || e.ctrlKey) && e.key === "d") {
      e.preventDefault();
      window.location.href = "/coupons";
      trackNavigationEvent("keyboard_shortcut_used", {
        shortcut: "cmd_d",
        action: "open_coupons",
        platform: e.metaKey ? "mac" : "windows"
      });
    }
  }, [isMobileMenuOpen]);
  useEffect(() => {
    setIsDarkMode(isDarkMode());
    if (typeof window !== "undefined") {
      setCurrentPath(window.location.pathname);
    }
    document.addEventListener("keydown", handleGlobalKeyDown);
    setTimeout(() => {
      setBookmarksLoading(false);
    }, 1e3);
    return () => {
      document.removeEventListener("keydown", handleGlobalKeyDown);
    };
  }, [handleGlobalKeyDown]);
  useEffect(() => {
    if (!isMobileMenuOpen) return;
    const mobileMenu = document.getElementById("mobile-navigation");
    if (!mobileMenu) return;
    const focusableElements = mobileMenu.querySelectorAll(
      'a[href], button:not([disabled]), [tabindex]:not([tabindex="-1"])'
    );
    const firstElement = focusableElements[0];
    const lastElement = focusableElements[focusableElements.length - 1];
    const handleKeyDown2 = (e) => {
      if (e.key === "Escape") {
        e.preventDefault();
        toggleMobileMenu();
        return;
      }
      if (e.key !== "Tab") return;
      if (e.shiftKey) {
        if (document.activeElement === firstElement) {
          e.preventDefault();
          lastElement?.focus();
        }
      } else {
        if (document.activeElement === lastElement) {
          e.preventDefault();
          firstElement?.focus();
        }
      }
    };
    document.addEventListener("keydown", handleKeyDown2);
    setTimeout(() => firstElement?.focus(), 0);
    return () => {
      document.removeEventListener("keydown", handleKeyDown2);
    };
  }, [isMobileMenuOpen]);
  return /* @__PURE__ */ jsxs(TooltipProvider, { children: [
    /* @__PURE__ */ jsx(
      "div",
      {
        "aria-live": "polite",
        "aria-atomic": "true",
        className: "sr-only",
        role: "status",
        children: announcement
      }
    ),
    /* @__PURE__ */ jsx(
      "a",
      {
        href: "#main-content",
        className: "sr-only focus:not-sr-only focus:absolute focus:top-4 focus:left-4 focus:z-[100] focus:px-4 focus:py-2 focus:bg-design-primary focus:text-white focus:rounded-md focus:shadow-lg transition-all",
        onFocus: (e) => e.currentTarget.classList.remove("sr-only"),
        onBlur: (e) => e.currentTarget.classList.add("sr-only"),
        children: "Skip to main content"
      }
    ),
    /* @__PURE__ */ jsxs(
      "header",
      {
        ref: navRef,
        role: "banner",
        className: computedValues.headerClasses,
        style: {
          "--scroll-offset": isScrolled ? "0.25rem" : "0",
          boxShadow: isScrolled ? "0 1px 3px rgba(0, 0, 0, 0.1)" : "0 1px 1px rgba(0, 0, 0, 0.02)"
        },
        children: [
          /* @__PURE__ */ jsxs("div", { className: "container mx-auto flex h-14 sm:h-16 md:h-20 items-center justify-between px-4 md:px-8 lg:px-12", children: [
            /* @__PURE__ */ jsxs("a", { href: "/", className: "flex items-center", "aria-label": "VapeHybrid Home", children: [
              /* @__PURE__ */ jsxs("div", { className: "logo-placeholder relative hidden md:block", style: { width: "160px", height: "32px" }, children: [
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/logo-dark.svg",
                    alt: "VapeHybrid",
                    className: "h-8 w-auto absolute top-0 left-0 hidden dark:block",
                    width: "160",
                    height: "32",
                    loading: "eager"
                  }
                ),
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/logo-light.svg",
                    alt: "VapeHybrid",
                    className: "h-8 w-auto absolute top-0 left-0 block dark:hidden",
                    width: "160",
                    height: "32",
                    loading: "eager"
                  }
                )
              ] }),
              /* @__PURE__ */ jsxs("div", { className: "logo-placeholder relative md:hidden", style: { width: "32px", height: "32px" }, children: [
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid dark icon.svg",
                    alt: "VapeHybrid",
                    className: "h-8 w-8 absolute top-0 left-0 hidden dark:block",
                    width: "32",
                    height: "32",
                    loading: "eager"
                  }
                ),
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid light icon.svg",
                    alt: "VapeHybrid",
                    className: "h-8 w-8 absolute top-0 left-0 block dark:hidden",
                    width: "32",
                    height: "32",
                    loading: "eager"
                  }
                )
              ] })
            ] }),
            /* @__PURE__ */ jsx(
              "nav",
              {
                className: "hidden md:flex items-center gap-6",
                "aria-label": "Main navigation",
                role: "navigation",
                children: navItems.map((item) => /* @__PURE__ */ jsx(
                  "a",
                  {
                    href: item.href,
                    className: cn(
                      "nav-link text-design-foreground hover:text-design-primary font-medium",
                      currentPath === item.href && "text-design-primary"
                    ),
                    "aria-current": currentPath === item.href ? "page" : void 0,
                    children: item.label
                  },
                  item.href
                ))
              }
            ),
            /* @__PURE__ */ jsx("div", { className: "hidden md:flex items-center gap-3", children: /* @__PURE__ */ jsxs(TooltipProvider, { children: [
              /* @__PURE__ */ jsxs(TooltipRoot, { children: [
                /* @__PURE__ */ jsx(TooltipTrigger, { asChild: true, children: /* @__PURE__ */ jsx(
                  "a",
                  {
                    href: "/search",
                    className: "flex items-center justify-center w-10 h-10 hover:bg-design-accent/10 rounded-md transition-colors",
                    "aria-label": "Search deals",
                    children: /* @__PURE__ */ jsx(Search, { size: 20, className: "text-design-foreground dark:text-white" })
                  }
                ) }),
                /* @__PURE__ */ jsx(TooltipContent, { children: /* @__PURE__ */ jsx("p", { children: "Search deals (⌘K)" }) })
              ] }),
              /* @__PURE__ */ jsxs(TooltipRoot, { children: [
                /* @__PURE__ */ jsx(TooltipTrigger, { asChild: true, children: /* @__PURE__ */ jsx(
                  "button",
                  {
                    onClick: toggleDarkMode$1,
                    "aria-label": isDarkMode$1 ? "Switch to light mode" : "Switch to dark mode",
                    className: "flex items-center justify-center w-10 h-10 hover:bg-design-accent/10 rounded-md transition-colors",
                    children: isDarkMode$1 ? /* @__PURE__ */ jsx(Sun, { size: 20, className: "text-design-foreground dark:text-white" }) : /* @__PURE__ */ jsx(Moon, { size: 20, className: "text-design-foreground dark:text-white" })
                  }
                ) }),
                /* @__PURE__ */ jsx(TooltipContent, { children: /* @__PURE__ */ jsx("p", { children: isDarkMode$1 ? "Switch to light mode" : "Switch to dark mode" }) })
              ] }),
              /* @__PURE__ */ jsxs(TooltipRoot, { children: [
                /* @__PURE__ */ jsx(TooltipTrigger, { asChild: true, children: /* @__PURE__ */ jsxs(
                  "a",
                  {
                    href: "/bookmarks",
                    "aria-label": `Saved Deals${bookmarksCount > 0 ? ` (${bookmarksCount} items)` : ""}`,
                    "aria-describedby": bookmarksCount > 0 ? "bookmarks-count-desc" : void 0,
                    className: `flex items-center justify-center w-10 h-10 hover:bg-design-accent/10 rounded-md transition-colors relative ${bookmarksCount > 0 ? "has-badge overflow-visible" : ""}`,
                    children: [
                      /* @__PURE__ */ jsx(Heart, { size: 20, className: "text-design-foreground dark:text-white" }),
                      bookmarksLoading ? /* @__PURE__ */ jsx("span", { className: "bookmark-count-skeleton absolute -top-1 -right-1 z-10" }) : bookmarksCount > 0 ? /* @__PURE__ */ jsxs(Fragment, { children: [
                        /* @__PURE__ */ jsx("span", { className: cn(
                          "nav-badge absolute -top-1 -right-1 bg-design-primary text-white text-xs rounded-full w-5 h-5 flex items-center justify-center z-10",
                          bookmarksLoading && "loading"
                        ), children: bookmarksCount > 9 ? "9+" : bookmarksCount }),
                        /* @__PURE__ */ jsxs("span", { id: "bookmarks-count-desc", className: "sr-only", children: [
                          "You have ",
                          bookmarksCount,
                          " saved deal",
                          bookmarksCount !== 1 ? "s" : ""
                        ] })
                      ] }) : null
                    ]
                  }
                ) }),
                /* @__PURE__ */ jsx(TooltipContent, { children: /* @__PURE__ */ jsxs("p", { children: [
                  "Saved Deals",
                  bookmarksCount > 0 ? ` (${bookmarksCount})` : ""
                ] }) })
              ] })
            ] }) }),
            /* @__PURE__ */ jsxs("div", { className: "flex md:hidden items-center gap-1", children: [
              /* @__PURE__ */ jsxs(TooltipProvider, { children: [
                /* @__PURE__ */ jsxs(TooltipRoot, { children: [
                  /* @__PURE__ */ jsx(TooltipTrigger, { asChild: true, children: /* @__PURE__ */ jsx(
                    "a",
                    {
                      href: "/search",
                      className: cn(
                        "nav-action-button relative inline-flex items-center justify-center",
                        "w-10 h-10 rounded-full",
                        "text-design-foreground hover:text-design-primary",
                        "hover:bg-design-accent/10",
                        "focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary",
                        "dark:text-white/90 dark:hover:text-white",
                        "flex-shrink-0",
                        "m-1"
                        // Add margin for better touch target
                      ),
                      "aria-label": "Search",
                      children: /* @__PURE__ */ jsx(
                        Search,
                        {
                          className: "w-5 h-5",
                          "aria-hidden": "true"
                        }
                      )
                    }
                  ) }),
                  /* @__PURE__ */ jsx(TooltipContent, { side: "bottom", sideOffset: 8, children: /* @__PURE__ */ jsx("p", { children: "Search" }) })
                ] }),
                /* @__PURE__ */ jsxs(TooltipRoot, { children: [
                  /* @__PURE__ */ jsx(TooltipTrigger, { asChild: true, children: /* @__PURE__ */ jsxs(
                    "a",
                    {
                      href: "/bookmarks",
                      className: cn(
                        "relative inline-flex items-center justify-center",
                        "w-10 h-10 rounded-full",
                        "text-design-foreground hover:text-design-primary",
                        "hover:bg-design-accent/10",
                        "focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary",
                        "transition-colors duration-200",
                        "dark:text-white/90 dark:hover:text-white",
                        "flex-shrink-0",
                        "m-1"
                        // Add margin for better touch target
                      ),
                      "aria-label": `Saved Deals${bookmarksCount > 0 ? ` (${bookmarksCount} items)` : ""}`,
                      children: [
                        /* @__PURE__ */ jsx(
                          Heart,
                          {
                            className: "w-5 h-5",
                            "aria-hidden": "true"
                          }
                        ),
                        bookmarksCount > 0 && /* @__PURE__ */ jsx(
                          "span",
                          {
                            className: cn(
                              "absolute -top-0.5 -right-0.5",
                              "flex items-center justify-center min-w-[1.25rem] h-5 px-1",
                              "text-xs font-semibold text-white",
                              "bg-design-primary rounded-full",
                              "ring-2 ring-white dark:ring-gray-900",
                              "shadow-sm"
                            ),
                            "aria-hidden": "true",
                            children: bookmarksCount > 9 ? "9+" : bookmarksCount
                          }
                        )
                      ]
                    }
                  ) }),
                  /* @__PURE__ */ jsx(TooltipContent, { side: "bottom", sideOffset: 8, children: /* @__PURE__ */ jsxs("p", { children: [
                    "Saved Deals",
                    bookmarksCount > 0 ? ` (${bookmarksCount})` : ""
                  ] }) })
                ] }),
                /* @__PURE__ */ jsxs(TooltipRoot, { children: [
                  /* @__PURE__ */ jsx(TooltipTrigger, { asChild: true, children: /* @__PURE__ */ jsx(
                    "button",
                    {
                      onClick: toggleDarkMode$1,
                      className: cn(
                        "relative inline-flex items-center justify-center",
                        "w-10 h-10 rounded-full",
                        "text-design-foreground hover:text-design-primary",
                        "hover:bg-design-accent/10",
                        "focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary",
                        "transition-colors duration-200",
                        "dark:text-white/90 dark:hover:text-white",
                        "flex-shrink-0",
                        "m-1"
                        // Add margin for better touch target
                      ),
                      "aria-label": isDarkMode$1 ? "Switch to light mode" : "Switch to dark mode",
                      children: isDarkMode$1 ? /* @__PURE__ */ jsx(Sun, { className: "w-5 h-5", "aria-hidden": "true" }) : /* @__PURE__ */ jsx(Moon, { className: "w-5 h-5", "aria-hidden": "true" })
                    }
                  ) }),
                  /* @__PURE__ */ jsx(TooltipContent, { side: "bottom", sideOffset: 8, children: /* @__PURE__ */ jsx("p", { children: isDarkMode$1 ? "Light Mode" : "Dark Mode" }) })
                ] })
              ] }),
              /* @__PURE__ */ jsxs(TooltipRoot, { children: [
                /* @__PURE__ */ jsx(TooltipTrigger, { asChild: true, children: /* @__PURE__ */ jsx(
                  "button",
                  {
                    ref: menuButtonRef,
                    onClick: toggleMobileMenu,
                    className: cn(
                      "relative inline-flex items-center justify-center",
                      "w-10 h-10 rounded-full",
                      "text-design-foreground hover:text-design-primary",
                      "hover:bg-design-accent/10",
                      "focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary",
                      "transition-colors duration-200",
                      "dark:text-white/90 dark:hover:text-white",
                      "flex-shrink-0",
                      "m-1",
                      // Add margin for better touch target
                      "md:hidden"
                      // Only show on mobile
                    ),
                    "aria-expanded": isMobileMenuOpen,
                    "aria-controls": "mobile-navigation",
                    "aria-label": isMobileMenuOpen ? "Close menu" : "Open menu",
                    children: isMobileMenuOpen ? /* @__PURE__ */ jsx(X, { className: "w-6 h-6", "aria-hidden": "true" }) : /* @__PURE__ */ jsx(Menu, { className: "w-6 h-6", "aria-hidden": "true" })
                  }
                ) }),
                /* @__PURE__ */ jsx(TooltipContent, { side: "bottom", sideOffset: 8, children: /* @__PURE__ */ jsx("p", { children: isMobileMenuOpen ? "Close menu" : "Menu" }) })
              ] })
            ] })
          ] }),
          isMobileMenuOpen && /* @__PURE__ */ jsx(
            "div",
            {
              className: "fixed inset-0 bg-black/50 backdrop-blur-sm z-40 md:hidden",
              onClick: toggleMobileMenu,
              "aria-hidden": "true",
              style: { zIndex: 40 }
            }
          ),
          /* @__PURE__ */ jsx(
            "div",
            {
              id: "mobile-navigation",
              className: computedValues.mobileMenuClasses,
              "aria-hidden": false,
              style: {
                transitionProperty: "opacity, transform",
                transitionTimingFunction: "cubic-bezier(0.16, 1, 0.3, 1)",
                // More natural easing
                transitionDuration: "300ms",
                zIndex: 50
              },
              children: /* @__PURE__ */ jsxs("div", { className: "container mx-auto px-4 py-4 pt-20", children: [
                /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between mb-4 pb-4 border-b border-gray-200 dark:border-gray-700", children: [
                  /* @__PURE__ */ jsx("h2", { className: "text-lg font-semibold text-gray-900 dark:text-white", children: "Menu" }),
                  /* @__PURE__ */ jsx(
                    "button",
                    {
                      onClick: toggleMobileMenu,
                      className: "p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",
                      "aria-label": "Close menu",
                      children: /* @__PURE__ */ jsx(X, { className: "w-5 h-5 text-gray-900 dark:text-white" })
                    }
                  )
                ] }),
                /* @__PURE__ */ jsx(
                  "nav",
                  {
                    className: "flex flex-col space-y-1",
                    "aria-label": "Mobile navigation",
                    role: "navigation",
                    children: navItems.map((item) => /* @__PURE__ */ jsxs(
                      "a",
                      {
                        href: item.href,
                        onClick: () => {
                          setTimeout(() => {
                            setIsMobileMenuOpen(false);
                          }, 100);
                        },
                        className: cn(
                          "group mobile-menu-item py-4 px-4 rounded-xl relative z-50",
                          "text-gray-900 dark:text-white hover:text-design-primary",
                          "hover:bg-gray-100 dark:hover:bg-gray-800 hover:translate-x-1",
                          "focus:outline-none focus-visible:ring-2 focus-visible:ring-design-primary focus-visible:ring-offset-2",
                          "flex items-center justify-between",
                          "text-base font-medium",
                          "active:bg-gray-200 dark:active:bg-gray-700 active:scale-98",
                          "transition-all duration-200 ease-out",
                          "border border-transparent hover:border-gray-200 dark:hover:border-gray-700",
                          "cursor-pointer",
                          currentPath === item.href && "text-design-primary font-semibold bg-design-primary/10 border-design-primary/20"
                        ),
                        onKeyDown: (e) => handleKeyDown(e, true),
                        onFocus: (e) => setActiveElement(e.currentTarget),
                        onBlur: () => setActiveElement(null),
                        "aria-current": currentPath === item.href ? "page" : void 0,
                        children: [
                          /* @__PURE__ */ jsx("span", { className: "flex-1 text-left", children: item.label }),
                          /* @__PURE__ */ jsx(
                            ChevronRight,
                            {
                              className: "w-5 h-5 text-gray-500 dark:text-gray-400 transition-transform duration-200 group-hover:translate-x-1",
                              "aria-hidden": "true"
                            }
                          )
                        ]
                      },
                      item.href
                    ))
                  }
                )
              ] })
            }
          ),
          children
        ]
      }
    )
  ] });
}

const Container = React__default.forwardRef(
  ({
    size = "lg",
    padded = true,
    className,
    children,
    ...props
  }, ref) => {
    const sizeClass = {
      sm: "design-container-sm",
      md: "design-container-md",
      lg: "design-container-lg",
      xl: "design-container",
      full: "w-full"
    }[size];
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref,
        className: cn(
          sizeClass,
          padded && "px-4 sm:px-6 lg:px-8",
          className
        ),
        ...props,
        children
      }
    );
  }
);
Container.displayName = "Container";

const Section = React__default.forwardRef(
  ({
    size = "md",
    containerSize = "lg",
    background = "default",
    bordered = false,
    container = true,
    className,
    children,
    ...props
  }, ref) => {
    const sizeClass = {
      sm: "design-section-sm",
      md: "design-section",
      lg: "design-section-lg"
    }[size];
    const backgroundClass = {
      default: "bg-design-background",
      muted: "bg-design-muted",
      primary: "bg-design-primary text-design-on-primary",
      secondary: "bg-design-secondary text-design-on-secondary"
    }[background];
    const content = container ? /* @__PURE__ */ jsx(Container, { size: containerSize, children }) : children;
    return /* @__PURE__ */ jsx(
      "section",
      {
        ref,
        className: cn(
          sizeClass,
          backgroundClass,
          bordered && "border-y border-design-border",
          className
        ),
        ...props,
        children: content
      }
    );
  }
);
Section.displayName = "Section";

const Card = React__default.forwardRef(
  ({
    variant = "default",
    interactive = false,
    hoverable = true,
    glowEffect = false,
    className,
    children,
    ...props
  }, ref) => {
    const baseStyles = "rounded-2xl border text-design-card-foreground transition-all duration-design-normal";
    const variantStyles = {
      default: "border-design-border bg-design-card shadow-design",
      outline: "border-design-border bg-transparent",
      glass: "border-design-border/20 bg-design-card/80 backdrop-blur-sm shadow-design",
      filled: "border-design-primary/20 bg-design-primary/10",
      elevated: "shadow-design-md border-design-border bg-design-card"
    };
    const hoverStyles = hoverable ? "hover:shadow-design-md hover:scale-[1.02] hover:border-design-border/50" : "";
    const interactiveStyles = interactive ? "cursor-pointer focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-design-primary focus-visible:ring-offset-2 focus-visible:ring-offset-design-background" : "";
    const glowStyles = glowEffect ? "card-glow border-glow" : "";
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref,
        className: cn(
          baseStyles,
          variantStyles[variant],
          hoverStyles,
          interactiveStyles,
          glowStyles,
          className
        ),
        tabIndex: interactive ? 0 : void 0,
        "data-interactive": interactive ? "true" : void 0,
        ...props,
        children
      }
    );
  }
);
Card.displayName = "Card";
const CardHeader = React__default.forwardRef(
  ({ className, spacing = "default", ...props }, ref) => {
    const spacingStyles = {
      tight: "space-y-1",
      default: "space-y-1.5",
      loose: "space-y-2.5"
    };
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref,
        className: cn(
          "flex flex-col p-5",
          spacingStyles[spacing],
          className
        ),
        ...props
      }
    );
  }
);
CardHeader.displayName = "CardHeader";
const CardTitle = React__default.forwardRef(
  ({ className, as: Component = "h3", size = "md", ...props }, ref) => {
    const sizeStyles = {
      sm: "text-sm",
      md: "text-base",
      lg: "text-lg"
    };
    return /* @__PURE__ */ jsx(
      Component,
      {
        ref,
        className: cn(
          sizeStyles[size],
          "font-semibold leading-tight line-clamp-2",
          className
        ),
        ...props
      }
    );
  }
);
CardTitle.displayName = "CardTitle";
const CardDescription = React__default.forwardRef(
  ({ className, size = "sm", ...props }, ref) => {
    const sizeStyles = {
      xs: "text-xs",
      sm: "text-sm",
      md: "text-base"
    };
    return /* @__PURE__ */ jsx(
      "p",
      {
        ref,
        className: cn(
          sizeStyles[size],
          "text-design-muted-foreground",
          className
        ),
        ...props
      }
    );
  }
);
CardDescription.displayName = "CardDescription";
const CardContent = React__default.forwardRef(
  ({ className, spacing = "default", noPadding = false, ...props }, ref) => {
    const spacingStyles = {
      tight: "space-y-2",
      default: "space-y-4",
      loose: "space-y-6"
    };
    const paddingStyles = noPadding ? "" : "px-5 pt-0";
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref,
        className: cn(
          paddingStyles,
          spacingStyles[spacing],
          className
        ),
        ...props
      }
    );
  }
);
CardContent.displayName = "CardContent";
const CardFooter = React__default.forwardRef(
  ({ className, align = "between", noPadding = false, ...props }, ref) => {
    const alignStyles = {
      start: "justify-start",
      center: "justify-center",
      end: "justify-end",
      between: "justify-between"
    };
    const paddingStyles = noPadding ? "" : "px-5 pt-0 pb-5";
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref,
        className: cn(
          "flex items-center",
          alignStyles[align],
          paddingStyles,
          className
        ),
        ...props
      }
    );
  }
);
CardFooter.displayName = "CardFooter";
const CardImage = React__default.forwardRef(
  ({ className, aspectRatio = "auto", overlay = false, alt = "", ...props }, ref) => {
    const aspectRatioStyles = {
      auto: "",
      square: "aspect-square",
      video: "aspect-video",
      portrait: "aspect-[3/4]"
    };
    return /* @__PURE__ */ jsxs("div", { className: cn(
      "relative overflow-hidden",
      aspectRatioStyles[aspectRatio]
    ), children: [
      /* @__PURE__ */ jsx(
        "img",
        {
          ref,
          className: cn(
            "w-full h-full object-cover transition-transform duration-500 group-hover:scale-105",
            className
          ),
          alt,
          ...props
        }
      ),
      overlay && /* @__PURE__ */ jsx("div", { className: "absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" })
    ] });
  }
);
CardImage.displayName = "CardImage";
const CardBadge = React__default.forwardRef(
  ({ className, variant = "primary", position = "top-left", children, ...props }, ref) => {
    const variantStyles = {
      primary: "bg-design-primary text-white",
      secondary: "bg-design-secondary text-design-foreground dark:text-black",
      accent: "bg-design-tetradic1 text-white",
      success: "bg-green-500 text-white",
      warning: "bg-design-tetradic2 text-design-foreground",
      danger: "bg-red-500 text-white"
    };
    const positionStyles = {
      "top-left": "absolute top-4 left-4",
      "top-right": "absolute top-4 right-4",
      "bottom-left": "absolute bottom-4 left-4",
      "bottom-right": "absolute bottom-4 right-4"
    };
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref,
        className: cn(
          "text-xs font-bold px-3 py-1 rounded-full shadow-md",
          variantStyles[variant],
          positionStyles[position],
          className
        ),
        ...props,
        children
      }
    );
  }
);
CardBadge.displayName = "CardBadge";

const normalizeUrl = (url) => {
  if (!url || typeof url !== "string" || url.trim() === "" || url === "YOUR_LOGO_URL") {
    return null;
  }
  const trimmedUrl = url.trim();
  if (trimmedUrl.startsWith("http://") || trimmedUrl.startsWith("https://")) {
    return trimmedUrl;
  } else {
    return trimmedUrl.startsWith("/") ? trimmedUrl : `/${trimmedUrl}`;
  }
};

const DealImage = ({
  src,
  alt,
  width = 400,
  height = 400,
  className = "",
  priority = false,
  fetchpriority = "auto",
  fallbackSrc = "/placeholder-image.svg",
  onLoad,
  index = 0
}) => {
  const [error, setError] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [imageUrl, setImageUrl] = useState(fallbackSrc);
  useEffect(() => {
    if (!src) {
      setImageUrl(fallbackSrc);
      return;
    }
    const normalizedUrl = normalizeUrl(src);
    if (!normalizedUrl) {
      setImageUrl(fallbackSrc);
      return;
    }
    setImageUrl(normalizedUrl);
    if (process.env.NODE_ENV !== "production") {
      console.log("DealImage - Processing URL:", {
        originalSrc: src,
        normalizedUrl,
        fallbackSrc,
        finalImageUrl: normalizedUrl || fallbackSrc
      });
    }
  }, [src, fallbackSrc]);
  const handleError = () => {
    setError(true);
    setImageUrl(fallbackSrc);
    if (process.env.NODE_ENV !== "production") {
      console.error("DealImage - Image failed to load:", {
        originalSrc: src,
        currentImageUrl: imageUrl,
        fallbackSrc,
        error: "Image load error"
      });
    }
  };
  const handleLoad = () => {
    setLoaded(true);
    if (onLoad) onLoad();
    if (process.env.NODE_ENV !== "production") {
      console.log("DealImage - Image loaded successfully:", {
        originalSrc: src,
        loadedImageUrl: imageUrl
      });
    }
  };
  return /* @__PURE__ */ jsxs("div", { className: "relative w-full h-full flex items-center justify-center bg-white", children: [
    !loaded && !error && /* @__PURE__ */ jsxs("div", { className: "absolute inset-0 bg-design-muted animate-pulse flex flex-col items-center justify-center z-20", children: [
      /* @__PURE__ */ jsx("div", { className: "w-16 h-16 rounded-full bg-design-muted-foreground/20 flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("svg", { className: "w-8 h-8 text-design-muted-foreground/40", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: /* @__PURE__ */ jsx("path", { d: "M4 16L8.586 11.414C8.96106 11.0391 9.46967 10.8284 10 10.8284C10.5303 10.8284 11.0389 11.0391 11.414 11.414L16 16M14 14L15.586 12.414C15.9611 12.0391 16.4697 11.8284 17 11.8284C17.5303 11.8284 18.0389 12.0391 18.414 12.414L20 14M14 8H14.01M6 20H18C18.5304 20 19.0391 19.7893 19.4142 19.4142C19.7893 19.0391 20 18.5304 20 18V6C20 5.46957 19.7893 4.96086 19.4142 4.58579C19.0391 4.21071 18.5304 4 18 4H6C5.46957 4 4.96086 4.21071 4.58579 4.58579C4.21071 4.96086 4 5.46957 4 6V18C4 18.5304 4.21071 19.0391 4.58579 19.4142C4.96086 19.7893 5.46957 20 6 20Z", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }) }) }),
      /* @__PURE__ */ jsx("div", { className: "text-design-primary dark:text-design-primary font-bold text-sm", children: "Vape Hybrid" }),
      /* @__PURE__ */ jsx("div", { className: "text-design-muted-foreground text-xs mt-1", children: "Loading image..." })
    ] }),
    /* @__PURE__ */ jsxs("picture", { className: "flex items-center justify-center w-full h-full", children: [
      imageUrl !== fallbackSrc && !imageUrl.endsWith(".svg") && !imageUrl.startsWith("http") && /* @__PURE__ */ jsx(
        "source",
        {
          srcSet: `${imageUrl.replace(/\.(jpg|jpeg|png)$/i, ".webp")}`,
          type: "image/webp"
        }
      ),
      /* @__PURE__ */ jsx(
        "img",
        {
          src: imageUrl,
          alt,
          width,
          height,
          className: `${className} object-contain z-10 ${error ? "opacity-0" : ""}`,
          loading: priority ? "eager" : "lazy",
          decoding: "async",
          onError: handleError,
          onLoad: handleLoad,
          style: {
            display: "block",
            margin: "0 auto",
            maxWidth: "100%",
            maxHeight: "100%",
            width: "auto",
            height: "auto"
          },
          ...{ fetchpriority }
        }
      )
    ] }),
    error && /* @__PURE__ */ jsx("div", { className: "absolute inset-0 flex items-center justify-center bg-white dark:bg-white/10 z-20", children: /* @__PURE__ */ jsxs("div", { className: "text-design-muted-foreground text-center p-4", children: [
      /* @__PURE__ */ jsx("div", { className: "mb-2", children: /* @__PURE__ */ jsx("svg", { className: "w-12 h-12 mx-auto text-design-muted-foreground/40", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: /* @__PURE__ */ jsx("path", { d: "M4 16L8.586 11.414C8.96106 11.0391 9.46967 10.8284 10 10.8284C10.5303 10.8284 11.0389 11.0391 11.414 11.414L16 16M14 14L15.586 12.414C15.9611 12.0391 16.4697 11.8284 17 11.8284C17.5303 11.8284 18.0389 12.0391 18.414 12.414L20 14M14 8H14.01M6 20H18C18.5304 20 19.0391 19.7893 19.4142 19.4142C19.7893 19.0391 20 18.5304 20 18V6C20 5.46957 19.7893 4.96086 19.4142 4.58579C19.0391 4.21071 18.5304 4 18 4H6C5.46957 4 4.96086 4.21071 4.58579 4.58579C4.21071 4.96086 4 5.46957 4 6V18C4 18.5304 4.21071 19.0391 4.58579 19.4142C4.96086 19.7893 5.46957 20 6 20Z", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }) }) }),
      /* @__PURE__ */ jsx("div", { className: "text-sm font-medium", children: "Image not available" })
    ] }) })
  ] });
};

const Button = React__default.forwardRef(
  ({
    variant = "primary",
    size = "md",
    loading = false,
    isLoading = false,
    // For backward compatibility
    disabled = false,
    leftIcon,
    rightIcon,
    glowEffect = false,
    isFullWidth = false,
    withAnimation = false,
    className,
    children,
    ...props
  }, ref) => {
    const isLoadingState = loading || isLoading;
    const variantClass = {
      primary: "c-button c-button--primary",
      secondary: "c-button c-button--secondary",
      outline: "c-button c-button--outline",
      ghost: "c-button c-button--ghost",
      destructive: "c-button c-button--destructive",
      accent: "c-button c-button--accent",
      link: "c-button c-button--link"
    }[variant];
    const sizeClass = {
      xs: "c-button--xs",
      sm: "c-button--sm",
      md: "",
      // Default size, no additional class needed
      lg: "c-button--lg",
      xl: "c-button--xl",
      icon: "c-button--icon"
    }[size];
    const glowClass = glowEffect ? "c-button-glow" : "";
    const animationClass = withAnimation ? "c-button--animated" : "";
    const loadingClass = isLoadingState ? "c-button--loading" : "";
    const disabledClass = disabled ? "c-button--disabled" : "";
    const fullWidthClass = isFullWidth ? "c-button--full-width" : "";
    const legacyClass = {
      primary: "design-button design-button-primary",
      secondary: "design-button design-button-secondary",
      outline: "design-button design-button-outline",
      ghost: "design-button",
      destructive: "design-button",
      accent: "design-button",
      link: "design-button"
    }[variant];
    const legacySizeClass = {
      xs: "design-button-sm",
      sm: "design-button-sm",
      md: "",
      // Default size
      lg: "design-button-lg",
      xl: "design-button-lg",
      icon: ""
    }[size];
    const legacyGlowClass = glowEffect ? "button-glow border-glow" : "";
    return /* @__PURE__ */ jsxs(
      "button",
      {
        ref,
        className: cn(
          // New ITCSS classes
          variantClass,
          sizeClass,
          glowClass,
          loadingClass,
          disabledClass,
          fullWidthClass,
          animationClass,
          // Legacy classes for backward compatibility
          legacyClass,
          legacySizeClass,
          legacyGlowClass,
          // Custom class from props
          className
        ),
        disabled: disabled || isLoadingState,
        ...props,
        children: [
          leftIcon && /* @__PURE__ */ jsx("span", { className: cn("c-button__icon c-button__icon--left"), children: leftIcon }),
          /* @__PURE__ */ jsx("span", { className: "c-button__label", children }),
          rightIcon && /* @__PURE__ */ jsx("span", { className: cn("c-button__icon c-button__icon--right"), children: rightIcon }),
          isLoadingState && /* @__PURE__ */ jsx("span", { className: "c-button__spinner", children: /* @__PURE__ */ jsxs("svg", { className: "animate-spin h-5 w-5", xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", children: [
            /* @__PURE__ */ jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }),
            /* @__PURE__ */ jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })
          ] }) })
        ]
      }
    );
  }
);
Button.displayName = "Button";

const Button$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: Button
}, Symbol.toStringTag, { value: 'Module' }));

function BookmarkButton({
  dealId,
  className,
  showText = false
}) {
  const [bookmarked, setBookmarked] = useState(false);
  const dealIdString = dealId.toString();
  useEffect(() => {
    if (typeof window !== "undefined") {
      try {
        const storedBookmarkIds = localStorage.getItem("bookmarkIds");
        if (storedBookmarkIds) {
          const bookmarkIds = JSON.parse(storedBookmarkIds);
          setBookmarked(bookmarkIds.includes(dealIdString));
        }
      } catch (error) {
        console.error("Error checking bookmark status:", error);
      }
      const handleStorageChange = (e) => {
        if (e.key === "bookmarkIds") {
          try {
            const newBookmarkIds = e.newValue ? JSON.parse(e.newValue) : [];
            setBookmarked(newBookmarkIds.includes(dealIdString));
          } catch (error) {
            console.error("Error parsing bookmarks from storage event:", error);
          }
        }
      };
      const handleCustomBookmarkChange = (e) => {
        try {
          const newBookmarkIds = e.detail ? e.detail : [];
          setBookmarked(newBookmarkIds.includes(dealIdString));
        } catch (error) {
          console.error("Error handling custom bookmark change event:", error);
        }
      };
      window.addEventListener("storage", handleStorageChange);
      window.addEventListener("bookmarkChange", handleCustomBookmarkChange);
      return () => {
        window.removeEventListener("storage", handleStorageChange);
        window.removeEventListener("bookmarkChange", handleCustomBookmarkChange);
      };
    }
  }, [dealIdString]);
  const handleBookmarkToggle = (e) => {
    e.preventDefault();
    e.stopPropagation();
    if (typeof window !== "undefined") {
      try {
        const storedBookmarkIds = localStorage.getItem("bookmarkIds");
        let bookmarkIds = storedBookmarkIds ? JSON.parse(storedBookmarkIds) : [];
        if (bookmarked) {
          bookmarkIds = bookmarkIds.filter((id) => id !== dealIdString);
          toast.success("Removed from bookmarks");
        } else {
          if (!bookmarkIds.includes(dealIdString)) {
            bookmarkIds.push(dealIdString);
            toast.success("Added to bookmarks");
          }
        }
        localStorage.setItem("bookmarkIds", JSON.stringify(bookmarkIds));
        setBookmarked(!bookmarked);
        window.dispatchEvent(new StorageEvent("storage", {
          key: "bookmarkIds",
          newValue: JSON.stringify(bookmarkIds)
        }));
        window.dispatchEvent(new CustomEvent("bookmarkChange", {
          detail: bookmarkIds
        }));
      } catch (error) {
        console.error("Error toggling bookmark:", error);
        toast.error("Error saving bookmark");
      }
    }
  };
  return /* @__PURE__ */ jsxs(
    Button,
    {
      variant: "ghost",
      size: "sm",
      onClick: handleBookmarkToggle,
      className: `${showText ? "flex items-center gap-2" : ""} ${className || ""}`,
      "aria-label": bookmarked ? "Remove from bookmarks" : "Add to bookmarks",
      "data-bookmarked": bookmarked,
      children: [
        /* @__PURE__ */ jsx(Heart, { size: showText ? 16 : 20, fill: bookmarked ? "red" : "none" }),
        showText && /* @__PURE__ */ jsx("span", { className: "text-sm", children: bookmarked ? "Saved" : "Save" })
      ]
    }
  );
}

const STAFF_IMAGES = [
  {
    name: "Chyou Shen",
    initials: "CS",
    webp: "/staff/Chyou_Shen-removebg.webp",
    fallback: "/staff/Chyou_Shen-removebg.png"
  },
  {
    name: "Cynthia S. Garcia",
    initials: "CG",
    webp: "/staff/Cynthia_S._Garcia-removebg.webp",
    fallback: "/staff/Cynthia_S._Garcia-removebg.png"
  },
  {
    name: "Dannielle E. Benn",
    initials: "DB",
    webp: "/staff/Dannielle_E._Benn-removebg.webp",
    fallback: "/staff/Dannielle_E._Benn-removebg.png"
  },
  {
    name: "Geoffrey J. Koehler",
    initials: "GK",
    webp: "/staff/Geoffrey_J._Koehler-removebg.webp",
    fallback: "/staff/Geoffrey_J._Koehler-removebg.png"
  },
  {
    name: "Harvey B. Green",
    initials: "HG",
    webp: "/staff/Harvey_B._Green-removebg-preview.webp",
    fallback: "/staff/Harvey_B._Green-removebg-preview.png"
  },
  {
    name: "Victoria F. Ingram",
    initials: "VI",
    webp: "/staff/Victoria_F._Ingram-removebg.webp",
    fallback: "/staff/Victoria_F._Ingram-removebg.png"
  },
  // Additional staff with only initials (no images)
  {
    name: "Kevin H. Anthony",
    initials: "KA",
    webp: null,
    fallback: null
  },
  {
    name: "Lachlan Finch-Hatton",
    initials: "LH",
    webp: null,
    fallback: null
  },
  {
    name: "Rodolfo M. Tom",
    initials: "RT",
    webp: null,
    fallback: null
  },
  {
    name: "Winnie D. Ratcliffe",
    initials: "WR",
    webp: null,
    fallback: null
  }
];
const AVATAR_COLORS = [
  "#4F46E5",
  // Indigo
  "#0EA5E9",
  // Sky blue
  "#10B981",
  // Emerald
  "#F59E0B",
  // Amber
  "#EF4444",
  // Red
  "#8B5CF6",
  // Violet
  "#EC4899",
  // Pink
  "#06B6D4",
  // Cyan
  "#F97316",
  // Orange
  "#84CC16"
  // Lime
];
const TIME_UNITS = [
  { unit: "hour", max: 24, text: "h" },
  { unit: "day", max: 7, text: "d" },
  { unit: "week", max: 4, text: "w" }
];
function getRandomStaffImages(count = 3, seed) {
  const useSeed = seed || 42;
  const result = [];
  for (let i = 0; i < count; i++) {
    const staffIndex = (useSeed + i) % STAFF_IMAGES.length;
    const colorIndex = (useSeed + i) % AVATAR_COLORS.length;
    const staff = STAFF_IMAGES[staffIndex];
    const color = AVATAR_COLORS[colorIndex];
    result.push({
      imagePath: staff.fallback,
      // PNG fallback or null
      webpPath: staff.webp,
      // WebP version or null
      color,
      initials: staff.initials,
      // Use the predefined initials
      name: staff.name
      // Include the staff member's name
    });
  }
  return result;
}
function generateUsageInfo(dealId) {
  const seed = dealId % 100;
  const timeIndex = seed % TIME_UNITS.length;
  const timeUnit = TIME_UNITS[timeIndex];
  const timeValue = 1 + seed % (timeUnit.max - 1);
  const count = 1 + seed % 5;
  return {
    timeAgo: `${timeValue}${timeUnit.text}`,
    count
  };
}

const copyWithFallback$3 = (text) => {
  const textarea = document.createElement("textarea");
  textarea.value = text;
  textarea.style.position = "fixed";
  textarea.style.left = "-999999px";
  textarea.style.top = "-999999px";
  document.body.appendChild(textarea);
  textarea.focus();
  textarea.select();
  let success = false;
  try {
    success = document.execCommand("copy");
    if (success) {
      toast.success("Coupon code copied to clipboard!");
    } else {
      toast.error("Failed to copy code");
    }
  } catch (err) {
    if (process.env.NODE_ENV !== "production") {
      console.error("Fallback copy failed:", err);
    }
    toast.error("Failed to copy code");
  }
  document.body.removeChild(textarea);
  return success;
};
const DealCard = ({
  deal,
  isRevealed = false,
  onShowCouponModal,
  className,
  priority = false,
  ...props
}) => {
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);
  const cardRef = useRef(null);
  const staffAvatars = useMemo(() => getRandomStaffImages(3, deal.id), [deal.id]);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = usageInfo.timeAgo;
  const count = usageInfo.count;
  const daysRemaining = deal.deal_end_date ? Math.ceil((new Date(deal.deal_end_date).getTime() - (/* @__PURE__ */ new Date()).getTime()) / (1e3 * 60 * 60 * 24)) : null;
  const isExpiringSoon = daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  const isExpired = daysRemaining !== null && daysRemaining <= 0;
  const getImageUrl = () => {
    const normalizeUrl = (url) => {
      if (!url || typeof url !== "string" || url.trim() === "" || url === "YOUR_LOGO_URL") {
        return null;
      }
      const trimmedUrl = url.trim();
      if (trimmedUrl.startsWith("http://") || trimmedUrl.startsWith("https://")) {
        return trimmedUrl;
      } else {
        return trimmedUrl.startsWith("/") ? trimmedUrl : `/${trimmedUrl}`;
      }
    };
    if (process.env.NODE_ENV !== "production") {
      console.log("Deal Image URLs:", {
        imagebig_url: deal.imagebig_url,
        image_url: deal.image_url,
        imagesmall_url: deal.imagesmall_url,
        brand_logo_url: deal.brand_logo_url,
        merchant_logo_url: deal.merchant_logo_url,
        brands_logo_url: deal.brands?.logo_url,
        merchants_logo_url: deal.merchants?.logo_url
      });
    }
    const dealImage = normalizeUrl(deal.imagebig_url) || normalizeUrl(deal.image_url) || normalizeUrl(deal.imagesmall_url);
    if (dealImage) {
      return dealImage;
    }
    const brandLogo = normalizeUrl(deal.brands?.logo_url) || normalizeUrl(deal.brand_logo_url);
    if (brandLogo) {
      return brandLogo;
    }
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) || normalizeUrl(deal.merchant_logo_url);
    if (merchantLogo) {
      return merchantLogo;
    }
    return "/placeholder-image.svg";
  };
  const imageUrl = getImageUrl();
  const handleCardClick = React__default.useCallback(() => {
    requestAnimationFrame(() => {
      setIsCodeRevealed(true);
      if (typeof window !== "undefined") {
        const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
        const hasBeenClickedBefore = clickedDeals[deal.id];
        const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
        revealedCodes[deal.id] = true;
        localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
        clickedDeals[deal.id] = true;
        localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
        if (deal.coupon_code) {
          try {
            if (navigator.clipboard && navigator.clipboard.writeText) {
              navigator.clipboard.writeText(deal.coupon_code || "NO CODE REQUIRED").then(() => {
                toast.success("Coupon code copied to clipboard!");
              }).catch((err) => {
                if (process.env.NODE_ENV !== "production") {
                  console.error("Failed to copy with Clipboard API:", err);
                }
                copyWithFallback$3(deal.coupon_code || "NO CODE REQUIRED");
              });
            } else {
              copyWithFallback$3(deal.coupon_code || "NO CODE REQUIRED");
            }
          } catch (err) {
            if (process.env.NODE_ENV !== "production") {
              console.error("Error copying code:", err);
            }
            toast.info(`Your code is: ${deal.coupon_code}`, {
              description: "Please copy it manually"
            });
          }
        }
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get("view") || "grid";
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set("dealId", deal.id.toString());
        popupUrl.searchParams.set("showPopup", "true");
        popupUrl.searchParams.set("view", viewMode);
        setTimeout(() => {
          window.open(popupUrl.toString(), "_blank");
          if (onShowCouponModal) {
            onShowCouponModal(deal);
          }
          if (!hasBeenClickedBefore && deal.tracking_url) {
            window.location.href = deal.tracking_url;
          }
        }, 50);
      }
    });
  }, [deal, onShowCouponModal]);
  const handleCopy = React__default.useCallback((e) => {
    e.stopPropagation();
    requestAnimationFrame(() => {
      setIsCodeRevealed(true);
      if (typeof window !== "undefined") {
        const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
        const hasBeenClickedBefore = clickedDeals[deal.id];
        const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
        revealedCodes[deal.id] = true;
        localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
        clickedDeals[deal.id] = true;
        localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
        if (deal.coupon_code) {
          try {
            if (navigator.clipboard && navigator.clipboard.writeText) {
              navigator.clipboard.writeText(deal.coupon_code || "NO CODE REQUIRED").then(() => {
                toast.success("Coupon code copied to clipboard!");
              }).catch((err) => {
                if (process.env.NODE_ENV !== "production") {
                  console.error("Failed to copy with Clipboard API:", err);
                }
                copyWithFallback$3(deal.coupon_code || "NO CODE REQUIRED");
              });
            } else {
              copyWithFallback$3(deal.coupon_code || "NO CODE REQUIRED");
            }
          } catch (err) {
            if (process.env.NODE_ENV !== "production") {
              console.error("Error copying code:", err);
            }
            toast.info(`Your code is: ${deal.coupon_code}`, {
              description: "Please copy it manually"
            });
          }
        }
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get("view") || "grid";
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set("dealId", deal.id.toString());
        popupUrl.searchParams.set("showPopup", "true");
        popupUrl.searchParams.set("view", viewMode);
        setTimeout(() => {
          window.open(popupUrl.toString(), "_blank");
          if (onShowCouponModal) {
            onShowCouponModal(deal);
          }
          if (!hasBeenClickedBefore && deal.tracking_url) {
            window.location.href = deal.tracking_url;
          }
        }, 50);
      }
    });
  }, [deal, onShowCouponModal]);
  return /* @__PURE__ */ jsxs(
    Card,
    {
      ref: cardRef,
      variant: "default",
      interactive: true,
      glowEffect: true,
      className: cn(
        "deal-card relative overflow-hidden cursor-pointer w-full flex flex-col",
        className
      ),
      style: { borderRadius: "25px" },
      onClick: handleCardClick,
      "aria-label": `Deal: ${deal.title}`,
      ...props,
      children: [
        isExpiringSoon && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full", children: daysRemaining === 1 ? "Expires today" : `Expires in ${daysRemaining} days` }),
        isExpired && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full", children: "Expired" }),
        /* @__PURE__ */ jsx("div", { className: "w-full p-4", children: /* @__PURE__ */ jsx("div", { className: "flex justify-center", children: /* @__PURE__ */ jsx(
          "div",
          {
            className: "relative overflow-hidden rounded-lg flex items-center justify-center w-full aspect-square min-h-[280px] bg-white dark:bg-white",
            style: {
              boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.05)",
              padding: "8px"
            },
            children: /* @__PURE__ */ jsx(
              DealImage,
              {
                src: imageUrl,
                alt: deal.title || "Deal image",
                width: 400,
                height: 400,
                priority,
                className: "",
                fallbackSrc: "/placeholder-image.svg"
              }
            )
          }
        ) }) }),
        /* @__PURE__ */ jsxs("div", { className: "px-4 pb-4 flex-1", children: [
          /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mb-2", children: [
            /* @__PURE__ */ jsxs("div", { className: "text-xl font-bold text-design-foreground", children: [
              deal.discount ? `${deal.discount}% Off` : deal.price ? `${deal.currency || "$"}${deal.price}` : "20% Off",
              isExpiringSoon && /* @__PURE__ */ jsx("span", { className: "ml-2 text-xs font-normal text-design-warning animate-pulse", children: daysRemaining === 1 ? "Ends today!" : `Ends in ${daysRemaining} days!` })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
              /* @__PURE__ */ jsx("div", { className: "deal-coupon-code", children: deal.coupon_code ? /* @__PURE__ */ jsxs(Fragment, { children: [
                /* @__PURE__ */ jsx(
                  "span",
                  {
                    className: isCodeRevealed ? "" : "blur-[4px] select-none",
                    style: { color: "var(--design-foreground)", fontWeight: "bold" },
                    children: deal.coupon_code
                  }
                ),
                /* @__PURE__ */ jsx("span", { className: "sr-only", children: isCodeRevealed ? `Coupon code: ${deal.coupon_code}` : "Click to reveal coupon code" })
              ] }) : "NO CODE" }),
              /* @__PURE__ */ jsxs("div", { className: "deal-card-actions", children: [
                /* @__PURE__ */ jsx(
                  BookmarkButton,
                  {
                    dealId: deal.id.toString(),
                    className: "heart-bookmark-button"
                  }
                ),
                /* @__PURE__ */ jsx(
                  "button",
                  {
                    className: "eye-button p-1 rounded-full hover:bg-design-muted transition-colors relative z-10",
                    onClick: (e) => {
                      e.stopPropagation();
                      window.open(`/deal/${deal.id}`, "_blank");
                    },
                    title: "View deal details",
                    style: { marginRight: "2px" },
                    children: /* @__PURE__ */ jsx(Eye, { size: 18, className: "text-design-muted-foreground hover:text-design-foreground transition-colors" })
                  }
                )
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "text-sm text-design-muted-foreground mb-1 flex items-center", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium", children: deal.merchants?.name || deal.brands?.name || "Brand" }),
            deal.verified && /* @__PURE__ */ jsxs("span", { className: "c-verified-badge verified-badge ml-2", children: [
              /* @__PURE__ */ jsx(
                "img",
                {
                  src: "/Vapehybrid light icon.svg",
                  alt: "Verified",
                  className: "dark:hidden"
                }
              ),
              /* @__PURE__ */ jsx(
                "img",
                {
                  src: "/Vapehybrid dark icon.svg",
                  alt: "Verified",
                  className: "hidden dark:block"
                }
              ),
              /* @__PURE__ */ jsx("span", { className: "text-design-primary dark:text-design-primary text-xs", children: "Verified" })
            ] }),
            deal.success_rate !== void 0 && /* @__PURE__ */ jsxs("span", { className: `c-success-rate-badge success-rate-badge ml-2 ${deal.success_rate >= 80 ? "high" : deal.success_rate >= 50 ? "medium" : "low"}`, children: [
              /* @__PURE__ */ jsx(ThumbsUp, { size: 10 }),
              /* @__PURE__ */ jsxs("span", { className: "text-design-primary dark:text-design-primary text-xs", children: [
                Math.round(deal.success_rate || 85),
                "%"
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsx("h3", { className: "deal-card-title line-clamp-2 mb-2 text-base font-semibold", children: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-3", children: [
            /* @__PURE__ */ jsx("div", { className: "flex -space-x-2", children: staffAvatars.slice(0, count || deal.usage_count || 3).map((avatar, index) => /* @__PURE__ */ jsx(
              "div",
              {
                className: "relative group",
                "data-tooltip-id": `staff-tooltip-${deal.id}-${index}`,
                "data-tooltip-content": avatar.name,
                children: /* @__PURE__ */ jsx(
                  "div",
                  {
                    className: "w-6 h-6 rounded-full border-2 border-design-card overflow-hidden flex-shrink-0 transition-all duration-200 group-hover:-translate-y-0.5",
                    style: { backgroundColor: avatar.color },
                    children: avatar.webpPath && avatar.imagePath ? /* @__PURE__ */ jsxs("picture", { children: [
                      /* @__PURE__ */ jsx("source", { srcSet: avatar.webpPath, type: "image/webp" }),
                      /* @__PURE__ */ jsx(
                        "img",
                        {
                          src: avatar.imagePath,
                          alt: `Staff member ${avatar.name}`,
                          className: "w-full h-full object-cover",
                          onError: (e) => {
                            const img = e.target;
                            img.style.display = "none";
                            const fallback = document.createElement("div");
                            fallback.className = "w-full h-full flex items-center justify-center text-white text-xs font-bold";
                            fallback.textContent = avatar.initials;
                            img.parentNode?.replaceChild(fallback, img);
                          },
                          loading: "lazy",
                          width: 24,
                          height: 24
                        }
                      )
                    ] }) : /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center text-white text-xs font-bold", children: avatar.initials })
                  }
                )
              },
              index
            )) }),
            /* @__PURE__ */ jsxs("span", { className: "ml-2", children: [
              "Verified ",
              usageTimeAgo,
              " by ",
              count || deal.usage_count || 3,
              " staffer",
              (count || deal.usage_count || 3) > 1 ? "s" : ""
            ] })
          ] }),
          isExpiringSoon && /* @__PURE__ */ jsxs("div", { className: "mb-3 text-xs text-design-warning px-2 py-1 rounded flex items-center", children: [
            /* @__PURE__ */ jsx("span", { className: "animate-pulse mr-1", children: "⏱" }),
            /* @__PURE__ */ jsxs("span", { children: [
              "Limited time offer! ",
              daysRemaining === 1 ? "Ends today" : `Ends in ${daysRemaining} days`
            ] })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "flex justify-center w-full", children: /* @__PURE__ */ jsx(
            "button",
            {
              className: "copy-code-button",
              onClick: (e) => {
                e.stopPropagation();
                handleCopy(e);
              },
              "aria-label": `Copy coupon code ${deal.coupon_code || "for this deal"}`,
              children: "Copy Code"
            }
          ) })
        ] })
      ]
    }
  );
};

const DealCard$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  DealCard
}, Symbol.toStringTag, { value: 'Module' }));

const copyWithFallback$2 = (text) => {
  const textarea = document.createElement("textarea");
  textarea.value = text;
  textarea.style.position = "fixed";
  textarea.style.left = "-999999px";
  textarea.style.top = "-999999px";
  document.body.appendChild(textarea);
  textarea.focus();
  textarea.select();
  let success = false;
  try {
    success = document.execCommand("copy");
    if (success) {
      toast.success("Coupon code copied to clipboard!");
    } else {
      toast.error("Failed to copy code");
    }
  } catch (err) {
    console.error("Fallback copy failed:", err);
    toast.error("Failed to copy code");
  }
  document.body.removeChild(textarea);
  return success;
};
const DealListCard = ({
  deal,
  isRevealed = false,
  onShowCouponModal,
  className,
  ...props
}) => {
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);
  const cardRef = useRef(null);
  const staffAvatars = useMemo(() => getRandomStaffImages(3), []);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = usageInfo.timeAgo;
  const count = usageInfo.count;
  const daysRemaining = deal.deal_end_date ? Math.ceil((new Date(deal.deal_end_date).getTime() - (/* @__PURE__ */ new Date()).getTime()) / (1e3 * 60 * 60 * 24)) : null;
  const isExpiringSoon = daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  const isExpired = daysRemaining !== null && daysRemaining <= 0;
  const getImageUrl = () => {
    const normalizeUrl = (url) => {
      if (!url || typeof url !== "string" || url.trim() === "" || url === "YOUR_LOGO_URL") {
        return null;
      }
      const trimmedUrl = url.trim();
      if (trimmedUrl.startsWith("http://") || trimmedUrl.startsWith("https://")) {
        return trimmedUrl;
      } else {
        return trimmedUrl.startsWith("/") ? trimmedUrl : `/${trimmedUrl}`;
      }
    };
    const dealImage = normalizeUrl(deal.imagebig_url) || normalizeUrl(deal.image_url) || normalizeUrl(deal.imagesmall_url);
    if (dealImage) return dealImage;
    const brandLogo = normalizeUrl(deal.brands?.logo_url) || normalizeUrl(deal.brand_logo_url);
    if (brandLogo) return brandLogo;
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) || normalizeUrl(deal.merchant_logo_url);
    if (merchantLogo) return merchantLogo;
    return "/placeholder-image.svg";
  };
  const imageUrl = getImageUrl();
  const handleCardClick = () => {
    setIsCodeRevealed(true);
    if (typeof window !== "undefined") {
      const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
      revealedCodes[deal.id] = true;
      localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
    }
    if (deal.coupon_code) {
      try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(deal.coupon_code || "NO CODE REQUIRED").then(() => {
            toast.success("Coupon code copied to clipboard!");
          }).catch((err) => {
            console.error("Failed to copy with Clipboard API:", err);
            copyWithFallback$2(deal.coupon_code || "NO CODE REQUIRED");
          });
        } else {
          copyWithFallback$2(deal.coupon_code || "NO CODE REQUIRED");
        }
      } catch (err) {
        console.error("Error copying code:", err);
        toast.info(`Your code is: ${deal.coupon_code}`, {
          description: "Please copy it manually"
        });
      }
    }
    const currentUrl = new URL(window.location.href);
    const viewMode = currentUrl.searchParams.get("view") || "list";
    const popupUrl = new URL(window.location.href);
    popupUrl.searchParams.set("dealId", deal.id.toString());
    popupUrl.searchParams.set("showPopup", "true");
    popupUrl.searchParams.set("view", viewMode);
    window.open(popupUrl.toString(), "_blank");
    if (onShowCouponModal) {
      onShowCouponModal(deal);
    }
    if (deal.tracking_url) {
      window.location.href = deal.tracking_url;
    }
  };
  const handleCopy = (e) => {
    e.stopPropagation();
    setIsCodeRevealed(true);
    if (typeof window !== "undefined") {
      const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
      revealedCodes[deal.id] = true;
      localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
    }
    if (deal.coupon_code) {
      try {
        if (navigator.clipboard && navigator.clipboard.writeText) {
          navigator.clipboard.writeText(deal.coupon_code || "NO CODE REQUIRED").then(() => {
            toast.success("Coupon code copied to clipboard!");
          }).catch((err) => {
            console.error("Failed to copy with Clipboard API:", err);
            copyWithFallback$2(deal.coupon_code || "NO CODE REQUIRED");
          });
        } else {
          copyWithFallback$2(deal.coupon_code || "NO CODE REQUIRED");
        }
      } catch (err) {
        console.error("Error copying code:", err);
        toast.info(`Your code is: ${deal.coupon_code}`, {
          description: "Please copy it manually"
        });
      }
    }
    const currentUrl = new URL(window.location.href);
    const viewMode = currentUrl.searchParams.get("view") || "list";
    const popupUrl = new URL(window.location.href);
    popupUrl.searchParams.set("dealId", deal.id.toString());
    popupUrl.searchParams.set("showPopup", "true");
    popupUrl.searchParams.set("view", viewMode);
    window.open(popupUrl.toString(), "_blank");
    if (onShowCouponModal) {
      onShowCouponModal(deal);
    }
    if (deal.tracking_url) {
      window.location.href = deal.tracking_url;
    }
  };
  return /* @__PURE__ */ jsxs(
    Card,
    {
      ref: cardRef,
      variant: "default",
      interactive: true,
      glowEffect: true,
      className: cn(
        "deal-list-card relative cursor-pointer w-full flex flex-col sm:flex-row items-center justify-between",
        className
      ),
      style: { padding: "0.5rem", borderRadius: "25px" },
      onClick: handleCardClick,
      "aria-label": `Deal: ${deal.title}`,
      ...props,
      children: [
        isExpiringSoon && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full", children: daysRemaining === 1 ? "Expires today" : `Expires in ${daysRemaining} days` }),
        isExpired && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full", children: "Expired" }),
        /* @__PURE__ */ jsx("div", { className: "w-full sm:w-[200px] py-2 sm:py-0", children: /* @__PURE__ */ jsx("div", { className: "flex justify-center h-full", children: /* @__PURE__ */ jsx(
          "div",
          {
            className: "relative flex items-center justify-center w-full h-full aspect-square max-w-[160px] sm:min-w-[160px] sm:max-w-[180px] sm:max-h-[180px]",
            style: {
              background: "transparent"
            },
            children: /* @__PURE__ */ jsx(
              DealImage,
              {
                src: imageUrl,
                alt: deal.title || "Deal image",
                width: 180,
                height: 180,
                className: "w-full h-full mx-auto",
                fallbackSrc: "/placeholder-image.svg"
              }
            )
          }
        ) }) }),
        /* @__PURE__ */ jsx("div", { className: "flex-1 p-3 w-full", children: /* @__PURE__ */ jsxs("div", { className: "h-full flex flex-col justify-between", children: [
          /* @__PURE__ */ jsx("div", { className: "mb-2", children: /* @__PURE__ */ jsxs("div", { className: "text-xl font-bold text-design-foreground", children: [
            deal.discount ? `${deal.discount}% Off` : deal.price ? `${deal.currency || "$"}${deal.price}` : "20% Off",
            isExpiringSoon && /* @__PURE__ */ jsx("span", { className: "ml-2 text-xs font-normal text-design-warning animate-pulse", children: daysRemaining === 1 ? "Ends today!" : `Ends in ${daysRemaining} days!` })
          ] }) }),
          /* @__PURE__ */ jsxs("div", { className: "text-sm text-design-muted-foreground mb-1 flex items-center", children: [
            deal.merchants?.name || deal.brands?.name || "Brand",
            deal.verified && /* @__PURE__ */ jsxs("span", { className: "c-verified-badge verified-badge ml-2", children: [
              /* @__PURE__ */ jsx(
                "img",
                {
                  src: "/Vapehybrid light icon.svg",
                  alt: "Verified",
                  className: "dark:hidden"
                }
              ),
              /* @__PURE__ */ jsx(
                "img",
                {
                  src: "/Vapehybrid dark icon.svg",
                  alt: "Verified",
                  className: "hidden dark:block"
                }
              ),
              /* @__PURE__ */ jsx("span", { className: "text-design-primary dark:text-design-primary", children: "Verified" })
            ] }),
            deal.success_rate !== void 0 && /* @__PURE__ */ jsxs("span", { className: `c-success-rate-badge success-rate-badge ml-2 ${deal.success_rate >= 80 ? "high" : deal.success_rate >= 50 ? "medium" : "low"}`, children: [
              /* @__PURE__ */ jsx(ThumbsUp, { size: 10 }),
              /* @__PURE__ */ jsxs("span", { className: "text-design-primary dark:text-design-primary", children: [
                Math.round(deal.success_rate || 85),
                "% success"
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsx("h3", { className: "deal-card-title line-clamp-2 mb-2 text-base font-semibold", children: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title }),
          /* @__PURE__ */ jsx("div", { className: "flex flex-1", children: /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
            deal.description && /* @__PURE__ */ jsx("p", { className: "text-sm text-design-muted-foreground mb-3 line-clamp-2", children: deal.description }),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-3", children: [
              /* @__PURE__ */ jsx("div", { className: "c-avatar-group user-avatar-group", children: staffAvatars.slice(0, count || deal.usage_count || 3).map((avatar, index) => /* @__PURE__ */ jsx("div", { className: "c-avatar c-avatar--xs user-avatar", style: { backgroundColor: avatar.color }, children: /* @__PURE__ */ jsxs("picture", { children: [
                avatar.webpPath && /* @__PURE__ */ jsx("source", { srcSet: avatar.webpPath, type: "image/webp" }),
                avatar.imagePath && /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: avatar.imagePath,
                    alt: "User",
                    className: "w-full h-full object-cover",
                    onError: (e) => {
                      e.target.style.display = "none";
                      e.target.parentElement.parentElement.textContent = avatar.initials;
                    }
                  }
                )
              ] }) }, index)) }),
              /* @__PURE__ */ jsxs("span", { children: [
                "Verified ",
                usageTimeAgo,
                " by ",
                count || deal.usage_count || 3,
                " staffer",
                (count || deal.usage_count || 3) > 1 ? "s" : ""
              ] })
            ] }),
            isExpiringSoon && /* @__PURE__ */ jsxs("div", { className: "mb-3 text-xs text-design-warning px-2 py-1 rounded flex items-center", children: [
              /* @__PURE__ */ jsx("span", { className: "animate-pulse mr-1", children: "⏱" }),
              /* @__PURE__ */ jsxs("span", { children: [
                "Limited time offer! ",
                daysRemaining === 1 ? "Ends today" : `Ends in ${daysRemaining} days`
              ] })
            ] })
          ] }) })
        ] }) }),
        /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center px-4 sm:pr-4 sm:pl-0 gap-2 w-full sm:min-w-[150px] sm:w-auto py-3 sm:py-0", children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              className: "deal-coupon-code relative cursor-pointer px-3 py-1 w-full text-center",
              onClick: (e) => {
                e.stopPropagation();
                handleCopy(e);
              },
              title: isCodeRevealed ? "Click to copy" : "Click to reveal code",
              children: deal.coupon_code ? /* @__PURE__ */ jsxs(Fragment, { children: [
                /* @__PURE__ */ jsx(
                  "span",
                  {
                    className: `text-base font-bold ${isCodeRevealed ? "" : "blur-[4px] select-none"}`,
                    style: { color: "var(--design-foreground)" },
                    children: deal.coupon_code
                  }
                ),
                /* @__PURE__ */ jsx("span", { className: "sr-only", children: isCodeRevealed ? `Coupon code: ${deal.coupon_code}` : "Click to reveal coupon code" })
              ] }) : "NO CODE"
            }
          ),
          /* @__PURE__ */ jsx(
            "button",
            {
              className: "copy-code-button",
              onClick: (e) => {
                e.stopPropagation();
                handleCopy(e);
              },
              "aria-label": `Copy coupon code ${deal.coupon_code || "for this deal"}`,
              children: "Copy Code"
            }
          ),
          /* @__PURE__ */ jsxs("div", { className: "deal-card-actions", children: [
            /* @__PURE__ */ jsx(
              BookmarkButton,
              {
                dealId: deal.id.toString(),
                className: "heart-bookmark-button"
              }
            ),
            /* @__PURE__ */ jsx(
              "button",
              {
                className: "eye-button p-1 rounded-full hover:bg-design-muted transition-colors relative z-10",
                onClick: (e) => {
                  e.stopPropagation();
                  window.open(`/deal/${deal.id}`, "_blank");
                },
                title: "View deal details",
                style: { marginRight: "2px" },
                children: /* @__PURE__ */ jsx(Eye, { size: 18, className: "text-design-muted-foreground hover:text-design-foreground transition-colors" })
              }
            )
          ] })
        ] })
      ]
    }
  );
};

const DealListCard$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  DealListCard
}, Symbol.toStringTag, { value: 'Module' }));

const copyToClipboard = async (text) => {
  if (!text) {
    toast.error("Nothing to copy");
    return false;
  }
  try {
    if (navigator.clipboard && navigator.clipboard.writeText) {
      await navigator.clipboard.writeText(text);
      toast.success("Coupon code copied to clipboard!");
      return true;
    } else {
      return copyWithFallback$1(text);
    }
  } catch (err) {
    if (process.env.NODE_ENV !== "production") {
      console.error("Error copying text:", err);
    }
    return copyWithFallback$1(text);
  }
};
const copyWithFallback$1 = (text) => {
  const textarea = document.createElement("textarea");
  textarea.value = text;
  textarea.style.position = "fixed";
  textarea.style.left = "-999999px";
  textarea.style.top = "-999999px";
  document.body.appendChild(textarea);
  textarea.focus();
  textarea.select();
  let success = false;
  try {
    success = document.execCommand("copy");
    if (success) {
      toast.success("Coupon code copied to clipboard!");
    } else {
      toast.error("Failed to copy code");
    }
  } catch (err) {
    if (process.env.NODE_ENV !== "production") {
      console.error("Fallback copy failed:", err);
    }
    toast.error("Failed to copy code");
  }
  document.body.removeChild(textarea);
  return success;
};

const copyWithFallback = (text) => {
  const textarea = document.createElement("textarea");
  textarea.value = text;
  textarea.style.position = "fixed";
  textarea.style.left = "-999999px";
  textarea.style.top = "-999999px";
  document.body.appendChild(textarea);
  textarea.focus();
  textarea.select();
  let success = false;
  try {
    success = document.execCommand("copy");
    if (success) {
      toast.success("Coupon code copied to clipboard!");
    } else {
      toast.error("Failed to copy code");
    }
  } catch (err) {
    if (process.env.NODE_ENV !== "production") {
      console.error("Fallback copy failed:", err);
    }
    toast.error("Failed to copy code");
  }
  document.body.removeChild(textarea);
  return success;
};
const ImprovedDealCard = ({
  deal,
  className,
  isRevealed = false,
  priority = false,
  ...props
}) => {
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);
  const cardRef = useRef(null);
  const staffAvatars = useMemo(() => getRandomStaffImages(5, deal.id), [deal.id]);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = usageInfo.timeAgo;
  const count = deal.usage_count || usageInfo.count;
  const daysRemaining = deal.deal_end_date ? Math.ceil((new Date(deal.deal_end_date).getTime() - (/* @__PURE__ */ new Date()).getTime()) / (1e3 * 60 * 60 * 24)) : null;
  const isExpiringSoon = daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  const imageUrl = useMemo(() => {
    const dealImage = normalizeUrl(deal.imagebig_url) || normalizeUrl(deal.image_url) || normalizeUrl(deal.imagesmall_url);
    if (dealImage) {
      return dealImage;
    }
    const brandLogo = normalizeUrl(deal.brands?.logo_url) || normalizeUrl(deal.brand_logo_url);
    if (brandLogo) {
      return brandLogo;
    }
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) || normalizeUrl(deal.merchant_logo_url);
    if (merchantLogo) {
      return merchantLogo;
    }
    return "/placeholder-image.svg";
  }, [
    deal.imagebig_url,
    deal.image_url,
    deal.imagesmall_url,
    deal.brands?.logo_url,
    deal.brand_logo_url,
    deal.merchants?.logo_url,
    deal.merchant_logo_url
  ]);
  const handleCardClick = React__default.useCallback(() => {
    requestAnimationFrame(() => {
      setIsCodeRevealed(true);
      if (typeof window !== "undefined") {
        const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
        const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
        revealedCodes[deal.id] = true;
        localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
        clickedDeals[deal.id] = true;
        localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
        if (deal.coupon_code) {
          try {
            if (navigator.clipboard && navigator.clipboard.writeText) {
              navigator.clipboard.writeText(deal.coupon_code || "NO CODE REQUIRED").then(() => {
                toast.success("Coupon code copied to clipboard!");
              }).catch((err) => {
                if (process.env.NODE_ENV !== "production") {
                  console.error("Failed to copy with Clipboard API:", err);
                }
                copyWithFallback(deal.coupon_code || "NO CODE REQUIRED");
              });
            } else {
              copyWithFallback(deal.coupon_code || "NO CODE REQUIRED");
            }
          } catch (err) {
            if (process.env.NODE_ENV !== "production") {
              console.error("Error copying code:", err);
            }
            toast.info(`Your code is: ${deal.coupon_code}`, {
              description: "Please copy it manually"
            });
          }
        }
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get("view") || "grid";
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set("dealId", deal.id.toString());
        popupUrl.searchParams.set("showPopup", "true");
        popupUrl.searchParams.set("solidBg", "true");
        popupUrl.searchParams.set("view", viewMode);
        window.open(popupUrl.toString(), "_blank");
        if (deal.tracking_url) {
          window.location.href = deal.tracking_url;
        } else {
          window.location.href = `/go/${deal.id}`;
        }
      }
    });
  }, [deal, props.onShowCouponModal]);
  const handleCopy = React__default.useCallback((e) => {
    e.stopPropagation();
    requestAnimationFrame(() => {
      setIsCodeRevealed(true);
      if (typeof window !== "undefined") {
        const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
        const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
        revealedCodes[deal.id] = true;
        localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
        clickedDeals[deal.id] = true;
        localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
        if (deal.coupon_code) {
          try {
            if (navigator.clipboard && navigator.clipboard.writeText) {
              navigator.clipboard.writeText(deal.coupon_code || "NO CODE REQUIRED").then(() => {
                toast.success("Coupon code copied to clipboard!");
              }).catch((err) => {
                if (process.env.NODE_ENV !== "production") {
                  console.error("Failed to copy with Clipboard API:", err);
                }
                copyWithFallback(deal.coupon_code || "NO CODE REQUIRED");
              });
            } else {
              copyWithFallback(deal.coupon_code || "NO CODE REQUIRED");
            }
          } catch (err) {
            if (process.env.NODE_ENV !== "production") {
              console.error("Error copying code:", err);
            }
            toast.info(`Your code is: ${deal.coupon_code}`, {
              description: "Please copy it manually"
            });
          }
        }
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get("view") || "grid";
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set("dealId", deal.id.toString());
        popupUrl.searchParams.set("showPopup", "true");
        popupUrl.searchParams.set("solidBg", "true");
        popupUrl.searchParams.set("view", viewMode);
        window.open(popupUrl.toString(), "_blank");
        if (deal.tracking_url) {
          window.location.href = deal.tracking_url;
        } else {
          window.location.href = `/go/${deal.id}`;
        }
      }
    });
  }, [deal, props.onShowCouponModal]);
  return /* @__PURE__ */ jsxs(
    Card,
    {
      ref: cardRef,
      variant: "default",
      interactive: true,
      glowEffect: true,
      className: cn(
        "improved-deal-card deal-card relative overflow-hidden cursor-pointer w-full flex flex-col max-h-[550px] transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:border-design-primary/50 dark:hover:border-design-primary/50",
        "border-black/15 dark:border-white/15",
        // Distinguishable border in both modes
        className
      ),
      style: {
        borderRadius: "25px",
        borderWidth: "1.5px"
      },
      "data-theme-border": "true",
      onClick: handleCardClick,
      "aria-label": `Deal: ${deal.title}`,
      role: "button",
      tabIndex: 0,
      ...props,
      children: [
        /* @__PURE__ */ jsx("div", { className: "w-full p-3", style: { height: "52%" }, children: /* @__PURE__ */ jsx("div", { className: "flex justify-center h-full", children: /* @__PURE__ */ jsx(
          "div",
          {
            className: "relative overflow-hidden rounded-lg flex items-center justify-center w-full h-full image-container bg-white dark:bg-white",
            style: {
              maxHeight: "calc(550px * 0.52 - 24px)",
              // 52% of 550px minus padding
              aspectRatio: "1/1",
              boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.05)"
            },
            children: /* @__PURE__ */ jsx(
              DealImage,
              {
                src: imageUrl,
                alt: deal.title || "Deal image",
                width: 400,
                height: 400,
                priority,
                fetchpriority: priority ? "high" : "auto",
                className: "w-full h-full mx-auto",
                fallbackSrc: "/placeholder-image.svg",
                index: 0
              }
            )
          }
        ) }) }),
        /* @__PURE__ */ jsxs("div", { className: "px-3 pb-0 flex-1 flex flex-col", style: { height: "48%", maxHeight: "calc(550px * 0.48 - 24px)" }, children: [
          /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mb-2", children: [
            /* @__PURE__ */ jsx("div", { className: "relative", children: /* @__PURE__ */ jsxs("div", { className: "text-xl font-bold text-design-foreground", children: [
              deal.discount ? /* @__PURE__ */ jsxs("span", { className: "text-green-600 dark:text-green-500", children: [
                "Save ",
                deal.discount,
                "% 🔥"
              ] }) : deal.price ? `${deal.currency || "$"}${deal.price}` : "20% Off",
              isExpiringSoon && /* @__PURE__ */ jsx("span", { className: "ml-2 text-xs font-normal text-design-warning animate-pulse", children: daysRemaining === 1 ? "Ends today!" : `Ends in ${daysRemaining} days!` })
            ] }) }),
            /* @__PURE__ */ jsx("div", { className: "flex items-center ml-1", children: /* @__PURE__ */ jsx(
              "div",
              {
                className: "deal-coupon-code text-xs bg-design-muted px-2 py-1 rounded-full border border-design-muted-foreground/10 relative overflow-hidden",
                children: deal.coupon_code ? /* @__PURE__ */ jsxs(Fragment, { children: [
                  /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: `transition-all duration-300 ${isCodeRevealed ? "blur-none opacity-100" : "blur-[3px] select-none"}`,
                      "aria-hidden": !isCodeRevealed,
                      children: deal.coupon_code
                    }
                  ),
                  isCodeRevealed && /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: "absolute inset-0 bg-design-primary/10 animate-reveal-sweep",
                      "aria-hidden": "true"
                    }
                  )
                ] }) : "NO CODE"
              }
            ) })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "text-xs text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1", children: [
            /* @__PURE__ */ jsx("span", { className: "font-medium truncate max-w-[120px]", title: deal.merchants?.name || deal.brands?.name || "Brand", children: deal.merchants?.name || deal.brands?.name || "Brand" }),
            deal.verified && /* @__PURE__ */ jsxs("span", { className: "c-verified-badge verified-badge ml-2", children: [
              /* @__PURE__ */ jsx(
                "img",
                {
                  src: "/Vapehybrid light icon.svg",
                  alt: "Verified",
                  className: "dark:hidden"
                }
              ),
              /* @__PURE__ */ jsx(
                "img",
                {
                  src: "/Vapehybrid dark icon.svg",
                  alt: "Verified",
                  className: "hidden dark:block"
                }
              ),
              /* @__PURE__ */ jsx("span", { className: "text-design-primary dark:text-design-primary text-xs", children: "Verified" })
            ] }),
            deal.success_rate !== void 0 && /* @__PURE__ */ jsxs(
              "span",
              {
                className: `ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full ${deal.success_rate >= 90 ? "bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground" : deal.success_rate >= 70 ? "bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground" : "bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"}`,
                title: `${Math.round(deal.success_rate || 85)}% success rate`,
                children: [
                  /* @__PURE__ */ jsx(ThumbsUp, { size: 10, className: "mr-1" }),
                  Math.round(deal.success_rate || 85),
                  "%"
                ]
              }
            ),
            isExpiringSoon && /* @__PURE__ */ jsxs(
              "span",
              {
                className: `ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full ${daysRemaining === 1 ? "bg-design-destructive/15 text-design-destructive dark:bg-design-destructive/25 dark:text-design-destructive-foreground" : "bg-design-warning/15 text-design-warning dark:bg-design-warning/25 dark:text-design-warning-foreground"}`,
                title: `Expires in ${daysRemaining} ${daysRemaining === 1 ? "day" : "days"}`,
                children: [
                  /* @__PURE__ */ jsx("svg", { className: "w-3 h-3 mr-1", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: /* @__PURE__ */ jsx("path", { d: "M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }) }),
                  daysRemaining === 1 ? "Ends today" : `${daysRemaining}d left`
                ]
              }
            )
          ] }),
          /* @__PURE__ */ jsx("h3", { className: "deal-card-title line-clamp-2 mb-2 text-sm font-semibold", children: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-1", children: [
            /* @__PURE__ */ jsxs("div", { className: "c-avatar-group user-avatar-group flex relative", style: { marginRight: "0.35rem" }, children: [
              staffAvatars.slice(0, Math.min(3, count || 3)).map((avatar, index) => /* @__PURE__ */ jsxs(
                "div",
                {
                  className: "c-avatar c-avatar--xs user-avatar relative transition-transform hover:scale-110 hover:z-10 overflow-hidden",
                  style: {
                    width: "20px",
                    height: "20px",
                    marginLeft: index > 0 ? "-8px" : "0",
                    borderRadius: "50%",
                    border: "1.5px solid white",
                    boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                    zIndex: 3 - index,
                    backgroundColor: avatar.color
                    // Fallback background color
                  },
                  title: `Staff member: ${avatar.name}`,
                  children: [
                    avatar.webpPath && avatar.imagePath ? /* @__PURE__ */ jsxs("picture", { children: [
                      /* @__PURE__ */ jsx("source", { srcSet: avatar.webpPath, type: "image/webp" }),
                      /* @__PURE__ */ jsx(
                        "img",
                        {
                          src: avatar.imagePath,
                          alt: avatar.initials,
                          className: "w-full h-full object-cover",
                          onError: (e) => {
                            const target = e.target;
                            target.style.display = "none";
                            const parent = target.parentElement?.parentElement;
                            if (parent) {
                              parent.classList.add("fallback-active");
                            }
                          }
                        }
                      )
                    ] }) : (
                      // Fallback to initials if no image path
                      /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center text-white font-bold", children: /* @__PURE__ */ jsx("span", { style: { fontSize: "8px" }, children: avatar.initials }) })
                    ),
                    /* @__PURE__ */ jsx("div", { className: "fallback-content absolute inset-0 flex items-center justify-center text-white font-bold opacity-0", children: /* @__PURE__ */ jsx("span", { style: { fontSize: "8px" }, children: avatar.initials }) })
                  ]
                },
                index
              )),
              count > 3 && /* @__PURE__ */ jsxs(
                "div",
                {
                  className: "c-avatar c-avatar--xs user-avatar flex items-center justify-center bg-design-muted-foreground/20",
                  style: {
                    width: "20px",
                    height: "20px",
                    marginLeft: "-8px",
                    borderRadius: "50%",
                    border: "1.5px solid white",
                    boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                    fontSize: "8px"
                  },
                  title: `${count - 3} more staff members`,
                  children: [
                    "+",
                    count - 3
                  ]
                }
              )
            ] }),
            /* @__PURE__ */ jsxs("span", { children: [
              "Verified ",
              /* @__PURE__ */ jsx("time", { dateTime: deal.last_verified_at, title: new Date(deal.last_verified_at || "").toLocaleString(), children: usageTimeAgo }),
              " by ",
              count || 3,
              " staffer",
              (count || 3) > 1 ? "s" : ""
            ] })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "flex-grow" }),
          /* @__PURE__ */ jsxs(
            "div",
            {
              className: "flex justify-between items-center w-full mt-auto mb-2 px-2 pt-2 border-t border-design-muted border-opacity-20",
              role: "group",
              "aria-label": "Deal actions",
              style: { touchAction: "manipulation" },
              children: [
                /* @__PURE__ */ jsxs(
                  "button",
                  {
                    className: "eye-button p-2 rounded-full hover:bg-design-muted transition-colors relative z-10",
                    onClick: (e) => {
                      e.stopPropagation();
                      window.open(`/deal/${deal.id}`, "_blank");
                    },
                    "aria-label": "View deal details",
                    title: "View deal details",
                    tabIndex: 0,
                    style: { minWidth: "36px", minHeight: "36px" },
                    children: [
                      /* @__PURE__ */ jsx(Eye, { size: 18, className: "text-design-muted-foreground hover:text-design-foreground transition-colors" }),
                      /* @__PURE__ */ jsx("span", { className: "sr-only", children: "View deal details" })
                    ]
                  }
                ),
                /* @__PURE__ */ jsx(
                  "button",
                  {
                    className: "copy-code-button h-8 px-4 text-sm mx-2 transition-colors hover:bg-design-primary hover:text-white dark:hover:text-black focus:outline-none focus:ring-2 focus:ring-design-primary focus:ring-offset-2",
                    onClick: (e) => {
                      e.preventDefault();
                      e.stopPropagation();
                      handleCopy(e);
                    },
                    "aria-label": "Copy Code",
                    title: "Copy coupon code",
                    tabIndex: 0,
                    children: "Copy Code"
                  }
                ),
                /* @__PURE__ */ jsx(
                  BookmarkButton,
                  {
                    dealId: deal.id.toString(),
                    className: "heart-bookmark-button"
                  }
                )
              ]
            }
          )
        ] })
      ]
    }
  );
};

const ImprovedDealCard$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  ImprovedDealCard
}, Symbol.toStringTag, { value: 'Module' }));

const DealStructuredData = ({ deal, imageUrl }) => {
  const ratingValue = deal.success_rate ? deal.success_rate / 20 : 4.5;
  const ratingCount = deal.usage_count || Math.floor(Math.random() * 50) + 10;
  const getCurrencyCode = (currencyValue) => {
    if (!currencyValue) return "USD";
    const currencyMap = {
      "$": "USD",
      "€": "EUR",
      "£": "GBP",
      "¥": "JPY",
      "₹": "INR",
      "₽": "RUB",
      "CN¥": "CNY",
      "A$": "AUD",
      "C$": "CAD"
    };
    if (/^[A-Z]{3}$/.test(currencyValue)) {
      return currencyValue;
    }
    return currencyMap[currencyValue] || "USD";
  };
  const offerObject = {
    "@type": "Offer",
    "name": deal.title,
    "description": deal.description || `${deal.title} - Save with this deal`,
    "price": deal.price || "0",
    "priceCurrency": getCurrencyCode(deal.currency),
    "availability": "https://schema.org/InStock",
    "url": `${typeof window !== "undefined" ? window.location.origin : ""}/deal/${deal.id}`,
    "seller": {
      "@type": "Organization",
      "name": deal.merchants?.name || deal.brands?.name || "Retailer"
    },
    "discount": deal.discount ? `${deal.discount}%` : void 0,
    "validFrom": deal.deal_start_date || void 0,
    "validThrough": deal.deal_end_date || void 0,
    "priceValidUntil": deal.deal_end_date || void 0
  };
  const productObject = {
    "name": deal.title,
    "description": deal.description || `${deal.title} - Save with this deal`,
    "image": imageUrl,
    "brand": {
      "@type": "Brand",
      "name": deal.brands?.name || ""
    },
    // Add aggregateRating property to satisfy Schema.org requirements
    "aggregateRating": {
      "@type": "AggregateRating",
      "ratingValue": ratingValue.toFixed(1),
      "bestRating": "5",
      "worstRating": "1",
      "ratingCount": ratingCount
    }
  };
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Product",
    "name": productObject.name,
    "description": productObject.description,
    "image": productObject.image,
    "brand": productObject.brand,
    "offers": offerObject,
    "aggregateRating": productObject.aggregateRating
  };
  const cleanedData = JSON.parse(
    JSON.stringify(structuredData, (_, value) => value === void 0 ? null : value).replace(/"null"/g, "null")
  );
  return /* @__PURE__ */ jsx(
    "script",
    {
      type: "application/ld+json",
      dangerouslySetInnerHTML: { __html: JSON.stringify(cleanedData) }
    }
  );
};

const generateCouponUrl = (deal) => {
  if (deal.slug) {
    return `/coupon/${deal.slug}`;
  }
  const fallbackSlug = deal.normalized_title || deal.cleaned_title || generateSlug(deal.title || deal.id.toString());
  return `/coupon/${fallbackSlug}`;
};
const generateSlug = (title) => {
  return title.toLowerCase().replace(/[^a-z0-9\s-]/g, "").replace(/\s+/g, "-").replace(/-+/g, "-").replace(/^-+|-+$/g, "").trim();
};

function usePrefetch(url, options = {}) {
  const { delay = 100, once = true } = options;
  const hasPrefetched = useRef(false);
  const timeoutRef = useRef(null);
  const prefetch = useCallback((url2) => {
    if (once && hasPrefetched.current) return;
    if (!url2 || typeof window === "undefined") return;
    try {
      const link = document.createElement("link");
      link.rel = "prefetch";
      link.href = url2;
      link.as = url2.endsWith(".js") ? "script" : url2.endsWith(".css") ? "style" : url2.includes("/api/") ? "fetch" : "document";
      document.head.appendChild(link);
      hasPrefetched.current = true;
      if (process.env.NODE_ENV !== "production") {
        console.log(`Prefetched: ${url2}`);
      }
    } catch (error) {
      console.error("Error prefetching:", error);
    }
  }, [once]);
  const prefetchDeal = useCallback((dealId) => {
    if (once && hasPrefetched.current) return;
    if (!dealId || typeof window === "undefined") return;
    try {
      prefetch(`/deal/${dealId}`);
      hasPrefetched.current = true;
    } catch (error) {
      console.error("Error prefetching deal:", error);
    }
  }, [prefetch, once]);
  const handleMouseEnter = useCallback(() => {
    if (timeoutRef.current) {
      window.clearTimeout(timeoutRef.current);
    }
    timeoutRef.current = window.setTimeout(() => {
      if (url.includes("/deal/")) {
        const dealId = url.split("/").pop();
        if (dealId) prefetchDeal(dealId);
      } else {
        prefetch(url);
      }
    }, delay);
  }, [url, delay, prefetch, prefetchDeal]);
  const handleMouseLeave = useCallback(() => {
    if (timeoutRef.current) {
      window.clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
  }, []);
  return {
    onMouseEnter: handleMouseEnter,
    onMouseLeave: handleMouseLeave,
    prefetch,
    prefetchDeal
  };
}

function useEngagementTracking(id, type = "component", options = {}) {
  const {
    minDwellTime = 2e3,
    trackOnce = true,
    trackOnUnmount = true,
    trackHover = true,
    trackScroll = false
  } = options;
  const startTimeRef = useRef(0);
  const dwellTimeRef = useRef(0);
  const isVisibleRef = useRef(false);
  const isHoveredRef = useRef(false);
  const isClickedRef = useRef(false);
  const hasTrackedRef = useRef(false);
  const timerRef = useRef(null);
  const [isVisible, setIsVisible] = useState(false);
  const handleVisible = useCallback(() => {
    if (!isVisibleRef.current) {
      isVisibleRef.current = true;
      startTimeRef.current = Date.now();
    }
  }, []);
  const handleInvisible = useCallback(() => {
    if (isVisibleRef.current) {
      isVisibleRef.current = false;
      dwellTimeRef.current += Date.now() - startTimeRef.current;
    }
  }, []);
  const handleMouseEnter = useCallback(() => {
    isHoveredRef.current = true;
  }, []);
  const handleMouseLeave = useCallback(() => {
    isHoveredRef.current = false;
  }, []);
  const handleClick = useCallback(() => {
    isClickedRef.current = true;
  }, []);
  const trackEngagement = useCallback(() => {
    if (trackOnce && hasTrackedRef.current) return;
    const finalDwellTime = isVisibleRef.current ? dwellTimeRef.current + (Date.now() - startTimeRef.current) : dwellTimeRef.current;
    const engagementData = {
      id,
      type,
      dwellTime: finalDwellTime,
      hovered: isHoveredRef.current,
      clicked: isClickedRef.current,
      engaged: finalDwellTime >= minDwellTime,
      firstVisible: startTimeRef.current,
      lastVisible: Date.now()
    };
    if (!engagementData.engaged && !engagementData.clicked) return;
    try {
      if (process.env.NODE_ENV !== "production") {
        console.log("Engagement tracked:", engagementData);
      } else {
        if (typeof navigator.sendBeacon === "function") {
          const blob = new Blob([JSON.stringify(engagementData)], { type: "application/json" });
          navigator.sendBeacon("/api/track-engagement", blob);
        } else {
          fetch("/api/track-engagement", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify(engagementData),
            // Use keepalive to ensure the request completes even if the page is unloading
            keepalive: true
          }).catch((error) => console.error("Error tracking engagement:", error));
        }
      }
      hasTrackedRef.current = true;
    } catch (error) {
      console.error("Error tracking engagement:", error);
    }
  }, [id, type, minDwellTime, trackOnce]);
  useEffect(() => {
    return () => {
      if (timerRef.current) {
        window.clearTimeout(timerRef.current);
      }
      if (trackOnUnmount) {
        trackEngagement();
      }
    };
  }, [trackOnUnmount, trackEngagement]);
  useEffect(() => {
    if (isVisible && !hasTrackedRef.current) {
      timerRef.current = window.setTimeout(() => {
        trackEngagement();
      }, minDwellTime);
      return () => {
        if (timerRef.current) {
          window.clearTimeout(timerRef.current);
          timerRef.current = null;
        }
      };
    }
  }, [isVisible, minDwellTime, trackEngagement]);
  return {
    isVisible,
    setIsVisible,
    handleVisible,
    handleInvisible,
    handleMouseEnter,
    handleMouseLeave,
    handleClick,
    trackEngagement
  };
}

const DelayedStructuredData$1 = ({ deal, imageUrl }) => {
  const [shouldRender, setShouldRender] = useState(false);
  useEffect(() => {
    const requestIdleCallbackPolyfill = window.requestIdleCallback || ((cb) => setTimeout(cb, 1));
    const idleCallbackId = requestIdleCallbackPolyfill(() => {
      setShouldRender(true);
    });
    return () => {
      const cancelIdleCallbackPolyfill = window.cancelIdleCallback || ((id) => clearTimeout(id));
      cancelIdleCallbackPolyfill(idleCallbackId);
    };
  }, []);
  if (!shouldRender) return null;
  return /* @__PURE__ */ jsx(DealStructuredData, { deal, imageUrl });
};
const OptimizedDealCard = ({
  deal,
  className,
  isRevealed = false,
  priority = false,
  onShowCouponModal,
  viewMode,
  // Extract viewMode to prevent it from being passed to DOM
  ...props
}) => {
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);
  const cardRef = useRef(null);
  const dealUrl = `/deal/${deal.id}`;
  const { onMouseEnter: handlePrefetch, onMouseLeave: handleCancelPrefetch } = usePrefetch(dealUrl, { delay: 150 });
  const {
    setIsVisible,
    handleMouseEnter: trackMouseEnter,
    handleMouseLeave: trackMouseLeave,
    handleClick: trackClick
  } = useEngagementTracking(deal.id, "deal", { minDwellTime: 2e3 });
  const staffAvatars = useMemo(() => getRandomStaffImages(5, deal.id), [deal.id]);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = useMemo(() => usageInfo.timeAgo, [usageInfo]);
  const count = useMemo(() => deal.usage_count || usageInfo.count, [deal.usage_count, usageInfo.count]);
  const daysRemaining = useMemo(() => {
    if (!deal.deal_end_date) return null;
    return Math.ceil((new Date(deal.deal_end_date).getTime() - (/* @__PURE__ */ new Date()).getTime()) / (1e3 * 60 * 60 * 24));
  }, [deal.deal_end_date]);
  useMemo(() => {
    if (daysRemaining === null || daysRemaining <= 0) return null;
    if (daysRemaining > 365) {
      const years = Math.floor(daysRemaining / 365);
      return `${years}+ ${years === 1 ? "year" : "years"} left`;
    }
    if (daysRemaining > 30) {
      const months = Math.floor(daysRemaining / 30);
      return `${months} ${months === 1 ? "month" : "months"} left`;
    }
    if (daysRemaining > 1) {
      return `${daysRemaining}d left`;
    }
    return "Ends today";
  }, [daysRemaining]);
  useMemo(() => {
    return daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  }, [daysRemaining]);
  const imageUrl = useMemo(() => {
    const dealImage = normalizeUrl(deal.imagebig_url) || normalizeUrl(deal.image_url) || normalizeUrl(deal.imagesmall_url);
    if (dealImage) return dealImage;
    const brandLogo = normalizeUrl(deal.brands?.logo_url) || normalizeUrl(deal.brand_logo_url);
    if (brandLogo) return brandLogo;
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) || normalizeUrl(deal.merchant_logo_url);
    if (merchantLogo) return merchantLogo;
    return "/placeholder-image.svg";
  }, [
    deal.imagebig_url,
    deal.image_url,
    deal.imagesmall_url,
    deal.brands?.logo_url,
    deal.brand_logo_url,
    deal.merchants?.logo_url,
    deal.merchant_logo_url
  ]);
  useEffect(() => {
    if (!cardRef.current) return;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting);
          if (entry.isIntersecting) {
            if (process.env.NODE_ENV !== "production") {
              console.log("Deal impression tracked:", deal.id);
            } else {
              try {
                let sessionId = localStorage.getItem("vh_session_id");
                if (!sessionId) {
                  sessionId = "session_" + Math.random().toString(36).substring(2, 15);
                  localStorage.setItem("vh_session_id", sessionId);
                }
                if (typeof navigator.sendBeacon === "function") {
                  const trackingData = new FormData();
                  trackingData.append("deal_id", deal.id.toString());
                  trackingData.append("session_id", sessionId);
                  navigator.sendBeacon("/api/track-impression?deal_id=" + deal.id + "&session_id=" + sessionId, trackingData);
                } else {
                  fetch("/api/track-impression?deal_id=" + deal.id + "&session_id=" + sessionId, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ deal_id: deal.id, session_id: sessionId }),
                    keepalive: true
                  });
                }
              } catch (error) {
                console.error("Error tracking impression:", error);
              }
            }
          }
        });
      },
      { threshold: 0.5 }
      // Card is considered visible when 50% is in view
    );
    observer.observe(cardRef.current);
    return () => {
      observer.disconnect();
    };
  }, [deal.id, cardRef, setIsVisible]);
  const handleCardClick = useCallback(() => {
    setIsCodeRevealed(true);
    if (typeof window !== "undefined") {
      const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
      const hasBeenClickedBefore = clickedDeals[deal.id];
      const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
      revealedCodes[deal.id] = true;
      localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
      clickedDeals[deal.id] = true;
      localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
      if (deal.coupon_code) {
        copyToClipboard(deal.coupon_code);
      }
      const currentUrl = new URL(window.location.href);
      const viewMode2 = currentUrl.searchParams.get("view") || "grid";
      const popupUrl = new URL(window.location.href);
      popupUrl.searchParams.set("dealId", deal.id.toString());
      popupUrl.searchParams.set("showPopup", "true");
      popupUrl.searchParams.set("view", viewMode2);
      window.open(popupUrl.toString(), "_blank");
      if (onShowCouponModal) {
        onShowCouponModal(deal);
      }
      if (!hasBeenClickedBefore && deal.tracking_url) {
        window.location.href = deal.tracking_url;
      } else if (!hasBeenClickedBefore) {
        window.location.href = `/go/${deal.id}`;
      }
    }
  }, [deal, onShowCouponModal]);
  const handleDealClick = handleCardClick;
  const handleCopy = useCallback((e) => {
    e.stopPropagation();
    trackClick();
    requestAnimationFrame(() => {
      setIsCodeRevealed(true);
      if (typeof window !== "undefined") {
        if (typeof navigator.sendBeacon === "function") {
          try {
            const trackingData = new FormData();
            trackingData.append("deal_id", deal.id.toString());
            trackingData.append("fallback", "true");
            navigator.sendBeacon("/api/track-click", trackingData);
            console.log(`Tracking copy for deal_id=${deal.id} only`);
          } catch (error) {
            console.error("Error sending beacon:", error);
          }
        }
        const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
        const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
        revealedCodes[deal.id] = true;
        localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
        clickedDeals[deal.id] = true;
        localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
        if (deal.coupon_code) {
          copyToClipboard(deal.coupon_code);
        }
        const currentUrl = new URL(window.location.href);
        const viewMode2 = currentUrl.searchParams.get("view") || "grid";
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set("dealId", deal.id.toString());
        popupUrl.searchParams.set("showPopup", "true");
        popupUrl.searchParams.set("solidBg", "true");
        popupUrl.searchParams.set("view", viewMode2);
        window.open(popupUrl.toString(), "_blank");
        if (deal.tracking_url) {
          window.location.href = deal.tracking_url;
        } else {
          window.location.href = `/go/${deal.id}`;
        }
      }
    });
  }, [deal, trackClick]);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    typeof window !== "undefined" && /* @__PURE__ */ jsx(DelayedStructuredData$1, { deal, imageUrl }),
    /* @__PURE__ */ jsxs(
      Card,
      {
        ref: cardRef,
        variant: "default",
        interactive: true,
        glowEffect: true,
        className: cn(
          "optimized-deal-card deal-card relative overflow-hidden cursor-pointer w-full flex flex-col max-h-[550px]",
          "transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.01]",
          "hover:border-design-primary/50 dark:hover:border-design-primary/50",
          "border-black/15 dark:border-white/15",
          // Distinguishable border in both modes
          "optimized-card",
          // Add content-visibility optimization
          className
        ),
        style: {
          borderRadius: "25px",
          borderWidth: "1.5px"
        },
        "data-theme-border": "true",
        onClick: () => {
          handleDealClick();
          trackClick();
        },
        onMouseEnter: () => {
          handlePrefetch();
          trackMouseEnter();
        },
        onMouseLeave: () => {
          handleCancelPrefetch();
          trackMouseLeave();
        },
        "aria-label": `Deal: ${deal.title}`,
        role: "button",
        tabIndex: 0,
        ...props,
        children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              id: `tracking-pixel-${deal.id}`,
              style: { position: "absolute", opacity: 0, pointerEvents: "none" },
              "aria-hidden": "true"
            }
          ),
          /* @__PURE__ */ jsx("div", { className: "w-full p-3", style: { height: "52%" }, children: /* @__PURE__ */ jsx("div", { className: "flex justify-center h-full", children: /* @__PURE__ */ jsx(
            "div",
            {
              className: "relative overflow-hidden rounded-lg flex items-center justify-center w-full h-full image-container bg-white dark:bg-white",
              style: {
                maxHeight: "calc(550px * 0.52 - 24px)",
                // 52% of 550px minus padding
                aspectRatio: "1/1",
                boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.05)",
                padding: "8px"
                // Add padding to ensure image doesn't touch the edges
              },
              children: /* @__PURE__ */ jsx(
                DealImage,
                {
                  src: imageUrl,
                  alt: deal.title || "Deal image",
                  width: 400,
                  height: 400,
                  priority,
                  fetchpriority: priority ? "high" : "auto",
                  className: "",
                  fallbackSrc: "/placeholder-image.svg",
                  index: 0
                }
              )
            }
          ) }) }),
          /* @__PURE__ */ jsxs("div", { className: "px-3 pb-0 flex-1 flex flex-col", style: { height: "48%", maxHeight: "calc(550px * 0.48 - 24px)" }, children: [
            /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center mb-2", children: [
              /* @__PURE__ */ jsx("div", { className: "relative", children: /* @__PURE__ */ jsx("div", { className: "text-xl font-bold text-design-foreground", children: deal.discount ? /* @__PURE__ */ jsxs("span", { className: "font-bold text-primary dark:text-primary", children: [
                deal.discount,
                "% ",
                /* @__PURE__ */ jsx("span", { className: "text-xs font-bold text-gray-900 dark:text-gray-400", children: "off " })
              ] }) : deal.price ? `${deal.currency || "$"}${deal.price}` : "20% Off" }) }),
              /* @__PURE__ */ jsx("div", { className: "flex items-center ml-1", children: /* @__PURE__ */ jsx(
                "div",
                {
                  className: "deal-coupon-code text-xs bg-design-muted px-2 py-1 rounded-full border border-design-muted-foreground/10 relative overflow-hidden",
                  children: deal.coupon_code ? /* @__PURE__ */ jsxs(Fragment, { children: [
                    /* @__PURE__ */ jsx(
                      "span",
                      {
                        className: `transition-all duration-300 ${isCodeRevealed ? "blur-none opacity-100" : "blur-[3px] select-none"}`,
                        "aria-hidden": !isCodeRevealed,
                        children: deal.coupon_code
                      }
                    ),
                    isCodeRevealed && /* @__PURE__ */ jsx(
                      "span",
                      {
                        className: "absolute inset-0 bg-design-primary/10 animate-reveal-sweep",
                        "aria-hidden": "true"
                      }
                    )
                  ] }) : "NO CODE"
                }
              ) })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "text-xs text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1", children: [
              /* @__PURE__ */ jsx(
                "span",
                {
                  className: "font-medium truncate max-w-[120px] hover:underline cursor-help",
                  title: `Brand: ${deal.merchants?.name || deal.brands?.name || "Unknown"}`,
                  children: deal.merchants?.name || deal.brands?.name || "Brand"
                }
              ),
              deal.verified && /* @__PURE__ */ jsxs("span", { className: "c-verified-badge verified-badge ml-2 inline-flex items-center", children: [
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid light icon.svg",
                    alt: "Verified",
                    className: "dark:hidden inline-block",
                    width: "10",
                    height: "10"
                  }
                ),
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid dark icon.svg",
                    alt: "Verified",
                    className: "hidden dark:inline-block",
                    width: "10",
                    height: "10"
                  }
                ),
                /* @__PURE__ */ jsx("span", { className: "text-design-primary dark:text-design-primary text-[10px] inline-block", children: "Verified" })
              ] }),
              deal.success_rate !== void 0 && /* @__PURE__ */ jsxs(
                "span",
                {
                  className: `ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full ${deal.success_rate >= 90 ? "bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground" : deal.success_rate >= 70 ? "bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground" : "bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"}`,
                  title: `${Math.round(deal.success_rate || 85)}% success rate`,
                  children: [
                    /* @__PURE__ */ jsx(ThumbsUp, { size: 10, className: "mr-1" }),
                    Math.round(deal.success_rate || 85),
                    "%"
                  ]
                }
              ),
              daysRemaining !== null && daysRemaining > 0 && /* @__PURE__ */ jsxs("span", { className: "ml-2 text-xs flex items-center text-design-muted-foreground", children: [
                /* @__PURE__ */ jsx("svg", { className: "w-3 h-3 mr-1", viewBox: "0 0 24 24", fill: "none", xmlns: "http://www.w3.org/2000/svg", children: /* @__PURE__ */ jsx("path", { d: "M12 8V12L15 15M21 12C21 16.9706 16.9706 21 12 21C7.02944 21 3 16.9706 3 12C3 7.02944 7.02944 3 12 3C16.9706 3 21 7.02944 21 12Z", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round" }) }),
                daysRemaining > 365 ? `${Math.floor(daysRemaining / 365)}+ ${Math.floor(daysRemaining / 365) === 1 ? "year" : "years"} left` : daysRemaining > 30 ? `${Math.floor(daysRemaining / 30)} ${Math.floor(daysRemaining / 30) === 1 ? "month" : "months"} left` : daysRemaining === 1 ? "Ends today" : `${daysRemaining}d left`
              ] })
            ] }),
            /* @__PURE__ */ jsx(
              "h3",
              {
                className: "deal-card-title line-clamp-2 mb-2 text-sm font-semibold overflow-hidden",
                title: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title,
                children: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title
              }
            ),
            /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-1", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex -space-x-2", children: [
                staffAvatars.slice(0, Math.min(3, count || 3)).map((avatar, index) => /* @__PURE__ */ jsxs(
                  "div",
                  {
                    className: "relative inline-flex items-center justify-center w-5 h-5 rounded-full border-2 border-white bg-white dark:border-gray-800 dark:bg-gray-800 overflow-hidden",
                    style: {
                      zIndex: 3 - index,
                      backgroundColor: avatar.color || "#6b7280"
                      // Fallback background color
                    },
                    title: `Staff member: ${avatar.name}`,
                    children: [
                      avatar.webpPath || avatar.imagePath ? /* @__PURE__ */ jsx(
                        "img",
                        {
                          src: avatar.webpPath || avatar.imagePath || "",
                          alt: avatar.initials,
                          className: "w-full h-full object-cover",
                          onError: (e) => {
                            const target = e.target;
                            target.style.display = "none";
                            const fallback = target.nextElementSibling;
                            if (fallback) {
                              fallback.style.display = "flex";
                            }
                          },
                          loading: "lazy"
                        }
                      ) : null,
                      /* @__PURE__ */ jsx(
                        "div",
                        {
                          className: "absolute inset-0 flex items-center justify-center text-white font-bold",
                          style: {
                            display: !avatar.webpPath && !avatar.imagePath ? "flex" : "none",
                            fontSize: "8px",
                            textShadow: "0 1px 1px rgba(0,0,0,0.3)"
                          },
                          children: avatar.initials
                        }
                      )
                    ]
                  },
                  index
                )),
                count > 3 && /* @__PURE__ */ jsxs(
                  "div",
                  {
                    className: "relative flex items-center justify-center w-5 h-5 rounded-full border-2 border-white bg-gray-100 dark:border-gray-800 dark:bg-gray-700 text-gray-600 dark:text-gray-300",
                    style: {
                      zIndex: 0,
                      fontSize: "8px",
                      fontWeight: "bold",
                      boxShadow: "0 1px 2px rgba(0,0,0,0.1)"
                    },
                    title: `${count - 3} more staff members`,
                    children: [
                      "+",
                      count - 3
                    ]
                  }
                )
              ] }),
              /* @__PURE__ */ jsxs("span", { className: "ml-2", children: [
                "Verified ",
                /* @__PURE__ */ jsx("time", { dateTime: deal.last_verified_at, title: new Date(deal.last_verified_at || "").toLocaleString(), children: usageTimeAgo }),
                " by ",
                count || 3,
                " staffer",
                (count || 3) > 1 ? "s" : ""
              ] })
            ] }),
            /* @__PURE__ */ jsx("div", { className: "flex-grow" }),
            /* @__PURE__ */ jsxs(
              "div",
              {
                className: "flex justify-between items-center w-full mt-auto mb-2 px-2 pt-2 border-t border-design-muted border-opacity-20",
                role: "group",
                "aria-label": "Deal actions",
                style: { touchAction: "manipulation" },
                children: [
                  /* @__PURE__ */ jsxs(
                    "button",
                    {
                      className: "eye-button p-2 rounded-full hover:bg-design-muted transition-colors relative z-10",
                      onClick: (e) => {
                        e.stopPropagation();
                        window.open(generateCouponUrl(deal), "_blank");
                      },
                      "aria-label": "View coupon details",
                      title: "View coupon details",
                      tabIndex: 0,
                      style: { minWidth: "36px", minHeight: "36px" },
                      children: [
                        /* @__PURE__ */ jsx(Eye, { size: 18, className: "text-design-muted-foreground hover:text-design-foreground transition-colors" }),
                        /* @__PURE__ */ jsx("span", { className: "sr-only", children: "View deal details" })
                      ]
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    "button",
                    {
                      className: "copy-code-button h-8 px-4 text-sm mx-2 transition-colors hover:bg-design-primary hover:text-white dark:hover:text-black focus:outline-none focus:ring-2 focus:ring-design-primary focus:ring-offset-2 rounded-[25px]",
                      onClick: handleCopy,
                      "aria-label": "Copy Code",
                      title: "Copy coupon code",
                      tabIndex: 0,
                      children: "Copy Code"
                    }
                  ),
                  /* @__PURE__ */ jsx(
                    BookmarkButton,
                    {
                      dealId: deal.id.toString(),
                      className: "bookmark-button p-2 rounded-full hover:bg-design-muted transition-colors"
                    }
                  )
                ]
              }
            )
          ] })
        ]
      }
    )
  ] });
};

const OptimizedDealCard$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  OptimizedDealCard
}, Symbol.toStringTag, { value: 'Module' }));

const MerchantCard = React__default.forwardRef(
  ({
    name,
    logoUrl,
    description,
    dealCount,
    averageDiscount,
    onClick,
    className,
    ...props
  }, ref) => {
    return /* @__PURE__ */ jsx(
      Card,
      {
        ref,
        className: cn(
          "merchant-card",
          onClick && "cursor-pointer",
          className
        ),
        interactive: !!onClick,
        onClick,
        ...props,
        children: /* @__PURE__ */ jsxs("div", { className: "merchant-card-content", children: [
          logoUrl && /* @__PURE__ */ jsx("div", { className: "merchant-card-logo", children: /* @__PURE__ */ jsx("img", { src: logoUrl, alt: name }) }),
          /* @__PURE__ */ jsxs("div", { className: "merchant-card-info", children: [
            /* @__PURE__ */ jsx("h3", { className: "merchant-card-name", children: name }),
            description && /* @__PURE__ */ jsx("p", { className: "merchant-card-description", children: description }),
            /* @__PURE__ */ jsxs("div", { className: "merchant-card-stats", children: [
              dealCount !== void 0 && /* @__PURE__ */ jsxs("div", { className: "merchant-card-stat", children: [
                /* @__PURE__ */ jsx("span", { className: "merchant-card-stat-value", children: dealCount }),
                /* @__PURE__ */ jsx("span", { className: "merchant-card-stat-label", children: dealCount === 1 ? "Deal" : "Deals" })
              ] }),
              averageDiscount && /* @__PURE__ */ jsxs("div", { className: "merchant-card-stat", children: [
                /* @__PURE__ */ jsx("span", { className: "merchant-card-stat-value", children: averageDiscount }),
                /* @__PURE__ */ jsx("span", { className: "merchant-card-stat-label", children: "Avg. Discount" })
              ] })
            ] })
          ] })
        ] })
      }
    );
  }
);
MerchantCard.displayName = "MerchantCard";

const CategoryCard = React__default.forwardRef(
  ({
    name,
    imageUrl,
    dealCount,
    onClick,
    className,
    ...props
  }, ref) => {
    return /* @__PURE__ */ jsxs(
      Card,
      {
        ref,
        className: cn(
          "category-card",
          onClick && "cursor-pointer",
          className
        ),
        interactive: !!onClick,
        onClick,
        ...props,
        children: [
          imageUrl && /* @__PURE__ */ jsx("div", { className: "category-card-image", children: /* @__PURE__ */ jsx("img", { src: imageUrl, alt: name }) }),
          /* @__PURE__ */ jsxs("div", { className: "category-card-info", children: [
            /* @__PURE__ */ jsx("h3", { className: "category-card-name", children: name }),
            dealCount !== void 0 && /* @__PURE__ */ jsxs("div", { className: "category-card-count", children: [
              /* @__PURE__ */ jsx("span", { children: dealCount }),
              /* @__PURE__ */ jsx("span", { children: dealCount === 1 ? "Deal" : "Deals" })
            ] })
          ] })
        ]
      }
    );
  }
);
CategoryCard.displayName = "CategoryCard";

const IconButton = React__default.forwardRef(
  ({
    icon,
    variant = "primary",
    size = "md",
    round = false,
    loading = false,
    disabled = false,
    className,
    ...props
  }, ref) => {
    const variantClass = {
      primary: "design-button-primary",
      secondary: "design-button-secondary",
      outline: "design-button-outline",
      ghost: "design-button-ghost",
      destructive: "design-button-destructive",
      accent: "design-button-accent",
      link: "design-button-link"
    }[variant];
    const sizeClass = {
      xs: "design-button-sm p-1",
      sm: "design-button-sm p-1",
      md: "p-2",
      lg: "design-button-lg p-3",
      xl: "design-button-lg p-3",
      icon: "p-2"
    }[size];
    return /* @__PURE__ */ jsx(
      "button",
      {
        ref,
        className: cn(
          "design-button",
          "design-icon-button",
          variantClass,
          sizeClass,
          round && "rounded-full",
          loading && "design-button-loading",
          disabled && "opacity-50 cursor-not-allowed",
          className
        ),
        disabled: disabled || loading,
        ...props,
        children: loading ? /* @__PURE__ */ jsxs("svg", { className: "animate-spin h-5 w-5", xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", children: [
          /* @__PURE__ */ jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }),
          /* @__PURE__ */ jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })
        ] }) : icon
      }
    );
  }
);
IconButton.displayName = "IconButton";

const LinkButton = React__default.forwardRef(
  ({
    variant = "primary",
    size = "md",
    disabled = false,
    leftIcon,
    rightIcon,
    className,
    children,
    ...props
  }, ref) => {
    const variantStyles = {
      primary: "bg-primary text-white/90 dark:text-black/90 hover:text-white hover:bg-primary/90 shadow-lg",
      secondary: "bg-design-secondary text-white/90 dark:text-black/90 hover:text-white hover:bg-design-secondary/90 shadow-lg",
      outline: "border border-design-border bg-transparent text-design-foreground hover:bg-design-accent/5",
      ghost: "bg-transparent text-design-foreground hover:bg-design-accent/5",
      destructive: "bg-red-600 text-white/90 dark:text-white/90 hover:text-white hover:bg-red-700 shadow-lg"
    }[variant];
    const sizeStyles = {
      sm: "px-4 py-2 text-sm",
      md: "px-6 py-3 text-base",
      lg: "px-8 py-4 text-lg"
    }[size];
    if (disabled) {
      return /* @__PURE__ */ jsxs(
        "span",
        {
          className: cn(
            "inline-flex items-center justify-center font-bold rounded-full transition-all duration-300",
            variantStyles,
            sizeStyles,
            "opacity-50 cursor-not-allowed",
            className
          ),
          children: [
            leftIcon && /* @__PURE__ */ jsx("span", { className: "mr-2", children: leftIcon }),
            children,
            rightIcon && /* @__PURE__ */ jsx("span", { className: "ml-2", children: rightIcon })
          ]
        }
      );
    }
    return /* @__PURE__ */ jsxs(
      "a",
      {
        ref,
        className: cn(
          "inline-flex items-center justify-center font-bold rounded-full transition-all duration-300",
          variantStyles,
          sizeStyles,
          className
        ),
        ...props,
        children: [
          leftIcon && /* @__PURE__ */ jsx("span", { className: "mr-2", children: leftIcon }),
          children,
          rightIcon && /* @__PURE__ */ jsx("span", { className: "ml-2", children: rightIcon })
        ]
      }
    );
  }
);
LinkButton.displayName = "LinkButton";

const Input = React__default.forwardRef(
  ({
    error = false,
    errorMessage,
    leftIcon,
    rightIcon,
    label,
    helperText,
    className,
    id,
    ...props
  }, ref) => {
    const inputId = id || `input-${Math.random().toString(36).substring(2, 9)}`;
    return /* @__PURE__ */ jsxs("div", { className: "design-form-group", children: [
      label && /* @__PURE__ */ jsx(
        "label",
        {
          htmlFor: inputId,
          className: "design-label",
          children: label
        }
      ),
      /* @__PURE__ */ jsxs("div", { className: "design-input-wrapper", children: [
        leftIcon && /* @__PURE__ */ jsx("div", { className: "design-input-icon-left", children: leftIcon }),
        /* @__PURE__ */ jsx(
          "input",
          {
            ref,
            id: inputId,
            className: cn(
              "design-input",
              error && "design-input-error",
              leftIcon && "pl-10",
              rightIcon && "pr-10",
              className
            ),
            ...props
          }
        ),
        rightIcon && /* @__PURE__ */ jsx("div", { className: "design-input-icon-right", children: rightIcon })
      ] }),
      error && errorMessage && /* @__PURE__ */ jsx("div", { className: "design-input-error-message", children: errorMessage }),
      !error && helperText && /* @__PURE__ */ jsx("div", { className: "design-input-helper-text", children: helperText })
    ] });
  }
);
Input.displayName = "Input";

const Label = React__default.forwardRef(
  ({
    required = false,
    className,
    children,
    ...props
  }, ref) => {
    return /* @__PURE__ */ jsxs(
      "label",
      {
        ref,
        className: cn(
          "design-label",
          className
        ),
        ...props,
        children: [
          children,
          required && /* @__PURE__ */ jsx("span", { className: "design-label-required", children: "*" })
        ]
      }
    );
  }
);
Label.displayName = "Label";

const Textarea = forwardRef(
  ({ className, ...props }, ref) => {
    return /* @__PURE__ */ jsx(
      "textarea",
      {
        className: cn(
          "design-textarea",
          className
        ),
        ref,
        ...props
      }
    );
  }
);
Textarea.displayName = "Textarea";

const Select = SelectPrimitive.Root;
const SelectValue = SelectPrimitive.Value;
const SelectTrigger = React.forwardRef(({ className, children, ...props }, ref) => /* @__PURE__ */ jsxs(
  SelectPrimitive.Trigger,
  {
    ref,
    className: `flex h-10 w-full items-center justify-between rounded-md border border-design-border bg-design-background px-3 py-2 text-sm ring-offset-background placeholder:text-design-muted-foreground focus:outline-none focus:ring-2 focus:ring-design-ring focus:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 ${className}`,
    "aria-label": props["aria-label"] || "Select option",
    ...props,
    children: [
      children,
      /* @__PURE__ */ jsx(SelectPrimitive.Icon, { asChild: true, children: /* @__PURE__ */ jsx(ChevronDown, { className: "h-4 w-4 opacity-50", "aria-hidden": "true" }) })
    ]
  }
));
SelectTrigger.displayName = SelectPrimitive.Trigger.displayName;
const SelectContent = React.forwardRef(({ className, children, position = "popper", ...props }, ref) => /* @__PURE__ */ jsx(SelectPrimitive.Portal, { children: /* @__PURE__ */ jsx(
  SelectPrimitive.Content,
  {
    ref,
    className: `relative z-50 min-w-[8rem] overflow-hidden rounded-md border border-design-border bg-design-card text-design-foreground shadow-design-md data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 ${position === "popper" && "data-[side=bottom]:translate-y-1 data-[side=left]:-translate-x-1 data-[side=right]:translate-x-1 data-[side=top]:-translate-y-1"} ${className}`,
    position,
    ...props,
    children: /* @__PURE__ */ jsx(
      SelectPrimitive.Viewport,
      {
        className: `p-1 ${position === "popper" && "h-[var(--radix-select-trigger-height)] w-full min-w-[var(--radix-select-trigger-width)]"}`,
        children
      }
    )
  }
) }));
SelectContent.displayName = SelectPrimitive.Content.displayName;
const SelectLabel = React.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsx(
  SelectPrimitive.Label,
  {
    ref,
    className: `py-1.5 pl-8 pr-2 text-sm font-semibold text-design-foreground ${className}`,
    ...props
  }
));
SelectLabel.displayName = SelectPrimitive.Label.displayName;
const SelectItem = React.forwardRef(({ className, children, ...props }, ref) => /* @__PURE__ */ jsxs(
  SelectPrimitive.Item,
  {
    ref,
    className: `relative flex w-full cursor-default select-none items-center rounded-sm py-1.5 pl-8 pr-2 text-sm outline-none focus:bg-design-muted focus:text-design-foreground data-[disabled]:pointer-events-none data-[disabled]:opacity-50 ${className}`,
    ...props,
    children: [
      /* @__PURE__ */ jsx("span", { className: "absolute left-2 flex h-3.5 w-3.5 items-center justify-center", children: /* @__PURE__ */ jsx(SelectPrimitive.ItemIndicator, { children: /* @__PURE__ */ jsx(Check, { className: "h-4 w-4" }) }) }),
      /* @__PURE__ */ jsx(SelectPrimitive.ItemText, { children })
    ]
  }
));
SelectItem.displayName = SelectPrimitive.Item.displayName;
const SelectSeparator = React.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsx(
  SelectPrimitive.Separator,
  {
    ref,
    className: `-mx-1 my-1 h-px bg-design-border ${className}`,
    ...props
  }
));
SelectSeparator.displayName = SelectPrimitive.Separator.displayName;

const select = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  Select,
  SelectContent,
  SelectItem,
  SelectLabel,
  SelectSeparator,
  SelectTrigger,
  SelectValue
}, Symbol.toStringTag, { value: 'Module' }));

function SortSelect({ value, currentUrl }) {
  const handleValueChange = useCallback((newValue) => {
    requestAnimationFrame(() => {
      if (currentUrl) {
        const newUrl = new URL(currentUrl);
        newUrl.searchParams.set("sort", newValue);
        window.location.href = newUrl.toString();
      } else {
        const currentPath = window.location.pathname;
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.set("sort", newValue);
        window.location.href = `${currentPath}?${searchParams.toString()}`;
      }
    });
  }, [currentUrl]);
  const sortOptions = [
    { value: "newest", label: "Newest First" },
    { value: "price_asc", label: "Price: Low to High" },
    { value: "price_desc", label: "Price: High to Low" },
    { value: "expiring_soon", label: "Expiring Soon" },
    { value: "discount_desc", label: "Discount: High to Low" },
    { value: "most_popular", label: "Most Popular" }
  ];
  const currentLabel = sortOptions.find((option) => option.value === value)?.label || "Sort by";
  return /* @__PURE__ */ jsx("div", { className: "min-w-[70px] sm:min-w-[80px] lg:min-w-[100px] flex-shrink-0", children: /* @__PURE__ */ jsxs(Select, { defaultValue: value, onValueChange: handleValueChange, children: [
    /* @__PURE__ */ jsx(
      SelectTrigger,
      {
        className: "w-full bg-design-background/60 border border-design-border/40 text-design-foreground rounded-md h-7 px-1 sm:px-2 text-[10px] sm:text-xs focus:ring-1 focus:ring-design-primary/30 focus:border-design-primary/50 transition-all duration-200 backdrop-blur-sm",
        children: /* @__PURE__ */ jsx(SelectValue, { placeholder: "Sort", children: currentLabel })
      }
    ),
    /* @__PURE__ */ jsx(SelectContent, { className: "bg-design-background border-design-border rounded-md overflow-hidden z-50 min-w-[120px] shadow-lg backdrop-blur-sm", children: sortOptions.map((option) => /* @__PURE__ */ jsx(
      SelectItem,
      {
        value: option.value,
        className: "text-design-foreground hover:bg-design-muted/50 focus:bg-design-muted/50 text-xs cursor-pointer transition-colors",
        children: option.label
      },
      option.value
    )) })
  ] }) });
}

const Heading = React__default.forwardRef(
  ({
    level = 1,
    display = false,
    centered = false,
    className,
    children,
    ...props
  }, ref) => {
    const Component = `h${level}`;
    const typographyClass = display ? `typography-display-${level <= 2 ? level : 1}` : `typography-h${level}`;
    return /* @__PURE__ */ jsx(
      Component,
      {
        ref,
        className: cn(
          typographyClass,
          centered && "text-center",
          className
        ),
        ...props,
        children
      }
    );
  }
);
Heading.displayName = "Heading";

const Text = React__default.forwardRef(
  ({
    variant = "body",
    color = "default",
    truncate = false,
    lineClamp,
    className,
    children,
    ...props
  }, ref) => {
    const variantClass = {
      body: "typography-body",
      "body-large": "typography-body-large",
      "body-small": "typography-body-small",
      "body-xs": "typography-body-xs",
      caption: "typography-caption",
      overline: "typography-overline"
    }[variant];
    const colorClass = color !== "default" ? {
      primary: "typography-primary",
      secondary: "typography-secondary",
      muted: "typography-muted",
      success: "typography-success",
      warning: "typography-warning",
      error: "typography-error"
    }[color] : "";
    return /* @__PURE__ */ jsx(
      "p",
      {
        ref,
        className: cn(
          variantClass,
          colorClass,
          truncate && "typography-truncate",
          lineClamp && `typography-line-clamp-${lineClamp}`,
          className
        ),
        ...props,
        children
      }
    );
  }
);
Text.displayName = "Text";

const Link = React__default.forwardRef(
  ({
    variant = "default",
    external = false,
    className,
    children,
    ...props
  }, ref) => {
    const externalProps = external ? {
      target: "_blank",
      rel: "noopener noreferrer"
    } : {};
    return /* @__PURE__ */ jsxs(
      "a",
      {
        ref,
        className: cn(
          "typography-link",
          variant === "muted" && "typography-link-muted",
          variant === "hover-only" && "typography-link-hover-only",
          className
        ),
        ...externalProps,
        ...props,
        children: [
          children,
          external && /* @__PURE__ */ jsxs(
            "svg",
            {
              xmlns: "http://www.w3.org/2000/svg",
              width: "12",
              height: "12",
              viewBox: "0 0 24 24",
              fill: "none",
              stroke: "currentColor",
              strokeWidth: "2",
              strokeLinecap: "round",
              strokeLinejoin: "round",
              className: "ml-1 inline-block",
              children: [
                /* @__PURE__ */ jsx("path", { d: "M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6" }),
                /* @__PURE__ */ jsx("polyline", { points: "15 3 21 3 21 9" }),
                /* @__PURE__ */ jsx("line", { x1: "10", y1: "14", x2: "21", y2: "3" })
              ]
            }
          )
        ]
      }
    );
  }
);
Link.displayName = "Link";

const Alert = React__default.forwardRef(
  ({
    variant = "info",
    title,
    icon,
    dismissible = false,
    onDismiss,
    className,
    children,
    ...props
  }, ref) => {
    const defaultIcon = {
      info: /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
        /* @__PURE__ */ jsx("circle", { cx: "12", cy: "12", r: "10" }),
        /* @__PURE__ */ jsx("line", { x1: "12", y1: "16", x2: "12", y2: "12" }),
        /* @__PURE__ */ jsx("line", { x1: "12", y1: "8", x2: "12.01", y2: "8" })
      ] }),
      success: /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
        /* @__PURE__ */ jsx("path", { d: "M22 11.08V12a10 10 0 1 1-5.93-9.14" }),
        /* @__PURE__ */ jsx("polyline", { points: "22 4 12 14.01 9 11.01" })
      ] }),
      warning: /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
        /* @__PURE__ */ jsx("path", { d: "M10.29 3.86L1.82 18a2 2 0 0 0 1.71 3h16.94a2 2 0 0 0 1.71-3L13.71 3.86a2 2 0 0 0-3.42 0z" }),
        /* @__PURE__ */ jsx("line", { x1: "12", y1: "9", x2: "12", y2: "13" }),
        /* @__PURE__ */ jsx("line", { x1: "12", y1: "17", x2: "12.01", y2: "17" })
      ] }),
      error: /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
        /* @__PURE__ */ jsx("circle", { cx: "12", cy: "12", r: "10" }),
        /* @__PURE__ */ jsx("line", { x1: "15", y1: "9", x2: "9", y2: "15" }),
        /* @__PURE__ */ jsx("line", { x1: "9", y1: "9", x2: "15", y2: "15" })
      ] })
    }[variant];
    const alertIcon = icon || defaultIcon;
    return /* @__PURE__ */ jsxs(
      "div",
      {
        ref,
        className: cn(
          "design-alert",
          `design-alert-${variant}`,
          className
        ),
        role: "alert",
        ...props,
        children: [
          alertIcon && /* @__PURE__ */ jsx("div", { className: "design-alert-icon", children: alertIcon }),
          /* @__PURE__ */ jsxs("div", { className: "design-alert-content", children: [
            title && /* @__PURE__ */ jsx("div", { className: "design-alert-title", children: title }),
            /* @__PURE__ */ jsx("div", { className: "design-alert-message", children })
          ] }),
          dismissible && onDismiss && /* @__PURE__ */ jsx(
            "button",
            {
              className: "design-alert-dismiss",
              onClick: onDismiss,
              "aria-label": "Dismiss",
              children: /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
                /* @__PURE__ */ jsx("line", { x1: "18", y1: "6", x2: "6", y2: "18" }),
                /* @__PURE__ */ jsx("line", { x1: "6", y1: "6", x2: "18", y2: "18" })
              ] })
            }
          )
        ]
      }
    );
  }
);
Alert.displayName = "Alert";

const Badge = React__default.forwardRef(
  ({
    variant = "primary",
    className,
    children,
    ...props
  }, ref) => {
    const variantClass = {
      primary: "design-badge-primary",
      secondary: "design-badge-secondary",
      outline: "design-badge-outline",
      success: "design-badge-success",
      warning: "design-badge-warning",
      error: "design-badge-error"
    }[variant];
    return /* @__PURE__ */ jsx(
      "span",
      {
        ref,
        className: cn(
          "design-badge",
          variantClass,
          className
        ),
        ...props,
        children
      }
    );
  }
);
Badge.displayName = "Badge";

const Spinner = React__default.forwardRef(
  ({
    size = "md",
    variant = "primary",
    label = "Loading...",
    className,
    ...props
  }, ref) => {
    const sizeClass = {
      sm: "design-spinner-sm",
      md: "",
      lg: "design-spinner-lg"
    }[size];
    const variantClass = {
      primary: "design-spinner-primary",
      secondary: "design-spinner-secondary",
      muted: "design-spinner-muted"
    }[variant];
    return /* @__PURE__ */ jsxs(
      "div",
      {
        ref,
        className: cn(
          "design-spinner",
          sizeClass,
          variantClass,
          className
        ),
        role: "status",
        "aria-label": label,
        ...props,
        children: [
          /* @__PURE__ */ jsxs("svg", { className: "animate-spin", xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", children: [
            /* @__PURE__ */ jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }),
            /* @__PURE__ */ jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })
          ] }),
          /* @__PURE__ */ jsx("span", { className: "sr-only", children: label })
        ]
      }
    );
  }
);
Spinner.displayName = "Spinner";

const Skeleton = React__default.forwardRef(
  ({
    width,
    height,
    rounded = true,
    animated = true,
    className,
    style,
    ...props
  }, ref) => {
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref,
        className: cn(
          "design-skeleton",
          animated && "animate-pulse",
          rounded === true && "rounded",
          rounded === "full" && "rounded-full",
          className
        ),
        style: {
          width: width !== void 0 ? typeof width === "number" ? `${width}px` : width : void 0,
          height: height !== void 0 ? typeof height === "number" ? `${height}px` : height : void 0,
          ...style
        },
        "aria-hidden": "true",
        ...props
      }
    );
  }
);
Skeleton.displayName = "Skeleton";

const Divider = React__default.forwardRef(
  ({
    orientation = "horizontal",
    variant = "default",
    label,
    labelPosition = "center",
    className,
    ...props
  }, ref) => {
    const variantClass = {
      default: "design-divider-default",
      muted: "design-divider-muted",
      primary: "design-divider-primary"
    }[variant];
    const labelPositionClass = label ? {
      left: "design-divider-label-left",
      center: "design-divider-label-center",
      right: "design-divider-label-right"
    }[labelPosition] : "";
    return /* @__PURE__ */ jsx(
      "div",
      {
        ref,
        className: cn(
          "design-divider",
          orientation === "vertical" ? "design-divider-vertical" : "design-divider-horizontal",
          variantClass,
          label && "design-divider-with-label",
          labelPositionClass,
          className
        ),
        role: "separator",
        "aria-orientation": orientation,
        ...props,
        children: label && /* @__PURE__ */ jsx("span", { className: "design-divider-label", children: label })
      }
    );
  }
);
Divider.displayName = "Divider";

const CouponCode = React__default.forwardRef(
  ({
    code,
    revealed = false,
    onReveal,
    onCopy,
    className,
    ...props
  }, ref) => {
    const handleCopy = () => {
      if (onCopy) {
        onCopy();
      } else {
        navigator.clipboard.writeText(code).then(() => {
          console.log("Copied to clipboard");
        }).catch((err) => {
          console.error("Failed to copy: ", err);
        });
      }
    };
    return /* @__PURE__ */ jsxs(
      "div",
      {
        ref,
        className: cn(
          "coupon-code",
          revealed && "coupon-code-revealed",
          className
        ),
        ...props,
        children: [
          /* @__PURE__ */ jsx("div", { className: "coupon-code-display", children: revealed ? /* @__PURE__ */ jsx("code", { className: "coupon-code-text", children: code }) : /* @__PURE__ */ jsxs("div", { className: "coupon-code-hidden", children: [
            /* @__PURE__ */ jsx("span", { className: "coupon-code-dots", children: "••••••" }),
            onReveal && /* @__PURE__ */ jsx(
              "button",
              {
                className: "coupon-code-reveal-button",
                onClick: onReveal,
                "aria-label": "Reveal coupon code",
                children: "Reveal Code"
              }
            )
          ] }) }),
          revealed && /* @__PURE__ */ jsxs(
            "button",
            {
              className: "copy-code-button",
              onClick: handleCopy,
              "aria-label": "Copy coupon code",
              children: [
                /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "16", height: "16", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", className: "mr-1", children: [
                  /* @__PURE__ */ jsx("rect", { width: "8", height: "4", x: "8", y: "2", rx: "1", ry: "1" }),
                  /* @__PURE__ */ jsx("path", { d: "M16 4h2a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V6a2 2 0 0 1 2-2h2" }),
                  /* @__PURE__ */ jsx("path", { d: "m9 14 2 2 4-4" })
                ] }),
                /* @__PURE__ */ jsx("span", { children: "Copy" })
              ]
            }
          )
        ]
      }
    );
  }
);
CouponCode.displayName = "CouponCode";

const CouponExpiry = React__default.forwardRef(
  ({
    expiryDate,
    format = "relative",
    className,
    ...props
  }, ref) => {
    const expiryDateObj = typeof expiryDate === "string" ? new Date(expiryDate) : expiryDate;
    const now = /* @__PURE__ */ new Date();
    const daysRemaining = Math.ceil((expiryDateObj.getTime() - now.getTime()) / (1e3 * 60 * 60 * 24));
    const formattedDate = expiryDateObj.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric"
    });
    let relativeText = "";
    let statusClass = "";
    if (daysRemaining < 0) {
      relativeText = "Expired";
      statusClass = "coupon-expiry-expired";
    } else if (daysRemaining === 0) {
      relativeText = "Expires today";
      statusClass = "coupon-expiry-today";
    } else if (daysRemaining === 1) {
      relativeText = "Expires tomorrow";
      statusClass = "coupon-expiry-soon";
    } else if (daysRemaining <= 3) {
      relativeText = `Expires in ${daysRemaining} days`;
      statusClass = "coupon-expiry-soon";
    } else {
      relativeText = `Expires in ${daysRemaining} days`;
      statusClass = "coupon-expiry-future";
    }
    let displayText = "";
    if (format === "relative") {
      displayText = relativeText;
    } else if (format === "absolute") {
      displayText = `Expires on ${formattedDate}`;
    } else if (format === "both") {
      displayText = `${relativeText} (${formattedDate})`;
    }
    return /* @__PURE__ */ jsxs(
      "div",
      {
        ref,
        className: cn(
          "coupon-expiry",
          statusClass,
          className
        ),
        ...props,
        children: [
          /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "12", height: "12", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
            /* @__PURE__ */ jsx("circle", { cx: "12", cy: "12", r: "10" }),
            /* @__PURE__ */ jsx("polyline", { points: "12 6 12 12 16 14" })
          ] }),
          /* @__PURE__ */ jsx("span", { children: displayText })
        ]
      }
    );
  }
);
CouponExpiry.displayName = "CouponExpiry";

function ToastProvider({
  children,
  position = "top-right",
  duration = 3e3
}) {
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    children,
    /* @__PURE__ */ jsx(
      Toaster,
      {
        position,
        toastOptions: {
          style: {
            background: "var(--color-card)",
            color: "var(--color-card-foreground)",
            border: "1px solid var(--color-border)"
          },
          className: "font-sans",
          duration
        }
      }
    )
  ] });
}

const NewsletterPopup = memo(({
  isOpen,
  onClose,
  title = "Never Miss a Deal",
  subtitle = "Join 50,000+ subscribers and get the best deals delivered to your inbox",
  buttonText = "Subscribe Now",
  exitIntent = false
}) => {
  const [email, setEmail] = useState(() => {
    if (typeof window !== "undefined") {
      return localStorage.getItem("newsletter_email_cache") || "";
    }
    return "";
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState(null);
  const [isVisible, setIsVisible] = useState(isOpen);
  useEffect(() => {
    setIsVisible(isOpen);
  }, [isOpen]);
  useEffect(() => {
    if (email && typeof window !== "undefined") {
      localStorage.setItem("newsletter_email_cache", email);
    }
  }, [email]);
  const handleBackdropClick = useCallback((e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);
  const handleSubmit = useCallback(async (e) => {
    e.preventDefault();
    setError(null);
    setIsSubmitting(true);
    const emailRegex = /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/;
    if (!emailRegex.test(email)) {
      setError("Please enter a valid email address");
      setIsSubmitting(false);
      return;
    }
    const source = exitIntent ? "exit_intent_popup" : "newsletter_popup";
    const initial_page = window.location.pathname;
    const device_info = navigator.userAgent;
    const urlParams = new URLSearchParams(window.location.search);
    const utm_params = {
      utm_source: urlParams.get("utm_source"),
      utm_medium: urlParams.get("utm_medium"),
      utm_campaign: urlParams.get("utm_campaign"),
      utm_term: urlParams.get("utm_term"),
      utm_content: urlParams.get("utm_content")
    };
    try {
      const response = await fetch("/api/newsletter/subscribe", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email,
          source,
          utm_params,
          device_info,
          initial_page
        })
      });
      const result = await response.json();
      if (!response.ok) {
        throw new Error(result.error || "Something went wrong. Please try again later.");
      }
      setIsSubmitted(true);
      setEmail("");
      toast.success(result.message || "Thanks for subscribing! Check your email to confirm.");
      localStorage.setItem("newsletterSubscribed", "true");
      setTimeout(() => {
        onClose();
        if (exitIntent) {
          localStorage.setItem("exitIntentPopupSeen", "true");
        }
      }, 3e3);
    } catch (err) {
      setError(err.message || "Failed to subscribe. Please try again later.");
      toast.error(err.message || "Failed to subscribe. Please try again later.");
    } finally {
      setIsSubmitting(false);
    }
  }, [email, exitIntent, onClose]);
  if (!isVisible) return null;
  return /* @__PURE__ */ jsx(
    "div",
    {
      className: "fixed inset-0 bg-black/50 backdrop-blur-sm flex items-center justify-center z-50 p-4",
      onClick: handleBackdropClick,
      children: /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-design-border rounded-xl p-8 max-w-md w-full shadow-lg relative", children: [
        /* @__PURE__ */ jsx(
          "button",
          {
            onClick: onClose,
            className: "absolute top-4 right-4 text-gray-500 dark:text-gray-400 hover:text-gray-700 dark:hover:text-gray-200 transition-colors",
            "aria-label": "Close",
            children: /* @__PURE__ */ jsx(X, { className: "h-6 w-6" })
          }
        ),
        /* @__PURE__ */ jsxs("div", { className: "text-center mb-6", children: [
          /* @__PURE__ */ jsx("h2", { className: "text-2xl font-bold text-gray-800 dark:text-white mb-2", children: title }),
          /* @__PURE__ */ jsx("p", { className: "text-gray-600 dark:text-gray-300", children: subtitle })
        ] }),
        isSubmitted ? /* @__PURE__ */ jsxs("div", { className: "bg-green-50 dark:bg-green-900/20 border border-green-200 dark:border-green-800 rounded-lg p-4 text-center", children: [
          /* @__PURE__ */ jsx("p", { className: "font-medium text-green-700 dark:text-green-400", children: "Thanks for subscribing!" }),
          /* @__PURE__ */ jsx("p", { className: "text-sm text-gray-600 dark:text-gray-300 mt-1", children: "Please check your email to confirm your subscription." })
        ] }) : /* @__PURE__ */ jsxs("form", { onSubmit: handleSubmit, children: [
          /* @__PURE__ */ jsx("div", { className: "mb-4", children: /* @__PURE__ */ jsx(
            "input",
            {
              type: "email",
              value: email,
              onChange: (e) => setEmail(e.target.value),
              placeholder: "Enter your email",
              className: "w-full h-12 px-4 rounded-lg border border-gray-300 dark:border-gray-600 bg-white dark:bg-gray-800 text-gray-800 dark:text-white focus:outline-none focus:ring-2 focus:ring-design-primary",
              required: true
            }
          ) }),
          error && /* @__PURE__ */ jsx("div", { className: "mb-4 text-center text-red-600 dark:text-red-400 text-sm", children: error }),
          /* @__PURE__ */ jsx(
            "button",
            {
              type: "submit",
              disabled: isSubmitting,
              className: `w-full h-12 rounded-lg font-bold transition-all duration-300 ${isSubmitting ? "bg-design-primary/70 cursor-not-allowed" : "bg-design-primary hover:bg-design-primary/90"} text-white dark:text-black`,
              children: isSubmitting ? /* @__PURE__ */ jsxs("span", { className: "flex items-center justify-center", children: [
                /* @__PURE__ */ jsxs("svg", { className: "animate-spin -ml-1 mr-2 h-4 w-4 text-white dark:text-black", xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", children: [
                  /* @__PURE__ */ jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }),
                  /* @__PURE__ */ jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })
                ] }),
                "Processing..."
              ] }) : buttonText
            }
          ),
          /* @__PURE__ */ jsxs("div", { className: "mt-4 text-xs text-center text-gray-600 dark:text-gray-300", children: [
            /* @__PURE__ */ jsxs("div", { className: "flex flex-wrap justify-center gap-x-4 gap-y-1", children: [
              /* @__PURE__ */ jsx("span", { children: "✓ Exclusive deals" }),
              /* @__PURE__ */ jsx("span", { children: "✓ No spam" }),
              /* @__PURE__ */ jsx("span", { children: "✓ Weekly digest" })
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "mt-2", children: [
              "By subscribing, you agree to our ",
              /* @__PURE__ */ jsx("a", { href: "/privacy", className: "underline text-design-primary hover:text-design-primary/80 transition-colors", children: "Privacy Policy" }),
              "."
            ] })
          ] })
        ] })
      ] })
    }
  );
});

const GlowingButton = ({
  children,
  onClick,
  className = "",
  variant = "primary",
  asLink = false,
  href = "#",
  isFullWidth = false,
  forceSpan = false,
  ...props
}) => {
  const [, startTransition] = React__default.useTransition();
  const [isMounted, setIsMounted] = useState(false);
  useEffect(() => {
    const timeout = setTimeout(() => {
      startTransition(() => {
        setIsMounted(true);
      });
    }, 10);
    return () => clearTimeout(timeout);
  }, []);
  const baseClasses = cn(
    "relative text-lg px-6 py-2 rounded-full transition-all duration-300 inline-flex items-center justify-center gap-2",
    isFullWidth ? "w-full" : "",
    className
  );
  const contentClasses = cn(
    "relative z-10 text-white dark:text-black flex items-center justify-center gap-2"
  );
  const borderClasses = cn(
    "absolute inset-0 rounded-full bg-gradient-to-tr from-[#4a4cd9] to-[#262cbd] dark:from-[#1df292] dark:to-[#0db875]",
    isMounted && "shadow-glow dark:shadow-glow-dark"
    // Only add shadow effects after mounting
  );
  const bgClasses = cn(
    "absolute inset-[1px] rounded-full bg-gradient-to-b from-[#4a4cd9] to-[#262cbd] dark:from-[#1df292] dark:to-[#0db875]"
  );
  const buttonContent = /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx("span", { className: borderClasses }),
    /* @__PURE__ */ jsx("span", { className: bgClasses }),
    /* @__PURE__ */ jsx("span", { className: contentClasses, children })
  ] });
  if (typeof window === "undefined" || !isMounted) {
    return /* @__PURE__ */ jsx("span", { className: `${baseClasses} group`, ...props, children: buttonContent });
  }
  if (forceSpan) {
    return /* @__PURE__ */ jsx("span", { className: `${baseClasses} group`, ...props, children: buttonContent });
  }
  if (asLink) {
    const { forceSpan: _, ...anchorProps } = props;
    return /* @__PURE__ */ jsx("a", { href, className: `${baseClasses} group`, ...anchorProps, children: buttonContent });
  }
  return /* @__PURE__ */ jsx(
    "div",
    {
      onClick,
      className: `${baseClasses} group`,
      role: "button",
      tabIndex: 0,
      ...props,
      children: buttonContent
    }
  );
};

function useToast() {
  const toast$1 = (title, {
    description,
    variant = "default",
    duration = 3e3
  } = {}) => {
    const toastFn = (() => {
      switch (variant) {
        case "success":
          return toast.success;
        case "destructive":
          return toast.error;
        default:
          return toast;
      }
    })();
    const options = description ? {
      description
    } : {};
    toastFn(title, {
      ...options,
      duration
    });
  };
  return {
    toast: toast$1,
    // Expose the original sonner toast for direct access if needed
    sonnerToast: toast
  };
}

const DealCouponModal = ({ deal, onClose, onVote }) => {
  const [copied, setCopied] = useState(false);
  const modalImageUrl = React__default.useMemo(() => {
    const normalizeUrl = (url) => {
      if (!url || url.trim() === "") return null;
      const trimmedUrl = url.trim();
      if (trimmedUrl.startsWith("http://") || trimmedUrl.startsWith("https://")) {
        return trimmedUrl;
      }
      return trimmedUrl.startsWith("/") ? trimmedUrl : `/${trimmedUrl}`;
    };
    if (deal.brand_logo_url) return normalizeUrl(deal.brand_logo_url);
    if (deal.brands?.logo_url) return normalizeUrl(deal.brands.logo_url);
    if (deal.merchant_logo_url) return normalizeUrl(deal.merchant_logo_url);
    if (deal.merchants?.logo_url) return normalizeUrl(deal.merchants.logo_url);
    return normalizeUrl(deal.image_url);
  }, [deal]);
  let merchantName = deal.merchants?.name || deal.merchant_name || "Brand";
  if (merchantName === "Vapesourcing Electronics Co.,Ltd.") {
    merchantName = "Vapesourcing";
  }
  const copyCode = useCallback(() => {
    if (!deal.coupon_code) return;
    try {
      navigator.clipboard.writeText(deal.coupon_code).then(() => {
        setCopied(true);
        toast.success("Coupon code copied!", {
          duration: 2e3,
          position: "top-center"
        });
        setTimeout(() => setCopied(false), 2e3);
      });
    } catch (err) {
      const textArea = document.createElement("textarea");
      textArea.value = deal.coupon_code;
      document.body.appendChild(textArea);
      textArea.select();
      document.execCommand("copy");
      document.body.removeChild(textArea);
      setCopied(true);
      toast.success("Coupon code copied!", {
        duration: 2e3,
        position: "top-center"
      });
      setTimeout(() => setCopied(false), 2e3);
    }
  }, [deal.coupon_code]);
  const handleShopClick = useCallback(() => {
    if (deal.tracking_url) {
      window.open(deal.tracking_url, "_blank");
    } else {
      window.open(`/go/${deal.id}`, "_blank");
    }
  }, [deal]);
  const handleVote = useCallback((isGood) => {
    if (onVote) {
      onVote(deal.id, isGood);
      toast.success(isGood ? "Thanks for your positive feedback!" : "Thanks for your feedback!");
    }
  }, [deal.id, onVote]);
  const handleBackdropClick = useCallback((e) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  }, [onClose]);
  return /* @__PURE__ */ jsx(
    "div",
    {
      className: "fixed inset-0 bg-black/70 backdrop-blur-sm flex items-center justify-center z-50 p-4",
      onClick: handleBackdropClick,
      style: {
        animation: "fadeIn 0.2s ease-out"
      },
      role: "dialog",
      "aria-modal": "true",
      "aria-labelledby": "modal-title",
      children: /* @__PURE__ */ jsxs(
        "div",
        {
          className: "bg-design-card text-design-card-foreground border border-design-border rounded-[25px] p-6 pt-8 pb-8 max-w-md w-full shadow-xl relative",
          style: {
            animation: "scaleIn 0.2s ease-out",
            maxHeight: "85vh",
            overflowY: "auto"
          },
          children: [
            /* @__PURE__ */ jsx(
              "button",
              {
                onClick: onClose,
                className: "absolute top-4 right-4 p-2 text-design-muted-foreground hover:text-design-foreground hover:bg-design-muted rounded-full transition-colors z-10",
                "aria-label": "Close",
                children: /* @__PURE__ */ jsx(X, { className: "h-5 w-5" })
              }
            ),
            /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center", children: [
              /* @__PURE__ */ jsx("div", { className: "deal-coupon-modal-logo mt-3 mb-4", children: modalImageUrl && /* @__PURE__ */ jsx(
                "img",
                {
                  src: modalImageUrl,
                  alt: `${merchantName} logo`,
                  className: "max-h-24 max-w-full object-contain",
                  width: "120",
                  height: "60",
                  loading: "eager"
                }
              ) }),
              /* @__PURE__ */ jsx("h3", { id: "modal-title", className: "text-2xl font-bold text-center mb-2", children: deal.discount ? `${deal.discount}% Off` : "Special Deal" }),
              /* @__PURE__ */ jsx("p", { className: "text-sm text-design-muted-foreground text-center mb-4", children: deal.title }),
              deal.coupon_code && /* @__PURE__ */ jsx("p", { className: "text-center text-xs text-design-primary mb-3 font-medium", children: "Click the button below to copy the code" }),
              deal.coupon_code ? /* @__PURE__ */ jsx(
                "div",
                {
                  className: "bg-design-muted dark:bg-gray-800 w-full rounded-[25px] p-0.5 shadow-sm select-all cursor-pointer mb-4",
                  onClick: copyCode,
                  onKeyDown: (e) => e.key === "Enter" && copyCode(),
                  role: "button",
                  tabIndex: 0,
                  "aria-label": `Copy coupon code ${deal.coupon_code}`,
                  children: /* @__PURE__ */ jsxs("div", { className: "flex justify-between items-center", children: [
                    /* @__PURE__ */ jsx("div", { className: "font-mono font-bold tracking-wider pl-6 py-3 text-lg", children: deal.coupon_code }),
                    /* @__PURE__ */ jsxs("div", { className: "bg-design-primary rounded-r-[25px] text-white dark:text-black py-3 px-4 flex items-center font-medium", children: [
                      copied ? /* @__PURE__ */ jsx(Check, { className: "h-4 w-4 mr-2" }) : /* @__PURE__ */ jsx(Copy, { className: "h-4 w-4 mr-2" }),
                      copied ? "Copied" : "Copy"
                    ] })
                  ] })
                }
              ) : /* @__PURE__ */ jsx("div", { className: "bg-design-muted w-full rounded-[25px] p-4 text-center mb-4", children: /* @__PURE__ */ jsx("span", { className: "font-medium", children: "No code needed" }) }),
              /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center gap-3 mb-6", children: [
                /* @__PURE__ */ jsx("span", { className: "text-sm text-design-muted-foreground", children: "Did this code work?" }),
                /* @__PURE__ */ jsxs("div", { className: "flex gap-4", children: [
                  /* @__PURE__ */ jsxs(
                    "button",
                    {
                      onClick: () => handleVote(false),
                      className: "flex items-center gap-2 px-3 py-1.5 bg-design-muted hover:bg-red-100 dark:hover:bg-red-900/20 rounded-full text-sm transition-colors",
                      "aria-label": "Vote that the code did not work",
                      children: [
                        /* @__PURE__ */ jsx(ThumbsDown, { className: "h-4 w-4 text-red-500" }),
                        /* @__PURE__ */ jsx("span", { children: "No" })
                      ]
                    }
                  ),
                  /* @__PURE__ */ jsxs(
                    "button",
                    {
                      onClick: () => handleVote(true),
                      className: "flex items-center gap-2 px-3 py-1.5 bg-design-muted hover:bg-green-100 dark:hover:bg-green-900/20 rounded-full text-sm transition-colors",
                      "aria-label": "Vote that the code worked",
                      children: [
                        /* @__PURE__ */ jsx(ThumbsUp, { className: "h-4 w-4 text-green-500" }),
                        /* @__PURE__ */ jsx("span", { children: "Yes" })
                      ]
                    }
                  )
                ] })
              ] }),
              /* @__PURE__ */ jsxs(
                "button",
                {
                  onClick: handleShopClick,
                  className: "w-full bg-design-primary hover:bg-design-primary/90 text-white dark:text-black py-3 px-4 rounded-[25px] font-medium transition-colors flex items-center justify-center gap-2 mb-5",
                  children: [
                    /* @__PURE__ */ jsx(ExternalLink, { className: "h-4 w-4" }),
                    /* @__PURE__ */ jsx("span", { children: "Get Deal Now" })
                  ]
                }
              ),
              /* @__PURE__ */ jsxs("div", { className: "flex justify-center items-center gap-6 text-sm text-design-muted-foreground", children: [
                /* @__PURE__ */ jsx(
                  "a",
                  {
                    href: `/deal/${deal.id}`,
                    className: "flex items-center text-design-muted-foreground hover:text-design-primary transition-colors",
                    "aria-label": "View deal details",
                    children: /* @__PURE__ */ jsx(Eye, { className: "h-4 w-4" })
                  }
                ),
                /* @__PURE__ */ jsx(BookmarkButton, { dealId: deal.id }),
                deal.verified && deal.last_verified_at && /* @__PURE__ */ jsxs("div", { className: "flex items-center text-green-600 dark:text-green-400", children: [
                  /* @__PURE__ */ jsx(ShieldCheck, { className: "h-4 w-4 mr-2" }),
                  /* @__PURE__ */ jsx("span", { children: "Verified" })
                ] })
              ] })
            ] })
          ]
        }
      )
    }
  );
};

const DealCouponModal$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: DealCouponModal
}, Symbol.toStringTag, { value: 'Module' }));

function ViewToggle({ currentView, currentUrl, client, onViewChange }) {
  const handleViewChange = useCallback((view) => {
    if (onViewChange) {
      onViewChange(view);
      return;
    }
    if (typeof window !== "undefined") {
      requestAnimationFrame(() => {
        const currentPath = window.location.pathname;
        const searchParams = new URLSearchParams(window.location.search);
        searchParams.set("view", view);
        window.location.href = `${currentPath}?${searchParams.toString()}`;
      });
    }
  }, [onViewChange]);
  return /* @__PURE__ */ jsxs("div", { className: "flex items-center overflow-hidden border border-design-border/40 bg-design-background/60 rounded-md flex-shrink-0 backdrop-blur-sm", children: [
    /* @__PURE__ */ jsx(
      "button",
      {
        onClick: () => handleViewChange("grid"),
        className: `flex items-center justify-center w-7 h-7 transition-all duration-200 ${currentView === "grid" ? "bg-design-primary text-white shadow-sm" : "text-design-muted-foreground hover:bg-design-muted/50 hover:text-design-foreground"}`,
        "aria-label": "Grid view",
        title: "Grid view",
        children: /* @__PURE__ */ jsx(Grid, { size: 10, className: "flex-shrink-0" })
      }
    ),
    /* @__PURE__ */ jsx(
      "button",
      {
        onClick: () => handleViewChange("list"),
        className: `hidden sm:flex items-center justify-center w-7 h-7 transition-all duration-200 ${currentView === "list" ? "bg-design-primary text-white shadow-sm" : "text-design-muted-foreground hover:bg-design-muted/50 hover:text-design-foreground"}`,
        "aria-label": "List view",
        title: "List view",
        children: /* @__PURE__ */ jsx(List, { size: 10, className: "flex-shrink-0" })
      }
    )
  ] });
}

const $$Astro$2 = createAstro("http://localhost:4321");
const $$Pagination = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$Pagination;
  const { currentPage, totalPages, baseUrl, searchParams = new URLSearchParams() } = Astro2.props;
  const getPageNumbers = () => {
    const pageNumbers2 = [];
    const maxPagesToShow = 5;
    if (totalPages <= maxPagesToShow) {
      for (let i = 1; i <= totalPages; i++) {
        pageNumbers2.push(i);
      }
    } else {
      pageNumbers2.push(1);
      let start = Math.max(2, currentPage - 1);
      let end = Math.min(totalPages - 1, currentPage + 1);
      if (currentPage <= 2) {
        end = Math.min(totalPages - 1, maxPagesToShow - 1);
      } else if (currentPage >= totalPages - 1) {
        start = Math.max(2, totalPages - maxPagesToShow + 2);
      }
      if (start > 2) {
        pageNumbers2.push("...");
      }
      for (let i = start; i <= end; i++) {
        pageNumbers2.push(i);
      }
      if (end < totalPages - 1) {
        pageNumbers2.push("...");
      }
      if (totalPages > 1) {
        pageNumbers2.push(totalPages);
      }
    }
    return pageNumbers2;
  };
  const pageNumbers = getPageNumbers();
  const createPageUrl = (page) => {
    const newParams = new URLSearchParams(searchParams.toString());
    newParams.set("page", page.toString());
    return `${baseUrl}${newParams.toString() ? "?" + newParams.toString() : ""}`;
  };
  return renderTemplate`${maybeRenderHead()}<div class="flex justify-center items-center space-x-2"> <!-- Previous button --> ${currentPage > 1 && renderTemplate`<a${addAttribute(createPageUrl(currentPage - 1), "href")} class="px-3 py-2 rounded-full border border-design-border/20 bg-white/50 dark:bg-black/50 backdrop-blur-sm text-design-foreground hover:bg-primary/10 transition-all duration-300 shadow-sm" aria-label="Go to previous page"> <span class="sr-only">Previous</span> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"> <path d="m15 18-6-6 6-6"></path> </svg> </a>`} <!-- Page numbers --> ${pageNumbers.map(
    (page, index) => page === "..." ? renderTemplate`<span class="px-3 py-2 text-design-muted-foreground">...</span>` : renderTemplate`<a${addAttribute(createPageUrl(page), "href")}${addAttribute(`w-10 h-10 flex items-center justify-center rounded-full transition-all duration-300 ${currentPage === page ? "bg-primary text-white/90 dark:text-black/90 font-bold shadow-sm" : "bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/20 hover:bg-primary/10 text-design-foreground shadow-sm"}`, "class")}${addAttribute(`Go to page ${page}`, "aria-label")}${addAttribute(currentPage === page ? "page" : void 0, "aria-current")}> ${page} </a>`
  )} <!-- Next button --> ${currentPage < totalPages && renderTemplate`<a${addAttribute(createPageUrl(currentPage + 1), "href")} class="px-3 py-2 rounded-full border border-design-border/20 bg-white/50 dark:bg-black/50 backdrop-blur-sm text-design-foreground hover:bg-primary/10 transition-all duration-300 shadow-sm" aria-label="Go to next page"> <span class="sr-only">Next</span> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-4 w-4"> <path d="m9 18 6-6-6-6"></path> </svg> </a>`} </div>`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Pagination.astro", void 0);

const $$Astro$1 = createAstro("http://localhost:4321");
const $$PerPageSelect = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$PerPageSelect;
  const { value, options, url } = Astro2.props;
  const createLimitUrl = (limit) => {
    const newUrl = new URL(url.href);
    newUrl.searchParams.set("limit", limit.toString());
    newUrl.searchParams.set("page", "1");
    return newUrl.toString();
  };
  return renderTemplate`${maybeRenderHead()}<div class="flex items-center gap-3"> <label for="per-page" class="text-sm font-medium text-design-muted-foreground">
Per page:
</label> <div class="relative"> <select id="per-page" class="h-9 rounded-full border border-design-border/20 bg-white/50 dark:bg-black/50 backdrop-blur-sm px-4 py-1 text-sm text-design-foreground focus:outline-none focus:ring-2 focus:ring-primary/30 focus:ring-offset-1 appearance-none cursor-pointer pr-9 shadow-sm transition-all duration-300" onchange="window.location.href=this.options[this.selectedIndex].dataset.url"> ${options.map((option) => renderTemplate`<option${addAttribute(option, "value")}${addAttribute(value === option, "selected")}${addAttribute(createLimitUrl(option), "data-url")}> ${option} </option>`)} </select> <div class="absolute right-3 top-1/2 -translate-y-1/2 pointer-events-none text-primary"> <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"> <path d="m6 9 6 6 6-6"></path> </svg> </div> </div> </div>`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/PerPageSelect.astro", void 0);

const Sheet = SheetPrimitive.Root;
const SheetPortal = SheetPrimitive.Portal;
const SheetOverlay = React.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsx(
  SheetPrimitive.Overlay,
  {
    className: `fixed inset-0 z-50 bg-black/50 data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 ${className}`,
    ...props,
    ref
  }
));
SheetOverlay.displayName = SheetPrimitive.Overlay.displayName;
const sheetVariants = cva(
  "fixed z-50 gap-4 bg-design-background p-6 shadow-design-lg transition ease-in-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
  {
    variants: {
      side: {
        top: "inset-x-0 top-0 border-b border-design-border data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top",
        bottom: "inset-x-0 bottom-0 border-t border-design-border data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom",
        left: "inset-y-0 left-0 h-full w-3/4 border-r border-design-border data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left sm:max-w-sm",
        right: "inset-y-0 right-0 h-full w-3/4 border-l border-design-border data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right sm:max-w-sm"
      }
    },
    defaultVariants: {
      side: "right"
    }
  }
);
const SheetContent = React.forwardRef(({ side = "right", className, children, ...props }, ref) => /* @__PURE__ */ jsxs(SheetPortal, { children: [
  /* @__PURE__ */ jsx(SheetOverlay, {}),
  /* @__PURE__ */ jsxs(
    SheetPrimitive.Content,
    {
      ref,
      className: sheetVariants({ side, className }),
      "aria-describedby": "sheet-description",
      ...props,
      children: [
        /* @__PURE__ */ jsx("div", { id: "sheet-description", className: "sr-only", children: "Sheet content containing filters for deals" }),
        children,
        /* @__PURE__ */ jsxs(
          SheetPrimitive.Close,
          {
            className: "absolute right-4 top-[12.5px] rounded-full w-5 h-5 flex items-center justify-center bg-design-primary text-white dark:text-black opacity-90 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-1 focus:ring-design-ring focus:ring-offset-2 disabled:pointer-events-none z-50 shadow-sm",
            "aria-label": "Close",
            children: [
              /* @__PURE__ */ jsx(X, { className: "h-3 w-3" }),
              /* @__PURE__ */ jsx("span", { className: "sr-only", children: "Close" })
            ]
          }
        )
      ]
    }
  )
] }));
SheetContent.displayName = SheetPrimitive.Content.displayName;
const SheetTitle = React.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsx(
  SheetPrimitive.Title,
  {
    ref,
    className: `text-lg font-semibold text-design-foreground ${className}`,
    ...props
  }
));
SheetTitle.displayName = SheetPrimitive.Title.displayName;
const SheetDescription = React.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsx(
  SheetPrimitive.Description,
  {
    ref,
    className: `text-sm text-design-muted-foreground ${className}`,
    ...props
  }
));
SheetDescription.displayName = SheetPrimitive.Description.displayName;

const sheet = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  Sheet,
  SheetContent,
  SheetDescription,
  SheetOverlay,
  SheetPortal,
  SheetTitle
}, Symbol.toStringTag, { value: 'Module' }));

const Slider = React.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsxs(
  SliderPrimitive.Root,
  {
    ref,
    className: `relative flex w-full touch-none select-none items-center ${className}`,
    ...props,
    children: [
      /* @__PURE__ */ jsx(SliderPrimitive.Track, { className: "relative h-2 w-full grow overflow-hidden rounded-full bg-design-muted", children: /* @__PURE__ */ jsx(SliderPrimitive.Range, { className: "absolute h-full bg-design-primary" }) }),
      props.defaultValue?.map((_, i) => /* @__PURE__ */ jsx(
        SliderPrimitive.Thumb,
        {
          className: "block h-5 w-5 rounded-full border-2 border-design-primary bg-design-background shadow-md ring-offset-background transition-colors hover:bg-design-primary/10 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-design-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50"
        },
        i
      ))
    ]
  }
));
Slider.displayName = SliderPrimitive.Root.displayName;

const Checkbox = React.forwardRef(({ className, ...props }, ref) => /* @__PURE__ */ jsx(
  CheckboxPrimitive.Root,
  {
    ref,
    className: `peer h-4 w-4 shrink-0 rounded-sm border border-design-border bg-design-background ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-design-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 data-[state=checked]:bg-design-primary data-[state=checked]:text-white ${className}`,
    ...props,
    children: /* @__PURE__ */ jsx(CheckboxPrimitive.Indicator, { className: "flex items-center justify-center text-current", children: /* @__PURE__ */ jsx(Check, { className: "h-3.5 w-3.5" }) })
  }
));
Checkbox.displayName = CheckboxPrimitive.Root.displayName;

function FilterForm({
  categories,
  merchants,
  brands,
  initialFilters,
  // isMobile parameter is not used
  // isMobile = false,
  currentUrl = "/coupons"
}) {
  const [filters, setFilters] = useState(initialFilters || {
    categories: [],
    merchants: [],
    brands: [],
    priceRange: [0, 200],
    discountRange: [0, 100],
    expiringSoon: false,
    validLongTerm: false
  });
  const [minPrice, maxPrice] = filters.priceRange;
  const [minDiscount, maxDiscount] = filters.discountRange;
  const [categorySearch, setCategorySearch] = useState("");
  const [merchantSearch, setMerchantSearch] = useState("");
  const [brandSearch, setBrandSearch] = useState("");
  const filteredCategories = categories.filter(
    (category) => category.name.toLowerCase().includes(categorySearch.toLowerCase())
  );
  const filteredMerchants = merchants.filter(
    (merchant) => merchant.name.toLowerCase().includes(merchantSearch.toLowerCase())
  );
  const filteredBrands = brands.filter(
    (brand) => brand.name.toLowerCase().includes(brandSearch.toLowerCase())
  );
  const handleCheckboxChange = (value, checked, type) => {
    setFilters((prev) => {
      if (checked) {
        return {
          ...prev,
          [type]: [...prev[type], value]
        };
      } else {
        return {
          ...prev,
          [type]: prev[type].filter((id) => id !== value)
        };
      }
    });
  };
  const handleExpiryChange = (name, checked) => {
    setFilters((prev) => ({
      ...prev,
      [name]: checked
    }));
  };
  const priceRangeTimeoutRef = useRef(null);
  const discountRangeTimeoutRef = useRef(null);
  const handlePriceRangeChange = useCallback((value) => {
    if (priceRangeTimeoutRef.current) {
      window.clearTimeout(priceRangeTimeoutRef.current);
    }
    priceRangeTimeoutRef.current = window.setTimeout(() => {
      requestAnimationFrame(() => {
        setFilters((prev) => ({
          ...prev,
          priceRange: [value[0], value[1]]
        }));
      });
    }, 50);
  }, []);
  const handleDiscountRangeChange = useCallback((value) => {
    if (discountRangeTimeoutRef.current) {
      window.clearTimeout(discountRangeTimeoutRef.current);
    }
    discountRangeTimeoutRef.current = window.setTimeout(() => {
      requestAnimationFrame(() => {
        setFilters((prev) => ({
          ...prev,
          discountRange: [value[0], value[1]]
        }));
      });
    }, 50);
  }, []);
  const handleReset = () => {
    setFilters({
      categories: [],
      merchants: [],
      brands: [],
      priceRange: [0, 200],
      discountRange: [0, 100],
      expiringSoon: false,
      validLongTerm: false
    });
    setCategorySearch("");
    setMerchantSearch("");
    setBrandSearch("");
  };
  const activeFilterCount = filters.categories.length + filters.merchants.length + filters.brands.length + (filters.expiringSoon ? 1 : 0) + (filters.validLongTerm ? 1 : 0) + (minPrice > 0 || maxPrice < 200 ? 1 : 0) + (minDiscount > 0 || maxDiscount < 100 ? 1 : 0);
  const getSearchParams = () => {
    try {
      if (typeof window === "undefined") {
        return new URLSearchParams();
      }
      if (currentUrl && currentUrl.startsWith("http")) {
        return new URL(currentUrl).searchParams;
      }
      if (currentUrl && currentUrl.startsWith("/")) {
        const fullUrl = new URL(currentUrl, window.location.origin);
        return fullUrl.searchParams;
      }
      return new URL(window.location.href).searchParams;
    } catch (error) {
      console.warn("Error parsing URL in FilterForm:", error);
      return new URLSearchParams();
    }
  };
  const searchParams = getSearchParams();
  return /* @__PURE__ */ jsxs("form", { action: currentUrl, method: "get", className: "space-y-4", children: [
    /* @__PURE__ */ jsx("input", { type: "hidden", name: "view", value: searchParams.get("view") || "grid" }),
    /* @__PURE__ */ jsx("input", { type: "hidden", name: "sort", value: searchParams.get("sort") || "newest" }),
    /* @__PURE__ */ jsx("input", { type: "hidden", name: "minPrice", value: minPrice }),
    /* @__PURE__ */ jsx("input", { type: "hidden", name: "maxPrice", value: maxPrice }),
    /* @__PURE__ */ jsx("input", { type: "hidden", name: "minDiscount", value: minDiscount }),
    /* @__PURE__ */ jsx("input", { type: "hidden", name: "maxDiscount", value: maxDiscount }),
    filters.expiringSoon && /* @__PURE__ */ jsx("input", { type: "hidden", name: "expiringSoon", value: "true" }),
    filters.validLongTerm && /* @__PURE__ */ jsx("input", { type: "hidden", name: "validLongTerm", value: "true" }),
    filters.categories.map((id) => /* @__PURE__ */ jsx("input", { type: "hidden", name: "categories", value: id }, id)),
    filters.merchants.map((id) => /* @__PURE__ */ jsx("input", { type: "hidden", name: "merchants", value: id }, id)),
    filters.brands.map((id) => /* @__PURE__ */ jsx("input", { type: "hidden", name: "brands", value: id }, id)),
    activeFilterCount > 0 && /* @__PURE__ */ jsx("div", { className: "mb-2 -mt-1", children: /* @__PURE__ */ jsxs(
      Button,
      {
        type: "button",
        variant: "ghost",
        size: "sm",
        onClick: handleReset,
        className: "text-design-destructive hover:text-design-destructive/90 font-medium w-full justify-center border border-design-border rounded-sm text-xs py-1",
        children: [
          "Reset (",
          activeFilterCount,
          ")"
        ]
      }
    ) }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2 bg-design-muted/10 p-2 rounded-md", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsx("h4", { className: "text-xs font-medium text-design-foreground", children: "Price Range" }),
        /* @__PURE__ */ jsxs("div", { className: "text-xs text-design-muted-foreground", children: [
          "$",
          minPrice,
          " - $",
          maxPrice
        ] })
      ] }),
      /* @__PURE__ */ jsx(
        Slider,
        {
          defaultValue: [minPrice, maxPrice],
          min: 0,
          max: 200,
          step: 5,
          value: [minPrice, maxPrice],
          onValueChange: handlePriceRangeChange,
          className: "py-1"
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2 bg-design-muted/10 p-2 rounded-md", children: [
      /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between", children: [
        /* @__PURE__ */ jsx("h4", { className: "text-xs font-medium text-design-foreground", children: "Discount Range" }),
        /* @__PURE__ */ jsxs("div", { className: "text-xs text-design-muted-foreground", children: [
          minDiscount,
          "% - ",
          maxDiscount,
          "%"
        ] })
      ] }),
      /* @__PURE__ */ jsx(
        Slider,
        {
          defaultValue: [minDiscount, maxDiscount],
          min: 0,
          max: 100,
          step: 5,
          value: [minDiscount, maxDiscount],
          onValueChange: handleDiscountRangeChange,
          className: "py-1"
        }
      )
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2 bg-design-muted/10 p-2 rounded-md", children: [
      /* @__PURE__ */ jsx("h4", { className: "text-xs font-medium text-design-foreground mb-1", children: "Deal Expiry" }),
      /* @__PURE__ */ jsxs("div", { className: "space-y-1.5", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsx(
            Checkbox,
            {
              id: "expiringSoon",
              checked: filters.expiringSoon,
              onCheckedChange: (checked) => handleExpiryChange("expiringSoon", checked),
              className: "h-3.5 w-3.5"
            }
          ),
          /* @__PURE__ */ jsx(Label, { htmlFor: "expiringSoon", className: "text-xs font-normal cursor-pointer", children: "Expiring Soon (15 days or less)" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-2", children: [
          /* @__PURE__ */ jsx(
            Checkbox,
            {
              id: "validLongTerm",
              checked: filters.validLongTerm,
              onCheckedChange: (checked) => handleExpiryChange("validLongTerm", checked),
              className: "h-3.5 w-3.5"
            }
          ),
          /* @__PURE__ */ jsx(Label, { htmlFor: "validLongTerm", className: "text-xs font-normal cursor-pointer", children: "Valid Long Term (30+ days)" })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2 bg-design-muted/10 p-2 rounded-md", children: [
      /* @__PURE__ */ jsx("h4", { className: "text-xs font-medium text-design-foreground mb-1", children: "Categories" }),
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsx(
          "input",
          {
            type: "text",
            placeholder: "Search categories...",
            value: categorySearch,
            onChange: (e) => setCategorySearch(e.target.value),
            className: "w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"
          }
        ),
        categorySearch && /* @__PURE__ */ jsxs(
          "button",
          {
            type: "button",
            onClick: () => setCategorySearch(""),
            className: "absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground",
            children: [
              /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "12", height: "12", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
                /* @__PURE__ */ jsx("line", { x1: "18", y1: "6", x2: "6", y2: "18" }),
                /* @__PURE__ */ jsx("line", { x1: "6", y1: "6", x2: "18", y2: "18" })
              ] }),
              /* @__PURE__ */ jsx("span", { className: "sr-only", children: "Clear search" })
            ]
          }
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1", children: filteredCategories?.length > 0 ? filteredCategories.map((category) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-1.5", children: [
        /* @__PURE__ */ jsx(
          Checkbox,
          {
            id: `category-${category.id}`,
            checked: filters.categories.includes(category.id),
            onCheckedChange: (checked) => handleCheckboxChange(category.id, checked, "categories"),
            className: "h-3.5 w-3.5"
          }
        ),
        /* @__PURE__ */ jsx(Label, { htmlFor: `category-${category.id}`, className: "text-xs font-normal cursor-pointer", children: category.name })
      ] }, category.id)) : /* @__PURE__ */ jsx("p", { className: "text-xs text-design-muted-foreground py-1", children: categories.length > 0 ? "No matching categories" : "No categories found" }) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2 bg-design-muted/10 p-2 rounded-md", children: [
      /* @__PURE__ */ jsx("h4", { className: "text-xs font-medium text-design-foreground mb-1", children: "Merchants" }),
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsx(
          "input",
          {
            type: "text",
            placeholder: "Search merchants...",
            value: merchantSearch,
            onChange: (e) => setMerchantSearch(e.target.value),
            className: "w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"
          }
        ),
        merchantSearch && /* @__PURE__ */ jsxs(
          "button",
          {
            type: "button",
            onClick: () => setMerchantSearch(""),
            className: "absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground",
            children: [
              /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "12", height: "12", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
                /* @__PURE__ */ jsx("line", { x1: "18", y1: "6", x2: "6", y2: "18" }),
                /* @__PURE__ */ jsx("line", { x1: "6", y1: "6", x2: "18", y2: "18" })
              ] }),
              /* @__PURE__ */ jsx("span", { className: "sr-only", children: "Clear search" })
            ]
          }
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1", children: filteredMerchants?.length > 0 ? filteredMerchants.map((merchant) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-1.5", children: [
        /* @__PURE__ */ jsx(
          Checkbox,
          {
            id: `merchant-${merchant.id}`,
            checked: filters.merchants.includes(merchant.id),
            onCheckedChange: (checked) => handleCheckboxChange(merchant.id, checked, "merchants"),
            className: "h-3.5 w-3.5"
          }
        ),
        /* @__PURE__ */ jsx(Label, { htmlFor: `merchant-${merchant.id}`, className: "text-xs font-normal cursor-pointer", children: merchant.name })
      ] }, merchant.id)) : /* @__PURE__ */ jsx("p", { className: "text-xs text-design-muted-foreground py-1", children: merchants.length > 0 ? "No matching merchants" : "No merchants found" }) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-2 bg-design-muted/10 p-2 rounded-md", children: [
      /* @__PURE__ */ jsx("h4", { className: "text-xs font-medium text-design-foreground mb-1", children: "Brands" }),
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsx(
          "input",
          {
            type: "text",
            placeholder: "Search brands...",
            value: brandSearch,
            onChange: (e) => setBrandSearch(e.target.value),
            className: "w-full h-7 rounded-md border border-design-input bg-design-background px-2 py-1 text-xs text-design-foreground placeholder:text-design-muted-foreground focus:outline-none focus:ring-1 focus:ring-design-ring"
          }
        ),
        brandSearch && /* @__PURE__ */ jsxs(
          "button",
          {
            type: "button",
            onClick: () => setBrandSearch(""),
            className: "absolute right-2 top-1/2 -translate-y-1/2 text-design-muted-foreground hover:text-design-foreground",
            children: [
              /* @__PURE__ */ jsxs("svg", { xmlns: "http://www.w3.org/2000/svg", width: "12", height: "12", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: [
                /* @__PURE__ */ jsx("line", { x1: "18", y1: "6", x2: "6", y2: "18" }),
                /* @__PURE__ */ jsx("line", { x1: "6", y1: "6", x2: "18", y2: "18" })
              ] }),
              /* @__PURE__ */ jsx("span", { className: "sr-only", children: "Clear search" })
            ]
          }
        )
      ] }),
      /* @__PURE__ */ jsx("div", { className: "space-y-0.5 max-h-32 overflow-y-auto pr-1 custom-scrollbar mt-1", children: filteredBrands?.length > 0 ? filteredBrands.map((brand) => /* @__PURE__ */ jsxs("div", { className: "flex items-center space-x-1.5", children: [
        /* @__PURE__ */ jsx(
          Checkbox,
          {
            id: `brand-${brand.id}`,
            checked: filters.brands.includes(brand.id),
            onCheckedChange: (checked) => handleCheckboxChange(brand.id, checked, "brands"),
            className: "h-3.5 w-3.5"
          }
        ),
        /* @__PURE__ */ jsx(Label, { htmlFor: `brand-${brand.id}`, className: "text-xs font-normal cursor-pointer", children: brand.name })
      ] }, brand.id)) : /* @__PURE__ */ jsx("p", { className: "text-xs text-design-muted-foreground py-1", children: brands.length > 0 ? "No matching brands" : "No brands found" }) })
    ] }),
    /* @__PURE__ */ jsx("div", { className: "sticky bottom-0 pt-2 pb-2 bg-design-background border-t border-design-border z-40 shadow-md", children: /* @__PURE__ */ jsx(
      Button,
      {
        type: "submit",
        variant: "primary",
        className: "w-full text-xs font-medium bg-design-primary text-white dark:text-black cursor-pointer rounded-sm px-3 py-2 text-center transition-colors hover:bg-design-primary/90 flex items-center justify-center",
        children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-center", children: [
          /* @__PURE__ */ jsx("svg", { xmlns: "http://www.w3.org/2000/svg", className: "h-3.5 w-3.5 mr-1 inline-block", viewBox: "0 0 24 24", fill: "none", stroke: "currentColor", strokeWidth: "2", strokeLinecap: "round", strokeLinejoin: "round", children: /* @__PURE__ */ jsx("path", { d: "M22 3H2l8 9.46V19l4 2v-8.54L22 3z" }) }),
          /* @__PURE__ */ jsxs("span", { children: [
            "Apply ",
            activeFilterCount > 0 && `(${activeFilterCount})`
          ] })
        ] })
      }
    ) })
  ] });
}

const FilterForm$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: FilterForm
}, Symbol.toStringTag, { value: 'Module' }));

function FilterDrawer({
  categories,
  merchants,
  brands,
  initialFilters,
  currentUrl = "/",
  activeFilterCount = 0
}) {
  const [isOpen, setIsOpen] = useState(false);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    /* @__PURE__ */ jsx(
      Button,
      {
        onClick: () => setIsOpen(true),
        variant: "outline",
        size: "sm",
        className: "h-7 border border-design-border/40 bg-design-background/60 text-design-foreground hover:bg-design-muted/50 rounded-md text-[10px] sm:text-xs px-1 sm:px-2 transition-all duration-200 backdrop-blur-sm flex-shrink-0",
        children: /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-center gap-1", children: [
          /* @__PURE__ */ jsx(Filter, { size: 10 }),
          /* @__PURE__ */ jsx("span", { className: "font-medium", children: "Filters" }),
          activeFilterCount > 0 && /* @__PURE__ */ jsx("span", { className: "ml-1 bg-design-primary text-white rounded-full w-4 h-4 flex items-center justify-center text-[9px] font-bold", children: activeFilterCount })
        ] })
      }
    ),
    /* @__PURE__ */ jsx(Sheet, { open: isOpen, onOpenChange: setIsOpen, children: /* @__PURE__ */ jsxs(
      SheetContent,
      {
        side: "left",
        className: "w-full sm:max-w-md bg-design-background border-design-border p-0 overflow-hidden transition-transform duration-300 ease-out data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left",
        children: [
          /* @__PURE__ */ jsxs("div", { className: "border-b border-design-border h-10 sticky top-0 bg-design-background z-10 relative", children: [
            /* @__PURE__ */ jsx("div", { className: "absolute inset-0 flex items-center justify-center", children: /* @__PURE__ */ jsx(SheetTitle, { className: "text-sm font-medium text-design-foreground m-0", children: "Filters" }) }),
            /* @__PURE__ */ jsx(SheetDescription, { className: "sr-only", children: "Filter options for deals" })
          ] }),
          /* @__PURE__ */ jsx("div", { className: "px-4 pt-3 pb-24 overflow-y-auto h-[calc(100vh-2.5rem)] custom-scrollbar", children: isOpen && /* @__PURE__ */ jsx(
            FilterForm,
            {
              categories,
              merchants,
              brands,
              initialFilters,
              isMobile: true,
              currentUrl
            }
          ) })
        ]
      }
    ) })
  ] });
}

const FilterDrawer$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: FilterDrawer
}, Symbol.toStringTag, { value: 'Module' }));

function getWebPPath(imagePath) {
  if (!imagePath) return "";
  if (imagePath.endsWith(".svg")) return imagePath;
  if (imagePath.endsWith(".webp")) return imagePath;
  const lastSlashIndex = imagePath.lastIndexOf("/");
  const path = lastSlashIndex !== -1 ? imagePath.substring(0, lastSlashIndex + 1) : "";
  const filename = lastSlashIndex !== -1 ? imagePath.substring(lastSlashIndex + 1) : imagePath;
  const hasValidExtension = /\.(jpe?g|png|gif)$/i.test(filename);
  if (!hasValidExtension) {
    return "";
  }
  const filenameWithWebP = filename.replace(/\.(jpe?g|png|gif)$/i, ".webp");
  return path + filenameWithWebP;
}
function shouldHaveWebP(imagePath) {
  if (!imagePath) return false;
  if (imagePath.endsWith(".svg")) return false;
  if (imagePath.endsWith(".webp")) return false;
  return /\.(jpe?g|png|gif)$/i.test(imagePath);
}

const $$Astro = createAstro("http://localhost:4321");
const $$WebPImage = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$WebPImage;
  const {
    src,
    alt,
    width,
    height,
    class: className = "",
    loading = "lazy",
    decoding = "async",
    fetchpriority = "auto",
    style = "",
    sizes,
    fallbackSrc = "/placeholder-image.svg"
  } = Astro2.props;
  if (!src) {
    throw new Error("WebPImage: src is required");
  }
  const webpSrc = shouldHaveWebP(src) ? getWebPPath(src) : null;
  const validWebpSrc = webpSrc && webpSrc.endsWith(".webp") && webpSrc.length > 0 ? webpSrc : null;
  const imgAttributes = {
    src,
    alt,
    width: width || void 0,
    height: height || void 0,
    class: className,
    loading,
    decoding,
    fetchpriority,
    style,
    sizes: sizes || void 0,
    onerror: `this.onerror=null; this.src='${fallbackSrc}';`
  };
  return renderTemplate`${maybeRenderHead()}<picture data-astro-cid-hdpspurq> ${validWebpSrc && renderTemplate`<source${addAttribute(`${validWebpSrc} 1x`, "srcset")} type="image/webp" data-astro-cid-hdpspurq>`} <img${spreadAttributes(imgAttributes, void 0, { "class": "astro-hdpspurq" })} data-astro-cid-hdpspurq> </picture> `;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/WebPImage.astro", void 0);

function SearchBox({
  placeholder = "Search deals...",
  className = "",
  id = "search-box"
}) {
  const [query, setQuery] = useState("");
  const handleSubmit = (e) => {
    e.preventDefault();
    if (query.trim().length < 2) return;
    const queryLower = query.toLowerCase().trim();
    const currentUrl = new URL(window.location.href);
    currentUrl.searchParams.delete("search");
    currentUrl.searchParams.delete("q");
    currentUrl.searchParams.delete("brands");
    currentUrl.searchParams.delete("categories");
    const brandKeywords = ["geekvape", "voopoo", "smok", "vaporesso", "aspire"];
    const matchedBrand = brandKeywords.find((brand) => queryLower.includes(brand));
    const categoryKeywords = {
      "e-juice": "3",
      "e-liquid": "3",
      "liquid": "3",
      "disposable": "1",
      "disposables": "1",
      "puff": "1",
      "pod": "2",
      "pods": "2"
    };
    const matchedCategory = Object.keys(categoryKeywords).find((cat) => queryLower.includes(cat));
    if (matchedBrand) {
      currentUrl.searchParams.set("search", matchedBrand);
    } else if (matchedCategory) {
      currentUrl.searchParams.set("categories", categoryKeywords[matchedCategory]);
    } else {
      currentUrl.searchParams.set("search", query);
    }
    window.location.href = currentUrl.toString();
  };
  return /* @__PURE__ */ jsxs("form", { onSubmit: handleSubmit, className: `relative flex-1 lg:flex-none ${className}`, children: [
    /* @__PURE__ */ jsx("label", { htmlFor: id, className: "sr-only", children: placeholder }),
    /* @__PURE__ */ jsx(
      "input",
      {
        type: "search",
        id,
        name: id,
        value: query,
        onChange: (e) => setQuery(e.target.value),
        placeholder,
        autoComplete: "off",
        className: "w-full h-7 pl-6 pr-2 text-xs bg-white dark:bg-black text-design-foreground border border-design-border/40 rounded-md focus:outline-none focus:ring-1 focus:ring-design-primary/30 focus:border-design-primary/50 transition-all duration-200 placeholder:text-design-muted-foreground/60"
      }
    ),
    /* @__PURE__ */ jsx(
      "svg",
      {
        className: "absolute left-1.5 top-1/2 transform -translate-y-1/2 w-2.5 h-2.5 text-design-muted-foreground/60",
        fill: "none",
        stroke: "currentColor",
        viewBox: "0 0 24 24",
        children: /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: "2", d: "m21 21-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" })
      }
    )
  ] });
}

const NoticeBar = ({
  message,
  linkText,
  linkUrl,
  dismissible = true,
  // Changed default to true
  onDismiss,
  variant = "default",
  className,
  ...props
}) => {
  const [isVisible, setIsVisible] = useState(true);
  useEffect(() => {
    const isDismissed = localStorage.getItem("noticeBarDismissed");
    if (isDismissed === "true") {
      setIsVisible(false);
    }
  }, []);
  const handleDismiss = () => {
    setIsVisible(false);
    localStorage.setItem("noticeBarDismissed", "true");
    if (onDismiss) {
      onDismiss();
    }
  };
  if (!isVisible) {
    return null;
  }
  return /* @__PURE__ */ jsx(
    "div",
    {
      className: cn(
        "w-full py-1 px-2 text-center text-[12px] h-[28px] flex items-center justify-center",
        variant === "default" ? "bg-[#f4efefdb] text-gray-600 dark:bg-[#0f1011] dark:text-gray-300" : "bg-[#0f1011] text-gray-300",
        className
      ),
      role: "alert",
      ...props,
      children: /* @__PURE__ */ jsxs("div", { className: "container mx-auto flex items-center justify-center gap-2 h-full", children: [
        /* @__PURE__ */ jsx("span", { className: "block min-h-[16px]", children: message }),
        linkText && linkUrl && /* @__PURE__ */ jsx(
          "a",
          {
            href: linkUrl,
            className: cn(
              "font-medium hover:underline whitespace-nowrap",
              variant === "default" ? "text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300" : "text-blue-400 hover:text-blue-300"
            ),
            children: linkText
          }
        ),
        dismissible && /* @__PURE__ */ jsx(
          "button",
          {
            onClick: handleDismiss,
            className: "ml-2 p-1 rounded-full hover:bg-black/10 dark:hover:bg-white/10 flex-shrink-0",
            "aria-label": "Dismiss",
            children: /* @__PURE__ */ jsx(X, { size: 14 })
          }
        )
      ] })
    }
  );
};

const WebPImage = ({
  src,
  alt,
  width,
  height,
  className = "",
  loading = "lazy",
  decoding = "async",
  fetchpriority = "auto",
  style,
  sizes,
  fallbackSrc = "/placeholder-image.svg",
  onLoad,
  onError
}) => {
  const [error, setError] = useState(false);
  const [loaded, setLoaded] = useState(false);
  if (!src) {
    return /* @__PURE__ */ jsx(
      "img",
      {
        src: fallbackSrc,
        alt,
        className,
        style,
        width,
        height
      }
    );
  }
  const normalizedSrc = src.replace(/%20/g, "-").replace(/ /g, "-");
  const webpSrc = shouldHaveWebP(normalizedSrc) ? getWebPPath(normalizedSrc) : null;
  const handleError = (e) => {
    setError(true);
    if (onError) onError();
    const target = e.target;
    target.src = fallbackSrc;
  };
  const handleLoad = () => {
    setLoaded(true);
    if (onLoad) onLoad();
  };
  const validWebpSrc = webpSrc && webpSrc.endsWith(".webp") && webpSrc.length > 0 ? webpSrc : null;
  const srcSet = validWebpSrc ? `${validWebpSrc} 1x` : "";
  return /* @__PURE__ */ jsxs("picture", { children: [
    validWebpSrc && /* @__PURE__ */ jsx("source", { srcSet, type: "image/webp" }),
    /* @__PURE__ */ jsx(
      "img",
      {
        src: normalizedSrc,
        alt,
        width,
        height,
        className,
        loading,
        decoding,
        sizes,
        style: {
          maxWidth: "100%",
          height: "auto",
          display: "block",
          ...style
        },
        onError: handleError,
        onLoad: handleLoad,
        ...fetchpriority ? { fetchpriority } : {}
      }
    )
  ] });
};

const imageSizePresets = {
  dealCard: {
    sizes: "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 33vw",
    widths: [320, 480, 640, 800],
    // Flag to indicate this is likely to be an LCP image in grid view
    isLCP: true
  },
  brandLogo: {
    sizes: "(max-width: 640px) 80px, 128px",
    widths: [80, 128, 256],
    isLCP: false
  },
  heroImage: {
    sizes: "100vw",
    widths: [640, 960, 1280, 1920],
    isLCP: true
  },
  merchantLogo: {
    sizes: "(max-width: 640px) 60px, 100px",
    widths: [60, 100, 200],
    isLCP: false
  },
  categoryImage: {
    sizes: "(max-width: 640px) 100vw, (max-width: 1024px) 50vw, 25vw",
    widths: [320, 480, 640],
    isLCP: false
  }
};
const ResponsiveImage = ({
  src,
  alt,
  preset = "custom",
  sizes,
  widths,
  width,
  height,
  className = "",
  loading = "lazy",
  priority = false,
  fallbackSrc = "/placeholder-image.svg",
  // Use absolute path for fallback
  fallbackContent,
  onLoad,
  objectFit = "contain"
}) => {
  const [error, setError] = useState(false);
  const [loaded, setLoaded] = useState(false);
  const [isLoading, setIsLoading] = useState(true);
  preset !== "custom" ? imageSizePresets[preset]?.sizes : sizes;
  preset !== "custom" ? imageSizePresets[preset]?.widths : widths;
  const isLCPImage = preset !== "custom" ? imageSizePresets[preset]?.isLCP : false;
  const loadingAttribute = priority || isLCPImage ? "eager" : loading;
  const handleError = (e) => {
    const target = e.target;
    if (target.src !== fallbackSrc) {
      target.src = fallbackSrc;
      setError(true);
      setIsLoading(false);
    }
  };
  const handleLoad = () => {
    setLoaded(true);
    setIsLoading(false);
    if (onLoad) onLoad();
  };
  if (error && fallbackContent) {
    return /* @__PURE__ */ jsx(Fragment, { children: fallbackContent });
  }
  return /* @__PURE__ */ jsxs("div", { className: "relative w-full h-full", children: [
    isLoading && !loaded && /* @__PURE__ */ jsx("div", { className: "absolute inset-0 bg-design-muted" }),
    !error && /* @__PURE__ */ jsx(
      "img",
      {
        src: src || fallbackSrc,
        alt,
        width,
        height,
        className: `${className} ${objectFit === "contain" ? "object-contain" : objectFit === "cover" ? "object-cover" : `object-${objectFit}`}`,
        loading: loadingAttribute,
        onError: handleError,
        onLoad: handleLoad,
        style: {
          maxWidth: "100%",
          maxHeight: "100%",
          display: "block",
          margin: "0 auto"
        }
      }
    ),
    error && fallbackContent && /* @__PURE__ */ jsx("div", { className: "absolute inset-0 flex items-center justify-center", children: fallbackContent }),
    error && !fallbackContent && /* @__PURE__ */ jsx(
      "img",
      {
        src: fallbackSrc,
        alt,
        width,
        height,
        className,
        style: {
          maxWidth: "100%",
          maxHeight: "100%",
          display: "block",
          margin: "0 auto"
        }
      }
    )
  ] });
};

const DelayedStructuredData = ({ deal, imageUrl }) => {
  const [shouldRender, setShouldRender] = useState(false);
  useEffect(() => {
    const requestIdleCallbackPolyfill = window.requestIdleCallback || ((cb) => setTimeout(cb, 1));
    const idleCallbackId = requestIdleCallbackPolyfill(() => {
      setShouldRender(true);
    });
    return () => {
      const cancelIdleCallbackPolyfill = window.cancelIdleCallback || ((id) => clearTimeout(id));
      cancelIdleCallbackPolyfill(idleCallbackId);
    };
  }, []);
  if (!shouldRender) return null;
  return /* @__PURE__ */ jsx(DealStructuredData, { deal, imageUrl });
};
const OptimizedDealListCard = ({
  deal,
  className,
  isRevealed = false,
  priority = false,
  onShowCouponModal,
  ...props
}) => {
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);
  const cardRef = useRef(null);
  const dealUrl = `/deal/${deal.id}`;
  const { onMouseEnter: handlePrefetch, onMouseLeave: handleCancelPrefetch } = usePrefetch(dealUrl, { delay: 150 });
  const {
    setIsVisible,
    handleMouseEnter: trackMouseEnter,
    handleMouseLeave: trackMouseLeave,
    handleClick: trackClick
  } = useEngagementTracking(deal.id, "deal", { minDwellTime: 2e3 });
  const staffAvatars = useMemo(() => getRandomStaffImages(5, deal.id), [deal.id]);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = useMemo(() => usageInfo.timeAgo, [usageInfo]);
  const count = useMemo(() => deal.usage_count || usageInfo.count, [deal.usage_count, usageInfo.count]);
  const daysRemaining = useMemo(() => {
    if (!deal.deal_end_date) return null;
    return Math.ceil((new Date(deal.deal_end_date).getTime() - (/* @__PURE__ */ new Date()).getTime()) / (1e3 * 60 * 60 * 24));
  }, [deal.deal_end_date]);
  const isExpiringSoon = useMemo(() => {
    return daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  }, [daysRemaining]);
  const isExpired = useMemo(() => {
    return daysRemaining !== null && daysRemaining <= 0;
  }, [daysRemaining]);
  const imageUrl = useMemo(() => {
    const dealImage = normalizeUrl(deal.imagebig_url) || normalizeUrl(deal.image_url) || normalizeUrl(deal.imagesmall_url);
    if (dealImage) return dealImage;
    const brandLogo = normalizeUrl(deal.brands?.logo_url) || normalizeUrl(deal.brand_logo_url);
    if (brandLogo) return brandLogo;
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) || normalizeUrl(deal.merchant_logo_url);
    if (merchantLogo) return merchantLogo;
    return "/placeholder-image.svg";
  }, [
    deal.imagebig_url,
    deal.image_url,
    deal.imagesmall_url,
    deal.brands?.logo_url,
    deal.brand_logo_url,
    deal.merchants?.logo_url,
    deal.merchant_logo_url
  ]);
  useEffect(() => {
    if (!cardRef.current) return;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting);
          if (entry.isIntersecting) {
            if (process.env.NODE_ENV !== "production") {
              console.log("Deal impression tracked:", deal.id);
            } else {
              try {
                let sessionId = localStorage.getItem("vh_session_id");
                if (!sessionId) {
                  sessionId = "session_" + Math.random().toString(36).substring(2, 15);
                  localStorage.setItem("vh_session_id", sessionId);
                }
                if (typeof navigator.sendBeacon === "function") {
                  const trackingData = new FormData();
                  trackingData.append("deal_id", deal.id.toString());
                  trackingData.append("session_id", sessionId);
                  navigator.sendBeacon("/api/track-impression?deal_id=" + deal.id + "&session_id=" + sessionId, trackingData);
                } else {
                  fetch("/api/track-impression?deal_id=" + deal.id + "&session_id=" + sessionId, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ deal_id: deal.id, session_id: sessionId }),
                    keepalive: true
                  });
                }
              } catch (error) {
                console.error("Error tracking impression:", error);
              }
            }
          }
        });
      },
      { threshold: 0.5 }
      // Card is considered visible when 50% is in view
    );
    observer.observe(cardRef.current);
    return () => {
      observer.disconnect();
    };
  }, [deal.id, cardRef, setIsVisible]);
  const handleDealClick = useCallback(() => {
    setIsCodeRevealed(true);
    if (typeof window !== "undefined") {
      const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
      revealedCodes[deal.id] = true;
      localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
      if (deal.coupon_code) {
        copyToClipboard(deal.coupon_code);
      }
      const currentUrl = new URL(window.location.href);
      const viewMode = currentUrl.searchParams.get("view") || "list";
      const popupUrl = new URL(window.location.href);
      popupUrl.searchParams.set("dealId", deal.id.toString());
      popupUrl.searchParams.set("showPopup", "true");
      popupUrl.searchParams.set("view", viewMode);
      window.open(popupUrl.toString(), "_blank");
      if (onShowCouponModal) {
        onShowCouponModal(deal);
      }
      if (deal.tracking_url) {
        window.location.href = deal.tracking_url;
      } else {
        window.location.href = `/go/${deal.id}`;
      }
    }
  }, [deal, onShowCouponModal]);
  const handleCopy = useCallback((e) => {
    e.stopPropagation();
    trackClick();
    requestAnimationFrame(() => {
      setIsCodeRevealed(true);
      if (typeof window !== "undefined") {
        if (typeof navigator.sendBeacon === "function") {
          try {
            const trackingData = new FormData();
            trackingData.append("deal_id", deal.id.toString());
            trackingData.append("fallback", "true");
            navigator.sendBeacon("/api/track-click", trackingData);
            console.log(`Tracking copy for deal_id=${deal.id} only`);
          } catch (error) {
            console.error("Error sending beacon:", error);
          }
        }
        const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
        const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
        revealedCodes[deal.id] = true;
        localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
        clickedDeals[deal.id] = true;
        localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
        if (deal.coupon_code) {
          copyToClipboard(deal.coupon_code);
        }
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get("view") || "list";
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set("dealId", deal.id.toString());
        popupUrl.searchParams.set("showPopup", "true");
        popupUrl.searchParams.set("solidBg", "true");
        popupUrl.searchParams.set("view", viewMode);
        if (onShowCouponModal) {
          onShowCouponModal(deal);
        }
        window.open(popupUrl.toString(), "_blank");
        if (deal.tracking_url) {
          window.location.href = deal.tracking_url;
        } else {
          window.location.href = `/go/${deal.id}`;
        }
      }
    });
  }, [deal, trackClick, onShowCouponModal]);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    typeof window !== "undefined" && /* @__PURE__ */ jsx(DelayedStructuredData, { deal, imageUrl }),
    /* @__PURE__ */ jsxs(
      Card,
      {
        ref: cardRef,
        variant: "default",
        interactive: true,
        glowEffect: true,
        className: cn(
          "optimized-deal-list-card deal-list-card relative cursor-pointer w-full flex flex-col sm:flex-row items-center justify-between",
          "transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.005]",
          "hover:border-design-primary/50 dark:hover:border-design-primary/50",
          "border-black/15 dark:border-white/15",
          // Distinguishable border in both modes
          "optimized-card",
          // Add content-visibility optimization
          className
        ),
        style: {
          padding: "0.5rem",
          borderRadius: "25px",
          borderWidth: "1.5px"
        },
        onClick: () => {
          handleDealClick();
          trackClick();
        },
        onMouseEnter: () => {
          handlePrefetch();
          trackMouseEnter();
        },
        onMouseLeave: () => {
          handleCancelPrefetch();
          trackMouseLeave();
        },
        "aria-label": `Deal: ${deal.title}`,
        role: "button",
        tabIndex: 0,
        ...props,
        children: [
          /* @__PURE__ */ jsx(
            "div",
            {
              id: `tracking-pixel-${deal.id}`,
              style: { position: "absolute", opacity: 0, pointerEvents: "none" },
              "aria-hidden": "true"
            }
          ),
          isExpiringSoon && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full", children: daysRemaining === 1 ? "Expires today" : `Expires in ${daysRemaining} days` }),
          isExpired && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full", children: "Expired" }),
          /* @__PURE__ */ jsx("div", { className: "w-full sm:w-[200px] py-2 sm:py-0", children: /* @__PURE__ */ jsx("div", { className: "flex justify-center h-full", children: /* @__PURE__ */ jsx(
            "div",
            {
              className: "relative flex items-center justify-center w-full h-full aspect-square max-w-[160px] sm:min-w-[160px] sm:max-w-[180px] sm:max-h-[180px] bg-white dark:bg-white rounded-lg overflow-hidden",
              style: {
                boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.05)",
                padding: "8px"
                // Add padding to ensure image doesn't touch the edges
              },
              children: /* @__PURE__ */ jsx(
                DealImage,
                {
                  src: imageUrl,
                  alt: deal.title || "Deal image",
                  width: 180,
                  height: 180,
                  priority,
                  fetchpriority: priority ? "high" : "auto",
                  className: "",
                  fallbackSrc: "/placeholder-image.svg",
                  index: 0
                }
              )
            }
          ) }) }),
          /* @__PURE__ */ jsx("div", { className: "flex-1 p-3 w-full", children: /* @__PURE__ */ jsxs("div", { className: "h-full flex flex-col justify-between", children: [
            /* @__PURE__ */ jsx("div", { className: "mb-2", children: /* @__PURE__ */ jsxs("div", { className: "text-xl font-bold text-design-foreground", children: [
              deal.discount ? /* @__PURE__ */ jsxs("span", { className: "text-green-600 dark:text-green-500", children: [
                deal.discount,
                "% ",
                /* @__PURE__ */ jsx("span", { className: "text-xs font-normal text-gray-500 dark:text-gray-400", children: "off " }),
                "🔥"
              ] }) : deal.price ? `${deal.currency || "$"}${deal.price}` : "20% Off",
              daysRemaining !== null && daysRemaining > 0 && /* @__PURE__ */ jsx("span", { className: "ml-2 text-xs font-normal text-design-warning animate-pulse", children: daysRemaining > 365 ? `${Math.floor(daysRemaining / 365)}+ ${Math.floor(daysRemaining / 365) === 1 ? "year" : "years"} left` : daysRemaining > 30 ? `${Math.floor(daysRemaining / 30)} ${Math.floor(daysRemaining / 30) === 1 ? "month" : "months"} left` : daysRemaining === 1 ? "Ends today!" : `Ends in ${daysRemaining} days!` })
            ] }) }),
            /* @__PURE__ */ jsxs("div", { className: "text-sm text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1", children: [
              /* @__PURE__ */ jsx(
                "span",
                {
                  className: "font-medium truncate max-w-[120px] hover:underline cursor-help",
                  title: `Brand: ${deal.merchants?.name || deal.brands?.name || "Unknown"}`,
                  children: deal.merchants?.name || deal.brands?.name || "Brand"
                }
              ),
              deal.verified && /* @__PURE__ */ jsxs("span", { className: "c-verified-badge verified-badge ml-2", children: [
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid light icon.svg",
                    alt: "Verified",
                    className: "dark:hidden",
                    width: 16,
                    height: 16
                  }
                ),
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid dark icon.svg",
                    alt: "Verified",
                    className: "hidden dark:block",
                    width: 16,
                    height: 16
                  }
                ),
                /* @__PURE__ */ jsx("span", { className: "text-design-primary dark:text-design-primary", children: "Verified" })
              ] }),
              deal.success_rate !== void 0 && /* @__PURE__ */ jsxs(
                "span",
                {
                  className: `ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full ${deal.success_rate >= 90 ? "bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground" : deal.success_rate >= 70 ? "bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground" : "bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"}`,
                  title: `${Math.round(deal.success_rate || 85)}% success rate`,
                  children: [
                    /* @__PURE__ */ jsx(ThumbsUp, { size: 10, className: "mr-1" }),
                    Math.round(deal.success_rate || 85),
                    "%"
                  ]
                }
              )
            ] }),
            /* @__PURE__ */ jsx(
              "h3",
              {
                className: "deal-card-title line-clamp-2 mb-2 text-base font-semibold overflow-hidden",
                title: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title,
                children: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title
              }
            ),
            /* @__PURE__ */ jsx("div", { className: "flex flex-1", children: /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
              deal.description && /* @__PURE__ */ jsx(
                "p",
                {
                  className: "text-sm text-design-muted-foreground mb-3 line-clamp-2",
                  title: deal.description,
                  children: deal.description
                }
              ),
              /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-3", children: [
                /* @__PURE__ */ jsxs("div", { className: "c-avatar-group user-avatar-group flex relative", style: { marginRight: "0.35rem" }, children: [
                  staffAvatars.slice(0, Math.min(3, count || 3)).map((avatar, index) => /* @__PURE__ */ jsxs(
                    "div",
                    {
                      className: "c-avatar c-avatar--xs user-avatar relative transition-transform hover:scale-110 hover:z-10 overflow-hidden",
                      style: {
                        width: "20px",
                        height: "20px",
                        marginLeft: index > 0 ? "-8px" : "0",
                        borderRadius: "50%",
                        border: "1.5px solid white",
                        boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                        zIndex: 3 - index,
                        backgroundColor: avatar.color
                        // Fallback background color
                      },
                      title: `Staff member: ${avatar.name}`,
                      children: [
                        avatar.webpPath && avatar.imagePath ? /* @__PURE__ */ jsxs("picture", { children: [
                          /* @__PURE__ */ jsx("source", { srcSet: avatar.webpPath, type: "image/webp" }),
                          /* @__PURE__ */ jsx(
                            "img",
                            {
                              src: avatar.imagePath,
                              alt: avatar.initials,
                              className: "w-full h-full object-cover",
                              onError: (e) => {
                                const target = e.target;
                                target.style.display = "none";
                                const parent = target.parentElement?.parentElement;
                                if (parent) {
                                  parent.classList.add("fallback-active");
                                }
                              }
                            }
                          )
                        ] }) : (
                          // Fallback to initials if no image path
                          /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center text-white font-bold", children: /* @__PURE__ */ jsx("span", { style: { fontSize: "8px" }, children: avatar.initials }) })
                        ),
                        /* @__PURE__ */ jsx("div", { className: "fallback-content absolute inset-0 flex items-center justify-center text-white font-bold opacity-0", children: /* @__PURE__ */ jsx("span", { style: { fontSize: "8px" }, children: avatar.initials }) })
                      ]
                    },
                    index
                  )),
                  count > 3 && /* @__PURE__ */ jsxs(
                    "div",
                    {
                      className: "c-avatar c-avatar--xs user-avatar flex items-center justify-center bg-design-muted-foreground/20",
                      style: {
                        width: "20px",
                        height: "20px",
                        marginLeft: "-8px",
                        borderRadius: "50%",
                        border: "1.5px solid white",
                        boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                        fontSize: "8px"
                      },
                      title: `${count - 3} more staff members`,
                      children: [
                        "+",
                        count - 3
                      ]
                    }
                  )
                ] }),
                /* @__PURE__ */ jsxs("span", { children: [
                  "Verified ",
                  /* @__PURE__ */ jsx("time", { dateTime: deal.last_verified_at, title: new Date(deal.last_verified_at || "").toLocaleString(), children: usageTimeAgo }),
                  " by ",
                  count || 3,
                  " staffer",
                  (count || 3) > 1 ? "s" : ""
                ] })
              ] }),
              daysRemaining !== null && daysRemaining > 0 && /* @__PURE__ */ jsxs("div", { className: "mb-3 text-xs text-design-warning px-2 py-1 rounded flex items-center", children: [
                /* @__PURE__ */ jsx("span", { className: "animate-pulse mr-1", children: "⏱" }),
                /* @__PURE__ */ jsxs("span", { children: [
                  "Limited time offer! ",
                  daysRemaining > 365 ? `${Math.floor(daysRemaining / 365)}+ ${Math.floor(daysRemaining / 365) === 1 ? "year" : "years"} left` : daysRemaining > 30 ? `${Math.floor(daysRemaining / 30)} ${Math.floor(daysRemaining / 30) === 1 ? "month" : "months"} left` : daysRemaining === 1 ? "Ends today" : `Ends in ${daysRemaining} days`
                ] })
              ] })
            ] }) })
          ] }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center px-4 sm:pr-4 sm:pl-0 gap-2 w-full sm:min-w-[150px] sm:w-auto py-3 sm:py-0", children: [
            /* @__PURE__ */ jsx(
              "div",
              {
                className: "deal-coupon-code relative cursor-pointer px-3 py-1 w-full text-center bg-design-muted rounded-full border border-design-muted-foreground/10 overflow-hidden",
                onClick: (e) => {
                  e.stopPropagation();
                  handleCopy(e);
                },
                title: isCodeRevealed ? "Click to copy" : "Click to reveal code",
                "aria-label": isCodeRevealed ? `Copy coupon code: ${deal.coupon_code || "NO CODE"}` : "Click to reveal coupon code",
                children: deal.coupon_code ? /* @__PURE__ */ jsxs(Fragment, { children: [
                  /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: `text-base font-bold transition-all duration-300 ${isCodeRevealed ? "blur-none opacity-100" : "blur-[4px] select-none"}`,
                      style: { color: "var(--design-foreground)" },
                      "aria-hidden": !isCodeRevealed,
                      children: deal.coupon_code
                    }
                  ),
                  isCodeRevealed && /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: "absolute inset-0 bg-design-primary/10 animate-reveal-sweep",
                      "aria-hidden": "true"
                    }
                  ),
                  /* @__PURE__ */ jsx("span", { className: "sr-only", children: isCodeRevealed ? `Coupon code: ${deal.coupon_code}` : "Click to reveal coupon code" })
                ] }) : "NO CODE"
              }
            ),
            /* @__PURE__ */ jsx(
              "button",
              {
                className: "copy-code-button px-4 py-1 rounded-full border border-design-muted-foreground/20 hover:border-design-primary/50 dark:hover:border-design-primary/50 transition-colors",
                onClick: (e) => {
                  e.stopPropagation();
                  handleCopy(e);
                },
                "aria-label": `Copy coupon code ${deal.coupon_code || "for this deal"}`,
                children: "Copy Code"
              }
            ),
            /* @__PURE__ */ jsxs("div", { className: "deal-card-actions flex items-center mt-1", children: [
              /* @__PURE__ */ jsx(
                BookmarkButton,
                {
                  dealId: deal.id.toString(),
                  className: "bookmark-button p-2 rounded-full hover:bg-design-muted transition-colors"
                }
              ),
              /* @__PURE__ */ jsx(
                "button",
                {
                  className: "eye-button p-2 rounded-full hover:bg-design-muted transition-colors relative z-10",
                  onClick: (e) => {
                    e.stopPropagation();
                    window.open(generateCouponUrl(deal), "_blank");
                  },
                  title: "View coupon details",
                  "aria-label": "View coupon details",
                  children: /* @__PURE__ */ jsx(Eye, { size: 18, className: "text-design-muted-foreground hover:text-design-foreground transition-colors" })
                }
              )
            ] })
          ] })
        ]
      }
    )
  ] });
};

const OptimizedDealListCard$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  OptimizedDealListCard
}, Symbol.toStringTag, { value: 'Module' }));

const CompactDealCard = ({
  deal,
  isRevealed = false,
  dealUrl,
  className,
  ...props
}) => {
  const [loading, setLoading] = useState(true);
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);
  const daysRemaining = deal.deal_end_date ? Math.ceil((new Date(deal.deal_end_date).getTime() - (/* @__PURE__ */ new Date()).getTime()) / (1e3 * 60 * 60 * 24)) : null;
  const isExpiringSoon = daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  const isExpired = daysRemaining !== null && daysRemaining <= 0;
  const getImageUrl = () => {
    if (deal.brands?.logo_url) {
      const brandLogoUrl = deal.brands.logo_url.trim();
      if (brandLogoUrl !== "") {
        if (brandLogoUrl.startsWith("http://") || brandLogoUrl.startsWith("https://")) {
          return brandLogoUrl;
        } else {
          return brandLogoUrl.startsWith("/") ? brandLogoUrl : `/${brandLogoUrl}`;
        }
      }
    }
    if (deal.merchants?.logo_url) {
      const merchantLogoUrl = deal.merchants.logo_url.trim();
      if (merchantLogoUrl !== "") {
        if (merchantLogoUrl.startsWith("http://") || merchantLogoUrl.startsWith("https://")) {
          return merchantLogoUrl;
        } else {
          return merchantLogoUrl.startsWith("/") ? merchantLogoUrl : `/${merchantLogoUrl}`;
        }
      }
    }
    if (deal.categories?.category_logo) {
      const categoryLogoUrl = deal.categories.category_logo.trim();
      if (categoryLogoUrl !== "") {
        if (categoryLogoUrl.startsWith("http://") || categoryLogoUrl.startsWith("https://")) {
          return categoryLogoUrl;
        } else {
          return categoryLogoUrl.startsWith("/") ? categoryLogoUrl : `/${categoryLogoUrl}`;
        }
      }
    }
    if (deal.imagebig_url && deal.imagebig_url.trim() !== "") return deal.imagebig_url;
    if (deal.image_url && deal.image_url.trim() !== "") return deal.image_url;
    if (deal.imagesmall_url && deal.imagesmall_url.trim() !== "") return deal.imagesmall_url;
    return "/placeholder-image.svg";
  };
  const imageUrl = getImageUrl();
  const handleCopyCode = () => {
    setIsCodeRevealed(true);
    if (typeof window !== "undefined") {
      const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
      revealedCodes[deal.id] = true;
      localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
    }
    if (deal.coupon_code) {
      if (navigator.clipboard && navigator.clipboard.writeText) {
        navigator.clipboard.writeText(deal.coupon_code);
        toast.success("Coupon code copied to clipboard!");
      } else {
        const textarea = document.createElement("textarea");
        textarea.value = deal.coupon_code;
        document.body.appendChild(textarea);
        textarea.select();
        try {
          document.execCommand("copy");
          toast.success("Coupon code copied to clipboard!");
        } catch (err) {
          toast.error("Failed to copy code");
        }
        document.body.removeChild(textarea);
      }
    }
    if (deal.tracking_url) {
      window.open(deal.tracking_url, "_blank");
    }
  };
  const finalDealUrl = dealUrl || (deal.slug ? `/deal/${deal.slug}` : `/deal/${deal.id}`);
  const handleCardClick = () => {
    if (typeof window !== "undefined") {
      window.open(finalDealUrl, "_blank");
    }
  };
  return /* @__PURE__ */ jsx("div", { className: "flex justify-center w-full", children: /* @__PURE__ */ jsxs(
    Card,
    {
      variant: "default",
      interactive: true,
      onClick: handleCardClick,
      className: cn(
        "compact-deal-card relative overflow-hidden cursor-pointer w-full h-full max-w-[200px] min-h-[280px]",
        "flex flex-col justify-between",
        className
      ),
      style: { borderRadius: "12px" },
      ...props,
      children: [
        isExpiringSoon && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-warning text-black dark:text-black text-xs font-bold px-2 py-1 rounded-full", children: daysRemaining === 1 ? "Expires today" : `Expires in ${daysRemaining} days` }),
        isExpired && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-destructive text-white text-xs font-bold px-2 py-1 rounded-full", children: "Expired" }),
        deal.discount && /* @__PURE__ */ jsx("div", { className: "absolute top-2 left-2 z-10 bg-design-primary text-white dark:text-black text-xs font-bold px-2 py-1 rounded-full", children: deal.discount }),
        /* @__PURE__ */ jsxs("div", { className: "p-3 flex flex-col h-full", children: [
          /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-2", children: /* @__PURE__ */ jsx("div", { className: "w-12 h-12 rounded-full bg-white dark:bg-gray-800 flex items-center justify-center overflow-hidden border border-design-border/20 shadow-sm", children: /* @__PURE__ */ jsx(
            "img",
            {
              src: imageUrl,
              alt: deal.brands?.name || deal.merchants?.name || "Brand",
              className: "w-10 h-10 object-contain",
              loading: "lazy",
              onLoad: () => setLoading(false),
              onError: (e) => {
                const target = e.target;
                if (!target.src.includes("placeholder-image.svg")) {
                  if (deal.brands?.logo_url && !target.src.includes(deal.brands.logo_url)) {
                    const brandLogoUrl = deal.brands.logo_url.trim();
                    if (brandLogoUrl !== "") {
                      if (brandLogoUrl.startsWith("http://") || brandLogoUrl.startsWith("https://")) {
                        target.src = brandLogoUrl;
                      } else {
                        target.src = brandLogoUrl.startsWith("/") ? brandLogoUrl : `/${brandLogoUrl}`;
                      }
                      return;
                    }
                  }
                  if (deal.merchants?.logo_url && !target.src.includes(deal.merchants.logo_url)) {
                    const merchantLogoUrl = deal.merchants.logo_url.trim();
                    if (merchantLogoUrl !== "") {
                      if (merchantLogoUrl.startsWith("http://") || merchantLogoUrl.startsWith("https://")) {
                        target.src = merchantLogoUrl;
                      } else {
                        target.src = merchantLogoUrl.startsWith("/") ? merchantLogoUrl : `/${merchantLogoUrl}`;
                      }
                      return;
                    }
                  }
                  target.src = "/placeholder-image.svg";
                }
                setLoading(false);
              }
            }
          ) }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex justify-center items-center gap-2 mb-2", children: [
            deal.price && /* @__PURE__ */ jsxs("span", { className: "text-design-foreground font-bold text-sm", children: [
              deal.currency || "$",
              deal.price
            ] }),
            deal.discount && /* @__PURE__ */ jsx("span", { className: "text-design-primary font-bold text-sm", children: deal.discount })
          ] }),
          /* @__PURE__ */ jsx("h3", { className: "text-xs font-semibold text-design-foreground text-center line-clamp-2 mb-2 leading-tight min-h-[2.5rem] flex items-center justify-center", children: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title }),
          /* @__PURE__ */ jsxs("div", { className: "hidden", children: [
            "Title: ",
            deal.title,
            ", Cleaned: ",
            deal.cleaned_title || "null",
            ", Price: ",
            deal.price || "null",
            ", Discount: ",
            deal.discount || "null"
          ] }),
          /* @__PURE__ */ jsx("div", { className: "text-xs text-design-muted-foreground text-center mb-2", children: deal.brands?.name || deal.merchants?.name || "Brand" }),
          /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-center gap-2 mb-3", children: [
            deal.verified && /* @__PURE__ */ jsxs("span", { className: "verified-badge text-[10px] px-2 py-1 bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200 rounded-full flex items-center gap-1", children: [
              /* @__PURE__ */ jsx(
                "img",
                {
                  src: "/Vapehybrid light icon.svg",
                  alt: "Verified",
                  className: "dark:hidden w-2 h-2"
                }
              ),
              /* @__PURE__ */ jsx(
                "img",
                {
                  src: "/Vapehybrid dark icon.svg",
                  alt: "Verified",
                  className: "hidden dark:block w-2 h-2"
                }
              ),
              /* @__PURE__ */ jsx("span", { children: "Verified" })
            ] }),
            deal.success_rate !== void 0 && /* @__PURE__ */ jsxs("span", { className: `success-rate-badge text-[10px] px-2 py-1 rounded-full flex items-center gap-1 ${deal.success_rate >= 80 ? "bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-200" : deal.success_rate >= 50 ? "bg-yellow-100 dark:bg-yellow-900 text-yellow-800 dark:text-yellow-200" : "bg-red-100 dark:bg-red-900 text-red-800 dark:text-red-200"}`, children: [
              /* @__PURE__ */ jsx(ThumbsUp, { size: 8 }),
              /* @__PURE__ */ jsxs("span", { children: [
                Math.round(deal.success_rate),
                "%"
              ] })
            ] })
          ] }),
          deal.coupon_code && /* @__PURE__ */ jsxs("div", { className: "mt-auto mb-3", children: [
            /* @__PURE__ */ jsx(
              "div",
              {
                className: "relative cursor-pointer px-2 py-2 w-full text-center bg-design-foreground text-design-background rounded-lg mb-2 text-xs shadow-sm",
                onClick: (e) => {
                  e.stopPropagation();
                  handleCopyCode();
                },
                title: isCodeRevealed ? "Click to copy" : "Click to reveal code",
                children: /* @__PURE__ */ jsxs(Fragment, { children: [
                  /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: `font-mono font-bold ${isCodeRevealed ? "" : "blur-[2px] select-none"}`,
                      children: deal.coupon_code
                    }
                  ),
                  /* @__PURE__ */ jsx("span", { className: "sr-only", children: isCodeRevealed ? `Coupon code: ${deal.coupon_code}` : "Click to reveal coupon code" })
                ] })
              }
            ),
            /* @__PURE__ */ jsx("div", { className: "flex justify-center mb-2", children: /* @__PURE__ */ jsxs(
              "button",
              {
                onClick: (e) => {
                  e.stopPropagation();
                  handleCopyCode();
                },
                className: "bg-design-primary hover:bg-design-primary/90 text-white dark:text-black px-3 py-1.5 rounded-lg text-xs font-medium transition-colors flex items-center gap-1",
                "aria-label": isCodeRevealed ? `Copy coupon code ${deal.coupon_code}` : "Reveal coupon code",
                children: [
                  /* @__PURE__ */ jsx(ExternalLink, { size: 12, "aria-hidden": "true" }),
                  isCodeRevealed ? "Copy" : "Reveal"
                ]
              }
            ) })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "deal-card-actions flex items-center justify-between px-2 mt-auto", children: [
            /* @__PURE__ */ jsx(
              BookmarkButton,
              {
                dealId: deal.id.toString(),
                className: "heart-bookmark-button w-8 h-8 flex items-center justify-center rounded-full hover:bg-design-muted/20 transition-colors"
              }
            ),
            /* @__PURE__ */ jsx(
              "button",
              {
                onClick: (e) => {
                  e.stopPropagation();
                  if (typeof window !== "undefined") {
                    window.open(finalDealUrl, "_blank");
                  }
                },
                className: "eye-button w-8 h-8 flex items-center justify-center rounded-full hover:bg-design-muted/20 transition-colors relative z-10",
                title: "View deal details",
                children: /* @__PURE__ */ jsx(Eye, { size: 16, className: "text-design-muted-foreground hover:text-design-foreground transition-colors" })
              }
            )
          ] })
        ] })
      ]
    }
  ) });
};

const CompactDealCard$1 = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  CompactDealCard
}, Symbol.toStringTag, { value: 'Module' }));

export { $$WebPImage as $, CompactDealCard$1 as A, BookmarkButton as B, Card as C, DealCouponModal as D, FilterDrawer as F, GlowingButton as G, ImprovedDealCard$1 as I, NoticeBar as N, OptimizedDealCard as O, ResponsiveImage as R, SearchBox as S, ToastProvider as T, ViewToggle as V, WebPImage as W, SortSelect as a, $$PerPageSelect as b, $$Pagination as c, NewsletterPopup as d, copyToClipboard as e, usePrefetch as f, generateCouponUrl as g, useEngagementTracking as h, getRandomStaffImages as i, generateUsageInfo as j, cn as k, DealImage as l, DealStructuredData as m, normalizeUrl as n, Navbar as o, Button$1 as p, DealCard$1 as q, DealListCard$1 as r, OptimizedDealCard$1 as s, select as t, useToast as u, DealCouponModal$1 as v, sheet as w, FilterForm$1 as x, FilterDrawer$1 as y, OptimizedDealListCard$1 as z };
