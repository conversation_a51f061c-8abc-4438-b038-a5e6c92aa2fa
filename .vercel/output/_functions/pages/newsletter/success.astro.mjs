import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../assets/js/MainLayout-BVJnMCp3.js';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Success = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Success;
  const message = Astro2.url.searchParams.get("message") || "Your subscription has been confirmed!";
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Subscription Confirmed | VapeHybrid" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-4xl mx-auto px-4 py-20 text-center"> <div class="bg-design-card/80 backdrop-blur-sm rounded-xl p-10 border border-design-border shadow-lg"> <div class="w-20 h-20 bg-design-success/20 rounded-full flex items-center justify-center mx-auto mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-design-success" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clip-rule="evenodd"></path> </svg> </div> <h1 class="text-3xl font-bold text-design-foreground mb-4">Subscription Confirmed!</h1> <p class="text-design-muted-foreground mb-8 max-w-lg mx-auto"> ${message} </p> <div class="flex flex-col sm:flex-row gap-4 justify-center"> <a href="/" class="inline-block px-6 py-3 bg-design-primary text-white dark:text-black text-sm font-medium rounded-lg hover:bg-design-primary/90 transition-all duration-300 shadow-md">
Return to Homepage
</a> <a href="/deals" class="inline-block px-6 py-3 bg-design-secondary text-design-foreground dark:text-black text-sm font-medium rounded-lg hover:bg-design-secondary/90 transition-all duration-300 shadow-md">
Browse Latest Deals
</a> </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/success.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/success.astro";
const $$url = "/newsletter/success";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Success,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
