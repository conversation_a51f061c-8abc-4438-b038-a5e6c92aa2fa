import{r as e,j as t}from"./vendor/react-bq1VYqFi.js";import{N as n}from"./app/design-system-lJ5ci6hi.js";import"./vendor/ui-components-_FzKGR3X.js";import"./vendor/utils-hwhCH1Yb.js";const s=["/newsletter","/newsletter/","/contact","/contact/","/admin","/admin/","/newsletter/preferences","/newsletter/confirm","/newsletter/success","/newsletter/error","/newsletter/unsubscribed"],r=()=>{const[r,o]=e.useState(!1),[i,a]=e.useState(!1);e.useEffect((()=>{const e=window.location.pathname;if(s.some((t=>e===t||e.startsWith(`${t}/`))))return;if(localStorage.getItem("exitIntentPopupSeen"))return;if(localStorage.getItem("newsletterSubscribed"))return;const t=setTimeout((()=>{const e=e=>{i||e.clientY<=0&&(o(!0),a(!0))};return document.addEventListener("mouseleave",e),()=>{document.removeEventListener("mouseleave",e)}}),3e3);return()=>{clearTimeout(t)}}),[i]);return t.jsx(n,{isOpen:r,onClose:()=>{o(!1),localStorage.setItem("exitIntentPopupSeen","true")},title:"Wait! Don't Miss Out",subtitle:"Get exclusive deals delivered straight to your inbox.",buttonText:"Get Deal Alerts",exitIntent:!0})};export{r as default};
