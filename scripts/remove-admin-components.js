/**
 * Remove Admin Components Script
 *
 * This script removes admin-related components and utilities from the codebase
 * to improve security and reduce bundle size.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Files to be removed
const filesToRemove = [
  'src/lib/supabase-admin.ts',
  'src/lib/supabase-react-client.ts',
  'src/lib/jwt.ts',
  'src/lib/mock-data.ts'
];

// Recycle bin directory
const recycleBinDir = 'recycle-bin/admin-components';

// Create recycle bin directory if it doesn't exist
if (!fs.existsSync(recycleBinDir)) {
  fs.mkdirSync(recycleBinDir, { recursive: true });
  console.log(`Created recycle bin directory: ${recycleBinDir}`);
}

// Move files to recycle bin
let movedCount = 0;
let errorCount = 0;

filesToRemove.forEach(filePath => {
  try {
    if (fs.existsSync(filePath)) {
      // Create destination path
      const fileName = path.basename(filePath);
      const destPath = path.join(recycleBinDir, fileName);

      // Read file content
      const content = fs.readFileSync(filePath, 'utf8');

      // Write to recycle bin
      fs.writeFileSync(destPath, content);

      // Remove original file
      fs.unlinkSync(filePath);

      console.log(`✅ Moved ${filePath} to ${destPath}`);
      movedCount++;
    } else {
      console.log(`⚠️ File not found: ${filePath}`);
    }
  } catch (error) {
    console.error(`❌ Error processing ${filePath}:`, error);
    errorCount++;
  }
});

console.log('\nSummary:');
console.log(`- ${movedCount} files moved to recycle bin`);
console.log(`- ${errorCount} errors encountered`);

if (movedCount === 0 && errorCount === 0) {
  console.log('No admin components found to remove.');
}

console.log('\nNext steps:');
console.log('1. Review the removed files in the recycle bin');
console.log('2. Update imports in any files that might be referencing these components');
console.log('3. Run tests to ensure the application still works correctly');
