/**
 * Convert How-It-Works Images Script
 *
 * This script converts the images in the public/how directory to WebP format.
 *
 * Usage:
 * node scripts/convert-how-images.js
 */

import fs from 'fs';
import path from 'path';
import sharp from 'sharp';

// Configuration
const sourceDir = 'public/how';
const quality = 80; // WebP quality (0-100)

// Convert an image to WebP format
const convertToWebP = async (imagePath) => {
  try {
    const outputPath = `${imagePath.substring(0, imagePath.lastIndexOf('.'))}.webp`;

    // Skip if WebP version already exists
    if (fs.existsSync(outputPath)) {
      console.log(`WebP version already exists: ${outputPath}`);
      return;
    }

    // Process the image
    await sharp(imagePath)
      .webp({ quality })
      .toFile(outputPath);

    console.log(`Converted: ${imagePath} -> ${outputPath}`);

    // Get file sizes for comparison
    const originalSize = fs.statSync(imagePath).size;
    const webpSize = fs.statSync(outputPath).size;
    const savings = ((originalSize - webpSize) / originalSize * 100).toFixed(2);

    console.log(`Size reduction: ${savings}% (${(originalSize / 1024).toFixed(2)}KB -> ${(webpSize / 1024).toFixed(2)}KB)`);
  } catch (error) {
    console.error(`Error converting ${imagePath}:`, error);
  }
};

// Main function
const convertHowImages = async () => {
  console.log('Starting image conversion for How-It-Works page...');

  // Check if directory exists
  if (!fs.existsSync(sourceDir)) {
    console.error(`Directory not found: ${sourceDir}`);
    return;
  }

  // Get all files in the directory
  const files = fs.readdirSync(sourceDir);
  
  // Filter for image files
  const imageFiles = files.filter(file => {
    const ext = path.extname(file).toLowerCase();
    return ['.jpg', '.jpeg', '.png', '.gif'].includes(ext);
  });

  console.log(`Found ${imageFiles.length} images to process`);

  // Process each image
  for (const file of imageFiles) {
    const imagePath = path.join(sourceDir, file);
    await convertToWebP(imagePath);
  }

  console.log('Image conversion complete!');
};

// Run the script
convertHowImages().catch(console.error);
