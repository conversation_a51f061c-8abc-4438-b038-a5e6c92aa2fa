import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$LegalPageLayout } from '../assets/js/LegalPageLayout-DSHTXlhZ.js';
import { G as GlowingButton } from '../assets/js/app/design-system-CUssmfny.js';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Legal = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Legal;
  const title = "Legal Information | VapeHybrid";
  const description = "Legal information, terms, policies, and disclosures for VapeHybrid.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": title,
    "description": description,
    "publisher": {
      "@type": "Organization",
      "name": "VapeHybrid",
      "logo": {
        "@type": "ImageObject",
        "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`
      }
    }
  };
  const legalPages = [
    {
      title: "Terms & Conditions",
      description: "Understand the terms and conditions governing the use of VapeHybrid's services.",
      url: "/terms",
      icon: "\u{1F4DC}"
    },
    {
      title: "Privacy Policy",
      description: "Learn how VapeHybrid collects, uses, and protects your personal information.",
      url: "/privacy",
      icon: "\u{1F512}"
    },
    {
      title: "Affiliate Disclosure",
      description: "Transparency about our affiliate relationships and how they influence our content.",
      url: "/affiliate-disclosure",
      icon: "\u{1F91D}"
    },
    {
      title: "Disclaimer",
      description: "Important disclaimers regarding the content and services provided by VapeHybrid.",
      url: "/disclaimer",
      icon: "\u26A0\uFE0F"
    },
    {
      title: "Cookie Policy",
      description: "Information about the cookies used on VapeHybrid and how to manage your preferences.",
      url: "/cookie-policy",
      icon: "\u{1F36A}"
    },
    {
      title: "About Us",
      description: "Learn more about VapeHybrid's mission, team, and commitment to providing the best vape deals.",
      url: "/about",
      icon: "\u2139\uFE0F"
    },
    {
      title: "Contact Us",
      description: "Get in touch with the VapeHybrid team for inquiries or support.",
      url: "/contact",
      icon: "\u{1F4E7}"
    }
  ];
  return renderTemplate`${renderComponent($$result, "LegalPageLayout", $$LegalPageLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<h1 class="text-3xl font-bold text-design-foreground mb-6">Legal Information</h1> <p class="mb-8">
This page provides access to all legal documents, policies, and disclosures for VapeHybrid.
    We are committed to transparency and compliance with all applicable laws and regulations.
</p> <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mt-8"> ${legalPages.map((page) => renderTemplate`<div class="bg-design-card border border-design-border rounded-lg p-6 hover:shadow-md transition-all duration-300"> <div class="flex items-start gap-4 mb-4"> <div class="text-3xl">${page.icon}</div> <div> <h2 class="text-xl font-semibold text-design-foreground mb-2 mt-0">${page.title}</h2> <p class="text-design-muted-foreground text-sm">${page.description}</p> </div> </div> <div class="flex justify-end"> ${renderComponent($$result2, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": page.url, "className": "text-sm px-4 py-2", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": ($$result3) => renderTemplate`
View Details
` })} </div> </div>`)} </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/legal.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/legal.astro";
const $$url = "/legal";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Legal,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
