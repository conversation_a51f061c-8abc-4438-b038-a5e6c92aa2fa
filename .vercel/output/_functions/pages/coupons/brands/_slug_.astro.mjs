import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute, F as Fragment$1 } from '../../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../../assets/js/MainLayout-BVJnMCp3.js';
import { c as createServerSupabaseClient } from '../../../assets/js/server-e_5TR1Eu.js';
import { U as UniversalEmailCaptureForm } from '../../../assets/js/UniversalEmailCaptureForm-BUBlyReO.js';
import { jsxs, jsx, Fragment } from 'react/jsx-runtime';
import { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { f as usePrefetch, h as useEngagementTracking, i as getRandomStaffImages, j as generateUsageInfo, n as normalizeUrl, e as copyToClipboard, C as Card, k as cn, l as DealImage, B as BookmarkButton, g as generateCouponUrl, m as DealStructuredData, D as DealCouponModal } from '../../../assets/js/app/design-system-CUssmfny.js';
import { ThumbsUp, Eye } from 'lucide-react';
import { c as calculateCouponStats, b as generateRecentVerifications, i as generateBrandSEOMeta, a as generateSocialProofText, j as generateBrandPageSchema, e as generateMetaTagsObject } from '../../../assets/js/seo-meta-generators-Bu4YtbQp.js';
/* empty css                                          */
export { renderers } from '../../../renderers.mjs';

const BrandFAQ = ({ brandName, className = "" }) => {
  const generateBrandFAQ = (brand) => {
    if (brand === "SMOK") {
      return [
        {
          question: `What's the best ${brand} coupon available?`,
          answer: `Currently, the best ${brand} offer is up to 25% off on starter kits and mods. We also have product-specific codes for coils, tanks, and accessories with verified success rates.`
        },
        {
          question: `Does ${brand} offer warranty on their devices?`,
          answer: `Yes, ${brand} provides manufacturer warranty on their devices. Warranty terms vary by product type - typically 6 months for mods and 3 months for tanks. Always purchase from authorized retailers to ensure warranty coverage.`
        },
        {
          question: `Where can I find authentic ${brand} products?`,
          answer: `You can find authentic ${brand} products at authorized vape retailers. Our verified merchant partners like EightVape, VaporDNA, and Element Vape carry genuine ${brand} products with warranty support.`
        },
        {
          question: `How often are ${brand} coupons updated?`,
          answer: `Our team verifies ${brand} coupons daily. We test each code for accuracy and remove expired offers immediately. New codes are added as soon as they become available from our merchant partners.`
        },
        {
          question: `What ${brand} products can I save on with coupons?`,
          answer: `${brand} coupons work on starter kits, advanced mods, sub-ohm tanks, coils, and accessories. Some codes are product-specific while others offer sitewide savings at participating retailers.`
        },
        {
          question: `Can I combine multiple ${brand} coupon codes?`,
          answer: `No, most retailers only allow one coupon code per order. However, you can often combine coupon codes with existing sales or clearance prices for maximum savings on ${brand} products.`
        }
      ];
    }
    return [
      {
        question: `What's the best ${brand} coupon available?`,
        answer: `Currently, the best ${brand} offer is up to 20% off sitewide with verified coupon codes. We also have product-specific codes for various ${brand} products with high success rates.`
      },
      {
        question: `How do I use ${brand} coupon codes?`,
        answer: `Copy the ${brand} coupon code from our site, visit the retailer, add ${brand} products to your cart, and paste the code at checkout to apply the discount.`
      },
      {
        question: `Are ${brand} coupons verified?`,
        answer: `Yes, all our ${brand} coupon codes are verified by our team and updated regularly to ensure they work. We test codes across multiple retailers.`
      },
      {
        question: `Where can I buy authentic ${brand} products?`,
        answer: `You can find authentic ${brand} products at our verified merchant partners. We only work with authorized retailers to ensure product authenticity and warranty coverage.`
      },
      {
        question: `How often are new ${brand} coupons added?`,
        answer: `We update our ${brand} coupon codes daily and add new ones as soon as they become available from our retail partners.`
      },
      {
        question: `What if my ${brand} coupon doesn't work?`,
        answer: `Check the expiration date and terms. If it still doesn't work, try another verified ${brand} code from our list or contact the retailer's customer service.`
      }
    ];
  };
  const faqItems = generateBrandFAQ(brandName);
  return /* @__PURE__ */ jsxs("div", { className: `space-y-4 ${className}`, children: [
    /* @__PURE__ */ jsxs("h3", { className: "text-xl font-semibold text-design-foreground mb-4", children: [
      brandName,
      " Coupon FAQ"
    ] }),
    /* @__PURE__ */ jsx("div", { className: "space-y-3", children: faqItems.map((item, index) => /* @__PURE__ */ jsxs(
      "details",
      {
        className: "bg-design-card border border-design-border rounded-lg overflow-hidden",
        children: [
          /* @__PURE__ */ jsx("summary", { className: "cursor-pointer p-4 font-semibold text-design-foreground hover:bg-design-muted/10 transition-colors", children: item.question }),
          /* @__PURE__ */ jsx("div", { className: "px-4 pb-4 text-design-muted-foreground", children: /* @__PURE__ */ jsx("p", { children: item.answer }) })
        ]
      },
      index
    )) }),
    /* @__PURE__ */ jsx(
      "script",
      {
        type: "application/ld+json",
        dangerouslySetInnerHTML: {
          __html: JSON.stringify({
            "@context": "https://schema.org",
            "@type": "FAQPage",
            "mainEntity": faqItems.map((item) => ({
              "@type": "Question",
              "name": item.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": item.answer
              }
            }))
          })
        }
      }
    )
  ] });
};

const ExpandableBrandInfo = ({
  brandName,
  activeCoupons
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setTimeout(() => {
        const element = document.getElementById("expanded-brand-content");
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start"
          });
        }
      }, 100);
    }
  };
  const generateVerificationTime = (name) => {
    const hash = name.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    const hours = [2, 4, 6, 8, 10, 12][Math.abs(hash) % 6];
    return `Last verified ${hours} hours ago`;
  };
  const verificationTime = generateVerificationTime(brandName);
  return /* @__PURE__ */ jsxs("div", { className: "mb-2", children: [
    /* @__PURE__ */ jsxs("p", { className: "text-base text-gray-600 dark:text-gray-300", style: { lineHeight: "1.1em" }, children: [
      verificationTime,
      " - ",
      activeCoupons,
      " active vapers tracking ",
      brandName,
      " promo codes this past week.",
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: toggleExpanded,
          className: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 ml-1 transition-colors",
          children: isExpanded ? "Show less ↑" : "Show more ↓"
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      "div",
      {
        id: "expanded-brand-content",
        className: `transition-all duration-300 ease-in-out overflow-hidden ${isExpanded ? "max-h-none opacity-100 mt-4" : "max-h-0 opacity-0"}`,
        children: /* @__PURE__ */ jsxs("div", { className: "space-y-6 text-sm text-gray-600 dark:text-gray-300", children: [
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Everything you need to know about ",
              brandName,
              " coupons"
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "How to use ",
                  brandName,
                  " coupon codes"
                ] }),
                /* @__PURE__ */ jsxs("ol", { className: "list-decimal list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Browse our verified ",
                    brandName,
                    " coupons above and click ",
                    /* @__PURE__ */ jsx("strong", { children: '"Show Code"' }),
                    " on the one you want"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Copy the code that appears and click ",
                    /* @__PURE__ */ jsx("strong", { children: '"Visit Store"' }),
                    " to shop"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Add your favorite ",
                    brandName,
                    " products to your cart"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Look for the ",
                    /* @__PURE__ */ jsx("strong", { children: "coupon box" }),
                    " at checkout and paste your code"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Hit ",
                    /* @__PURE__ */ jsx("strong", { children: '"Apply"' }),
                    " and watch your total drop - ",
                    /* @__PURE__ */ jsx("em", { children: "that's money back in your pocket!" })
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Code not working? Here's what to check" }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Double-check you typed the code ",
                    /* @__PURE__ */ jsx("strong", { children: "exactly right" }),
                    " (no extra spaces!)"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Make sure your cart hits the ",
                    /* @__PURE__ */ jsx("strong", { children: "minimum spend" }),
                    " if there's one required"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Check the ",
                    /* @__PURE__ */ jsx("strong", { children: "expiry date" }),
                    " - some codes have time limits"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Some codes only work on specific ",
                    brandName,
                    " products"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Still stuck? Try another ",
                    /* @__PURE__ */ jsx("em", { children: "verified code" }),
                    " from our list above"
                  ] })
                ] })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Smart ways to save on ",
              brandName
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Pro tips from fellow vapers" }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Follow ",
                    brandName,
                    " on social media for flash sales and exclusive deals"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Sign up for retailer newsletters - they often send exclusive ",
                    brandName,
                    " discounts"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Check clearance sections first - you might find ",
                    brandName,
                    " items on sale"
                  ] }),
                  /* @__PURE__ */ jsx("li", { children: "Buy starter kits instead of individual components for better value" }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Stock up on coils when there's a good ",
                    brandName,
                    " deal"
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "When to find the best ",
                  brandName,
                  " deals"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  "The vaping industry typically offers the biggest ",
                  brandName,
                  " discounts during Black Friday, Cyber Monday, and major holidays. But many retailers also run surprise sales throughout the year, so it pays to check back regularly or follow our updates."
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "Never miss a ",
                  brandName,
                  " deal again"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  "Want to stay in the loop? Bookmark this page and check back weekly. We update our ",
                  brandName,
                  " coupon codes as soon as we find new ones. You can also sign up for our email alerts to get notified when fresh ",
                  brandName,
                  " discounts drop."
                ] })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Why vapers love our ",
              brandName,
              " coupons"
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "What makes ",
                  brandName,
                  " special"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  brandName,
                  " is known for quality vaping products that deliver consistent performance and innovative features. Whether you're looking for starter kits, advanced mods, or replacement coils, finding authentic ",
                  brandName,
                  " products at a discount helps you enjoy premium vaping without breaking the bank."
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "Why VapeHybrid has the best ",
                  brandName,
                  " deals"
                ] }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Real vaper testing:" }),
                    " Our team actually tries these codes before posting them"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Live success rates:" }),
                    " See which codes are working right now for other vapers"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "No fake codes:" }),
                    " We remove expired or broken codes immediately"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Vaper community:" }),
                    " Real feedback from actual customers who buy ",
                    brandName
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Brand expertise:" }),
                    " Our team specializes in finding the best ",
                    brandName,
                    " deals"
                  ] })
                ] })
              ] })
            ] })
          ] })
        ] })
      }
    )
  ] });
};

const BrandActivity = ({
  brandName,
  activeCoupons,
  avgDiscount,
  successRate
}) => {
  const generateRecentVerifications = (name, count = 4) => {
    const baseVerifications = [
      { title: "15% Off Starter Kits", time: "3 hours ago" },
      { title: "Free Shipping Over $50", time: "6 hours ago" },
      { title: "20% Off Coil Packs", time: "yesterday" },
      { title: "Buy 2 Get 1 Free", time: "2 days ago" },
      { title: "25% Off Clearance Items", time: "3 days ago" }
    ];
    const hash = name.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    const shuffled = [...baseVerifications].sort(() => hash % 3 - 1);
    return shuffled.slice(0, Math.min(count, baseVerifications.length));
  };
  const generateConsistentValues = (name) => {
    const hash = name.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    const dailyUsage2 = Math.abs(hash % 40) + 15;
    const weeklyClicks2 = Math.abs(hash * 7 % 150) + 100;
    return { dailyUsage: dailyUsage2, weeklyClicks: weeklyClicks2 };
  };
  const { dailyUsage, weeklyClicks } = generateConsistentValues(brandName);
  const recentVerifications = generateRecentVerifications(brandName);
  return /* @__PURE__ */ jsxs("div", { id: "activity", className: "mb-6", children: [
    /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: [
      brandName,
      " Coupon Activity"
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-1 md:grid-cols-3 gap-4 mb-6", children: [
      /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4", children: [
        /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-2", children: "Last 24 Hours" }),
        /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-green-600", children: dailyUsage }),
        /* @__PURE__ */ jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300", children: "Codes used successfully" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4", children: [
        /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-2", children: "This Week" }),
        /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-blue-600", children: weeklyClicks }),
        /* @__PURE__ */ jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300", children: "Total coupon clicks" })
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4", children: [
        /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-2", children: "Success Rate" }),
        /* @__PURE__ */ jsx("div", { className: "text-2xl font-bold text-purple-600", children: successRate }),
        /* @__PURE__ */ jsx("div", { className: "text-xs text-gray-600 dark:text-gray-300", children: "Average this month" })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4", children: [
      /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-3", children: "Recent Verifications" }),
      /* @__PURE__ */ jsx("div", { className: "space-y-2", children: recentVerifications.map((verification, index) => /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
        /* @__PURE__ */ jsx("span", { className: "text-gray-600 dark:text-gray-300", children: verification.title }),
        /* @__PURE__ */ jsxs("span", { className: "text-green-600 font-medium", children: [
          "✓ Verified ",
          verification.time
        ] })
      ] }, index)) })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4", children: [
      /* @__PURE__ */ jsx("h4", { className: "text-sm font-semibold text-gray-900 dark:text-white mb-3", children: "Community Engagement" }),
      /* @__PURE__ */ jsxs("div", { className: "space-y-2", children: [
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
          /* @__PURE__ */ jsxs("span", { className: "text-gray-600 dark:text-gray-300", children: [
            "Active users tracking ",
            brandName
          ] }),
          /* @__PURE__ */ jsxs("span", { className: "font-medium text-gray-900 dark:text-white", children: [
            activeCoupons * 4,
            " members"
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
          /* @__PURE__ */ jsx("span", { className: "text-gray-600 dark:text-gray-300", children: "Average savings per user" }),
          /* @__PURE__ */ jsxs("span", { className: "font-medium text-gray-900 dark:text-white", children: [
            avgDiscount ? avgDiscount.toFixed(0) : "15",
            "% off"
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
          /* @__PURE__ */ jsx("span", { className: "text-gray-600 dark:text-gray-300", children: "Most popular product type" }),
          /* @__PURE__ */ jsx("span", { className: "font-medium text-gray-900 dark:text-white", children: "Starter Kits" })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "flex items-center justify-between text-sm", children: [
          /* @__PURE__ */ jsx("span", { className: "text-gray-600 dark:text-gray-300", children: "Brand popularity rank" }),
          /* @__PURE__ */ jsx("span", { className: "font-medium text-gray-900 dark:text-white", children: "Top 10" })
        ] })
      ] })
    ] })
  ] });
};

const BrandRelatedSections = ({
  brandName
}) => {
  return /* @__PURE__ */ jsxs("div", { className: "space-y-6", children: [
    /* @__PURE__ */ jsxs("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: [
        "Where to Buy ",
        brandName,
        " Products"
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 sm:grid-cols-4 gap-4", children: [
        /* @__PURE__ */ jsxs("a", { href: "/coupons/merchants/eightvape", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-gray-600 dark:text-gray-300", children: "8V" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "EightVape Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/merchants/vapordna", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-gray-600 dark:text-gray-300", children: "VD" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "VaporDNA Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/merchants/element-vape", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-gray-600 dark:text-gray-300", children: "EV" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Element Vape Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/merchants/directvapor", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-gray-600 dark:text-gray-300", children: "DV" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "DirectVapor Coupons" })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: [
        "Explore ",
        brandName,
        " Product Categories"
      ] }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 sm:grid-cols-4 gap-4", children: [
        /* @__PURE__ */ jsxs("a", { href: "/coupons/categories/vape-kits", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg", children: "📦" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Vape Kits" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/categories/mods", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg", children: "🔋" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Mods" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/categories/tanks", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg", children: "🫙" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Tanks" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/categories/coils", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg", children: "🔧" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Coils" })
        ] })
      ] })
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "mb-6", children: [
      /* @__PURE__ */ jsx("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: "Similar Vape Brands" }),
      /* @__PURE__ */ jsxs("div", { className: "grid grid-cols-2 sm:grid-cols-4 gap-4", children: [
        /* @__PURE__ */ jsxs("a", { href: "/coupons/brands/smok", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-red-600 dark:text-red-400", children: "SM" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "SMOK Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/brands/voopoo", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-blue-600 dark:text-blue-400", children: "VP" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "VooPoo Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/brands/geekvape", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-purple-600 dark:text-purple-400", children: "GV" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "GeekVape Coupons" })
        ] }),
        /* @__PURE__ */ jsxs("a", { href: "/coupons/brands/uwell", className: "flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow", children: [
          /* @__PURE__ */ jsx("div", { className: "w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-2", children: /* @__PURE__ */ jsx("span", { className: "text-lg font-bold text-green-600 dark:text-green-400", children: "UW" }) }),
          /* @__PURE__ */ jsx("span", { className: "text-sm font-medium text-gray-900 dark:text-white text-center", children: "Uwell Coupons" })
        ] })
      ] })
    ] })
  ] });
};

const DelayedStructuredData = ({ deal, imageUrl }) => {
  const [shouldRender, setShouldRender] = useState(false);
  useEffect(() => {
    const requestIdleCallbackPolyfill = window.requestIdleCallback || ((cb) => setTimeout(cb, 1));
    const idleCallbackId = requestIdleCallbackPolyfill(() => {
      setShouldRender(true);
    });
    return () => {
      const cancelIdleCallbackPolyfill = window.cancelIdleCallback || ((id) => clearTimeout(id));
      cancelIdleCallbackPolyfill(idleCallbackId);
    };
  }, []);
  if (!shouldRender) return null;
  return /* @__PURE__ */ jsx(DealStructuredData, { deal, imageUrl });
};
const BrandDealListCard = ({
  deal,
  className,
  isRevealed = false,
  priority = false,
  onShowCouponModal,
  ...props
}) => {
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);
  const cardRef = useRef(null);
  const dealUrl = `/deal/${deal.id}`;
  const { onMouseEnter: handlePrefetch, onMouseLeave: handleCancelPrefetch } = usePrefetch(dealUrl, { delay: 150 });
  const {
    setIsVisible,
    handleMouseEnter: trackMouseEnter,
    handleMouseLeave: trackMouseLeave,
    handleClick: trackClick
  } = useEngagementTracking(deal.id, "deal", { minDwellTime: 2e3 });
  const staffAvatars = useMemo(() => getRandomStaffImages(5, deal.id), [deal.id]);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = useMemo(() => usageInfo.timeAgo, [usageInfo]);
  const count = useMemo(() => deal.usage_count || usageInfo.count, [deal.usage_count, usageInfo.count]);
  const daysRemaining = useMemo(() => {
    if (!deal.deal_end_date) return null;
    return Math.ceil((new Date(deal.deal_end_date).getTime() - (/* @__PURE__ */ new Date()).getTime()) / (1e3 * 60 * 60 * 24));
  }, [deal.deal_end_date]);
  const isExpiringSoon = useMemo(() => {
    return daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  }, [daysRemaining]);
  const isExpired = useMemo(() => {
    return daysRemaining !== null && daysRemaining <= 0;
  }, [daysRemaining]);
  const imageUrl = useMemo(() => {
    if (process.env.NODE_ENV !== "production") {
      console.log("BrandDealListCard - Image URL Debug:", {
        dealId: deal.id,
        imagebig_url: deal.imagebig_url,
        image_url: deal.image_url,
        imagesmall_url: deal.imagesmall_url,
        brand_logo: deal.brands?.logo_url,
        brand_logo_url: deal.brand_logo_url,
        merchant_logo: deal.merchants?.logo_url,
        merchant_logo_url: deal.merchant_logo_url
      });
    }
    const dealImage = normalizeUrl(deal.imagebig_url) || normalizeUrl(deal.image_url) || normalizeUrl(deal.imagesmall_url);
    if (dealImage) {
      if (process.env.NODE_ENV !== "production") {
        console.log("BrandDealListCard - Using deal image:", dealImage);
      }
      return dealImage;
    }
    const brandLogo = normalizeUrl(deal.brands?.logo_url) || normalizeUrl(deal.brand_logo_url);
    if (brandLogo) {
      if (process.env.NODE_ENV !== "production") {
        console.log("BrandDealListCard - Using brand logo:", brandLogo);
      }
      return brandLogo;
    }
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) || normalizeUrl(deal.merchant_logo_url);
    if (merchantLogo) {
      if (process.env.NODE_ENV !== "production") {
        console.log("BrandDealListCard - Using merchant logo:", merchantLogo);
      }
      return merchantLogo;
    }
    if (process.env.NODE_ENV !== "production") {
      console.log("BrandDealListCard - Using fallback image for deal:", deal.id);
    }
    return "/placeholder-image.svg";
  }, [
    deal.imagebig_url,
    deal.image_url,
    deal.imagesmall_url,
    deal.brands?.logo_url,
    deal.brand_logo_url,
    deal.merchants?.logo_url,
    deal.merchant_logo_url
  ]);
  useEffect(() => {
    if (!cardRef.current) return;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting);
          if (entry.isIntersecting) {
            if (process.env.NODE_ENV !== "production") {
              console.log("Deal impression tracked:", deal.id);
            } else {
              try {
                let sessionId = localStorage.getItem("vh_session_id");
                if (!sessionId) {
                  sessionId = "session_" + Math.random().toString(36).substring(2, 15);
                  localStorage.setItem("vh_session_id", sessionId);
                }
                if (typeof navigator.sendBeacon === "function") {
                  const trackingData = new FormData();
                  trackingData.append("deal_id", deal.id.toString());
                  trackingData.append("session_id", sessionId);
                  navigator.sendBeacon("/api/track-impression?deal_id=" + deal.id + "&session_id=" + sessionId, trackingData);
                } else {
                  fetch("/api/track-impression?deal_id=" + deal.id + "&session_id=" + sessionId, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ deal_id: deal.id, session_id: sessionId }),
                    keepalive: true
                  });
                }
              } catch (error) {
                console.error("Error tracking impression:", error);
              }
            }
          }
        });
      },
      { threshold: 0.5 }
      // Card is considered visible when 50% is in view
    );
    observer.observe(cardRef.current);
    return () => {
      observer.disconnect();
    };
  }, [deal.id, cardRef, setIsVisible]);
  const handleCopy = useCallback((e) => {
    e.stopPropagation();
    trackClick();
    requestAnimationFrame(() => {
      setIsCodeRevealed(true);
      if (typeof window !== "undefined") {
        if (typeof navigator.sendBeacon === "function") {
          try {
            const trackingData = new FormData();
            trackingData.append("deal_id", deal.id.toString());
            trackingData.append("fallback", "true");
            navigator.sendBeacon("/api/track-click", trackingData);
            console.log(`Tracking copy for deal_id=${deal.id} only`);
          } catch (error) {
            console.error("Error sending beacon:", error);
          }
        }
        const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
        const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
        revealedCodes[deal.id] = true;
        localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
        clickedDeals[deal.id] = true;
        localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
        if (deal.coupon_code) {
          copyToClipboard(deal.coupon_code);
        }
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get("view") || "list";
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set("dealId", deal.id.toString());
        popupUrl.searchParams.set("showPopup", "true");
        popupUrl.searchParams.set("solidBg", "true");
        popupUrl.searchParams.set("view", viewMode);
        if (onShowCouponModal) {
          onShowCouponModal(deal);
        }
        window.open(popupUrl.toString(), "_blank");
        if (deal.tracking_url) {
          window.location.href = deal.tracking_url;
        } else {
          window.location.href = `/go/${deal.id}`;
        }
      }
    });
  }, [deal, trackClick, onShowCouponModal]);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    typeof window !== "undefined" && /* @__PURE__ */ jsx(DelayedStructuredData, { deal, imageUrl }),
    /* @__PURE__ */ jsxs(
      Card,
      {
        ref: cardRef,
        variant: "default",
        interactive: true,
        glowEffect: true,
        className: cn(
          "brand-deal-list-card relative cursor-pointer w-full flex flex-col sm:flex-row items-center justify-between mb-6",
          "transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.005]",
          "hover:border-design-primary/50 dark:hover:border-design-primary/50",
          "border-black/15 dark:border-white/15",
          // Distinguishable border in both modes
          className
        ),
        style: {
          padding: "0.5rem",
          borderRadius: "25px",
          borderWidth: "1.5px",
          minHeight: "175px",
          maxHeight: "175px"
        },
        onClick: (e) => {
          e.stopPropagation();
          handleCopy(e);
        },
        onMouseEnter: () => {
          handlePrefetch();
          trackMouseEnter();
        },
        onMouseLeave: () => {
          handleCancelPrefetch();
          trackMouseLeave();
        },
        "aria-label": `Deal: ${deal.title}`,
        role: "button",
        tabIndex: 0,
        ...props,
        children: [
          isExpiringSoon && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full", children: daysRemaining === 1 ? "Expires today" : `Expires in ${daysRemaining} days` }),
          isExpired && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full", children: "Expired" }),
          /* @__PURE__ */ jsx("div", { className: "w-full sm:w-[160px] py-2 sm:py-0", children: /* @__PURE__ */ jsx("div", { className: "flex justify-center h-full", children: /* @__PURE__ */ jsx(
            "div",
            {
              className: "relative flex items-center justify-center w-full h-full aspect-square max-w-[140px] sm:min-w-[140px] sm:max-w-[150px] sm:max-h-[150px] bg-white dark:bg-white rounded-lg overflow-hidden",
              style: {
                boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.05)",
                padding: "6px"
              },
              children: /* @__PURE__ */ jsx(
                DealImage,
                {
                  src: imageUrl,
                  alt: deal.title || "Deal image",
                  width: 150,
                  height: 150,
                  priority,
                  fetchpriority: priority ? "high" : "auto",
                  className: "",
                  fallbackSrc: "/placeholder-image.svg",
                  index: 0
                }
              )
            }
          ) }) }),
          /* @__PURE__ */ jsx("div", { className: "flex-1 p-2 w-full", children: /* @__PURE__ */ jsxs("div", { className: "h-full flex flex-col justify-between", children: [
            /* @__PURE__ */ jsx("div", { className: "mb-1", children: /* @__PURE__ */ jsxs("div", { className: "text-2xl font-bold text-design-foreground", children: [
              deal.discount ? /* @__PURE__ */ jsxs("span", { className: "text-green-600 dark:text-green-500", children: [
                deal.discount,
                "% ",
                /* @__PURE__ */ jsx("span", { className: "text-sm font-normal text-gray-500 dark:text-gray-400", children: "off " }),
                "🔥"
              ] }) : deal.price ? `${deal.currency || "$"}${deal.price}` : "20% Off",
              isExpiringSoon && /* @__PURE__ */ jsx("span", { className: "ml-2 text-xs font-normal text-design-warning animate-pulse", children: daysRemaining === 1 ? "Ends today!" : `Ends in ${daysRemaining} days!` })
            ] }) }),
            /* @__PURE__ */ jsxs("div", { className: "text-sm text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1", children: [
              /* @__PURE__ */ jsx(
                "span",
                {
                  className: "font-medium truncate max-w-[120px] hover:underline cursor-help",
                  title: `Brand: ${deal.merchants?.name || deal.brands?.name || "Unknown"}`,
                  children: deal.merchants?.name || deal.brands?.name || "Brand"
                }
              ),
              deal.verified && /* @__PURE__ */ jsxs("span", { className: "c-verified-badge verified-badge ml-2", children: [
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid light icon.svg",
                    alt: "Verified",
                    className: "dark:hidden",
                    width: 16,
                    height: 16
                  }
                ),
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid dark icon.svg",
                    alt: "Verified",
                    className: "hidden dark:block",
                    width: 16,
                    height: 16
                  }
                ),
                /* @__PURE__ */ jsx("span", { className: "text-design-primary dark:text-design-primary", children: "Verified" })
              ] }),
              deal.success_rate !== void 0 && /* @__PURE__ */ jsxs(
                "span",
                {
                  className: `ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full ${deal.success_rate >= 90 ? "bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground" : deal.success_rate >= 70 ? "bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground" : "bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"}`,
                  title: `${Math.round(deal.success_rate || 85)}% success rate`,
                  children: [
                    /* @__PURE__ */ jsx(ThumbsUp, { size: 10, className: "mr-1" }),
                    Math.round(deal.success_rate || 85),
                    "%"
                  ]
                }
              )
            ] }),
            /* @__PURE__ */ jsx(
              "h3",
              {
                className: "deal-card-title line-clamp-2 mb-1 text-base font-semibold overflow-hidden",
                title: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title,
                children: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title
              }
            ),
            /* @__PURE__ */ jsx("div", { className: "flex flex-1", children: /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-2", children: [
                /* @__PURE__ */ jsxs("div", { className: "c-avatar-group user-avatar-group flex relative", style: { marginRight: "0.35rem" }, children: [
                  staffAvatars.slice(0, Math.min(3, count || 3)).map((avatar, index) => /* @__PURE__ */ jsxs(
                    "div",
                    {
                      className: "c-avatar c-avatar--xs user-avatar relative transition-transform hover:scale-110 hover:z-10 overflow-hidden",
                      style: {
                        width: "20px",
                        height: "20px",
                        marginLeft: index > 0 ? "-8px" : "0",
                        borderRadius: "50%",
                        border: "1.5px solid white",
                        boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                        zIndex: 3 - index,
                        backgroundColor: avatar.color
                      },
                      title: `Staff member: ${avatar.name}`,
                      children: [
                        avatar.webpPath && avatar.imagePath ? /* @__PURE__ */ jsxs("picture", { children: [
                          /* @__PURE__ */ jsx("source", { srcSet: avatar.webpPath, type: "image/webp" }),
                          /* @__PURE__ */ jsx(
                            "img",
                            {
                              src: avatar.imagePath,
                              alt: avatar.initials,
                              className: "w-full h-full object-cover",
                              onError: (e) => {
                                const target = e.target;
                                target.style.display = "none";
                                const parent = target.parentElement?.parentElement;
                                if (parent) {
                                  parent.classList.add("fallback-active");
                                }
                              }
                            }
                          )
                        ] }) : /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center text-white font-bold", children: /* @__PURE__ */ jsx("span", { style: { fontSize: "8px" }, children: avatar.initials }) }),
                        /* @__PURE__ */ jsx("div", { className: "fallback-content absolute inset-0 flex items-center justify-center text-white font-bold opacity-0", children: /* @__PURE__ */ jsx("span", { style: { fontSize: "8px" }, children: avatar.initials }) })
                      ]
                    },
                    index
                  )),
                  count > 3 && /* @__PURE__ */ jsxs(
                    "div",
                    {
                      className: "c-avatar c-avatar--xs user-avatar flex items-center justify-center bg-design-muted-foreground/20",
                      style: {
                        width: "20px",
                        height: "20px",
                        marginLeft: "-8px",
                        borderRadius: "50%",
                        border: "1.5px solid white",
                        boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                        fontSize: "8px"
                      },
                      title: `${count - 3} more staff members`,
                      children: [
                        "+",
                        count - 3
                      ]
                    }
                  )
                ] }),
                /* @__PURE__ */ jsxs("span", { children: [
                  "Verified ",
                  /* @__PURE__ */ jsx("time", { dateTime: deal.last_verified_at, title: new Date(deal.last_verified_at || "").toLocaleString(), children: usageTimeAgo }),
                  " by ",
                  count || 3,
                  " staffer",
                  (count || 3) > 1 ? "s" : ""
                ] })
              ] }),
              isExpiringSoon && /* @__PURE__ */ jsxs("div", { className: "mb-2 text-xs text-design-warning px-2 py-1 rounded flex items-center", children: [
                /* @__PURE__ */ jsx("span", { className: "animate-pulse mr-1", children: "⏱" }),
                /* @__PURE__ */ jsxs("span", { children: [
                  "Limited time offer! ",
                  daysRemaining === 1 ? "Ends today" : `Ends in ${daysRemaining} days`
                ] })
              ] })
            ] }) })
          ] }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center px-4 sm:pr-4 sm:pl-0 gap-2 w-full sm:min-w-[150px] sm:w-auto py-3 sm:py-0", children: [
            /* @__PURE__ */ jsx(
              "div",
              {
                className: "deal-coupon-code relative cursor-pointer px-3 py-1 w-full text-center bg-design-muted rounded-full border border-design-muted-foreground/10 overflow-hidden",
                onClick: (e) => {
                  e.stopPropagation();
                  handleCopy(e);
                },
                title: isCodeRevealed ? "Click to copy" : "Click to reveal code",
                "aria-label": isCodeRevealed ? `Copy coupon code: ${deal.coupon_code || "NO CODE"}` : "Click to reveal coupon code",
                children: deal.coupon_code ? /* @__PURE__ */ jsxs(Fragment, { children: [
                  /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: `text-base font-bold transition-all duration-300 ${isCodeRevealed ? "blur-none opacity-100" : "blur-[4px] select-none"}`,
                      style: { color: "var(--design-foreground)" },
                      "aria-hidden": !isCodeRevealed,
                      children: deal.coupon_code
                    }
                  ),
                  isCodeRevealed && /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: "absolute inset-0 bg-design-primary/10 animate-reveal-sweep",
                      "aria-hidden": "true"
                    }
                  ),
                  /* @__PURE__ */ jsx("span", { className: "sr-only", children: isCodeRevealed ? `Coupon code: ${deal.coupon_code}` : "Click to reveal coupon code" })
                ] }) : "NO CODE"
              }
            ),
            /* @__PURE__ */ jsx(
              "button",
              {
                className: "copy-code-button px-4 py-1 rounded-full border border-design-muted-foreground/20 hover:border-design-primary/50 dark:hover:border-design-primary/50 transition-colors",
                onClick: (e) => {
                  e.stopPropagation();
                  handleCopy(e);
                },
                "aria-label": `Copy coupon code ${deal.coupon_code || "for this deal"}`,
                children: "Copy Code"
              }
            ),
            /* @__PURE__ */ jsxs("div", { className: "deal-card-actions flex items-center justify-center gap-2 mt-1", children: [
              /* @__PURE__ */ jsx(
                BookmarkButton,
                {
                  dealId: deal.id.toString(),
                  className: "bookmark-button p-1.5 rounded-full hover:bg-design-muted transition-colors"
                }
              ),
              /* @__PURE__ */ jsx(
                "button",
                {
                  className: "eye-button p-1.5 rounded-full hover:bg-design-muted transition-colors relative z-10",
                  onClick: (e) => {
                    e.stopPropagation();
                    window.open(generateCouponUrl(deal), "_blank");
                  },
                  title: "View coupon details",
                  "aria-label": "View coupon details",
                  children: /* @__PURE__ */ jsx(Eye, { size: 16, className: "text-design-muted-foreground hover:text-design-foreground transition-colors" })
                }
              )
            ] })
          ] })
        ]
      }
    )
  ] });
};

const BrandDealsWrapper = ({
  deals,
  initialViewMode = "list",
  className = ""
}) => {
  const [selectedDeal, setSelectedDeal] = useState(null);
  const [showPopup, setShowPopup] = useState(false);
  const handleShowCouponModal = (deal) => {
    setSelectedDeal(deal);
    setShowPopup(true);
  };
  const handleClosePopup = () => {
    setShowPopup(false);
    setSelectedDeal(null);
  };
  if (!deals || deals.length === 0) {
    return /* @__PURE__ */ jsxs("div", { className: "text-center py-12 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl", children: [
      /* @__PURE__ */ jsx("p", { className: "text-gray-600 dark:text-gray-300", children: "No active coupons available at the moment." }),
      /* @__PURE__ */ jsx("p", { className: "text-sm text-gray-500 dark:text-gray-400 mt-2", children: "Check back soon for new deals!" })
    ] });
  }
  return /* @__PURE__ */ jsxs("div", { className: `brand-deals-wrapper ${className}`, children: [
    /* @__PURE__ */ jsx("div", { className: "space-y-6", style: { maxWidth: "730px" }, children: deals.map((deal, index) => /* @__PURE__ */ jsx(
      BrandDealListCard,
      {
        deal,
        priority: index < 3,
        onShowCouponModal: handleShowCouponModal,
        className: "w-full"
      },
      deal.id
    )) }),
    showPopup && selectedDeal && /* @__PURE__ */ jsx(
      DealCouponModal,
      {
        deal: selectedDeal,
        onClose: handleClosePopup
      }
    )
  ] });
};

const BrandAbout = ({ brandName, className = "" }) => {
  const getBrandInfo = (brand) => {
    if (brand === "SMOK") {
      return {
        founded: "2010",
        headquarters: "Shenzhen, China",
        description: "SMOK is one of the most recognizable names in the vaping industry, known for innovative designs and cutting-edge technology. Founded in 2010, SMOK has consistently pushed the boundaries of vaping hardware with their advanced mods, tanks, and starter kits that cater to both beginners and experienced vapers.",
        specialties: [
          "Advanced vape mods and starter kits",
          "Sub-ohm tanks with innovative coil technology",
          "Pod systems and all-in-one devices",
          "High-performance coils and accessories",
          "RGB lighting and customizable displays"
        ],
        trustSignals: [
          "Over 13 years in the vaping industry",
          "Sold in 50+ countries worldwide",
          "CE, FCC, and RoHS certified products",
          "Extensive product warranty coverage",
          "Continuous innovation and R&D investment"
        ]
      };
    }
    if (brand === "GeekVape") {
      return {
        founded: "2015",
        headquarters: "Shenzhen, China",
        description: "GeekVape is renowned for creating some of the most durable and innovative vaping devices in the industry. With a focus on rugged design and advanced technology, GeekVape has earned a reputation for producing reliable mods and tanks that can withstand extreme conditions while delivering exceptional performance.",
        specialties: [
          "Rugged and waterproof vape mods",
          "Advanced chipsets and temperature control",
          "High-performance RTA and RDA tanks",
          "Aegis series shock-resistant devices",
          "Professional-grade coil building supplies"
        ],
        trustSignals: [
          "IP67 rated waterproof devices",
          "Military-grade durability testing",
          "Award-winning product designs",
          "Global distribution network",
          "Strong community following"
        ]
      };
    }
    return {
      description: `${brand} is a trusted name in the vaping industry, known for quality products and innovative designs. The brand offers a comprehensive range of vaping devices and accessories that cater to vapers of all experience levels, from beginners to advanced users.`,
      specialties: [
        "High-quality vaping devices and accessories",
        "Innovative design and technology",
        "Wide range of products for all experience levels",
        "Reliable performance and durability",
        "Competitive pricing and value"
      ],
      trustSignals: [
        "Established vaping brand",
        "Quality assurance and testing",
        "Positive customer reviews",
        "Authorized retailer network",
        "Product warranty support"
      ]
    };
  };
  const brandInfo = getBrandInfo(brandName);
  return /* @__PURE__ */ jsxs("div", { id: "about", className: `mb-6 ${className}`, children: [
    /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-gray-900 dark:text-white mb-4", children: [
      "About ",
      brandName
    ] }),
    /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-4 font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors", children: [
          "Brand Overview",
          brandInfo.founded && /* @__PURE__ */ jsxs("span", { className: "ml-2 text-sm text-green-600 bg-green-100 dark:bg-green-900 px-2 py-1 rounded-full", children: [
            "Est. ",
            brandInfo.founded
          ] })
        ] }),
        /* @__PURE__ */ jsxs("div", { className: "p-4 pt-0 text-sm text-gray-600 dark:text-gray-300", children: [
          /* @__PURE__ */ jsx("p", { className: "mb-3", children: brandInfo.description }),
          brandInfo.headquarters && /* @__PURE__ */ jsxs("p", { className: "text-sm text-gray-500 dark:text-gray-400", children: [
            /* @__PURE__ */ jsx("strong", { children: "Headquarters:" }),
            " ",
            brandInfo.headquarters
          ] })
        ] })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsx("summary", { className: "cursor-pointer p-4 font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors", children: "Product Specialties" }),
        /* @__PURE__ */ jsx("div", { className: "p-4 pt-0 text-sm text-gray-600 dark:text-gray-300", children: /* @__PURE__ */ jsx("ul", { className: "space-y-2 list-disc list-inside", children: brandInfo.specialties.map((specialty, index) => /* @__PURE__ */ jsxs("li", { children: [
          /* @__PURE__ */ jsxs("strong", { children: [
            specialty.split(":")[0],
            ":"
          ] }),
          " ",
          specialty.split(":")[1] || specialty
        ] }, index)) }) })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-4 font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors", children: [
          "Why Choose ",
          brandName
        ] }),
        /* @__PURE__ */ jsx("div", { className: "p-4 pt-0 text-sm text-gray-600 dark:text-gray-300", children: /* @__PURE__ */ jsx("div", { className: "space-y-3", children: brandInfo.trustSignals.map((signal, index) => /* @__PURE__ */ jsx("div", { children: /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
          "✓ ",
          signal
        ] }) }, index)) }) })
      ] }),
      /* @__PURE__ */ jsxs("details", { className: "border border-gray-200 dark:border-gray-600 rounded-lg", children: [
        /* @__PURE__ */ jsxs("summary", { className: "cursor-pointer p-4 font-semibold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors", children: [
          "Shopping Tips for ",
          brandName
        ] }),
        /* @__PURE__ */ jsx("div", { className: "p-4 pt-0 text-sm text-gray-600 dark:text-gray-300", children: /* @__PURE__ */ jsxs("div", { className: "space-y-3", children: [
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Best Places to Buy" }),
            /* @__PURE__ */ jsx("p", { children: "Purchase from authorized retailers and verified online stores to ensure authenticity and warranty coverage. Check our deals above for the best current offers." })
          ] }),
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "How to Save Money" }),
            /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
              /* @__PURE__ */ jsx("li", { children: "Use coupon codes from our verified list above" }),
              /* @__PURE__ */ jsx("li", { children: "Buy in bulk during sales events for better per-unit pricing" }),
              /* @__PURE__ */ jsx("li", { children: "Sign up for retailer newsletters to get exclusive discounts" }),
              /* @__PURE__ */ jsxs("li", { children: [
                "Follow ",
                brandName,
                " on social media for flash sales"
              ] }),
              /* @__PURE__ */ jsx("li", { children: "Compare prices across multiple authorized retailers" })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { children: [
            /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "What to Look For" }),
            /* @__PURE__ */ jsx("p", { children: "Always verify authenticity codes, check expiration dates, and ensure proper packaging. Avoid deals that seem too good to be true from unverified sellers." })
          ] })
        ] }) })
      ] })
    ] })
  ] });
};

const $$Astro = createAstro("http://localhost:4321");
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/coupons/brands", 302);
  }
  const supabase = createServerSupabaseClient();
  const url = new URL(Astro2.request.url);
  const sort = url.searchParams.get("sort") || "newest";
  parseInt(url.searchParams.get("page") || "1");
  parseInt(url.searchParams.get("limit") || "24");
  const { data: brand, error: brandError } = await supabase.from("brands").select("*").eq("slug", slug).single();
  if (brandError || !brand) {
    console.error("Brand not found:", brandError);
    return Astro2.redirect("/coupons/brands", 302);
  }
  let dealsQuery = supabase.from("deals").select(`
    *,
    merchants:merchant_id (name, website_url, logo_url),
    brands:brand_id (name, logo_url, slug),
    categories:category_id (name, slug)
  `).eq("brand_id", brand.id).gt("deal_end_date", (/* @__PURE__ */ new Date()).toISOString());
  switch (sort) {
    case "newest":
      dealsQuery = dealsQuery.order("created_at", { ascending: false });
      break;
    case "expiring_soon":
      dealsQuery = dealsQuery.order("deal_end_date", { ascending: true });
      break;
    case "discount_desc":
      dealsQuery = dealsQuery.order("discount_percentage", { ascending: false, nullsLast: true });
      break;
    case "most_popular":
      dealsQuery = dealsQuery.order("click_count", { ascending: false, nullsLast: true });
      break;
    default:
      dealsQuery = dealsQuery.order("created_at", { ascending: false });
  }
  const { data: deals, error: dealsError } = await dealsQuery;
  const { data: brandStats } = await supabase.from("deals").select("discount, price, discount_percentage").eq("brand_id", brand.id).gt("deal_end_date", (/* @__PURE__ */ new Date()).toISOString());
  const couponStats = calculateCouponStats(deals || [], brand?.name || "");
  generateRecentVerifications(brand?.name || "", deals, 3);
  const generateBrandRating = (brandName) => {
    if (!brandName) return { stars: 5, count: 2 };
    const hash = brandName.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    const rating = 4 + Math.abs(hash) % 10 / 10;
    const reviewCount = 5 + Math.abs(hash) % 45;
    return {
      stars: Math.round(rating * 2) / 2,
      count: reviewCount
    };
  };
  const brandRating = generateBrandRating(brand?.name);
  const seoMeta = brand ? generateBrandSEOMeta(brand.name, couponStats) : null;
  const socialProof = brand ? generateSocialProofText(brand.name, "brand", couponStats) : null;
  const title = seoMeta?.title || `${brand.name} Coupon Codes & Promo Codes - Save Up to ${couponStats.bestDiscount} | VapeHybrid`;
  const description = seoMeta?.description || `Get the latest ${brand.name} coupon codes and promo codes. Save on ${brand.name} vape products with ${couponStats.activeCoupons} verified discount codes. Updated daily with real-time verification.`;
  const structuredData = brand ? generateBrandPageSchema(
    brand,
    deals || [],
    Astro2.url.origin,
    couponStats
  ) : void 0;
  const metaTags = seoMeta ? generateMetaTagsObject(seoMeta, Astro2.url.href, brand?.logo_url) : {};
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData, "openGraph": {
    basic: {
      title: metaTags["og:title"] || title,
      type: "website",
      image: metaTags["og:image"] || brand?.logo_url,
      url: metaTags["og:url"] || Astro2.url.href
    },
    optional: {
      description: metaTags["og:description"] || description,
      siteName: "VapeHybrid"
    },
    image: {
      alt: metaTags["og:image:alt"] || `${brand?.name} Coupons`,
      width: 1200,
      height: 630
    }
  }, "twitter": {
    card: "summary_large_image",
    site: "@vapehybrid",
    creator: "@vapehybrid",
    title: metaTags["twitter:title"] || title,
    description: metaTags["twitter:description"] || description,
    image: metaTags["twitter:image"] || brand?.logo_url
  }, "data-astro-cid-mzab7qh6": true }, { "default": async ($$result2) => renderTemplate`    ${maybeRenderHead()}<div class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 py-2 text-center" style="font-size: 11px;" data-astro-cid-mzab7qh6>
VapeHybrid may earn commission on purchases. <a href="/terms" class="underline hover:text-gray-900 dark:hover:text-white transition-colors" data-astro-cid-mzab7qh6>Learn more</a> </div>  <div class="py-3 border-b border-design-border" data-astro-cid-mzab7qh6> <div class="max-w-[1030px] mx-auto px-4" data-astro-cid-mzab7qh6> <nav class="text-left" aria-label="Breadcrumb" data-astro-cid-mzab7qh6> <ol class="flex items-center space-x-1 text-sm text-design-muted-foreground" data-astro-cid-mzab7qh6> <li data-astro-cid-mzab7qh6> <a href="/" class="hover:text-design-foreground transition-colors" data-astro-cid-mzab7qh6>Home</a> </li> <li data-astro-cid-mzab7qh6> <span class="mx-1" data-astro-cid-mzab7qh6>/</span> </li> <li data-astro-cid-mzab7qh6> <a href="/coupons" class="hover:text-design-foreground transition-colors" data-astro-cid-mzab7qh6>Coupons</a> </li> <li data-astro-cid-mzab7qh6> <span class="mx-1" data-astro-cid-mzab7qh6>/</span> </li> <li data-astro-cid-mzab7qh6> <a href="/coupons/brands" class="hover:text-design-foreground transition-colors" data-astro-cid-mzab7qh6>Brands</a> </li> <li data-astro-cid-mzab7qh6> <span class="mx-1" data-astro-cid-mzab7qh6>/</span> </li> <li data-astro-cid-mzab7qh6> <span class="text-design-foreground font-medium" data-astro-cid-mzab7qh6>${brand.name}</span> </li> </ol> </nav> </div> </div>  <div class="brand-page max-w-[1030px] mx-auto px-4 py-6" data-astro-cid-mzab7qh6> <!-- Two Column Layout: 1/3 left, 2/3 right --> <div class="grid grid-cols-1 lg:grid-cols-3 gap-8" data-astro-cid-mzab7qh6> <!-- Left Column (1/3) - Brand Info --> <div class="lg:col-span-1 bg-gray-100 dark:bg-gray-700 p-4 rounded-lg" data-astro-cid-mzab7qh6> <!-- Brand Logo --> <div class="bg-white dark:bg-gray-600 rounded-lg p-4 mb-4 border border-gray-200 dark:border-gray-500" data-astro-cid-mzab7qh6> ${brand.logo_url ? renderTemplate`<img${addAttribute(brand.logo_url, "src")}${addAttribute(brand.name, "alt")} class="w-full h-32 object-contain bg-gray-50 dark:bg-gray-500 rounded" data-astro-cid-mzab7qh6>` : renderTemplate`<div class="w-full h-32 bg-gray-50 dark:bg-gray-500 rounded flex items-center justify-center" data-astro-cid-mzab7qh6> <span class="text-2xl font-bold text-gray-400 dark:text-gray-300" data-astro-cid-mzab7qh6>${brand.name.charAt(0)}</span> </div>`} </div> <!-- Brand Name --> <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3" data-astro-cid-mzab7qh6>${brand.name}</h2> <!-- Star Ratings --> <div class="flex items-center gap-1 mb-2" data-astro-cid-mzab7qh6> <div class="flex text-yellow-400" data-astro-cid-mzab7qh6> <span data-astro-cid-mzab7qh6>${"\u2605".repeat(Math.floor(brandRating.stars))}${"\u2606".repeat(5 - Math.floor(brandRating.stars))}</span> </div> <span class="text-sm text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>(${brandRating.count} ratings)</span> </div> <!-- Verification Text --> <p class="text-sm text-gray-600 dark:text-gray-300 mb-4" data-astro-cid-mzab7qh6>
We have ${couponStats.activeCoupons} verified coupon codes for ${brand.name} today.
</p> <!-- Compact Statistics with Social Proof --> <div class="bg-white dark:bg-gray-700 rounded-lg p-3 mb-4 border border-gray-200 dark:border-gray-600" data-astro-cid-mzab7qh6> <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2" data-astro-cid-mzab7qh6>Quick Stats</h3> <div class="space-y-1 text-xs" data-astro-cid-mzab7qh6> <div class="flex justify-between" data-astro-cid-mzab7qh6> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>Active Coupons:</span> <span class="font-medium text-gray-900 dark:text-white" data-astro-cid-mzab7qh6>${couponStats.activeCoupons}</span> </div> <div class="flex justify-between" data-astro-cid-mzab7qh6> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>Best Discount:</span> <span class="font-medium text-green-600" data-astro-cid-mzab7qh6>${couponStats.bestDiscount}</span> </div> <div class="flex justify-between" data-astro-cid-mzab7qh6> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>Avg Savings:</span> <span class="font-medium text-gray-900 dark:text-white" data-astro-cid-mzab7qh6>${couponStats.avgSavings}</span> </div> <div class="flex justify-between" data-astro-cid-mzab7qh6> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>Success Rate:</span> <span class="font-medium text-blue-600" data-astro-cid-mzab7qh6>${couponStats.successRate}</span> </div>  ${socialProof && renderTemplate`${renderComponent($$result2, "Fragment", Fragment$1, { "data-astro-cid-mzab7qh6": true }, { "default": async ($$result3) => renderTemplate` <hr class="my-2 border-gray-200 dark:border-gray-600" data-astro-cid-mzab7qh6> <div class="flex justify-between" data-astro-cid-mzab7qh6> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>Last ✓ Verified :</span> <span class="text-green-600 font-medium" data-astro-cid-mzab7qh6>${socialProof.lastVerified.replace("Last verified ", "")}</span> </div> <div class="flex justify-between" data-astro-cid-mzab7qh6> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>Last user 💰 Recent Saved:</span> <span class="text-blue-600 font-medium" data-astro-cid-mzab7qh6>${socialProof.lastUserSaved.replace("Last user saved ", "")}</span> </div> <div class="text-xs text-gray-500 dark:text-gray-400 pt-1" data-astro-cid-mzab7qh6> ${socialProof.communityTracking} </div> ` })}`} </div> </div> <!-- Email Capture --> ${renderComponent($$result2, "UniversalEmailCaptureForm", UniversalEmailCaptureForm, { "client:load": true, "targetName": brand.name, "subscriptionType": "brand", "className": "mb-4", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/shared/UniversalEmailCaptureForm", "client:component-export": "default", "data-astro-cid-mzab7qh6": true })} </div> <!-- Right Column (2/3) - Main Content --> <div class="lg:col-span-2" style="min-width: 730px;" data-astro-cid-mzab7qh6> <!-- Header with Best Discount Badge --> <div class="flex items-start justify-between mb-4" data-astro-cid-mzab7qh6> <!-- Main Title --> <h1 class="text-3xl font-bold text-gray-900 dark:text-white flex-1" data-astro-cid-mzab7qh6> ${brand.name} Coupon Codes & Promo Codes
</h1> <!-- Best Discount Badge --> <div class="ml-4 bg-gradient-to-r from-orange-400 to-red-500 text-white rounded-lg p-3 text-center shadow-lg" data-astro-cid-mzab7qh6> <div class="text-2xl font-bold" data-astro-cid-mzab7qh6>${couponStats.bestDiscount}</div> <div class="text-sm font-medium" data-astro-cid-mzab7qh6>OFF</div> </div> </div> <!-- Verification Text with Expandable Content --> ${renderComponent($$result2, "ExpandableBrandInfo", ExpandableBrandInfo, { "client:load": true, "brandName": brand.name, "activeCoupons": couponStats.activeCoupons, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/ExpandableBrandInfo", "client:component-export": "default", "data-astro-cid-mzab7qh6": true })} <!-- Navigation Tabs --> <div class="flex gap-6 mb-6 border-b border-gray-200 dark:border-gray-600" data-astro-cid-mzab7qh6> <a href="#top-codes" class="pb-2 border-b-2 border-green-500 text-green-600 font-medium hover:text-green-700" data-astro-cid-mzab7qh6>All Coupons</a> <a href="#activity" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" data-astro-cid-mzab7qh6>Activity</a> <a href="#about" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" data-astro-cid-mzab7qh6>About</a> <a href="#faq" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" data-astro-cid-mzab7qh6>FAQ</a> </div> <!-- Today's Promo Codes Section --> <div id="top-codes" class="mb-6" data-astro-cid-mzab7qh6> <div class="flex items-center justify-between mb-4" data-astro-cid-mzab7qh6> <div data-astro-cid-mzab7qh6> <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2" data-astro-cid-mzab7qh6>
Today's ${brand.name} promo codes
</h2> <p class="text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>
Fresh codes tested by real vapers - ${sort === "discount_high" ? "biggest savings first" : sort === "newest" ? "newest first" : sort === "expiring" ? "expiring soon first" : "sorted by popularity"}! 🔥
</p> </div> <!-- Sort Controls --> <div class="flex items-center gap-3" data-astro-cid-mzab7qh6> <label for="sort-select" class="text-sm text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>Sort by:</label> <select id="sort-select" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white" onchange="window.location.href = new URL(window.location.href).pathname + '?sort=' + this.value" data-astro-cid-mzab7qh6> <option value="discount_high"${addAttribute(sort === "discount_high" ? "selected" : "", "sort === 'discount_high' ? 'selected' : ''")} data-astro-cid-mzab7qh6>Biggest Savings</option> <option value="newest"${addAttribute(sort === "newest" ? "selected" : "", "sort === 'newest' ? 'selected' : ''")} data-astro-cid-mzab7qh6>Newest First</option> <option value="expiring"${addAttribute(sort === "expiring" ? "selected" : "", "sort === 'expiring' ? 'selected' : ''")} data-astro-cid-mzab7qh6>Expiring Soon</option> <option value="popular"${addAttribute(sort === "popular" ? "selected" : "", "sort === 'popular' ? 'selected' : ''")} data-astro-cid-mzab7qh6>Most Popular</option> </select> </div> </div> <!-- Controls Section --> <div class="mb-6 flex flex-col md:flex-row justify-between items-start md:items-center gap-4" data-astro-cid-mzab7qh6> <div class="flex items-center gap-4" data-astro-cid-mzab7qh6> <span class="text-lg font-semibold text-design-foreground" data-astro-cid-mzab7qh6> ${deals?.length || 0} ${brand.name} Coupons Available
</span> </div> </div> <!-- Error Messages --> ${brandError && renderTemplate`<div class="bg-design-destructive/10 text-design-destructive p-4 rounded-md mb-6" data-astro-cid-mzab7qh6> <p data-astro-cid-mzab7qh6>There was an error loading the brand information. Please try again later.</p> </div>`} ${dealsError && renderTemplate`<div class="bg-design-destructive/10 text-design-destructive p-4 rounded-md mb-6" data-astro-cid-mzab7qh6> <p data-astro-cid-mzab7qh6>There was an error loading coupons. Please try again later.</p> </div>`} <!-- Coupon Cards - Brand-Specific List Format --> <div class="space-y-6" data-astro-cid-mzab7qh6> ${deals && deals.length > 0 ? renderTemplate`${renderComponent($$result2, "BrandDealsWrapper", BrandDealsWrapper, { "deals": deals, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandDealsWrapper", "client:component-export": "default", "data-astro-cid-mzab7qh6": true })}` : renderTemplate`<div class="text-center py-12 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl" data-astro-cid-mzab7qh6> <p class="text-gray-600 dark:text-gray-300" data-astro-cid-mzab7qh6>No active coupons available for ${brand.name} at the moment.</p> <p class="text-sm text-gray-500 dark:text-gray-400 mt-2" data-astro-cid-mzab7qh6>Check back soon for new deals!</p> </div>`} </div> </div> <!-- Enhanced Activity Section --> ${renderComponent($$result2, "BrandActivity", BrandActivity, { "client:load": true, "brandName": brand.name, "activeCoupons": couponStats.activeCoupons, "avgDiscount": couponStats.bestDiscount ? parseFloat(couponStats.bestDiscount.replace("%", "")) : 15, "successRate": couponStats.successRate, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandActivity", "client:component-export": "default", "data-astro-cid-mzab7qh6": true })} <!-- About Brand Section --> <div class="mb-6" data-astro-cid-mzab7qh6> ${renderComponent($$result2, "BrandAbout", BrandAbout, { "client:load": true, "brandName": brand.name, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandAbout", "client:component-export": "default", "data-astro-cid-mzab7qh6": true })} </div> <!-- FAQ Section --> <div id="faq" class="mb-6" data-astro-cid-mzab7qh6> ${renderComponent($$result2, "BrandFAQ", BrandFAQ, { "client:load": true, "brandName": brand.name, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandFAQ", "client:component-export": "default", "data-astro-cid-mzab7qh6": true })} </div> <!-- Enhanced Related Sections --> ${renderComponent($$result2, "BrandRelatedSections", BrandRelatedSections, { "client:load": true, "brandName": brand.name, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandRelatedSections", "client:component-export": "default", "data-astro-cid-mzab7qh6": true })} </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/brands/[slug].astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/brands/[slug].astro";
const $$url = "/coupons/brands/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
