import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../assets/js/MainLayout-BVJnMCp3.js';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Error = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Error;
  const errorMessage = Astro2.url.searchParams.get("message") || "An unknown error occurred while processing your request.";
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": "Subscription Error | VapeHybrid" }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-4xl mx-auto px-4 py-20 text-center"> <div class="bg-design-card/80 backdrop-blur-sm rounded-xl p-10 border border-design-border shadow-lg"> <div class="w-20 h-20 bg-design-destructive/20 rounded-full flex items-center justify-center mx-auto mb-6"> <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-design-destructive" viewBox="0 0 20 20" fill="currentColor"> <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"></path> </svg> </div> <h1 class="text-3xl font-bold text-design-foreground mb-4">Subscription Error</h1> <p class="text-design-muted-foreground mb-8 max-w-lg mx-auto"> ${errorMessage} </p> <div class="flex flex-col sm:flex-row gap-4 justify-center"> <a href="/" class="inline-block px-6 py-3 bg-design-primary text-white dark:text-black text-sm font-medium rounded-lg hover:bg-design-primary/90 transition-all duration-300 shadow-md">
Return to Homepage
</a> <a href="/#newsletter" class="inline-block px-6 py-3 bg-design-secondary text-design-foreground dark:text-black text-sm font-medium rounded-lg hover:bg-design-secondary/90 transition-all duration-300 shadow-md">
Try Again
</a> </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/error.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/error.astro";
const $$url = "/newsletter/error";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Error,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
