#!/usr/bin/env node

/**
 * Fix Routes Script for Cloudflare Workers
 * 
 * This script ensures that the _routes.json file properly excludes all static assets
 * from being processed by the worker, preventing 500 errors and MIME type issues.
 * 
 * Usage: node scripts/fix-routes.js
 * 
 * This script should be run after the Astro build process to ensure proper
 * static asset routing for Cloudflare Workers deployment.
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// Path to the _routes.json file
const routesPath = path.join(__dirname, '..', 'dist', '_routes.json');

// Critical static asset patterns that MUST be excluded from worker processing
const CRITICAL_EXCLUDES = [
  // JavaScript files (CRITICAL - causes 500 errors if missing)
  '/assets/js/*',
  '/assets/js/**/*',
  
  // CSS files (already working but ensure consistency)
  '/_astro/*',
  '/_astro/**/*',
  
  // All static asset directories
  '/assets/*',
  '/assets/**/*',
  
  // Image directories
  '/images/*',
  '/images/**/*',
  '/icons/*',
  '/icons/**/*',
  '/products/*',
  '/products/**/*',
  '/staff/*',
  '/staff/**/*',
  '/patterns/*',
  '/patterns/**/*',
  '/how/*',
  '/how/**/*',
  '/SVG/*',
  '/SVG/**/*',
  
  // Static files
  '/js/*',
  '/scripts/*',
  
  // Common static file extensions
  '/*.js',
  '/*.css',
  '/*.svg',
  '/*.png',
  '/*.jpg',
  '/*.jpeg',
  '/*.webp',
  '/*.avif',
  '/*.gif',
  '/*.ico',
  '/*.woff',
  '/*.woff2',
  '/*.ttf',
  '/*.eot',
  '/*.json',
  '/*.xml',
  '/*.txt',
  '/*.pdf'
];

function fixRoutesJson() {
  console.log('🔧 Fixing _routes.json for Cloudflare Workers...');
  
  // Check if _routes.json exists
  if (!fs.existsSync(routesPath)) {
    console.error('❌ _routes.json not found at:', routesPath);
    console.log('Creating new _routes.json file...');
    
    const newRoutes = {
      version: 1,
      include: ['/*'],
      exclude: CRITICAL_EXCLUDES
    };
    
    fs.writeFileSync(routesPath, JSON.stringify(newRoutes, null, 2));
    console.log('✅ Created new _routes.json with proper exclusions');
    return;
  }
  
  // Read existing _routes.json
  let routes;
  try {
    const routesContent = fs.readFileSync(routesPath, 'utf8');
    routes = JSON.parse(routesContent);
  } catch (error) {
    console.error('❌ Error reading _routes.json:', error.message);
    return;
  }
  
  console.log('📋 Current _routes.json structure:');
  console.log(`   - Include patterns: ${routes.include?.length || 0}`);
  console.log(`   - Exclude patterns: ${routes.exclude?.length || 0}`);
  
  // Ensure exclude array exists
  if (!routes.exclude) {
    routes.exclude = [];
  }
  
  // Track what we're adding
  const addedPatterns = [];
  
  // Add critical excludes that are missing
  for (const pattern of CRITICAL_EXCLUDES) {
    if (!routes.exclude.includes(pattern)) {
      routes.exclude.push(pattern);
      addedPatterns.push(pattern);
    }
  }
  
  // Sort excludes for better organization
  routes.exclude.sort();
  
  // Write updated _routes.json
  try {
    fs.writeFileSync(routesPath, JSON.stringify(routes, null, 2));
    
    if (addedPatterns.length > 0) {
      console.log('✅ Added missing exclusion patterns:');
      addedPatterns.forEach(pattern => console.log(`   + ${pattern}`));
    } else {
      console.log('✅ All critical patterns already present');
    }
    
    console.log(`📊 Final _routes.json: ${routes.exclude.length} exclusion patterns`);
    console.log('🎯 Static assets will now be served correctly!');
    
  } catch (error) {
    console.error('❌ Error writing _routes.json:', error.message);
  }
}

// Run the fix
fixRoutesJson();
