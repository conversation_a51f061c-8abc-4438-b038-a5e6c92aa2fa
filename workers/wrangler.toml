# Cloudflare Worker configuration for Supabase Cron Jobs
name = "vapehybrid-supabase-cron"
main = "supabase-cron.js"
compatibility_date = "2023-10-30"
compatibility_flags = ["nodejs_compat"]

# <PERSON>ron triggers - executes daily at 4:00 PM UTC
[triggers]
crons = ["0 16 * * *"]

# Environment variables (optional - can also use secrets)
[vars]
ENVIRONMENT = "production"

# KV namespace for logging (optional)
# [[kv_namespaces]]
# binding = "LOGS"
# id = "your-kv-namespace-id"

# Durable Objects (if needed for state management)
# [[durable_objects.bindings]]
# name = "SYNC_STATE"
# class_name = "SyncState"
