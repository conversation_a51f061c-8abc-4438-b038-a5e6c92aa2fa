import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../assets/js/MainLayout-BVJnMCp3.js';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Blog = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Blog;
  const title = "Blog | VapeHybrid";
  const description = "Read the latest articles, guides, and news about vaping products, deals, and industry trends.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Blog",
    "name": title,
    "description": description,
    "url": Astro2.url.href,
    "publisher": {
      "@type": "Organization",
      "name": "VapeHybrid",
      "logo": {
        "@type": "ImageObject",
        "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`
      }
    }
  };
  const blogPosts = [
    {
      title: "Top 10 Vape Deals This Month",
      excerpt: "Discover the best vape deals and discounts available this month, with savings of up to 50% on popular products.",
      date: "2024-07-15",
      author: "VapeHybrid Team",
      image: "/images/placeholder-blog-1.jpg",
      slug: "top-10-vape-deals-this-month"
    },
    {
      title: "Beginner's Guide to Vaping",
      excerpt: "Everything you need to know about getting started with vaping, from choosing your first device to understanding e-liquids.",
      date: "2024-07-10",
      author: "VapeHybrid Team",
      image: "/images/placeholder-blog-2.jpg",
      slug: "beginners-guide-to-vaping"
    },
    {
      title: "How to Find the Best Vape Coupons",
      excerpt: "Learn how to find and use vape coupons effectively to maximize your savings on every purchase.",
      date: "2024-07-05",
      author: "VapeHybrid Team",
      image: "/images/placeholder-blog-3.jpg",
      slug: "how-to-find-the-best-vape-coupons"
    }
  ];
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="container mx-auto px-4 py-12"> <div class="max-w-4xl mx-auto"> <h1 class="text-3xl md:text-4xl font-bold text-design-foreground mb-6">VapeHybrid Blog</h1> <p class="text-design-muted-foreground mb-12">Stay updated with the latest articles, guides, and news about vaping products, deals, and industry trends.</p> <div class="bg-design-card/80 backdrop-blur-sm rounded-xl p-8 border border-design-border shadow-sm mb-12"> <h2 class="text-xl font-semibold text-design-foreground mb-4">Coming Soon!</h2> <p class="text-design-muted-foreground">
We're currently working on our blog section to bring you valuable content about vaping products, deals, and industry trends. 
          Check back soon for articles, guides, and news!
</p> </div> <div class="space-y-8"> ${blogPosts.map((post) => renderTemplate`<div class="bg-design-card/80 backdrop-blur-sm rounded-xl overflow-hidden border border-design-border shadow-sm"> <div class="grid grid-cols-1 md:grid-cols-3 gap-6"> <div class="md:col-span-1 bg-design-muted/20 flex items-center justify-center p-6"> <div class="w-full h-48 bg-design-primary/10 rounded-lg flex items-center justify-center"> <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 text-design-primary/30" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path> </svg> </div> </div> <div class="md:col-span-2 p-6"> <div class="flex items-center text-design-muted-foreground text-sm mb-2"> <span>${post.date}</span> <span class="mx-2">•</span> <span>${post.author}</span> </div> <h3 class="text-xl font-semibold text-design-foreground mb-2">${post.title}</h3> <p class="text-design-muted-foreground mb-4">${post.excerpt}</p> <div class="flex items-center"> <span class="text-design-primary text-sm">Coming soon</span> </div> </div> </div> </div>`)} </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/blog.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/blog.astro";
const $$url = "/blog";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Blog,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
