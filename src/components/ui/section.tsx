import React from 'react';
import { motion } from 'framer-motion';

export interface SectionProps {
  id?: string;
  class?: string;
  children: React.ReactNode;
  background?: 'default' | 'primary' | 'secondary' | 'accent' | 'muted';
  pattern?: 'none' | 'dots' | 'grid' | 'circles';
  innerClass?: string;
}

export const Section: React.FC<SectionProps> = ({
  id,
  class: className = '',
  children,
  background = 'default',
  pattern = 'none',
  innerClass: innerClassName = '',
}) => {
  // Background styles based on the background prop - Monochromatic scheme
  const backgroundStyles = {
    default: 'bg-design-background',
    primary: 'bg-design-primary text-white dark:text-black',
    secondary: 'bg-design-primary text-white dark:bg-design-primary dark:text-black',
    accent: 'bg-design-primary text-white dark:bg-design-primary dark:text-black',
    muted: 'bg-design-muted',
  };

  // Pattern styles based on the pattern prop
  const patternStyles = {
    none: '',
    dots: "bg-[url('/patterns/dot-pattern-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/dot-pattern-dark.svg')] dark:opacity-5",
    grid: "bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-5",
    circles: "bg-[url('/patterns/circle-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/circle-dark.svg')] dark:opacity-5",
  };

  // Check if className contains bg-transparent to avoid applying background styles
  const hasTransparentBg = className.includes('bg-transparent');

  // Combine classes based on conditions
  const sectionClasses = [
    'home-section',
    !hasTransparentBg && backgroundStyles[background],
    className
  ].filter(Boolean).join(' ');

  return (
    <section
      id={id}
      className={sectionClasses}
    >
      {/* Background pattern */}
      {pattern !== 'none' && (
        <div className={`absolute inset-0 ${patternStyles[pattern]} z-[-1]`}></div>
      )}

      {/* Decorative elements - Monochromatic */}
      <div className="absolute top-20 left-10 w-32 h-32 bg-design-primary bg-opacity-5 dark:bg-design-primary dark:bg-opacity-5 rounded-full blur-3xl"></div>
      <div className="absolute bottom-20 right-10 w-40 h-40 bg-design-primary bg-opacity-5 dark:bg-design-primary dark:bg-opacity-5 rounded-full blur-3xl"></div>

      <div className={`home-section-inner ${innerClassName}`}>
        {children}
      </div>
    </section>
  );
};

export interface SectionHeaderProps {
  title: string;
  subtitle?: string;
  badge?: string;
  badgeIcon?: React.ReactNode;
  className?: string;
  titleClassName?: string;
  subtitleClassName?: string;
  showDivider?: boolean;
  align?: 'center' | 'left' | 'right';
}

export const SectionHeader: React.FC<SectionHeaderProps> = ({
  title,
  subtitle,
  badge,
  badgeIcon,
  className = '',
  titleClassName = '',
  subtitleClassName = '',
  showDivider = false,
  align = 'center',
}) => {
  const alignmentClasses = {
    center: 'text-center',
    left: 'text-left',
    right: 'text-right',
  };

  const dividerAlignmentClasses = {
    center: 'mx-auto',
    left: 'ml-0',
    right: 'mr-0 ml-auto',
  };

  return (
    <div className={`home-section-header ${alignmentClasses[align]} ${className}`}>
      {badge && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          viewport={{ once: true }}
          className="home-section-badge"
        >
          {badgeIcon && <span className="mr-2">{badgeIcon}</span>}
          <span className="text-design-primary font-medium text-sm">{badge}</span>
        </motion.div>
      )}

      {showDivider && (
        <div className={`home-section-divider ${dividerAlignmentClasses[align]}`}></div>
      )}

      <motion.h2
        initial={{ opacity: 0, y: 20 }}
        whileInView={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        viewport={{ once: true }}
        className={`home-h2 ${titleClassName}`}
      >
        {title}
      </motion.h2>

      {subtitle && (
        <motion.p
          initial={{ opacity: 0, y: 20 }}
          whileInView={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
          viewport={{ once: true }}
          className={`home-subtitle ${subtitleClassName}`}
        >
          {subtitle}
        </motion.p>
      )}
    </div>
  );
};

export default { Section, SectionHeader };
