{"name": "vape-hybrid-coupons", "type": "module", "version": "5.6.0", "description": "VapeHybrid Coupon Site - Browse, search, and use coupon codes for vape products", "author": "VapeHybrid Team", "engines": {"node": ">=20.0.0"}, "scripts": {"dev": "astro dev", "dev:host": "astro dev --host", "build:original": "astro build", "build": "npm run convert-all-images && npm cache clean --force && npm install --no-package-lock --legacy-peer-deps && astro build --verbose", "build:clean": "npm run clean && npm run convert-all-images && npm install --no-package-lock && astro build --verbose", "build:with-css": "npm run generate-css && astro build", "preview": "astro preview", "preview:local": "astro preview", "preview:clean": "npm run clean && npm run build && astro preview", "astro": "astro", "lint": "eslint . --ext .js,.jsx,.ts,.tsx,.astro", "format": "prettier --write .", "clean": "if exist dist rmdir /s /q dist && if exist .astro rmdir /s /q .astro && if exist .vite rmdir /s /q .vite", "build-tokens": "node scripts/build-tokens.js", "generate-css": "npm run build-tokens && node scripts/generate-css-variables.js", "install-design-system": "node scripts/install-design-system.js", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:jest": "jest --config=jest.config.cjs", "test:components": "jest --config=jest.config.cjs src/components/DesignSystem/__tests__", "test:styles": "jest --config=jest.config.cjs src/styles/__tests__", "dev:clean": "rm -rf .astro .vite && astro dev", "reset": "npm run clean && npm install && npm run dev", "validate-contrast": "npm run build-tokens && node scripts/validate-contrast.js", "test-css": "node scripts/test-css-system.js", "process-images": "node scripts/process-images.js", "convert-all-images": "node scripts/convert-all-images.js", "convert-icons": "node scripts/convert-icons.js", "convert-logos": "node scripts/convert-logos.js", "convert-testimonials": "node scripts/convert-testimonials.js", "convert-all": "npm run convert-all-images && npm run convert-icons && npm run convert-logos && npm run convert-testimonials", "build:with-images": "npm run process-images && npm run build", "build:optimized": "npm run convert-all && node scripts/optimize-build.js", "sync:supabase": "node scripts/supabase-sync-scheduler.js", "sync:test": "node scripts/supabase-sync-scheduler.js --test", "test:supabase": "node scripts/test-supabase-functions.js"}, "dependencies": {"@astrojs/check": "^0.9.4", "@astrojs/node": "^9.2.2", "@astrojs/react": "^3.0.10", "@astrojs/tailwind": "^5.1.0", "@astrojs/vercel": "^8.1.5", "@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.8", "@radix-ui/react-avatar": "^1.1.6", "@radix-ui/react-checkbox": "^1.2.2", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-dropdown-menu": "^2.1.11", "@radix-ui/react-icons": "^1.3.0", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-scroll-area": "^1.2.5", "@radix-ui/react-select": "^2.2.2", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.2.2", "@radix-ui/react-tabs": "^1.1.8", "@radix-ui/react-toast": "^1.2.11", "@radix-ui/react-tooltip": "^1.2.3", "@supabase/auth-ui-react": "^0.4.7", "@supabase/auth-ui-shared": "^0.1.8", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@types/jsonwebtoken": "^9.0.9", "@types/react-google-recaptcha": "^2.1.9", "astro": "^5.7.3", "astro-seo": "^0.8.4", "canvas-confetti": "^1.9.3", "chroma-js": "^3.1.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "compression": "^1.8.0", "dotenv": "^16.5.0", "framer-motion": "^12.8.0", "glob": "^10.4.5", "lucide-react": "^0.488.0", "node-fetch": "^3.3.2", "react": "^18.2.0", "react-dom": "^18.2.0", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.56.1", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "resend": "^4.5.0", "shadcn-ui": "^0.9.5", "sonner": "^2.0.3", "tailwind-merge": "^3.2.0", "uuid": "^11.1.0", "zod": "^3.24.3"}, "devDependencies": {"@babel/core": "^7.24.0", "@babel/preset-env": "^7.24.0", "@babel/preset-react": "^7.23.3", "@babel/preset-typescript": "^7.23.3", "@eslint/js": "^9.25.0", "@fullhuman/postcss-purgecss": "^5.0.0", "@types/canvas-confetti": "^1.9.0", "@types/react": "^18.2.55", "@types/react-dom": "^18.2.19", "@types/uuid": "^10.0.0", "@typescript-eslint/eslint-plugin": "^8.30.1", "@typescript-eslint/parser": "^8.30.1", "astro-compress": "^2.3.8", "autoprefixer": "^10.4.21", "bcryptjs": "^2.4.3", "critical": "^5.1.1", "eslint": "^9.25.0", "eslint-plugin-astro": "^1.3.1", "jsonwebtoken": "^9.0.2", "lightningcss": "^1.22.1", "postcss": "^8.5.3", "postcss-import": "^16.1.0", "purgecss": "^5.0.0", "sharp": "^0.33.5", "tailwindcss": "^3.4.3", "tailwindcss-animate": "^1.0.7", "terser": "^5.29.2", "typescript": "^5.8.3", "typescript-eslint": "^8.30.1"}}