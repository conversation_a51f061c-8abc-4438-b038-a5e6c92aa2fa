import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$LegalPageLayout } from '../assets/js/LegalPageLayout-DSHTXlhZ.js';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$CookiePolicy = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$CookiePolicy;
  const title = "Cookie Policy | VapeHybrid";
  const description = "Information about the cookies used on VapeHybrid and how to manage your preferences.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": title,
    "description": description,
    "publisher": {
      "@type": "Organization",
      "name": "VapeHybrid",
      "logo": {
        "@type": "ImageObject",
        "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`
      }
    }
  };
  (/* @__PURE__ */ new Date()).getFullYear();
  return renderTemplate`${renderComponent($$result, "LegalPageLayout", $$LegalPageLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<h1 class="text-3xl font-bold text-design-foreground mb-6">Cookie Policy</h1> <p class="text-design-muted-foreground mb-8">Last updated: ${(/* @__PURE__ */ new Date()).toLocaleDateString("en-US", { year: "numeric", month: "long", day: "numeric" })}</p> <div class="prose prose-lg max-w-none text-design-foreground prose-headings:text-design-foreground prose-strong:text-design-foreground prose-a:text-design-primary"> <h2>What Are Cookies?</h2> <p>Cookies are small text files that are stored on your computer or mobile device when you visit a website. They are widely used to make websites work more efficiently and provide information to the website owners.</p> <h2>How We Use Cookies</h2> <p>VapeHybrid uses cookies for various purposes, including:</p> <ul> <li>Ensuring the website functions properly</li> <li>Remembering your preferences and settings</li> <li>Analyzing how you use our website to improve your experience</li> <li>Tracking affiliate referrals for commission purposes</li> <li>Personalizing content and advertisements</li> </ul> <h2>Types of Cookies We Use</h2> <h3>1. Essential Cookies</h3> <p>These cookies are necessary for the website to function properly. They enable core functionality such as security, network management, and account access. You cannot opt out of these cookies as the website cannot function properly without them.</p> <p><strong>Examples:</strong></p> <ul> <li>Session cookies for managing user sessions</li> <li>Security cookies for detecting authentication abuses</li> <li>Load balancing cookies</li> </ul> <h3>2. Analytics Cookies</h3> <p>These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously. This helps us improve our website and your experience.</p> <p><strong>Examples:</strong></p> <ul> <li>Cloudflare analytics cookies</li> <li>First-party analytics cookies to track user behavior on our site</li> </ul> <h3>3. Affiliate Tracking Cookies</h3> <p>These cookies help us track when you click on affiliate links so that we can earn commissions from your purchases. They are essential to our business model as an affiliate marketing platform.</p> <p><strong>Examples:</strong></p> <ul> <li>ShareASale tracking cookies</li> <li>Commission Junction (CJ) tracking cookies</li> <li>Direct merchant affiliate cookies</li> </ul> <h3>4. Preference Cookies</h3> <p>These cookies allow us to remember choices you make and provide enhanced, personalized features. They may be set by us or by third-party providers whose services we have added to our pages.</p> <p><strong>Examples:</strong></p> <ul> <li>Language preference cookies</li> <li>Dark/light mode preference cookies</li> <li>View preference cookies (grid vs. list view)</li> </ul> <h2>Cookie Consent</h2> <p>When you first visit our website, you will be shown a cookie banner that allows you to accept or decline non-essential cookies. You can change your preferences at any time by clicking on the "Manage Cookie Preferences" link in the footer of our website.</p> <p>By continuing to use our website after seeing the cookie banner without changing your settings, you consent to our use of cookies as described in this policy.</p> <h2>Managing Your Cookie Preferences</h2> <p>You can manage your cookie preferences in several ways:</p> <h3>Through Our Website</h3> <p>You can manage your cookie preferences on our website by clicking on the "Manage Cookie Preferences" link in the footer.</p> <h3>Through Your Browser</h3> <p>Most web browsers allow you to control cookies through their settings. Here's how to manage cookies in popular browsers:</p> <ul> <li><strong>Google Chrome</strong>: Settings → Privacy and Security → Cookies and other site data</li> <li><strong>Mozilla Firefox</strong>: Options → Privacy & Security → Cookies and Site Data</li> <li><strong>Safari</strong>: Preferences → Privacy → Cookies and website data</li> <li><strong>Microsoft Edge</strong>: Settings → Cookies and site permissions → Cookies and site data</li> </ul> <p>Please note that restricting cookies may impact the functionality of our website.</p> <h2>Third-Party Cookies</h2> <p>Some cookies are placed by third parties on our website. These third parties have their own privacy policies, and we encourage you to read them. We do not control these cookies and are not responsible for their use.</p> <p>Third parties that may place cookies through our website include:</p> <ul> <li>Cloudflare (for performance and security)</li> <li>ShareASale, Commission Junction, and other affiliate networks</li> <li>Resend.com (for email communications)</li> </ul> <h2>Changes to This Cookie Policy</h2> <p>We may update our Cookie Policy from time to time. We will notify you of any changes by posting the new Cookie Policy on this page and updating the "Last updated" date at the top.</p> <p>You are advised to review this Cookie Policy periodically for any changes.</p> <h2>Contact Us</h2> <p>If you have any questions about our Cookie Policy, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/cookie-policy.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/cookie-policy.astro";
const $$url = "/cookie-policy";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$CookiePolicy,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
