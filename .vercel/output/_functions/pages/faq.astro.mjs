import { a as createComponent, d as renderComponent, f as renderScript, r as renderTemplate, m as maybeRenderHead, b as addAttribute, u as unescapeHTML } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$InfoPageLayout } from '../assets/js/InfoPageLayout-Bg861p5k.js';
import { $ as $$InfoPageHeader, a as $$InfoPageSection } from '../assets/js/InfoPageSection-CrV_q83G.js';
import { G as GlowingButton } from '../assets/js/app/design-system-CUssmfny.js';
import { f as faqs } from '../assets/js/faqs-CCHoNMx8.js';
export { renderers } from '../renderers.mjs';

const $$Faq = createComponent(($$result, $$props, $$slots) => {
  const categories = [
    { id: "general", name: "General Questions" },
    { id: "coupons", name: "Coupon & Deal Questions", slug: "coupon-codes" },
    { id: "vaping", name: "Vaping Questions" },
    { id: "products", name: "Product Questions" },
    { id: "shipping", name: "Shipping & Delivery" }
  ];
  const popularTags = [
    { id: "coupon-codes", name: "Coupon Codes" },
    { id: "best-deals", name: "Best Deals" },
    { id: "discount-juice", name: "Discount E-Juice" },
    { id: "disposables", name: "Disposable Vapes" },
    { id: "pod-systems", name: "Pod Systems" },
    { id: "starter-kits", name: "Starter Kits" },
    { id: "box-mods", name: "Box Mods" }
  ];
  const faqsByCategory = categories.map((category) => {
    const categoryFaqs = faqs.filter((faq) => faq.category === category.id);
    return {
      ...category,
      faqs: categoryFaqs
    };
  });
  const faqSchema = {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map((faq) => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
  const breadcrumbSchema = {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": [
      {
        "@type": "ListItem",
        "position": 1,
        "name": "Home",
        "item": "https://vapehybrid.com"
      },
      {
        "@type": "ListItem",
        "position": 2,
        "name": "FAQ",
        "item": "https://vapehybrid.com/faq"
      }
    ]
  };
  const schemas = [faqSchema, breadcrumbSchema];
  function enhanceFAQWithLinks(answer) {
    const keywordLinks = [
      { keyword: "pod systems", url: "/categories/pod-systems" },
      { keyword: "disposable vapes", url: "/categories/disposables" },
      { keyword: "e-liquid", url: "/categories/e-liquid" },
      { keyword: "nicotine salt", url: "/categories/nicotine-salts" },
      { keyword: "SMOK", url: "/brands/smok" },
      { keyword: "Geek Vape", url: "/brands/geek-vape" },
      { keyword: "Aegis", url: "/search?q=aegis" },
      { keyword: "coils", url: "/categories/coils" },
      { keyword: "starter kits", url: "/categories/starter-kits" },
      { keyword: "box mods", url: "/categories/box-mods" },
      { keyword: "Dinner Lady", url: "/brands/dinner-lady" },
      { keyword: "Naked 100", url: "/coupons/brands/naked-100" },
      { keyword: "Element Vape", url: "/merchants/element-vape" },
      { keyword: "DirectVapor", url: "/merchants/direct-vapor" },
      { keyword: "VaporDNA", url: "/merchants/vapor-dna" },
      { keyword: "Voopoo", url: "/coupons/brands/voopoo" },
      { keyword: "Vaporesso", url: "/coupons/brands/vaporesso" },
      { keyword: "Lost Vape", url: "/coupons/brands/lost-vape" }
    ];
    let enhancedAnswer = answer;
    keywordLinks.forEach(({ keyword, url }) => {
      const regex = new RegExp(`\\b${keyword}\\b`, "i");
      const match = enhancedAnswer.match(regex);
      if (match) {
        const exactMatch = match[0];
        enhancedAnswer = enhancedAnswer.replace(
          regex,
          `<a href="${url}" class="text-primary hover:underline">${exactMatch}</a>`
        );
      }
    });
    return enhancedAnswer;
  }
  return renderTemplate`${renderComponent($$result, "InfoPageLayout", $$InfoPageLayout, { "title": "Frequently Asked Questions | VapeHybrid", "description": "Find answers to common questions about vaping, coupons, deals, and our service. Get help with coupon codes, vape products, and more.", "structuredData": schemas, "pattern": "dots" }, { "default": ($$result2) => renderTemplate` ${renderComponent($$result2, "InfoPageHeader", $$InfoPageHeader, { "title": "Frequently Asked Questions", "subtitle": "Find answers to common questions about vaping, coupons, and our service", "pattern": "grid", "glass": true })}  ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "centerTitle": true, "centerContent": true, "background": "transparent", "pattern": "circles", "glass": true }, { "default": ($$result3) => renderTemplate` ${maybeRenderHead()}<div class="flex flex-wrap justify-center gap-3 mb-10"> ${categories.map((category) => renderTemplate`<a${addAttribute(`#${category.id}`, "href")} class="text-sm bg-primary/10 text-primary px-4 py-2 rounded-lg hover:bg-primary/20 transition-colors"> ${category.name} </a>`)} </div>  <div class="mb-12"> <h2 class="text-xl font-bold text-design-foreground mb-4 text-center">Browse by Popular Topics</h2> <div class="flex flex-wrap justify-center gap-3"> ${popularTags.map((tag) => renderTemplate`<a${addAttribute(`#${tag.id}`, "href")} class="text-[14px] bg-primary/10 text-primary px-4 py-2 rounded-full hover:bg-primary/20 transition-colors"> ${tag.name} </a>`)} </div> </div> ` })}  ${faqsByCategory.map((category) => renderTemplate`${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "title": category.name, "background": "transparent", "pattern": "dots", "glass": true }, { "default": ($$result3) => renderTemplate` <div${addAttribute(category.id, "id")} class="scroll-mt-20"> <div class="space-y-6"> ${category.faqs.length > 0 ? category.faqs.map((faq) => renderTemplate`<div${addAttribute(faq.tags?.[0] || "", "id")} class="bg-design-card/50 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300"> <h3 class="text-lg font-bold text-design-foreground mb-3"> ${faq.question} </h3> <div class="text-design-muted-foreground text-sm faq-answer"> <div>${unescapeHTML(enhanceFAQWithLinks(faq.answer))}</div> </div>  ${faq.tags && faq.tags.length > 0 && renderTemplate`<div class="mt-4 pt-4 border-t border-design-border/30"> <p class="text-sm font-medium text-design-foreground mb-2">Related Topics:</p> <div class="flex flex-wrap gap-2"> ${faq.tags.map((tag) => renderTemplate`<a${addAttribute(`#${tag}`, "href")} class="text-[14px] bg-primary/10 text-primary px-2 py-1 rounded-full hover:bg-primary/20 transition-colors"> ${tag} </a>`)} </div> </div>`} </div>`) : renderTemplate`<div class="text-center py-8 text-design-muted-foreground">
No FAQs in this category yet. Check back soon!
</div>`} </div> </div> ` })}`)} ${renderComponent($$result2, "InfoPageSection", $$InfoPageSection, { "centerTitle": true, "centerContent": true, "background": "transparent", "pattern": "wave", "glass": true }, { "default": ($$result3) => renderTemplate` <div class="bg-design-card/50 p-8 rounded-lg shadow-sm text-center"> <h2 class="text-xl font-bold text-design-foreground mb-3">
Didn't find what you're looking for?
</h2> <p class="text-design-muted-foreground mb-6">
Our support team is here to help with any questions you may have
</p> ${renderComponent($$result3, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/contact", "className": "text-base", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": ($$result4) => renderTemplate`
Contact Support
` })} </div> ` })} ` })} ${renderScript($$result, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/faq.astro?astro&type=script&index=0&lang.ts")}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/faq.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/faq.astro";
const $$url = "/faq";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Faq,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
