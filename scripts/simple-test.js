// Simple test for Supabase functions
console.log('🧪 Testing Supabase Functions...');

const testFunction = async () => {
  try {
    const response = await fetch('https://zlnvivfgzgcjuspktadj.supabase.co/functions/v1/raw-sync', {
      method: 'POST',
      headers: {
        'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbnZpdmZnemdjanVzcGt0YWRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDM2NjQyNTcsImV4cCI6MjA1OTI0MDI1N30.nOjNcWd-bneTXg5UHv2nlAc10l7Gi-cQTEm9V5yrqFY',
        'Content-Type': 'application/json'
      },
      body: JSON.stringify({ name: 'Functions' })
    });
    
    console.log('✅ Response status:', response.status);
    const result = await response.text();
    console.log('📝 Response:', result.substring(0, 200));
  } catch (error) {
    console.error('❌ Error:', error.message);
  }
};

testFunction();
