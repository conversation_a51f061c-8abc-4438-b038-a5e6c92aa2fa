import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeR<PERSON>Head, b as addAttribute } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../assets/js/MainLayout-BVJnMCp3.js';
import { c as createServerSupabaseClient } from '../../assets/js/server-e_5TR1Eu.js';
import { $ as $$PageBreadcrumbs } from '../../assets/js/PageBreadcrumbs-DYZTpX-J.js';
import { G as GlowingButton } from '../../assets/js/app/design-system-CUssmfny.js';
export { renderers } from '../../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Index = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Index;
  const supabase = createServerSupabaseClient();
  const { data, error } = await supabase.from("brands").select("*").order("name");
  if (error) {
    console.error("Error fetching brands:", error);
  }
  const brands = data || [];
  const groupedBrands = {};
  brands.forEach((brand) => {
    const firstLetter = brand.name.charAt(0).toUpperCase();
    if (!groupedBrands[firstLetter]) {
      groupedBrands[firstLetter] = [];
    }
    groupedBrands[firstLetter].push(brand);
  });
  const sortedLetters = groupedBrands ? Object.keys(groupedBrands).sort() : [];
  const title = "Vape Brand Coupons & Promo Codes - Save Up to 50% | VapeHybrid";
  const description = "Find verified coupon codes for top vape brands like Geek Bar, Lost Mary, Voopoo, and SMOK. Hand-tested promo codes with real success rates. Save up to 50% on premium vape products with exclusive brand discounts updated daily by vapers.";
  const structuredData = {
    "@context": "https://schema.org",
    "@graph": [
      // Main CollectionPage
      {
        "@type": "CollectionPage",
        "@id": Astro2.url.href,
        name: "Vape Brand Coupons Directory",
        description,
        url: Astro2.url.href,
        mainEntity: {
          "@type": "ItemList",
          numberOfItems: brands.length,
          itemListElement: brands?.map((brand, index) => ({
            "@type": "ListItem",
            position: index + 1,
            item: {
              "@type": "Brand",
              "@id": `${Astro2.url.origin}/coupons/brands/${brand.slug}`,
              name: brand.name,
              url: `${Astro2.url.origin}/coupons/brands/${brand.slug}`,
              logo: brand.logo_url || void 0,
              description: brand.description || `Save with verified ${brand.name} coupon codes and promo codes`
            }
          }))
        }
      },
      // BreadcrumbList Schema
      {
        "@type": "BreadcrumbList",
        itemListElement: [
          {
            "@type": "ListItem",
            position: 1,
            name: "Home",
            item: Astro2.url.origin
          },
          {
            "@type": "ListItem",
            position: 2,
            name: "Coupons",
            item: `${Astro2.url.origin}/coupons`
          },
          {
            "@type": "ListItem",
            position: 3,
            name: "Brand Coupons",
            item: Astro2.url.href
          }
        ]
      },
      // FAQ Schema for brand-related questions
      {
        "@type": "FAQPage",
        mainEntity: [
          {
            "@type": "Question",
            name: "Which vape brands offer the best coupon codes?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "Top vape brands like Geek Bar, Lost Mary, Voopoo, and SMOK regularly offer exclusive coupon codes with savings up to 50%. All codes are verified daily by our vaper community."
            }
          },
          {
            "@type": "Question",
            name: "How often are brand coupon codes updated?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "We update brand coupon codes daily and verify each code with real purchases. Expired codes are removed immediately to ensure 98% success rate."
            }
          },
          {
            "@type": "Question",
            name: "Can I stack multiple brand coupon codes?",
            acceptedAnswer: {
              "@type": "Answer",
              text: "Most vape brands allow only one coupon code per order, but some merchants accept manufacturer coupons plus store discounts. Check each brand's terms for stacking policies."
            }
          }
        ]
      }
    ]
  };
  const popularBrands = brands.slice(0, 8);
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": async ($$result2) => renderTemplate`  ${maybeRenderHead()}<div class="relative bg-gradient-to-br from-design-background via-design-background to-design-muted/20 py-16 overflow-hidden"> <!-- Background Pattern --> <div class="absolute inset-0 opacity-30"> <div class="absolute inset-0 bg-[radial-gradient(circle_at_1px_1px,_rgba(255,255,255,0.15)_1px,_transparent_0)] bg-[length:20px_20px] dark:bg-[radial-gradient(circle_at_1px_1px,_rgba(255,255,255,0.05)_1px,_transparent_0)]"></div> </div> <!-- Content --> <div class="max-w-[1280px] mx-auto px-4 relative z-10"> <div class="max-w-3xl mx-auto text-center"> ${renderComponent($$result2, "PageBreadcrumbs", $$PageBreadcrumbs, { "items": [
    { label: "Home", href: "/" },
    { label: "Coupons", href: "/coupons" },
    { label: "Brand Coupons", href: "/coupons/brands", current: true }
  ], "class": "text-center" })} <h1 class="text-4xl md:text-5xl font-normal text-design-foreground mb-6 leading-tight">
Vape Brand Coupons & Promo Codes - Save Big on Your Favorite Gear
</h1> <p class="text-lg text-design-muted-foreground mb-8 max-w-2xl mx-auto">
Score exclusive deals from top vape brands! Whether you're into Geek Bar disposables, Lost Mary flavors, or Voopoo mods, we've got verified coupon codes that actually work. All deals tested by real vapers, not bots.
</p> <!-- Brand Statistics --> <div class="grid grid-cols-2 md:grid-cols-4 gap-4 p-6 bg-design-card/50 border border-design-border rounded-lg max-w-2xl mx-auto"> <div class="text-center"> <div class="text-2xl font-bold text-primary">${brands.length}</div> <div class="text-sm text-design-muted-foreground">Brands</div> </div> <div class="text-center"> <div class="text-2xl font-bold text-primary">500+</div> <div class="text-sm text-design-muted-foreground">Active Coupons</div> </div> <div class="text-center"> <div class="text-2xl font-bold text-primary">50%</div> <div class="text-sm text-design-muted-foreground">Max Savings</div> </div> <div class="text-center"> <div class="text-2xl font-bold text-primary">98%</div> <div class="text-sm text-design-muted-foreground">Success Rate</div> </div> </div> </div> </div> </div> <div class="max-w-[960px] mx-auto px-4 pt-4 sm:pt-8 lg:pt-12 pb-12">  ${error && renderTemplate`<div class="bg-design-destructive/10 text-design-destructive p-4 rounded-md mb-8"> <p>There was an error loading brand coupons. Please try again later.</p> </div>`}  ${popularBrands.length > 0 && renderTemplate`<div class="mb-12"> <h2 class="text-2xl font-bold text-design-foreground mb-6 text-center">Most Popular Vape Brand Deals</h2> <p class="text-design-muted-foreground mb-8 text-center">These top vape brands consistently offer the best coupon codes with highest success rates among our vaper community. Updated daily.</p> <div class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 gap-4"> ${popularBrands.map((brand) => renderTemplate`<a${addAttribute(`/coupons/brands/${brand.slug}`, "href")} class="group bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-transparent rounded-xl p-4 transition-all duration-300 hover:shadow-md flex flex-col items-center text-center relative overflow-hidden card-glow">  <div class="w-16 h-16 mb-3 rounded-full bg-primary/5 dark:bg-primary/10 flex items-center justify-center overflow-hidden relative"> ${brand.logo_url ? renderTemplate`<img${addAttribute(brand.logo_url, "src")}${addAttribute(`${brand.name} logo`, "alt")} class="w-10 h-10 object-contain" loading="lazy">` : renderTemplate`<div class="w-10 h-10 bg-primary/20 rounded-full flex items-center justify-center"> <span class="text-primary font-bold text-sm">${brand.name.charAt(0)}</span> </div>`} </div>  <h3 class="font-semibold text-design-foreground group-hover:text-primary transition-colors text-sm line-clamp-2"> ${brand.name} </h3> <p class="text-xs text-design-muted-foreground mt-1">View Coupons</p> </a>`)} </div> </div>`}  <div class="mb-8 p-6 bg-design-card/30 border border-design-border rounded-lg shadow-sm"> <h3 class="text-base font-semibold text-design-foreground mb-3">Quick Vape Brand Navigation</h3> <p class="text-sm text-design-muted-foreground mb-4">Jump directly to your favorite vape brand using the alphabetical shortcuts below:</p> <div class="flex flex-wrap gap-2"> ${sortedLetters.map((letter) => renderTemplate`<a${addAttribute(`#section-${letter}`, "href")} class="w-10 h-10 rounded-full bg-primary/10 dark:bg-primary/20 text-primary dark:text-design-secondary font-semibold text-sm flex items-center justify-center hover:bg-primary hover:text-white transition-colors"${addAttribute(`Jump to brands starting with ${letter}`, "aria-label")}> ${letter} </a>`)} </div> </div>  <div class="space-y-16"> ${sortedLetters.map((letter) => renderTemplate`<div${addAttribute(`section-${letter}`, "id")} class="scroll-mt-24"> <div class="flex flex-col sm:flex-row items-start sm:items-center mb-6"> <div class="flex items-center justify-center w-12 h-12 rounded-full bg-primary/10 dark:bg-primary/20 text-primary dark:text-design-secondary font-bold text-xl mr-4 mb-4 sm:mb-0"> ${letter} </div> <div class="text-center sm:text-left"> <h2 class="text-2xl font-bold text-design-foreground mb-2">Vape Brand Coupons - ${letter}</h2> <p class="text-sm text-design-muted-foreground">Browse all vape brands starting with '${letter}' and find verified coupon codes from these manufacturers</p> </div> </div> <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-6"> ${groupedBrands[letter].map((brand) => renderTemplate`<a${addAttribute(`/coupons/brands/${brand.slug}`, "href")} class="group bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-transparent rounded-xl p-6 transition-all duration-300 hover:shadow-md flex flex-col items-center text-center relative overflow-hidden card-glow">  <div class="w-20 h-20 mb-4 rounded-full bg-primary/5 dark:bg-primary/10 flex items-center justify-center overflow-hidden relative"> ${brand.logo_url ? renderTemplate`<img${addAttribute(brand.logo_url, "src")}${addAttribute(`${brand.name} logo`, "alt")} class="w-12 h-12 object-contain group-hover:scale-110 transition-transform duration-300" loading="lazy">` : renderTemplate`<div class="w-12 h-12 bg-primary/20 rounded-full flex items-center justify-center group-hover:scale-110 transition-transform duration-300"> <span class="text-primary font-bold text-lg">${brand.name.charAt(0)}</span> </div>`}  <div class="absolute inset-0 bg-primary/10 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div> </div>  <h3 class="text-lg font-semibold text-design-foreground group-hover:text-primary transition-colors mb-2 line-clamp-2"> ${brand.name} </h3>  <div class="text-sm text-design-muted-foreground mb-4"> <span class="inline-flex items-center px-2 py-1 rounded-full bg-primary/10 text-primary">
View Coupon Codes
</span> </div> ${brand.description && renderTemplate`<p class="text-sm text-design-muted-foreground text-center line-clamp-2"> ${brand.description} </p>`}  <div class="absolute top-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity duration-300"> <svg class="w-5 h-5 text-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> </div> </a>`)} </div> </div>`)} </div>  <div class="mt-16 text-center p-8 bg-gradient-to-r from-primary/5 to-design-secondary/5 rounded-xl border border-design-border mx-auto"> <h3 class="text-2xl font-bold text-design-foreground mb-4">Can't Find Your Brand?</h3> <p class="text-design-muted-foreground mb-6 max-w-2xl mx-auto">
We're constantly adding new brand partnerships and coupon codes. Browse all available coupons or contact us to suggest a brand.
</p> <div class="flex flex-col sm:flex-row gap-4 justify-center"> ${renderComponent($$result2, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/coupons", "className": "text-sm px-6 py-2.5", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result3) => renderTemplate`
Browse All Coupons
<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg> ` })} <a href="/contact" class="inline-flex items-center px-6 py-2.5 border border-design-border rounded-lg text-design-foreground hover:bg-design-muted/10 transition-colors">
Suggest a Brand
</a> </div> </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/brands/index.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/brands/index.astro";
const $$url = "/coupons/brands";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Index,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
