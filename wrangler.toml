# Cloudflare Workers configuration
name = "vapehybrid"
main = "dist/_worker.js"
compatibility_date = "2024-09-23"
compatibility_flags = ["nodejs_compat"]

# Static assets configuration
[site]
bucket = "./dist"

# Environment variables
[vars]
NODE_ENV = "production"
PUBLIC_SITE_URL = "https://vapehybrid.com"
SUPABASE_URL = "https://zlnvivfgzgcjuspktadj.supabase.co"
SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbnZpdmZnemdjanVzcGt0YWRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODM1NDcyNTAsImV4cCI6MTk5OTEyMzI1MH0.Rl3ZH_ZZS-XbQnrYYKI-Rl-ck-1hqvXQnKYop3iGP68"
PUBLIC_SUPABASE_URL = "https://zlnvivfgzgcjuspktadj.supabase.co"
PUBLIC_SUPABASE_ANON_KEY = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpsbnZpdmZnemdjanVzcGt0YWRqIiwicm9sZSI6ImFub24iLCJpYXQiOjE2ODM1NDcyNTAsImV4cCI6MTk5OTEyMzI1MH0.Rl3ZH_ZZS-XbQnrYYKI-Rl-ck-1hqvXQnKYop3iGP68"
ASTRO_SITE_URL = "https://vapehybrid.com"
