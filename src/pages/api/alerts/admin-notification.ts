import type { APIRoute } from 'astro';
import { Resend } from 'resend';
import { getErrorMessage } from '../../../utils/error-utils';

// Initialize Resend with your API key
const resendApiKey =
  (typeof import.meta !== 'undefined' ? import.meta.env.RESEND_API_KEY : undefined) ||
  (typeof process !== 'undefined' ? process.env.RESEND_API_KEY : undefined) ||
  'RESEND_API_KEY';

const resend = new Resend(resendApiKey);

export const POST: APIRoute = async ({ request }) => {
  try {
    const { type, email, subscription_type, subscription_target, page } = await request.json();

    if (!type || !email) {
      return new Response(JSON.stringify({ 
        error: 'Missing required fields: type, email' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Send admin notification email
    try {
      await sendAdminNotification({
        type,
        email,
        subscription_type,
        subscription_target,
        page
      });

      return new Response(JSON.stringify({
        success: true,
        message: 'Admin notification sent successfully'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (emailError) {
      console.error('Error sending admin notification:', emailError);
      
      // Don't fail the request if admin notification fails
      return new Response(JSON.stringify({
        success: true,
        message: 'Subscription processed (admin notification failed)',
        warning: 'Admin notification could not be sent'
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Error in admin notification:', error);
    return new Response(JSON.stringify({
      error: 'Internal server error',
      details: getErrorMessage(error)
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

async function sendAdminNotification(data: any) {
  const { type, email, subscription_type, subscription_target, page } = data;
  
  const subject = `🔔 New ${subscription_type} subscription: ${subscription_target}`;
  
  const currentTime = new Date().toLocaleString('en-US', {
    timeZone: 'America/New_York',
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  });

  const { data: emailData, error } = await resend.emails.send({
    from: 'VapeHybrid Alerts <<EMAIL>>',
    to: ['<EMAIL>'],
    subject,
    html: `
      <!DOCTYPE html>
      <html>
      <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>${subject}</title>
      </head>
      <body style="margin: 0; padding: 0; font-family: Arial, sans-serif; background-color: #f5f5f5;">
        <div style="max-width: 600px; margin: 0 auto; background-color: #ffffff;">
          
          <!-- Header -->
          <div style="background: linear-gradient(135deg, #262cbd, #4f46e5); color: white; padding: 20px; text-align: center;">
            <h1 style="margin: 0; font-size: 24px; font-weight: bold;">VapeHybrid Admin</h1>
            <p style="margin: 10px 0 0 0; font-size: 14px; opacity: 0.9;">New Subscription Alert</p>
          </div>

          <!-- Alert Badge -->
          <div style="background: linear-gradient(135deg, #10b981, #059669); color: white; padding: 20px; text-align: center; margin: 0;">
            <h2 style="margin: 0; font-size: 20px; font-weight: bold;">New ${subscription_type.toUpperCase()} Subscription</h2>
            <p style="margin: 10px 0 0 0; font-size: 16px; opacity: 0.95;">${subscription_target}</p>
          </div>

          <!-- Subscription Details -->
          <div style="padding: 20px;">
            <div style="background-color: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #262cbd;">
              <h3 style="color: #262cbd; font-size: 18px; margin: 0 0 15px 0; font-weight: bold;">Subscription Details</h3>
              
              <table style="width: 100%; border-collapse: collapse;">
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151; width: 30%;">Email:</td>
                  <td style="padding: 8px 0; color: #111827;">${email}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Type:</td>
                  <td style="padding: 8px 0; color: #111827;">
                    <span style="background: #dbeafe; color: #1e40af; padding: 2px 8px; border-radius: 4px; font-size: 12px; font-weight: bold;">
                      ${subscription_type.toUpperCase()}
                    </span>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Target:</td>
                  <td style="padding: 8px 0; color: #111827; font-weight: bold;">${subscription_target}</td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Page:</td>
                  <td style="padding: 8px 0; color: #111827;">
                    <a href="${page}" style="color: #2563eb; text-decoration: none;">${page}</a>
                  </td>
                </tr>
                <tr>
                  <td style="padding: 8px 0; font-weight: bold; color: #374151;">Time:</td>
                  <td style="padding: 8px 0; color: #111827;">${currentTime} EST</td>
                </tr>
              </table>
            </div>

            <!-- Quick Actions -->
            <div style="background-color: #f0f9ff; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
              <h4 style="color: #0369a1; font-size: 16px; margin: 0 0 15px 0;">Quick Actions</h4>
              <div style="display: flex; gap: 10px; flex-wrap: wrap;">
                <a href="https://vapehybrid.com/api/alerts/manage-subscriptions?email=${encodeURIComponent(email)}" 
                   style="background: #2563eb; color: white; padding: 8px 16px; text-decoration: none; border-radius: 6px; font-size: 14px; display: inline-block;">
                  View User Subscriptions
                </a>
                <a href="https://vapehybrid.com/admin/subscribers" 
                   style="background: #059669; color: white; padding: 8px 16px; text-decoration: none; border-radius: 6px; font-size: 14px; display: inline-block;">
                  Admin Dashboard
                </a>
              </div>
            </div>

            <!-- Revenue Opportunity -->
            <div style="background-color: #fef3c7; padding: 20px; border-radius: 8px; margin-bottom: 20px; border-left: 4px solid #f59e0b;">
              <h4 style="color: #92400e; font-size: 16px; margin: 0 0 10px 0;">💰 Revenue Opportunity</h4>
              <p style="color: #78350f; font-size: 14px; margin: 0; line-height: 1.5;">
                This subscriber will receive targeted ${subscription_target} alerts with affiliate links. 
                Each email click generates potential commission revenue through the /go/ tracking system.
              </p>
            </div>

            <!-- System Stats -->
            <div style="background-color: #f3f4f6; padding: 15px; border-radius: 8px; text-align: center;">
              <p style="color: #6b7280; font-size: 12px; margin: 0;">
                <strong>VapeHybrid Multi-Subscription System</strong><br>
                Automated alert system with affiliate revenue tracking
              </p>
            </div>
          </div>

          <!-- Footer -->
          <div style="background-color: #f8f9fa; padding: 15px; text-align: center; border-top: 1px solid #e5e7eb;">
            <p style="color: #6b7280; font-size: 12px; margin: 0;">
              <strong>VapeHybrid Admin Notifications</strong><br>
              <a href="https://vapehybrid.com" style="color: #262cbd; text-decoration: none;">vapehybrid.com</a>
            </p>
          </div>

        </div>
      </body>
      </html>
    `,
  });

  if (error) {
    throw new Error(`Failed to send admin notification: ${error.message}`);
  }

  return emailData;
}
