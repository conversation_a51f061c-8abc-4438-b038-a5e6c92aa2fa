import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, b as addAttribute, F as Fragment$1 } from '../../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../../assets/js/MainLayout-BVJnMCp3.js';
import { c as createServerSupabaseClient } from '../../../assets/js/server-e_5TR1Eu.js';
import { jsxs, Fragment, jsx } from 'react/jsx-runtime';
import { useState, useRef, useMemo, useEffect, useCallback } from 'react';
import { f as usePrefetch, h as useEngagementTracking, i as getRandomStaffImages, j as generateUsageInfo, n as normalizeUrl, e as copyToClipboard, C as Card, k as cn, l as DealImage, B as BookmarkButton, g as generateCouponUrl, m as DealStructuredData } from '../../../assets/js/app/design-system-CUssmfny.js';
import { ThumbsUp, Eye } from 'lucide-react';
import { C as CouponPagePopup } from '../../../assets/js/CouponPagePopup-DcOOX4eE.js';
import { toast } from 'sonner';
import { c as calculateCouponStats, g as generateMerchantSEOMeta, a as generateSocialProofText, b as generateRecentVerifications, d as generateMerchantPageSchema, e as generateMetaTagsObject } from '../../../assets/js/seo-meta-generators-Bu4YtbQp.js';
/* empty css                                        */
export { renderers } from '../../../renderers.mjs';

const DelayedStructuredData = ({ deal, imageUrl }) => {
  const [shouldRender, setShouldRender] = useState(false);
  useEffect(() => {
    const requestIdleCallbackPolyfill = window.requestIdleCallback || ((cb) => setTimeout(cb, 1));
    const idleCallbackId = requestIdleCallbackPolyfill(() => {
      setShouldRender(true);
    });
    return () => {
      const cancelIdleCallbackPolyfill = window.cancelIdleCallback || ((id) => clearTimeout(id));
      cancelIdleCallbackPolyfill(idleCallbackId);
    };
  }, []);
  if (!shouldRender) return null;
  return /* @__PURE__ */ jsx(DealStructuredData, { deal, imageUrl });
};
const MerchantDealListCard = ({
  deal,
  className,
  isRevealed = false,
  priority = false,
  onShowCouponModal,
  ...props
}) => {
  const [isCodeRevealed, setIsCodeRevealed] = useState(isRevealed);
  const cardRef = useRef(null);
  const dealUrl = `/deal/${deal.id}`;
  const { onMouseEnter: handlePrefetch, onMouseLeave: handleCancelPrefetch } = usePrefetch(dealUrl, { delay: 150 });
  const {
    setIsVisible,
    handleMouseEnter: trackMouseEnter,
    handleMouseLeave: trackMouseLeave,
    handleClick: trackClick
  } = useEngagementTracking(deal.id, "deal", { minDwellTime: 2e3 });
  const staffAvatars = useMemo(() => getRandomStaffImages(5, deal.id), [deal.id]);
  const usageInfo = useMemo(() => generateUsageInfo(deal.id), [deal.id]);
  const usageTimeAgo = useMemo(() => usageInfo.timeAgo, [usageInfo]);
  const count = useMemo(() => deal.usage_count || usageInfo.count, [deal.usage_count, usageInfo.count]);
  const daysRemaining = useMemo(() => {
    if (!deal.deal_end_date) return null;
    return Math.ceil((new Date(deal.deal_end_date).getTime() - (/* @__PURE__ */ new Date()).getTime()) / (1e3 * 60 * 60 * 24));
  }, [deal.deal_end_date]);
  const isExpiringSoon = useMemo(() => {
    return daysRemaining !== null && daysRemaining > 0 && daysRemaining <= 7;
  }, [daysRemaining]);
  const isExpired = useMemo(() => {
    return daysRemaining !== null && daysRemaining <= 0;
  }, [daysRemaining]);
  const imageUrl = useMemo(() => {
    const dealImage = normalizeUrl(deal.imagebig_url) || normalizeUrl(deal.image_url) || normalizeUrl(deal.imagesmall_url);
    if (dealImage) return dealImage;
    const brandLogo = normalizeUrl(deal.brands?.logo_url) || normalizeUrl(deal.brand_logo_url);
    if (brandLogo) return brandLogo;
    const merchantLogo = normalizeUrl(deal.merchants?.logo_url) || normalizeUrl(deal.merchant_logo_url);
    if (merchantLogo) return merchantLogo;
    return "/placeholder-image.svg";
  }, [
    deal.imagebig_url,
    deal.image_url,
    deal.imagesmall_url,
    deal.brands?.logo_url,
    deal.brand_logo_url,
    deal.merchants?.logo_url,
    deal.merchant_logo_url
  ]);
  useEffect(() => {
    if (!cardRef.current) return;
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          setIsVisible(entry.isIntersecting);
          if (entry.isIntersecting) {
            if (process.env.NODE_ENV !== "production") {
              console.log("Deal impression tracked:", deal.id);
            } else {
              try {
                let sessionId = localStorage.getItem("vh_session_id");
                if (!sessionId) {
                  sessionId = "session_" + Math.random().toString(36).substring(2, 15);
                  localStorage.setItem("vh_session_id", sessionId);
                }
                if (typeof navigator.sendBeacon === "function") {
                  const trackingData = new FormData();
                  trackingData.append("deal_id", deal.id.toString());
                  trackingData.append("session_id", sessionId);
                  navigator.sendBeacon("/api/track-impression?deal_id=" + deal.id + "&session_id=" + sessionId, trackingData);
                } else {
                  fetch("/api/track-impression?deal_id=" + deal.id + "&session_id=" + sessionId, {
                    method: "POST",
                    headers: { "Content-Type": "application/json" },
                    body: JSON.stringify({ deal_id: deal.id, session_id: sessionId }),
                    keepalive: true
                  });
                }
              } catch (error) {
                console.error("Error tracking impression:", error);
              }
            }
          }
        });
      },
      { threshold: 0.5 }
      // Card is considered visible when 50% is in view
    );
    observer.observe(cardRef.current);
    return () => {
      observer.disconnect();
    };
  }, [deal.id, cardRef, setIsVisible]);
  const handleCopy = useCallback((e) => {
    e.stopPropagation();
    trackClick();
    requestAnimationFrame(() => {
      setIsCodeRevealed(true);
      if (typeof window !== "undefined") {
        if (typeof navigator.sendBeacon === "function") {
          try {
            const trackingData = new FormData();
            trackingData.append("deal_id", deal.id.toString());
            trackingData.append("fallback", "true");
            navigator.sendBeacon("/api/track-click", trackingData);
            console.log(`Tracking copy for deal_id=${deal.id} only`);
          } catch (error) {
            console.error("Error sending beacon:", error);
          }
        }
        const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
        const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
        revealedCodes[deal.id] = true;
        localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
        clickedDeals[deal.id] = true;
        localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
        if (deal.coupon_code) {
          copyToClipboard(deal.coupon_code);
        }
        const currentUrl = new URL(window.location.href);
        const viewMode = currentUrl.searchParams.get("view") || "list";
        const popupUrl = new URL(window.location.href);
        popupUrl.searchParams.set("dealId", deal.id.toString());
        popupUrl.searchParams.set("showPopup", "true");
        popupUrl.searchParams.set("solidBg", "true");
        popupUrl.searchParams.set("view", viewMode);
        if (onShowCouponModal) {
          onShowCouponModal(deal);
        }
        window.open(popupUrl.toString(), "_blank");
        if (deal.tracking_url) {
          window.location.href = deal.tracking_url;
        } else {
          window.location.href = `/go/${deal.id}`;
        }
      }
    });
  }, [deal, trackClick, onShowCouponModal]);
  return /* @__PURE__ */ jsxs(Fragment, { children: [
    typeof window !== "undefined" && /* @__PURE__ */ jsx(DelayedStructuredData, { deal, imageUrl }),
    /* @__PURE__ */ jsxs(
      Card,
      {
        ref: cardRef,
        variant: "default",
        interactive: true,
        glowEffect: true,
        className: cn(
          "merchant-deal-list-card relative cursor-pointer w-full flex flex-col sm:flex-row items-center justify-between mb-6",
          "transition-all duration-300 hover:shadow-lg hover:-translate-y-1 hover:scale-[1.005]",
          "hover:border-design-primary/50 dark:hover:border-design-primary/50",
          "border-black/15 dark:border-white/15",
          // Distinguishable border in both modes
          className
        ),
        style: {
          padding: "0.5rem",
          borderRadius: "25px",
          borderWidth: "1.5px",
          minHeight: "175px",
          maxHeight: "175px"
        },
        onClick: (e) => {
          e.stopPropagation();
          handleCopy(e);
        },
        onMouseEnter: () => {
          handlePrefetch();
          trackMouseEnter();
        },
        onMouseLeave: () => {
          handleCancelPrefetch();
          trackMouseLeave();
        },
        "aria-label": `Deal: ${deal.title}`,
        role: "button",
        tabIndex: 0,
        ...props,
        children: [
          isExpiringSoon && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-warning text-on-warning text-xs font-bold px-2 py-1 rounded-full", children: daysRemaining === 1 ? "Expires today" : `Expires in ${daysRemaining} days` }),
          isExpired && /* @__PURE__ */ jsx("div", { className: "absolute top-2 right-2 z-10 bg-design-destructive text-on-destructive text-xs font-bold px-2 py-1 rounded-full", children: "Expired" }),
          /* @__PURE__ */ jsx("div", { className: "w-full sm:w-[160px] py-2 sm:py-0", children: /* @__PURE__ */ jsx("div", { className: "flex justify-center h-full", children: /* @__PURE__ */ jsx(
            "div",
            {
              className: "relative flex items-center justify-center w-full h-full aspect-square max-w-[140px] sm:min-w-[140px] sm:max-w-[150px] sm:max-h-[150px] bg-white dark:bg-white rounded-lg overflow-hidden",
              style: {
                boxShadow: "inset 0 0 0 1px rgba(0,0,0,0.05)",
                padding: "6px"
              },
              children: /* @__PURE__ */ jsx(
                DealImage,
                {
                  src: imageUrl,
                  alt: deal.title || "Deal image",
                  width: 150,
                  height: 150,
                  priority,
                  fetchpriority: priority ? "high" : "auto",
                  className: "",
                  fallbackSrc: "/placeholder-image.svg",
                  index: 0
                }
              )
            }
          ) }) }),
          /* @__PURE__ */ jsx("div", { className: "flex-1 p-2 w-full", children: /* @__PURE__ */ jsxs("div", { className: "h-full flex flex-col justify-between", children: [
            /* @__PURE__ */ jsx("div", { className: "mb-1", children: /* @__PURE__ */ jsxs("div", { className: "text-2xl font-bold text-design-foreground", children: [
              deal.discount ? /* @__PURE__ */ jsxs("span", { className: "text-green-600 dark:text-green-500", children: [
                deal.discount,
                "% ",
                /* @__PURE__ */ jsx("span", { className: "text-sm font-normal text-gray-500 dark:text-gray-400", children: "off " }),
                "🔥"
              ] }) : deal.price ? `${deal.currency || "$"}${deal.price}` : "20% Off",
              isExpiringSoon && /* @__PURE__ */ jsx("span", { className: "ml-2 text-xs font-normal text-design-warning animate-pulse", children: daysRemaining === 1 ? "Ends today!" : `Ends in ${daysRemaining} days!` })
            ] }) }),
            /* @__PURE__ */ jsxs("div", { className: "text-sm text-design-muted-foreground mb-1 flex items-center flex-wrap gap-1", children: [
              /* @__PURE__ */ jsx(
                "span",
                {
                  className: "font-medium truncate max-w-[120px] hover:underline cursor-help",
                  title: `Brand: ${deal.merchants?.name || deal.brands?.name || "Unknown"}`,
                  children: deal.merchants?.name || deal.brands?.name || "Brand"
                }
              ),
              deal.verified && /* @__PURE__ */ jsxs("span", { className: "c-verified-badge verified-badge ml-2", children: [
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid light icon.svg",
                    alt: "Verified",
                    className: "dark:hidden",
                    width: 16,
                    height: 16
                  }
                ),
                /* @__PURE__ */ jsx(
                  "img",
                  {
                    src: "/Vapehybrid dark icon.svg",
                    alt: "Verified",
                    className: "hidden dark:block",
                    width: 16,
                    height: 16
                  }
                ),
                /* @__PURE__ */ jsx("span", { className: "text-design-primary dark:text-design-primary", children: "Verified" })
              ] }),
              deal.success_rate !== void 0 && /* @__PURE__ */ jsxs(
                "span",
                {
                  className: `ml-2 text-xs flex items-center px-1.5 py-0.5 rounded-full ${deal.success_rate >= 90 ? "bg-design-success/20 text-design-success dark:bg-design-success/30 dark:text-design-success-foreground" : deal.success_rate >= 70 ? "bg-design-warning/20 text-design-warning dark:bg-design-warning/30 dark:text-design-warning-foreground" : "bg-design-destructive/20 text-design-destructive dark:bg-design-destructive/30 dark:text-design-destructive-foreground"}`,
                  title: `${Math.round(deal.success_rate || 85)}% success rate`,
                  children: [
                    /* @__PURE__ */ jsx(ThumbsUp, { size: 10, className: "mr-1" }),
                    Math.round(deal.success_rate || 85),
                    "%"
                  ]
                }
              )
            ] }),
            /* @__PURE__ */ jsx(
              "h3",
              {
                className: "deal-card-title line-clamp-2 mb-1 text-base font-semibold overflow-hidden",
                title: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title,
                children: deal.cleaned_title && deal.cleaned_title !== "null" ? deal.cleaned_title : deal.title
              }
            ),
            /* @__PURE__ */ jsx("div", { className: "flex flex-1", children: /* @__PURE__ */ jsxs("div", { className: "flex-1", children: [
              /* @__PURE__ */ jsxs("div", { className: "flex items-center text-xs text-design-muted-foreground mb-2", children: [
                /* @__PURE__ */ jsxs("div", { className: "c-avatar-group user-avatar-group flex relative", style: { marginRight: "0.35rem" }, children: [
                  staffAvatars.slice(0, Math.min(3, count || 3)).map((avatar, index) => /* @__PURE__ */ jsxs(
                    "div",
                    {
                      className: "c-avatar c-avatar--xs user-avatar relative transition-transform hover:scale-110 hover:z-10 overflow-hidden",
                      style: {
                        width: "20px",
                        height: "20px",
                        marginLeft: index > 0 ? "-8px" : "0",
                        borderRadius: "50%",
                        border: "1.5px solid white",
                        boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                        zIndex: 3 - index,
                        backgroundColor: avatar.color
                      },
                      title: `Staff member: ${avatar.name}`,
                      children: [
                        avatar.webpPath && avatar.imagePath ? /* @__PURE__ */ jsxs("picture", { children: [
                          /* @__PURE__ */ jsx("source", { srcSet: avatar.webpPath, type: "image/webp" }),
                          /* @__PURE__ */ jsx(
                            "img",
                            {
                              src: avatar.imagePath,
                              alt: avatar.initials,
                              className: "w-full h-full object-cover",
                              onError: (e) => {
                                const target = e.target;
                                target.style.display = "none";
                                const parent = target.parentElement?.parentElement;
                                if (parent) {
                                  parent.classList.add("fallback-active");
                                }
                              }
                            }
                          )
                        ] }) : /* @__PURE__ */ jsx("div", { className: "w-full h-full flex items-center justify-center text-white font-bold", children: /* @__PURE__ */ jsx("span", { style: { fontSize: "8px" }, children: avatar.initials }) }),
                        /* @__PURE__ */ jsx("div", { className: "fallback-content absolute inset-0 flex items-center justify-center text-white font-bold opacity-0", children: /* @__PURE__ */ jsx("span", { style: { fontSize: "8px" }, children: avatar.initials }) })
                      ]
                    },
                    index
                  )),
                  count > 3 && /* @__PURE__ */ jsxs(
                    "div",
                    {
                      className: "c-avatar c-avatar--xs user-avatar flex items-center justify-center bg-design-muted-foreground/20",
                      style: {
                        width: "20px",
                        height: "20px",
                        marginLeft: "-8px",
                        borderRadius: "50%",
                        border: "1.5px solid white",
                        boxShadow: "0 1px 2px rgba(0,0,0,0.1)",
                        fontSize: "8px"
                      },
                      title: `${count - 3} more staff members`,
                      children: [
                        "+",
                        count - 3
                      ]
                    }
                  )
                ] }),
                /* @__PURE__ */ jsxs("span", { children: [
                  "Verified ",
                  /* @__PURE__ */ jsx("time", { dateTime: deal.last_verified_at, title: new Date(deal.last_verified_at || "").toLocaleString(), children: usageTimeAgo }),
                  " by ",
                  count || 3,
                  " staffer",
                  (count || 3) > 1 ? "s" : ""
                ] })
              ] }),
              isExpiringSoon && /* @__PURE__ */ jsxs("div", { className: "mb-2 text-xs text-design-warning px-2 py-1 rounded flex items-center", children: [
                /* @__PURE__ */ jsx("span", { className: "animate-pulse mr-1", children: "⏱" }),
                /* @__PURE__ */ jsxs("span", { children: [
                  "Limited time offer! ",
                  daysRemaining === 1 ? "Ends today" : `Ends in ${daysRemaining} days`
                ] })
              ] })
            ] }) })
          ] }) }),
          /* @__PURE__ */ jsxs("div", { className: "flex flex-col items-center justify-center px-4 sm:pr-4 sm:pl-0 gap-2 w-full sm:min-w-[150px] sm:w-auto py-3 sm:py-0", children: [
            /* @__PURE__ */ jsx(
              "div",
              {
                className: "deal-coupon-code relative cursor-pointer px-3 py-1 w-full text-center bg-design-muted rounded-full border border-design-muted-foreground/10 overflow-hidden",
                onClick: (e) => {
                  e.stopPropagation();
                  handleCopy(e);
                },
                title: isCodeRevealed ? "Click to copy" : "Click to reveal code",
                "aria-label": isCodeRevealed ? `Copy coupon code: ${deal.coupon_code || "NO CODE"}` : "Click to reveal coupon code",
                children: deal.coupon_code ? /* @__PURE__ */ jsxs(Fragment, { children: [
                  /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: `text-base font-bold transition-all duration-300 ${isCodeRevealed ? "blur-none opacity-100" : "blur-[4px] select-none"}`,
                      style: { color: "var(--design-foreground)" },
                      "aria-hidden": !isCodeRevealed,
                      children: deal.coupon_code
                    }
                  ),
                  isCodeRevealed && /* @__PURE__ */ jsx(
                    "span",
                    {
                      className: "absolute inset-0 bg-design-primary/10 animate-reveal-sweep",
                      "aria-hidden": "true"
                    }
                  ),
                  /* @__PURE__ */ jsx("span", { className: "sr-only", children: isCodeRevealed ? `Coupon code: ${deal.coupon_code}` : "Click to reveal coupon code" })
                ] }) : "NO CODE"
              }
            ),
            /* @__PURE__ */ jsx(
              "button",
              {
                className: "copy-code-button px-4 py-1 rounded-full border border-design-muted-foreground/20 hover:border-design-primary/50 dark:hover:border-design-primary/50 transition-colors",
                onClick: (e) => {
                  e.stopPropagation();
                  handleCopy(e);
                },
                "aria-label": `Copy coupon code ${deal.coupon_code || "for this deal"}`,
                children: "Copy Code"
              }
            ),
            /* @__PURE__ */ jsxs("div", { className: "deal-card-actions flex items-center justify-center gap-2 mt-1", children: [
              /* @__PURE__ */ jsx(
                BookmarkButton,
                {
                  dealId: deal.id.toString(),
                  className: "bookmark-button p-1.5 rounded-full hover:bg-design-muted transition-colors"
                }
              ),
              /* @__PURE__ */ jsx(
                "button",
                {
                  className: "eye-button p-1.5 rounded-full hover:bg-design-muted transition-colors relative z-10",
                  onClick: (e) => {
                    e.stopPropagation();
                    window.open(generateCouponUrl(deal), "_blank");
                  },
                  title: "View coupon details",
                  "aria-label": "View coupon details",
                  children: /* @__PURE__ */ jsx(Eye, { size: 16, className: "text-design-muted-foreground hover:text-design-foreground transition-colors" })
                }
              )
            ] })
          ] })
        ]
      }
    )
  ] });
};

const ExpandableMerchantInfo = ({
  merchantName,
  websiteUrl,
  activeCoupons
}) => {
  const [isExpanded, setIsExpanded] = useState(false);
  const toggleExpanded = () => {
    setIsExpanded(!isExpanded);
    if (!isExpanded) {
      setTimeout(() => {
        const element = document.getElementById("expanded-merchant-content");
        if (element) {
          element.scrollIntoView({
            behavior: "smooth",
            block: "start"
          });
        }
      }, 100);
    }
  };
  websiteUrl ? websiteUrl.replace(/^https?:\/\//, "") : "their website";
  const goLink = `/go/merchant/${merchantName.toLowerCase().replace(/\s+/g, "-")}`;
  const generateVerificationTime = (name) => {
    const hash = name.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    const hours = [2, 4, 6, 8, 10, 12, 18, 24][Math.abs(hash) % 8];
    if (hours <= 12) {
      return `${hours} hours ago`;
    } else {
      return `${Math.floor(hours / 24)} day${Math.floor(hours / 24) > 1 ? "s" : ""} ago`;
    }
  };
  const verificationTime = generateVerificationTime(merchantName);
  return /* @__PURE__ */ jsxs("div", { className: "mb-2", children: [
    /* @__PURE__ */ jsxs("p", { className: "text-base text-gray-600 dark:text-gray-300", style: { lineHeight: "1.1em" }, children: [
      "Last verified ",
      verificationTime,
      " - ",
      activeCoupons,
      " active community members tracking ",
      merchantName,
      " promo codes this past week.",
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: toggleExpanded,
          className: "text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 ml-1 transition-colors",
          children: isExpanded ? "Show less ↑" : "Show more ↓"
        }
      )
    ] }),
    /* @__PURE__ */ jsx(
      "div",
      {
        id: "expanded-merchant-content",
        className: `transition-all duration-300 ease-in-out overflow-hidden ${isExpanded ? "max-h-none opacity-100 mt-4" : "max-h-0 opacity-0"}`,
        children: /* @__PURE__ */ jsxs("div", { className: "space-y-6 text-sm text-gray-600 dark:text-gray-300", children: [
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Everything you need to know about ",
              merchantName
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "How to grab your ",
                  merchantName,
                  " discount"
                ] }),
                /* @__PURE__ */ jsxs("ol", { className: "list-decimal list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Pick your favorite vape gear on ",
                    /* @__PURE__ */ jsx("a", { href: goLink, target: "_blank", rel: "noopener noreferrer", className: "text-blue-600 dark:text-blue-400 hover:underline", children: merchantName }),
                    " and hit ",
                    /* @__PURE__ */ jsx("strong", { children: '"Add to Cart"' })
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Look for the ",
                    /* @__PURE__ */ jsx("strong", { children: "coupon box" }),
                    " at checkout and paste your ",
                    merchantName,
                    " code"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Hit ",
                    /* @__PURE__ */ jsx("strong", { children: '"Apply"' }),
                    " and watch your total drop - ",
                    /* @__PURE__ */ jsx("em", { children: "that's money back in your pocket!" })
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Code not working? Here's what to check" }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Double-check you typed the ",
                    merchantName,
                    " code ",
                    /* @__PURE__ */ jsx("strong", { children: "exactly right" }),
                    " (no extra spaces!)"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Make sure your cart hits the ",
                    /* @__PURE__ */ jsx("strong", { children: "minimum spend" }),
                    " if there's one required"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Check the ",
                    /* @__PURE__ */ jsx("strong", { children: "expiry date" }),
                    " - some codes have time limits"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Still stuck? Try another ",
                    /* @__PURE__ */ jsxs("em", { children: [
                      "verified ",
                      merchantName,
                      " code"
                    ] }),
                    " from our list above"
                  ] })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "Can you stack multiple ",
                  merchantName,
                  " codes?"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  /* @__PURE__ */ jsx("strong", { children: "Nope!" }),
                  " Like most vape shops, ",
                  merchantName,
                  " only lets you use ",
                  /* @__PURE__ */ jsx("em", { children: "one discount code per order" }),
                  ". The good news? We test all our codes so you can pick the ",
                  /* @__PURE__ */ jsx("strong", { children: "best one" }),
                  " that saves you the most cash on your vape haul."
                ] })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Smart ways to save more at ",
              merchantName
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsx("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: "Pro tips from fellow vapers" }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    "Sign up for ",
                    merchantName,
                    "'s newsletter - they often send exclusive codes to subscribers"
                  ] }),
                  /* @__PURE__ */ jsx("li", { children: "Follow them on social media for flash sales and surprise discounts" }),
                  /* @__PURE__ */ jsx("li", { children: "Check their clearance section first - you might find your favorite juice on sale" }),
                  /* @__PURE__ */ jsx("li", { children: "Create an account to get member-only deals and faster checkout" }),
                  /* @__PURE__ */ jsx("li", { children: "Keep an eye on their homepage banner - that's where they announce big sales" })
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "When does ",
                  merchantName,
                  " have the best sales?"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  "Like most vape shops, ",
                  merchantName,
                  " goes big during Black Friday, Cyber Monday, and the holidays. But here's a secret - they also run surprise sales throughout the year, so it pays to check back regularly or follow our updates."
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "Never miss a ",
                  merchantName,
                  " deal again"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  "Want to stay in the loop? Bookmark this page and check back weekly. We update our ",
                  merchantName,
                  " codes as soon as we find new ones. You can also follow us for instant alerts when fresh discounts drop."
                ] })
              ] })
            ] })
          ] }),
          /* @__PURE__ */ jsxs("div", { className: "bg-gray-100 dark:bg-gray-900 rounded-lg p-4", children: [
            /* @__PURE__ */ jsxs("h4", { className: "font-semibold text-gray-900 dark:text-white mb-3", children: [
              "Why vapers love ",
              merchantName
            ] }),
            /* @__PURE__ */ jsxs("div", { className: "space-y-4", children: [
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "What makes ",
                  merchantName,
                  " special"
                ] }),
                /* @__PURE__ */ jsxs("p", { className: "text-sm", children: [
                  merchantName,
                  " has built a solid reputation in the vaping community by offering quality products at fair prices. They stock everything from starter kits to premium mods, plus a huge selection of e-liquids from top brands."
                ] })
              ] }),
              /* @__PURE__ */ jsxs("div", { children: [
                /* @__PURE__ */ jsxs("h5", { className: "font-medium text-gray-900 dark:text-white mb-2", children: [
                  "Why VapeHybrid has the best ",
                  merchantName,
                  " deals"
                ] }),
                /* @__PURE__ */ jsxs("ul", { className: "list-disc list-inside space-y-1 text-sm", children: [
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Real vaper testing:" }),
                    " Our team actually tries these codes before posting them"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Live success rates:" }),
                    " See which codes are working right now for other vapers"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "No fake codes:" }),
                    " We remove expired or broken codes immediately"
                  ] }),
                  /* @__PURE__ */ jsxs("li", { children: [
                    /* @__PURE__ */ jsx("strong", { children: "Vaper community:" }),
                    " Real feedback from actual ",
                    merchantName,
                    " customers"
                  ] })
                ] })
              ] })
            ] })
          ] })
        ] })
      }
    )
  ] });
};

const EmailCaptureForm = ({
  targetName,
  subscriptionType,
  className = ""
}) => {
  const [email, setEmail] = useState("");
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [error, setError] = useState("");
  const handleSubmit = async (e) => {
    e.preventDefault();
    setError("");
    setIsSubmitting(true);
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(email)) {
      const errorMsg = "Please enter a valid email address";
      setError(errorMsg);
      toast.error(errorMsg);
      setIsSubmitting(false);
      return;
    }
    try {
      const response = await fetch("/api/alerts/subscribe-multi", {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email,
          subscription_type: subscriptionType,
          subscription_target: targetName,
          subscription_slug: targetName.toLowerCase().replace(/\s+/g, "-"),
          email_frequency: "instant",
          source_page: window.location.pathname
        })
      });
      const result = await response.json();
      if (!response.ok) {
        const errorMsg = result.message || "Something went wrong. Please try again.";
        setError(errorMsg);
        toast.error(errorMsg);
        setIsSubmitting(false);
        return;
      }
      toast.success(`🎉 Subscribed! Check your email to confirm and manage preferences.`);
      try {
        await fetch("/api/alerts/admin-notification", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            type: "new_subscription",
            email,
            subscription_type: subscriptionType,
            subscription_target: targetName,
            page: window.location.href
          })
        });
      } catch (e2) {
        console.log("Admin notification failed:", e2);
      }
      if (typeof gtag !== "undefined") {
        gtag("event", "email_signup", {
          event_category: "engagement",
          event_label: `${subscriptionType}_${targetName}`,
          value: 1
        });
      }
      setIsSubmitted(true);
      setEmail("");
    } catch (err) {
      const errorMsg = "Something went wrong. Please try again.";
      setError(errorMsg);
      toast.error(errorMsg);
    } finally {
      setIsSubmitting(false);
    }
  };
  if (isSubmitted) {
    return /* @__PURE__ */ jsxs("div", { className: `bg-design-card border border-design-border rounded-xl p-6 text-center ${className}`, children: [
      /* @__PURE__ */ jsxs("div", { className: "mb-4", children: [
        /* @__PURE__ */ jsx("svg", { className: "w-12 h-12 text-green-500 mx-auto mb-2", fill: "none", stroke: "currentColor", viewBox: "0 0 24 24", children: /* @__PURE__ */ jsx("path", { strokeLinecap: "round", strokeLinejoin: "round", strokeWidth: 2, d: "M5 13l4 4L19 7" }) }),
        /* @__PURE__ */ jsx("h3", { className: "text-lg font-semibold text-design-foreground mb-2", children: "You're all set!" }),
        /* @__PURE__ */ jsxs("p", { className: "text-design-muted-foreground", children: [
          "We'll email you fresh ",
          targetName,
          " coupons and exclusive deals whenever they're available."
        ] })
      ] }),
      /* @__PURE__ */ jsx(
        "button",
        {
          onClick: () => setIsSubmitted(false),
          className: "text-design-primary hover:text-design-primary/80 text-sm",
          children: "Sign up another email"
        }
      )
    ] });
  }
  return /* @__PURE__ */ jsxs("div", { className: `bg-design-card border border-design-border rounded-xl p-6 ${className}`, children: [
    /* @__PURE__ */ jsxs("div", { className: "text-center mb-4", children: [
      /* @__PURE__ */ jsxs("h3", { className: "text-lg font-semibold text-design-foreground mb-2", children: [
        "Get ",
        targetName,
        " coupons instantly!"
      ] }),
      /* @__PURE__ */ jsxs("p", { className: "text-design-muted-foreground text-sm", children: [
        "We'll email you with fresh ",
        targetName,
        " codes and other offers whenever they're found."
      ] })
    ] }),
    /* @__PURE__ */ jsxs("form", { onSubmit: handleSubmit, className: "space-y-4", children: [
      /* @__PURE__ */ jsxs("div", { children: [
        /* @__PURE__ */ jsx(
          "input",
          {
            type: "email",
            value: email,
            onChange: (e) => setEmail(e.target.value),
            placeholder: "Enter email address",
            required: true,
            disabled: isSubmitting,
            className: "w-full px-4 py-3 border border-design-border rounded-lg focus:ring-2 focus:ring-design-primary focus:border-transparent outline-none transition-all disabled:opacity-50 disabled:cursor-not-allowed"
          }
        ),
        error && /* @__PURE__ */ jsx("p", { className: "text-red-500 text-sm mt-1", children: error })
      ] }),
      /* @__PURE__ */ jsx(
        "button",
        {
          type: "submit",
          disabled: isSubmitting || !email,
          className: "w-full bg-design-primary hover:bg-design-primary/90 disabled:bg-design-primary/50 text-white font-medium py-3 px-6 rounded-lg transition-all disabled:cursor-not-allowed flex items-center justify-center",
          children: isSubmitting ? /* @__PURE__ */ jsxs(Fragment, { children: [
            /* @__PURE__ */ jsxs("svg", { className: "animate-spin -ml-1 mr-3 h-4 w-4 text-white", xmlns: "http://www.w3.org/2000/svg", fill: "none", viewBox: "0 0 24 24", children: [
              /* @__PURE__ */ jsx("circle", { className: "opacity-25", cx: "12", cy: "12", r: "10", stroke: "currentColor", strokeWidth: "4" }),
              /* @__PURE__ */ jsx("path", { className: "opacity-75", fill: "currentColor", d: "M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" })
            ] }),
            "Subscribing..."
          ] }) : "Get Alerts"
        }
      )
    ] }),
    /* @__PURE__ */ jsx("div", { className: "mt-4 text-center", children: /* @__PURE__ */ jsxs("p", { className: "text-xs text-design-muted-foreground", children: [
      "We'll email fresh codes • ",
      " ",
      /* @__PURE__ */ jsx("a", { href: "/privacy-policy", className: "hover:text-design-primary transition-colors", children: "Privacy Policy" }),
      " • ",
      "Unsubscribe anytime"
    ] }) })
  ] });
};

const $$Astro = createAstro("http://localhost:4321");
const $$id = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$id;
  const { id } = Astro2.params;
  const supabase = createServerSupabaseClient();
  let merchant = null;
  let error = null;
  const isNumericId = /^\d+$/.test(id);
  if (isNumericId) {
    const { data: merchantData, error: merchantError } = await supabase.from("merchants").select("*").eq("id", id).single();
    merchant = merchantData;
    error = merchantError;
  } else {
    const { data: merchantData, error: merchantError } = await supabase.from("merchants").select("*").eq("slug", id).single();
    merchant = merchantData;
    error = merchantError;
  }
  if (error) {
    console.error("Error fetching merchant:", error);
  }
  if (!merchant) {
    return Astro2.redirect("/coupons/merchants", 302);
  }
  const page = parseInt(Astro2.url.searchParams.get("page") || "1");
  const perPage = parseInt(Astro2.url.searchParams.get("perPage") || "12");
  const sort = Astro2.url.searchParams.get("sort") || "newest";
  Astro2.url.searchParams.get("view") || "list";
  const from = (page - 1) * perPage;
  const to = from + perPage - 1;
  let orderBy = {};
  switch (sort) {
    case "discount_high":
      orderBy = { discount: "desc" };
      break;
    case "discount_low":
      orderBy = { discount: "asc" };
      break;
    case "price_high":
      orderBy = { price: "desc" };
      break;
    case "price_low":
      orderBy = { price: "asc" };
      break;
    case "expiring":
      orderBy = { deal_end_date: "asc" };
      break;
    case "popular":
      orderBy = { click_count: "desc" };
      break;
    case "newest":
    default:
      orderBy = { created_at: "desc" };
  }
  let deals = null;
  let dealsError = null;
  if (merchant?.id) {
    const { data: dealsData, error: dealsErrorData, count: dealsCount } = await supabase.from("deals").select(`
      *,
      merchants:merchant_id (name, website_url, logo_url),
      brands:brand_id (name, logo_url, slug),
      categories:category_id (name, slug)
    `, { count: "exact" }).eq("merchant_id", merchant.id).order(Object.keys(orderBy)[0], { ascending: Object.values(orderBy)[0] === "asc" }).range(from, to);
    deals = dealsData;
    dealsError = dealsErrorData;
  }
  if (dealsError) {
    console.error("Error fetching deals:", dealsError);
  }
  const getOptimizedTitle = (merchant2, dealCount) => {
    if (!merchant2) return "Merchant Coupons Not Found";
    if (merchant2.name === "EJuice Connect") {
      return `EJuice Connect Coupon Codes & Promo Codes - 15% Off June 2025 | VapeHybrid`;
    }
    return `${merchant2.name} Coupon Codes & Promo Codes - Save Up To 30% | VapeHybrid`;
  };
  const getOptimizedDescription = (merchant2, dealCount) => {
    if (!merchant2) return "Browse vape coupons and deals by merchant on VapeHybrid.";
    if (merchant2.name === "EJuice Connect") {
      return `Save with verified EJuice Connect coupon codes and promo codes. Get up to 15% off e-liquids, vape devices, and coils. ${dealCount || 21} active coupons updated daily with real-time verification.`;
    }
    return `Save with verified ${merchant2.name} coupon codes and promo codes. Find exclusive discounts on vape products. ${dealCount || "Multiple"} active coupons verified daily.`;
  };
  const couponStats = calculateCouponStats(deals || [], merchant?.name || "");
  const seoMeta = merchant ? generateMerchantSEOMeta(merchant.name, couponStats) : null;
  const socialProof = merchant ? generateSocialProofText(merchant.name, "merchant", couponStats) : null;
  const title = seoMeta?.title || getOptimizedTitle(merchant, deals?.length);
  const description = seoMeta?.description || getOptimizedDescription(merchant, deals?.length);
  const recentVerifications = generateRecentVerifications(merchant?.name || "", deals);
  const generateMerchantRating = (merchantName) => {
    if (!merchantName) return { stars: 5, count: 2 };
    const hash = merchantName.split("").reduce((a, b) => {
      a = (a << 5) - a + b.charCodeAt(0);
      return a & a;
    }, 0);
    const rating = 3.5 + Math.abs(hash) % 15 / 10;
    const reviewCount = 2 + Math.abs(hash) % 48;
    return {
      stars: Math.round(rating * 2) / 2,
      // Round to nearest 0.5
      count: reviewCount
    };
  };
  const merchantRating = generateMerchantRating(merchant?.name);
  const generateStorePolicies = (merchantName) => {
    const basePolicies = [
      "Age verification required (21+)",
      "Customer service available Monday-Friday 9AM-5PM EST"
    ];
    const policies = [...basePolicies];
    if (merchantName?.toLowerCase().includes("ejuice") || merchantName?.toLowerCase().includes("juice")) {
      policies.unshift("No returns on opened e-liquid products");
      policies.unshift("Free shipping on orders over $50");
      policies.unshift("30-day return policy for unopened items");
    } else if (merchantName?.toLowerCase().includes("vape") || merchantName?.toLowerCase().includes("vapor")) {
      policies.unshift("Warranty coverage varies by manufacturer");
      policies.unshift("Free shipping on orders over $75");
      policies.unshift("14-day return policy for hardware");
    } else {
      policies.unshift("Warranty coverage varies by manufacturer");
      policies.unshift("Free shipping on orders over $60");
      policies.unshift("30-day return policy for unopened items");
    }
    return policies;
  };
  const storePolicies = generateStorePolicies(merchant?.name);
  const structuredData = merchant ? generateMerchantPageSchema(
    merchant,
    deals || [],
    Astro2.url.origin,
    couponStats
  ) : void 0;
  const metaTags = seoMeta ? generateMetaTagsObject(seoMeta, Astro2.url.href, merchant?.logo_url) : {};
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData, "openGraph": {
    basic: {
      title: metaTags["og:title"] || title,
      type: "website",
      image: metaTags["og:image"] || merchant?.logo_url,
      url: metaTags["og:url"] || Astro2.url.href
    },
    optional: {
      description: metaTags["og:description"] || description,
      siteName: "VapeHybrid"
    },
    image: {
      alt: metaTags["og:image:alt"] || `${merchant?.name} Coupons`,
      width: 1200,
      height: 630
    }
  }, "twitter": {
    card: "summary_large_image",
    site: "@vapehybrid",
    creator: "@vapehybrid",
    title: metaTags["twitter:title"] || title,
    description: metaTags["twitter:description"] || description,
    image: metaTags["twitter:image"] || merchant?.logo_url
  }, "data-astro-cid-thj6pv23": true }, { "default": async ($$result2) => renderTemplate`    ${maybeRenderHead()}<div class="bg-gray-100 dark:bg-gray-800 text-gray-700 dark:text-gray-300 py-2 text-center" style="font-size: 11px;" data-astro-cid-thj6pv23>
VapeHybrid may earn commission on purchases. <a href="/terms" class="underline hover:text-gray-900 dark:hover:text-white transition-colors" data-astro-cid-thj6pv23>Learn more</a> </div>  <div class="py-3 border-b border-design-border" data-astro-cid-thj6pv23> <div class="max-w-[1030px] mx-auto px-4" data-astro-cid-thj6pv23> <nav class="text-left" aria-label="Breadcrumb" data-astro-cid-thj6pv23> <ol class="flex items-center space-x-1 text-sm text-design-muted-foreground" data-astro-cid-thj6pv23> <li data-astro-cid-thj6pv23> <a href="/" class="hover:text-design-foreground transition-colors" data-astro-cid-thj6pv23>Home</a> </li> <li data-astro-cid-thj6pv23> <span class="mx-1" data-astro-cid-thj6pv23>/</span> </li> <li data-astro-cid-thj6pv23> <a href="/coupons" class="hover:text-design-foreground transition-colors" data-astro-cid-thj6pv23>Coupons</a> </li> <li data-astro-cid-thj6pv23> <span class="mx-1" data-astro-cid-thj6pv23>/</span> </li> <li data-astro-cid-thj6pv23> <a href="/coupons/merchants" class="hover:text-design-foreground transition-colors" data-astro-cid-thj6pv23>Merchants</a> </li> <li data-astro-cid-thj6pv23> <span class="mx-1" data-astro-cid-thj6pv23>/</span> </li> <li data-astro-cid-thj6pv23> <span class="text-design-foreground font-medium" data-astro-cid-thj6pv23>${merchant.name}</span> </li> </ol> </nav> </div> </div>  <div class="merchant-page max-w-[1030px] mx-auto px-4 py-6" data-astro-cid-thj6pv23> <!-- Two Column Layout: 1/3 left, 2/3 right --> <div class="grid grid-cols-1 lg:grid-cols-3 gap-8" data-astro-cid-thj6pv23> <!-- Left Column (1/3) - Merchant Info --> <div class="lg:col-span-1 bg-gray-100 dark:bg-gray-700 p-4 rounded-lg" data-astro-cid-thj6pv23> <!-- Merchant Logo --> <div class="bg-white dark:bg-gray-600 rounded-lg p-4 mb-4 border border-gray-200 dark:border-gray-500" data-astro-cid-thj6pv23> ${merchant.logo_url ? renderTemplate`<img${addAttribute(merchant.logo_url, "src")}${addAttribute(merchant.name, "alt")} class="w-full h-32 object-contain bg-gray-50 dark:bg-gray-500 rounded" data-astro-cid-thj6pv23>` : renderTemplate`<div class="w-full h-32 bg-gray-50 dark:bg-gray-500 rounded flex items-center justify-center" data-astro-cid-thj6pv23> <span class="text-2xl font-bold text-gray-400 dark:text-gray-300" data-astro-cid-thj6pv23>${merchant.name.charAt(0)}</span> </div>`} </div> <!-- Merchant Name --> <h2 class="text-lg font-semibold text-gray-900 dark:text-white mb-3" data-astro-cid-thj6pv23>${merchant.name}</h2> <!-- Star Ratings --> <div class="flex items-center gap-1 mb-2" data-astro-cid-thj6pv23> <div class="flex text-yellow-400" data-astro-cid-thj6pv23> <span data-astro-cid-thj6pv23>${"\u2605".repeat(Math.floor(merchantRating.stars))}${"\u2606".repeat(5 - Math.floor(merchantRating.stars))}</span> </div> <span class="text-sm text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>(${merchantRating.count} ratings)</span> </div> <!-- Verification Text --> <p class="text-sm text-gray-600 dark:text-gray-300 mb-4" data-astro-cid-thj6pv23>
We have ${couponStats.activeCoupons} verified coupon codes for ${merchant.name} today.
</p> <!-- Shop Button --> <a${addAttribute(`/go/merchant/${merchant.name.toLowerCase().replace(/\s+/g, "-")}`, "href")} target="_blank" rel="noopener noreferrer" class="copy-code-button block w-full text-center mb-6" data-astro-cid-thj6pv23>
Shop
</a> <!-- Compact Statistics with Social Proof --> <div class="bg-white dark:bg-gray-700 rounded-lg p-3 mb-4 border border-gray-200 dark:border-gray-600" data-astro-cid-thj6pv23> <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2" data-astro-cid-thj6pv23>Quick Stats</h3> <div class="space-y-1 text-xs" data-astro-cid-thj6pv23> <div class="flex justify-between" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Active Coupons:</span> <span class="font-medium text-gray-900 dark:text-white" data-astro-cid-thj6pv23>${couponStats.activeCoupons}</span> </div> <div class="flex justify-between" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Best Discount:</span> <span class="font-medium text-green-600" data-astro-cid-thj6pv23>${couponStats.bestDiscount}</span> </div> <div class="flex justify-between" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Avg Savings:</span> <span class="font-medium text-gray-900 dark:text-white" data-astro-cid-thj6pv23>${couponStats.avgSavings}</span> </div> <div class="flex justify-between" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Success Rate:</span> <span class="font-medium text-blue-600" data-astro-cid-thj6pv23>${couponStats.successRate}</span> </div>  ${socialProof && renderTemplate`${renderComponent($$result2, "Fragment", Fragment$1, { "data-astro-cid-thj6pv23": true }, { "default": async ($$result3) => renderTemplate` <hr class="my-2 border-gray-200 dark:border-gray-600" data-astro-cid-thj6pv23> <div class="flex justify-between" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>${socialProof.lastVerified}</span> <span class="text-green-600 font-medium" data-astro-cid-thj6pv23>✓ Verified</span> </div> <div class="flex justify-between" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>${socialProof.lastUserSaved}</span> <span class="text-blue-600 font-medium" data-astro-cid-thj6pv23>💰 Recent Save</span> </div> <div class="text-xs text-gray-500 dark:text-gray-400 pt-1" data-astro-cid-thj6pv23> ${socialProof.communityTracking} </div> ` })}`} </div> </div> <!-- Email Capture --> ${renderComponent($$result2, "EmailCaptureForm", EmailCaptureForm, { "client:load": true, "targetName": merchant.name, "subscriptionType": "merchant", "className": "mb-4", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Merchant/EmailCaptureForm", "client:component-export": "default", "data-astro-cid-thj6pv23": true })} <!-- Store Policies --> <div class="bg-white dark:bg-gray-700 rounded-lg p-3 mb-4 border border-gray-200 dark:border-gray-600" data-astro-cid-thj6pv23> <h3 class="text-sm font-semibold text-gray-900 dark:text-white mb-2" data-astro-cid-thj6pv23>Store Policies</h3> <div class="text-xs text-gray-600 dark:text-gray-300" style="line-height: 1.3;" data-astro-cid-thj6pv23> <ul class="space-y-1 list-disc list-inside" data-astro-cid-thj6pv23> ${storePolicies.map((policy) => renderTemplate`<li data-astro-cid-thj6pv23>${policy}</li>`)} </ul> </div> </div> </div> <!-- Right Column (2/3) - Main Content --> <div class="merchant-right-column lg:col-span-2" data-astro-cid-thj6pv23> <!-- Header with Best Discount Badge --> <div class="flex items-start justify-between mb-4" data-astro-cid-thj6pv23> <!-- Main Title --> <h1 class="text-3xl font-bold text-gray-900 dark:text-white flex-1" data-astro-cid-thj6pv23> ${merchant.name === "EJuice Connect" ? "EJuice Connect Promo Codes & Coupons June 2025" : `${merchant.name} Promo Codes & Coupons`} </h1> <!-- Best Discount Badge --> <div class="ml-4 bg-gradient-to-r from-orange-400 to-red-500 text-white rounded-lg p-3 text-center shadow-lg" data-astro-cid-thj6pv23> <div class="text-2xl font-bold" data-astro-cid-thj6pv23>${couponStats.bestDiscount}</div> <div class="text-sm font-medium" data-astro-cid-thj6pv23>OFF</div> </div> </div> <!-- Verification Text with Expandable Content --> ${renderComponent($$result2, "ExpandableMerchantInfo", ExpandableMerchantInfo, { "client:load": true, "merchantName": merchant.name, "websiteUrl": merchant.website_url, "activeCoupons": couponStats.activeCoupons, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Merchant/ExpandableMerchantInfo", "client:component-export": "default", "data-astro-cid-thj6pv23": true })} <!-- Navigation Tabs --> <div class="flex gap-6 mb-6 border-b border-gray-200 dark:border-gray-600" data-astro-cid-thj6pv23> <a href="#top-codes" class="pb-2 border-b-2 border-green-500 text-green-600 font-medium hover:text-green-700" data-astro-cid-thj6pv23>All Coupons</a> <a href="#activity" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" data-astro-cid-thj6pv23>Activity</a> <a href="#faq" class="pb-2 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white" data-astro-cid-thj6pv23>FAQ</a> </div> <!-- Today's Promo Codes Section --> <div id="top-codes" class="mb-6" data-astro-cid-thj6pv23> <div class="flex items-center justify-between mb-4" data-astro-cid-thj6pv23> <div data-astro-cid-thj6pv23> <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2" data-astro-cid-thj6pv23>
Today's ${merchant.name} promo codes
</h2> <p class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>
Fresh codes tested by real vapers - ${sort === "discount_high" ? "biggest savings first" : sort === "newest" ? "newest first" : sort === "expiring" ? "expiring soon first" : "sorted by popularity"}! 🔥
</p> </div> <!-- Sort Controls --> <div class="flex items-center gap-3" data-astro-cid-thj6pv23> <label for="sort-select" class="text-sm text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Sort by:</label> <select id="sort-select" class="px-3 py-1 border border-gray-300 dark:border-gray-600 rounded-md text-sm bg-white dark:bg-gray-800 text-gray-900 dark:text-white" onchange="window.location.href = new URL(window.location.href).pathname + '?sort=' + this.value + '&view=' + new URLSearchParams(window.location.search).get('view') || 'list'" data-astro-cid-thj6pv23> <option value="discount_high"${addAttribute(sort === "discount_high" ? "selected" : "", "sort === 'discount_high' ? 'selected' : ''")} data-astro-cid-thj6pv23>Biggest Savings</option> <option value="newest"${addAttribute(sort === "newest" ? "selected" : "", "sort === 'newest' ? 'selected' : ''")} data-astro-cid-thj6pv23>Newest First</option> <option value="expiring"${addAttribute(sort === "expiring" ? "selected" : "", "sort === 'expiring' ? 'selected' : ''")} data-astro-cid-thj6pv23>Expiring Soon</option> <option value="popular"${addAttribute(sort === "popular" ? "selected" : "", "sort === 'popular' ? 'selected' : ''")} data-astro-cid-thj6pv23>Most Popular</option> </select> </div> </div> <!-- Coupon Cards - List Format --> <div class="space-y-6" style="max-width: 730px;" data-astro-cid-thj6pv23> ${deals && deals.length > 0 ? deals.map((deal, index) => renderTemplate`${renderComponent($$result2, "MerchantDealListCard", MerchantDealListCard, { "deal": deal, "priority": index < 3, "className": "w-full", "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Merchant/MerchantDealListCard", "client:component-export": "default", "data-astro-cid-thj6pv23": true })}`) : renderTemplate`<div class="text-center py-12 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-xl" data-astro-cid-thj6pv23> <p class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>No active coupons available for ${merchant.name} at the moment.</p> <p class="text-sm text-gray-500 dark:text-gray-400 mt-2" data-astro-cid-thj6pv23>Check back soon for new deals!</p> </div>`} </div> </div> <!-- Activity Section --> <div id="activity" class="mb-6" data-astro-cid-thj6pv23> <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4" data-astro-cid-thj6pv23>${merchant.name} Coupon Activity</h3> <!-- Recent Activity Stats --> <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6" data-astro-cid-thj6pv23> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4" data-astro-cid-thj6pv23> <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2" data-astro-cid-thj6pv23>Last 24 Hours</h4> <div class="text-2xl font-bold text-green-600" data-astro-cid-thj6pv23>${Math.floor(Math.random() * 50) + 20}</div> <div class="text-xs text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Codes used successfully</div> </div> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4" data-astro-cid-thj6pv23> <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2" data-astro-cid-thj6pv23>This Week</h4> <div class="text-2xl font-bold text-blue-600" data-astro-cid-thj6pv23>${Math.floor(Math.random() * 200) + 150}</div> <div class="text-xs text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Total coupon clicks</div> </div> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4" data-astro-cid-thj6pv23> <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-2" data-astro-cid-thj6pv23>Success Rate</h4> <div class="text-2xl font-bold text-purple-600" data-astro-cid-thj6pv23>${couponStats.successRate}</div> <div class="text-xs text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Average this month</div> </div> </div> <!-- Recent Verifications --> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4 mb-4" data-astro-cid-thj6pv23> <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3" data-astro-cid-thj6pv23>Recent Verifications</h4> <div class="space-y-2" data-astro-cid-thj6pv23> ${recentVerifications.map((verification) => renderTemplate`<div class="flex items-center justify-between text-sm" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>${verification.title}</span> <span class="text-green-600 font-medium" data-astro-cid-thj6pv23>✓ Verified ${verification.time}</span> </div>`)} </div> </div> <!-- Community Engagement --> <div class="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg p-4" data-astro-cid-thj6pv23> <h4 class="text-sm font-semibold text-gray-900 dark:text-white mb-3" data-astro-cid-thj6pv23>Community Engagement</h4> <div class="space-y-2" data-astro-cid-thj6pv23> <div class="flex items-center justify-between text-sm" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Active users tracking ${merchant.name}</span> <span class="font-medium text-gray-900 dark:text-white" data-astro-cid-thj6pv23>${couponStats.activeCoupons * 3} members</span> </div> <div class="flex items-center justify-between text-sm" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Average savings per user</span> <span class="font-medium text-gray-900 dark:text-white" data-astro-cid-thj6pv23>${couponStats.avgSavings}</span> </div> <div class="flex items-center justify-between text-sm" data-astro-cid-thj6pv23> <span class="text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>Most popular code</span> <span class="font-medium text-gray-900 dark:text-white" data-astro-cid-thj6pv23>15% Off Sitewide</span> </div> </div> </div> </div> <!-- FAQ --> <div id="faq" class="mb-6" data-astro-cid-thj6pv23> <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-3" data-astro-cid-thj6pv23>${merchant.name} Promo Codes FAQ</h3> <div class="text-sm text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23> <div class="space-y-2" data-astro-cid-thj6pv23> <!-- FAQ Item 1 - Shipping & Returns --> <details class="border border-gray-200 dark:border-gray-600 rounded-lg" data-astro-cid-thj6pv23> <summary class="cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700" style="font-size: 17px;" data-astro-cid-thj6pv23>
Does ${merchant.name} offer free shipping?
</summary> <div class="p-3 pt-0 text-sm" data-astro-cid-thj6pv23>
Most ${merchant.name} orders qualify for free shipping with minimum purchase requirements. Check their current shipping policy or use a free shipping code from our list above.
</div> </details> <!-- FAQ Item 2 - Payment & Security --> <details class="border border-gray-200 dark:border-gray-600 rounded-lg" data-astro-cid-thj6pv23> <summary class="cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700" style="font-size: 17px;" data-astro-cid-thj6pv23>
Is it safe to shop at ${merchant.name}?
</summary> <div class="p-3 pt-0 text-sm" data-astro-cid-thj6pv23>
Yes, ${merchant.name} uses secure payment processing and follows industry standards for customer data protection. They're a legitimate vape retailer with proper age verification.
</div> </details> <!-- FAQ Item 3 - Product Authenticity --> <details class="border border-gray-200 dark:border-gray-600 rounded-lg" data-astro-cid-thj6pv23> <summary class="cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700" style="font-size: 17px;" data-astro-cid-thj6pv23>
Are ${merchant.name} products authentic?
</summary> <div class="p-3 pt-0 text-sm" data-astro-cid-thj6pv23> ${merchant.name} sources products directly from manufacturers and authorized distributors to ensure authenticity. All products come with manufacturer warranties where applicable.
</div> </details> <!-- FAQ Item 4 - Customer Support --> <details class="border border-gray-200 dark:border-gray-600 rounded-lg" data-astro-cid-thj6pv23> <summary class="cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700" style="font-size: 17px;" data-astro-cid-thj6pv23>
How do I contact ${merchant.name} customer service?
</summary> <div class="p-3 pt-0 text-sm" data-astro-cid-thj6pv23>
You can reach ${merchant.name} customer service through their website contact form, email, or phone during business hours. They typically respond within 24 hours.
</div> </details> <!-- FAQ Item 5 - Age Verification --> <details class="border border-gray-200 dark:border-gray-600 rounded-lg" data-astro-cid-thj6pv23> <summary class="cursor-pointer p-3 font-bold text-gray-900 dark:text-white hover:bg-gray-50 dark:hover:bg-gray-700" style="font-size: 17px;" data-astro-cid-thj6pv23>
What age verification does ${merchant.name} require?
</summary> <div class="p-3 pt-0 text-sm" data-astro-cid-thj6pv23> ${merchant.name} requires customers to be 21+ and uses age verification systems to comply with federal and state regulations. You'll need valid ID to complete your purchase.
</div> </details> </div> </div> </div> <!-- Related Merchants Section --> <div class="mb-6" data-astro-cid-thj6pv23> <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4" data-astro-cid-thj6pv23>Similar Vape Store Coupons</h3> <div class="grid grid-cols-2 sm:grid-cols-4 gap-4" data-astro-cid-thj6pv23> <a href="/coupons/merchants/eightvape" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg font-bold text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>8V</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>EightVape Coupons</span> </a> <a href="/coupons/merchants/vapordna" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg font-bold text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>VD</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>VaporDNA Coupons</span> </a> <a href="/coupons/merchants/vapesourcing" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg font-bold text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>VS</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>VapeSourcing Coupons</span> </a> <a href="/coupons/merchants/directvapor" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg font-bold text-gray-600 dark:text-gray-300" data-astro-cid-thj6pv23>DV</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>DirectVapor Coupons</span> </a> </div> </div> <!-- Explore Categories --> <div class="mb-6" data-astro-cid-thj6pv23> <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4" data-astro-cid-thj6pv23>Explore Vape Categories</h3> <div class="grid grid-cols-2 sm:grid-cols-4 gap-4" data-astro-cid-thj6pv23> <a href="/coupons/categories/e-liquids" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg" data-astro-cid-thj6pv23>💧</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>E-Liquids</span> </a> <a href="/coupons/categories/vape-kits" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg" data-astro-cid-thj6pv23>📦</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>Vape Kits</span> </a> <a href="/coupons/categories/coils" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg" data-astro-cid-thj6pv23>🔧</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>Coils</span> </a> <a href="/coupons/categories/accessories" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg" data-astro-cid-thj6pv23>⚡</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>Accessories</span> </a> </div> </div> <!-- Explore Brands --> <div class="mb-6" data-astro-cid-thj6pv23> <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-4" data-astro-cid-thj6pv23>Popular Vape Brands</h3> <div class="grid grid-cols-2 sm:grid-cols-4 gap-4" data-astro-cid-thj6pv23> <a href="/coupons/brands/smok" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-red-100 dark:bg-red-900 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg font-bold text-red-600 dark:text-red-400" data-astro-cid-thj6pv23>SM</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>SMOK Coupons</span> </a> <a href="/coupons/brands/voopoo" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg font-bold text-blue-600 dark:text-blue-400" data-astro-cid-thj6pv23>VP</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>VooPoo Coupons</span> </a> <a href="/coupons/brands/geekvape" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-purple-100 dark:bg-purple-900 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg font-bold text-purple-600 dark:text-purple-400" data-astro-cid-thj6pv23>GV</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>GeekVape Coupons</span> </a> <a href="/coupons/brands/uwell" class="flex flex-col items-center p-3 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-600 rounded-lg hover:shadow-md transition-shadow" data-astro-cid-thj6pv23> <div class="w-12 h-12 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center mb-2" data-astro-cid-thj6pv23> <span class="text-lg font-bold text-green-600 dark:text-green-400" data-astro-cid-thj6pv23>UW</span> </div> <span class="text-sm font-medium text-gray-900 dark:text-white text-center" data-astro-cid-thj6pv23>Uwell Coupons</span> </a> </div> </div> </div> </div> </div>  ${deals && deals.map((deal) => renderTemplate`${renderComponent($$result2, "CouponPagePopup", CouponPagePopup, { "deal": deal, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponPagePopup", "client:component-export": "CouponPagePopup", "data-astro-cid-thj6pv23": true })}`)}` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/merchants/[id].astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/merchants/[id].astro";
const $$url = "/coupons/merchants/[id]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$id,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
