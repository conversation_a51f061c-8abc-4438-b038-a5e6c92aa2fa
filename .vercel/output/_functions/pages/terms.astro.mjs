import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$LegalPageLayout } from '../assets/js/LegalPageLayout-DSHTXlhZ.js';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Terms = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Terms;
  const title = "Terms & Conditions | VapeHybrid";
  const description = "Understand the terms and conditions governing the use of VapeHybrid's services.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": title,
    "description": description,
    "publisher": {
      "@type": "Organization",
      "name": "VapeHybrid",
      "logo": {
        "@type": "ImageObject",
        "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`
      }
    }
  };
  (/* @__PURE__ */ new Date()).getFullYear();
  return renderTemplate`${renderComponent($$result, "LegalPageLayout", $$LegalPageLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<h1 class="text-3xl font-bold text-design-foreground mb-6">Terms & Conditions</h1> <p class="text-design-muted-foreground mb-8">Last updated: ${(/* @__PURE__ */ new Date()).toLocaleDateString("en-US", { year: "numeric", month: "long", day: "numeric" })}</p> <div class="prose prose-lg max-w-none text-design-foreground prose-headings:text-design-foreground prose-strong:text-design-foreground prose-a:text-design-primary"> <h2>1. Definitions</h2> <p>Throughout these Terms and Conditions, the following terms shall have the meanings defined below:</p> <ul> <li><strong>"Affiliate"</strong>: A business entity that partners with VapeHybrid to promote their products through our platform in exchange for commission.</li> <li><strong>"User"</strong>: Any individual who accesses or uses the VapeHybrid website.</li> <li><strong>"Commission"</strong>: The fee paid to VapeHybrid by Affiliates for sales generated through our platform.</li> <li><strong>"Platform"</strong>: The VapeHybrid website, including all its pages, features, and services.</li> <li><strong>"Merchant"</strong>: Third-party retailers or brands that sell products accessible through links on our platform.</li> <li><strong>"Deal"</strong>: Any coupon, discount, offer, or promotion featured on our platform.</li> </ul> <h2>2. Eligibility & Age Restrictions</h2> <p>By using the VapeHybrid platform, you confirm that:</p> <ul> <li>You are at least 21 years of age if you are in the United States, or at least 18 years of age if you are in the United Kingdom or other jurisdictions where the legal age for vaping products is 18+.</li> <li>You have the legal capacity to enter into binding contracts.</li> <li>You are not prohibited from using vaping products under the laws of your jurisdiction.</li> </ul> <p>VapeHybrid reserves the right to terminate accounts or deny access to users who do not meet these eligibility requirements.</p> <h2>3. Affiliate-Only Role</h2> <p>VapeHybrid does not sell products directly; all transactions occur on third-party sites. We are an affiliate marketing platform that:</p> <ul> <li>Curates and presents deals from various merchants.</li> <li>Provides affiliate links that redirect users to merchant websites.</li> <li>Earns commissions from merchants when users make purchases through our links.</li> </ul> <p>VapeHybrid is not responsible for the fulfillment, quality, safety, or legality of products purchased from merchants.</p> <h2>4. Limitation of Liability</h2> <p>To the maximum extent permitted by law:</p> <ul> <li>VapeHybrid's total liability for all claims related to these terms or your use of our platform is limited to $100 USD or the amount you paid to use our services in the past 12 months, whichever is greater.</li> <li>VapeHybrid is not liable for any indirect, incidental, special, consequential, or punitive damages, including lost profits, lost data, personal injury, or property damage.</li> <li>These limitations apply to any theory of liability, whether based on warranty, contract, tort, negligence, strict liability, or any other legal theory, and whether or not VapeHybrid has been informed of the possibility of such damage.</li> </ul> <h2>5. Indemnification</h2> <p>You agree to indemnify, defend, and hold harmless VapeHybrid, its officers, directors, employees, agents, and affiliates from and against any and all claims, liabilities, damages, losses, costs, expenses, or fees (including reasonable attorneys' fees) that arise from or relate to:</p> <ul> <li>Your use or misuse of the VapeHybrid platform.</li> <li>Your violation of these Terms and Conditions.</li> <li>Your violation of any rights of another person or entity.</li> <li>Your conduct in connection with the platform.</li> </ul> <h2>6. Governing Law & Jurisdiction</h2> <p>These Terms and Conditions shall be governed by and construed in accordance with the laws of the State of Delaware, USA, without regard to its conflict of law provisions.</p> <p>Any dispute arising out of or relating to these Terms and Conditions shall be subject to the exclusive jurisdiction of the state and federal courts located in Delaware, USA. You consent to the personal jurisdiction of such courts and waive any objection to the exercise of jurisdiction by such courts.</p> <h2>7. Amendments</h2> <p>VapeHybrid reserves the right to modify these Terms and Conditions at any time. We will provide notice of significant changes by:</p> <ul> <li>Posting a banner on our website.</li> <li>Sending an email to registered users (if applicable).</li> <li>Updating the "Last updated" date at the top of this page.</li> </ul> <p>Your continued use of the platform after such modifications constitutes your acceptance of the revised Terms and Conditions.</p> <h2>8. Termination</h2> <p>VapeHybrid may, in its sole discretion, suspend or terminate your access to the platform for any reason, including but not limited to:</p> <ul> <li>Violation of these Terms and Conditions.</li> <li>Engaging in fraudulent or illegal activities.</li> <li>Behavior that is harmful to other users, third parties, or the business interests of VapeHybrid.</li> </ul> <p>Upon termination, your right to use the platform will immediately cease.</p> <h2>9. Contact Information</h2> <p>If you have any questions or concerns about these Terms and Conditions, please contact us at:</p> <p>Email: <a href="mailto:<EMAIL>"><EMAIL></a></p> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/terms.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/terms.astro";
const $$url = "/terms";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Terms,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
