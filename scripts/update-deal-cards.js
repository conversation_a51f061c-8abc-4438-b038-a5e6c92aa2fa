/**
 * Update Deal Cards Script
 * 
 * This script updates all instances of ImprovedDealCard to OptimizedDealCard
 * throughout the codebase.
 * 
 * Usage:
 * node scripts/update-deal-cards.js
 */

const fs = require('fs');
const path = require('path');
const { promisify } = require('util');

const readdir = promisify(fs.readdir);
const readFile = promisify(fs.readFile);
const writeFile = promisify(fs.writeFile);
const stat = promisify(fs.stat);

// Configuration
const rootDir = path.resolve(__dirname, '..');
const excludeDirs = ['node_modules', '.git', 'dist', 'build', '.astro'];
const fileExtensions = ['.tsx', '.jsx', '.astro'];

// Patterns to replace
const importPattern = /import\s+\{\s*ImprovedDealCard\s*\}\s*from\s*['"](.+?)['"];?/g;
const componentPattern = /<ImprovedDealCard([^>]*)>/g;
const closingTagPattern = /<\/ImprovedDealCard>/g;

// Counters for reporting
let filesScanned = 0;
let filesModified = 0;
let importsReplaced = 0;
let componentsReplaced = 0;

/**
 * Check if a directory should be excluded
 */
function shouldExcludeDir(dirPath) {
  const dirName = path.basename(dirPath);
  return excludeDirs.includes(dirName);
}

/**
 * Check if a file should be processed
 */
function shouldProcessFile(filePath) {
  const ext = path.extname(filePath).toLowerCase();
  return fileExtensions.includes(ext);
}

/**
 * Process a single file
 */
async function processFile(filePath) {
  try {
    filesScanned++;
    
    // Read the file content
    const content = await readFile(filePath, 'utf8');
    
    // Skip files that don't contain ImprovedDealCard
    if (!content.includes('ImprovedDealCard')) {
      return;
    }
    
    // Replace import statements
    let newContent = content.replace(importPattern, (match, importPath) => {
      importsReplaced++;
      return `import { OptimizedDealCard } from "${importPath}";`;
    });
    
    // Replace component opening tags
    newContent = newContent.replace(componentPattern, (match, attributes) => {
      componentsReplaced++;
      return `<OptimizedDealCard${attributes}>`;
    });
    
    // Replace component closing tags
    newContent = newContent.replace(closingTagPattern, '</OptimizedDealCard>');
    
    // Only write the file if changes were made
    if (newContent !== content) {
      await writeFile(filePath, newContent, 'utf8');
      filesModified++;
      console.log(`Updated: ${path.relative(rootDir, filePath)}`);
    }
  } catch (error) {
    console.error(`Error processing file ${filePath}:`, error);
  }
}

/**
 * Recursively scan directories and process files
 */
async function scanDirectory(dirPath) {
  try {
    const entries = await readdir(dirPath, { withFileTypes: true });
    
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      
      if (entry.isDirectory()) {
        if (!shouldExcludeDir(fullPath)) {
          await scanDirectory(fullPath);
        }
      } else if (entry.isFile() && shouldProcessFile(fullPath)) {
        await processFile(fullPath);
      }
    }
  } catch (error) {
    console.error(`Error scanning directory ${dirPath}:`, error);
  }
}

/**
 * Main function
 */
async function main() {
  console.log('Starting to update deal card components...');
  
  const startTime = Date.now();
  
  try {
    await scanDirectory(rootDir);
    
    const endTime = Date.now();
    const duration = ((endTime - startTime) / 1000).toFixed(2);
    
    console.log('\nUpdate completed successfully!');
    console.log(`Files scanned: ${filesScanned}`);
    console.log(`Files modified: ${filesModified}`);
    console.log(`Import statements replaced: ${importsReplaced}`);
    console.log(`Component instances replaced: ${componentsReplaced}`);
    console.log(`Time taken: ${duration} seconds`);
  } catch (error) {
    console.error('Error during update process:', error);
    process.exit(1);
  }
}

// Run the script
main();
