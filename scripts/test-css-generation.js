/**
 * Test script for CSS generation
 * 
 * This script tests the CSS generation process to ensure that it works correctly
 * after the changes to the design tokens file structure.
 */

import { execSync } from 'child_process';
import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// Get the directory name of the current module
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

console.log('Testing CSS generation process...');

try {
  // Run the CSS generation script
  console.log('Running generate-css-variables.js...');
  execSync('node scripts/generate-css-variables.js', { stdio: 'inherit' });
  
  // Check if the generated CSS file exists
  const cssPath = path.join(__dirname, '../src/styles/generated-variables.css');
  if (fs.existsSync(cssPath)) {
    console.log('✅ CSS file generated successfully!');
    
    // Check if the CSS file contains the expected content
    const cssContent = fs.readFileSync(cssPath, 'utf8');
    
    if (cssContent.includes('--color-primary:') && 
        cssContent.includes('--font-sans:') && 
        cssContent.includes('--duration-fast:')) {
      console.log('✅ CSS file contains expected content!');
    } else {
      console.error('❌ CSS file does not contain expected content!');
      process.exit(1);
    }
  } else {
    console.error('❌ CSS file not generated!');
    process.exit(1);
  }
  
  console.log('✅ CSS generation test passed!');
} catch (error) {
  console.error('❌ CSS generation test failed!', error);
  process.exit(1);
}
