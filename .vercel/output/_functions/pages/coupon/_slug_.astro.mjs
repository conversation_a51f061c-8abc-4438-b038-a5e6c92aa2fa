import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead, F as Fragment, b as addAttribute } from '../../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../../assets/js/MainLayout-BVJnMCp3.js';
import { c as createServerSupabaseClient } from '../../assets/js/server-e_5TR1Eu.js';
import { e as copyToClipboard, B as BookmarkButton, O as OptimizedDealCard } from '../../assets/js/app/design-system-CUssmfny.js';
import { jsxs, jsx } from 'react/jsx-runtime';
import { useState, useCallback } from 'react';
import { ThumbsUp, ThumbsDown } from 'lucide-react';
import { C as CouponPagePopup } from '../../assets/js/CouponPagePopup-DcOOX4eE.js';
export { renderers } from '../../renderers.mjs';

const CouponActionButton = ({ deal, isExpired }) => {
  const [isCodeRevealed, setIsCodeRevealed] = useState(false);
  const handleButtonClick = useCallback(() => {
    setIsCodeRevealed(true);
    if (typeof window !== "undefined") {
      const clickedDeals = JSON.parse(localStorage.getItem("clickedDeals") || "{}");
      const hasBeenClickedBefore = clickedDeals[deal.id];
      const revealedCodes = JSON.parse(localStorage.getItem("revealedCodes") || "{}");
      revealedCodes[deal.id] = true;
      localStorage.setItem("revealedCodes", JSON.stringify(revealedCodes));
      clickedDeals[deal.id] = true;
      localStorage.setItem("clickedDeals", JSON.stringify(clickedDeals));
      if (deal.coupon_code) {
        copyToClipboard(deal.coupon_code);
      }
      const currentUrl = new URL(window.location.href);
      const viewMode = currentUrl.searchParams.get("view") || "grid";
      const popupUrl = new URL(window.location.href);
      popupUrl.searchParams.set("dealId", deal.id.toString());
      popupUrl.searchParams.set("showPopup", "true");
      popupUrl.searchParams.set("view", viewMode);
      window.open(popupUrl.toString(), "_blank");
      if (!hasBeenClickedBefore && deal.tracking_url) {
        window.location.href = deal.tracking_url;
      } else if (!hasBeenClickedBefore) {
        window.location.href = `/go/${deal.id}`;
      }
    }
  }, [deal]);
  return /* @__PURE__ */ jsxs("div", { className: "text-center", children: [
    /* @__PURE__ */ jsx("div", { className: "text-3xl font-bold text-primary mb-2", children: deal.discount }),
    /* @__PURE__ */ jsx("p", { className: "text-design-muted-foreground mb-4", children: "Discount Available" }),
    deal.coupon_code && /* @__PURE__ */ jsxs("div", { className: "mb-4 p-3 bg-design-muted/10 border border-design-border rounded-lg", children: [
      /* @__PURE__ */ jsx("p", { className: "text-sm text-design-muted-foreground mb-1", children: "Coupon Code:" }),
      /* @__PURE__ */ jsxs("div", { className: "relative", children: [
        /* @__PURE__ */ jsx(
          "code",
          {
            className: `text-lg font-mono font-bold text-design-foreground transition-all duration-300 ${isCodeRevealed ? "blur-none opacity-100 select-all" : "blur-[3px] select-none"}`,
            children: deal.coupon_code
          }
        ),
        isCodeRevealed && /* @__PURE__ */ jsx(
          "span",
          {
            className: "absolute inset-0 bg-design-primary/10 animate-reveal-sweep",
            "aria-hidden": "true"
          }
        )
      ] })
    ] }),
    !isExpired ? /* @__PURE__ */ jsx(
      "button",
      {
        onClick: handleButtonClick,
        className: "w-full bg-primary text-white py-3 px-6 rounded-lg hover:bg-primary/90 transition-colors font-semibold text-lg",
        children: deal.coupon_code ? "Reveal Code & Get Deal" : "Get Deal"
      }
    ) : /* @__PURE__ */ jsx(
      "button",
      {
        disabled: true,
        className: "w-full bg-design-muted text-design-muted-foreground py-3 px-6 rounded-lg cursor-not-allowed",
        children: "Coupon Expired"
      }
    )
  ] });
};

const CouponVotingButton = ({ dealId }) => {
  const [voteType, setVoteType] = useState(null);
  const handleVote = useCallback((type) => {
    if (voteType) return;
    setVoteType(type);
    if (typeof window !== "undefined" && typeof navigator.sendBeacon === "function") {
      try {
        navigator.sendBeacon("/api/track-click", JSON.stringify({
          dealId: dealId.toString(),
          action: `vote_${type}`,
          timestamp: Date.now()
        }));
      } catch (error) {
        console.warn("Failed to send vote tracking beacon:", error);
      }
    }
  }, [dealId, voteType]);
  return /* @__PURE__ */ jsxs("div", { className: "flex items-center gap-2", children: [
    /* @__PURE__ */ jsxs(
      "button",
      {
        onClick: () => handleVote("up"),
        disabled: !!voteType,
        className: `flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors ${voteType === "up" ? "bg-green-100 text-green-600 border-green-300 cursor-not-allowed" : voteType === "down" ? "opacity-50 cursor-not-allowed border-design-border" : "border-design-border hover:bg-design-muted/10"}`,
        children: [
          /* @__PURE__ */ jsx(ThumbsUp, { size: 16, className: voteType === "up" ? "fill-current" : "" }),
          /* @__PURE__ */ jsx("span", { className: "text-sm", children: voteType === "up" ? "Thanks!" : "Helpful" })
        ]
      }
    ),
    /* @__PURE__ */ jsxs(
      "button",
      {
        onClick: () => handleVote("down"),
        disabled: !!voteType,
        className: `flex items-center gap-2 px-3 py-2 rounded-lg border transition-colors ${voteType === "down" ? "bg-red-100 text-red-600 border-red-300 cursor-not-allowed" : voteType === "up" ? "opacity-50 cursor-not-allowed border-design-border" : "border-design-border hover:bg-design-muted/10"}`,
        children: [
          /* @__PURE__ */ jsx(ThumbsDown, { size: 16, className: voteType === "down" ? "fill-current" : "" }),
          /* @__PURE__ */ jsx("span", { className: "text-sm", children: voteType === "down" ? "Not Helpful" : "Not Helpful" })
        ]
      }
    )
  ] });
};

const CouponBookmarkButton = ({ dealId }) => {
  return /* @__PURE__ */ jsx(
    BookmarkButton,
    {
      dealId: dealId.toString(),
      className: "flex items-center gap-2 px-3 py-2 rounded-lg border border-design-border hover:bg-design-muted/10 transition-colors",
      showText: true
    }
  );
};

const $$Astro = createAstro("http://localhost:4321");
const $$slug = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$slug;
  const { slug } = Astro2.params;
  if (!slug) {
    return Astro2.redirect("/coupons", 302);
  }
  const supabase = createServerSupabaseClient();
  let deal = null;
  const { data: dealBySlug, error: slugError } = await supabase.from("deals").select(`
    *,
    merchants:merchant_id (name, website_url, logo_url),
    brands:brand_id (name, logo_url, slug),
    categories:category_id (name, slug)
  `).eq("slug", slug).single();
  if (dealBySlug) {
    deal = dealBySlug;
  } else {
    if (!isNaN(Number(slug))) {
      const { data: dealById, error: idError } = await supabase.from("deals").select(`
        *,
        merchants:merchant_id (name, website_url, logo_url),
        brands:brand_id (name, logo_url, slug),
        categories:category_id (name, slug)
      `).eq("id", parseInt(slug)).single();
      if (dealById) {
        deal = dealById;
      }
    }
  }
  if (!deal) {
    console.error("Coupon not found for slug:", slug);
    const possibleId = slug.match(/\d+$/)?.[0];
    if (possibleId) {
      console.log("Attempting fallback redirect to deal ID:", possibleId);
      return Astro2.redirect(`/deal/${possibleId}`, 302);
    }
    return Astro2.redirect("/coupons", 302);
  }
  const now = /* @__PURE__ */ new Date();
  const isExpired = deal.deal_end_date && new Date(deal.deal_end_date) < now;
  const { data: relatedCoupons } = await supabase.from("deals").select(`
    *,
    merchants:merchant_id (name, website_url, logo_url),
    brands:brand_id (name, logo_url, slug),
    categories:category_id (name, slug)
  `).or(`brand_id.eq.${deal.brand_id},category_id.eq.${deal.category_id}`).neq("id", deal.id).gt("deal_end_date", now.toISOString()).limit(8);
  const title = `${deal.title} - ${deal.discount} Coupon Code | VapeHybrid`;
  const description = `Get ${deal.discount} off with this verified ${deal.title} coupon code. ${deal.description || "Save on premium vape products"} - Updated daily with real-time verification.`;
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "Offer",
    name: deal.title,
    description: deal.description || `Save ${deal.discount} on ${deal.title}`,
    url: Astro2.url.href,
    image: deal.imagebig_url || deal.image_url,
    price: deal.price || void 0,
    priceCurrency: "USD",
    availability: isExpired ? "https://schema.org/OutOfStock" : "https://schema.org/InStock",
    validThrough: deal.deal_end_date,
    priceValidUntil: deal.deal_end_date,
    brand: deal.brands ? {
      "@type": "Brand",
      name: deal.brands.name
    } : void 0,
    seller: {
      "@type": "Organization",
      name: deal.merchants?.name || "VapeHybrid Partner",
      url: deal.merchants?.website_url
    },
    category: deal.categories?.name
  };
  const timeRemaining = deal.deal_end_date ? Math.max(0, new Date(deal.deal_end_date).getTime() - now.getTime()) : 0;
  const daysRemaining = Math.floor(timeRemaining / (1e3 * 60 * 60 * 24));
  const hoursRemaining = Math.floor(timeRemaining % (1e3 * 60 * 60 * 24) / (1e3 * 60 * 60));
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": async ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-6 pb-12"> <!-- Breadcrumbs --> <nav class="mb-6 text-center" aria-label="Breadcrumb"> <ol class="inline-flex items-center space-x-1 text-xs text-design-muted-foreground"> <li><a href="/" class="hover:text-design-foreground transition-colors">Home</a></li> <li><span class="mx-1">/</span></li> <li><a href="/coupons" class="hover:text-design-foreground transition-colors">Coupons</a></li> ${deal.brands && renderTemplate`${renderComponent($$result2, "Fragment", Fragment, {}, { "default": async ($$result3) => renderTemplate` <li><span class="mx-1">/</span></li> <li><a${addAttribute(`/coupons/brands/${deal.brands.slug}`, "href")} class="hover:text-design-foreground transition-colors">${deal.brands.name}</a></li> ` })}`} <li><span class="mx-1">/</span></li> <li class="text-design-foreground font-medium">${deal.title}</li> </ol> </nav>  ${isExpired && renderTemplate`<div class="mb-6 p-4 bg-design-destructive/10 border border-design-destructive/20 rounded-lg text-center"> <div class="flex items-center justify-center"> <svg class="w-5 h-5 text-design-destructive mr-2" fill="currentColor" viewBox="0 0 20 20"> <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z" clip-rule="evenodd"></path> </svg> <span class="text-design-destructive font-medium">This coupon has expired</span> </div> </div>`} <div class="grid grid-cols-1 lg:grid-cols-3 gap-8"> <!-- Main Content --> <div class="lg:col-span-2"> <!-- Coupon Header --> <div class="bg-design-card border border-design-border rounded-xl p-8 mb-8 text-center"> <!-- Coupon Image --> <div class="flex justify-center mb-6"> <div class="w-32 h-32 rounded-lg bg-design-muted/10 flex items-center justify-center overflow-hidden"> ${deal.imagebig_url || deal.image_url ? renderTemplate`<img${addAttribute(deal.imagebig_url || deal.image_url, "src")}${addAttribute(deal.title, "alt")} class="w-full h-full object-cover rounded-lg" loading="eager">` : renderTemplate`<div class="w-full h-full bg-primary/10 flex items-center justify-center"> <span class="text-primary font-bold text-3xl">${deal.title.charAt(0)}</span> </div>`} </div> </div> <!-- Coupon Info --> <h1 class="text-2xl md:text-3xl font-bold text-design-foreground mb-4"> ${deal.title} </h1> <!-- Discount Badge --> <div class="flex justify-center mb-6"> <span class="inline-flex items-center px-6 py-3 rounded-full text-xl font-bold bg-primary text-white"> ${deal.discount} </span> </div> <!-- Brand and Merchant Info --> <div class="flex flex-wrap justify-center gap-4 text-sm text-design-muted-foreground mb-6"> ${deal.brands && renderTemplate`<div class="flex items-center"> <span class="font-medium">Brand:</span> <a${addAttribute(`/coupons/brands/${deal.brands.slug}`, "href")} class="ml-1 text-primary hover:underline"> ${deal.brands.name} </a> </div>`} ${deal.merchants && renderTemplate`<div class="flex items-center"> <span class="font-medium">Store:</span> <span class="ml-1">${deal.merchants.name}</span> </div>`} ${deal.categories && renderTemplate`<div class="flex items-center"> <span class="font-medium">Category:</span> <a${addAttribute(`/coupons/categories/${deal.categories.slug}`, "href")} class="ml-1 text-primary hover:underline"> ${deal.categories.name} </a> </div>`} </div> <!-- Expiration Info --> ${deal.deal_end_date && !isExpired && renderTemplate`<div class="text-sm text-design-muted-foreground"> <span class="font-medium">Expires:</span> <span class="ml-1"> ${daysRemaining > 0 ? `${daysRemaining} days` : `${hoursRemaining} hours`} remaining
</span> </div>`} </div> <!-- Coupon Description --> ${deal.description && renderTemplate`<div class="bg-design-card border border-design-border rounded-xl p-6 mb-6"> <h2 class="text-lg font-semibold text-design-foreground mb-3">About This Coupon</h2> <p class="text-design-muted-foreground leading-relaxed">${deal.description}</p> </div>`} <!-- How to Use Section --> <div class="bg-design-card border border-design-border rounded-xl p-6 mb-6"> <h2 class="text-lg font-semibold text-design-foreground mb-4">How to Use This Coupon</h2> <ol class="list-decimal list-inside space-y-2 text-design-muted-foreground"> <li>Click the "Get Coupon" button below</li> <li>You'll be redirected to the merchant's website</li> <li>Add your desired items to cart</li> <li>Apply the coupon code at checkout</li> <li>Enjoy your savings!</li> </ol> </div> </div> <!-- Sidebar --> <div class="lg:col-span-1"> <!-- Get Coupon Card --> <div class="bg-design-card border border-design-border rounded-xl p-6 mb-6 sticky top-6"> ${renderComponent($$result2, "CouponActionButton", CouponActionButton, { "deal": deal, "isExpired": isExpired, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponActionButton", "client:component-export": "CouponActionButton" })} <!-- Voting and Bookmark Section --> <div class="mt-6 flex flex-col items-center gap-4"> ${renderComponent($$result2, "CouponVotingButton", CouponVotingButton, { "dealId": deal.id, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponVotingButton", "client:component-export": "CouponVotingButton" })} ${renderComponent($$result2, "CouponBookmarkButton", CouponBookmarkButton, { "dealId": deal.id, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponBookmarkButton", "client:component-export": "CouponBookmarkButton" })} </div> </div> <!-- Coupon Stats --> <div class="bg-design-card border border-design-border rounded-xl p-6 mb-6"> <h3 class="text-lg font-semibold text-design-foreground mb-4">Coupon Statistics</h3> <div class="space-y-3"> <div class="flex justify-between"> <span class="text-design-muted-foreground">Success Rate</span> <span class="font-semibold text-design-foreground">98%</span> </div> <div class="flex justify-between"> <span class="text-design-muted-foreground">Times Used</span> <span class="font-semibold text-design-foreground">${deal.click_count || 0}</span> </div> <div class="flex justify-between"> <span class="text-design-muted-foreground">Last Verified</span> <span class="font-semibold text-design-foreground">Today</span> </div> </div> </div> </div> </div> <!-- Related Coupons --> ${relatedCoupons && relatedCoupons.length > 0 && renderTemplate`<div class="mt-12"> <h3 class="text-2xl font-bold text-design-foreground mb-8 text-center">Related Coupons</h3> <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6"> ${relatedCoupons.map((relatedCoupon) => renderTemplate`${renderComponent($$result2, "OptimizedDealCard", OptimizedDealCard, { "deal": relatedCoupon, "viewMode": "grid", "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Card/OptimizedDealCard", "client:component-export": "OptimizedDealCard" })}`)} </div> </div>`} </div>  ${renderComponent($$result2, "CouponPagePopup", CouponPagePopup, { "deal": deal, "client:load": true, "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponPagePopup", "client:component-export": "CouponPagePopup" })} ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupon/[slug].astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupon/[slug].astro";
const $$url = "/coupon/[slug]";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$slug,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
