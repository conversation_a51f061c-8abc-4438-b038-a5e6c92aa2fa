import 'kleur/colors';
import { h as decodeKey } from './assets/js/astro/server-CU4H3-CM.js';
import 'clsx';
import 'cookie';
import './assets/js/astro-designed-error-pages-BPSgjeYS.js';
import 'es-module-lexer';
import { N as NOOP_MIDDLEWARE_FN } from './assets/js/noop-middleware-DbLitOMC.js';

function sanitizeParams(params) {
  return Object.fromEntries(
    Object.entries(params).map(([key, value]) => {
      if (typeof value === "string") {
        return [key, value.normalize().replace(/#/g, "%23").replace(/\?/g, "%3F")];
      }
      return [key, value];
    })
  );
}
function getParameter(part, params) {
  if (part.spread) {
    return params[part.content.slice(3)] || "";
  }
  if (part.dynamic) {
    if (!params[part.content]) {
      throw new TypeError(`Missing parameter: ${part.content}`);
    }
    return params[part.content];
  }
  return part.content.normalize().replace(/\?/g, "%3F").replace(/#/g, "%23").replace(/%5B/g, "[").replace(/%5D/g, "]");
}
function getSegment(segment, params) {
  const segmentPath = segment.map((part) => getParameter(part, params)).join("");
  return segmentPath ? "/" + segmentPath : "";
}
function getRouteGenerator(segments, addTrailingSlash) {
  return (params) => {
    const sanitizedParams = sanitizeParams(params);
    let trailing = "";
    if (addTrailingSlash === "always" && segments.length) {
      trailing = "/";
    }
    const path = segments.map((segment) => getSegment(segment, sanitizedParams)).join("") + trailing;
    return path || "/";
  };
}

function deserializeRouteData(rawRouteData) {
  return {
    route: rawRouteData.route,
    type: rawRouteData.type,
    pattern: new RegExp(rawRouteData.pattern),
    params: rawRouteData.params,
    component: rawRouteData.component,
    generate: getRouteGenerator(rawRouteData.segments, rawRouteData._meta.trailingSlash),
    pathname: rawRouteData.pathname || void 0,
    segments: rawRouteData.segments,
    prerender: rawRouteData.prerender,
    redirect: rawRouteData.redirect,
    redirectRoute: rawRouteData.redirectRoute ? deserializeRouteData(rawRouteData.redirectRoute) : void 0,
    fallbackRoutes: rawRouteData.fallbackRoutes.map((fallback) => {
      return deserializeRouteData(fallback);
    }),
    isIndex: rawRouteData.isIndex,
    origin: rawRouteData.origin
  };
}

function deserializeManifest(serializedManifest) {
  const routes = [];
  for (const serializedRoute of serializedManifest.routes) {
    routes.push({
      ...serializedRoute,
      routeData: deserializeRouteData(serializedRoute.routeData)
    });
    const route = serializedRoute;
    route.routeData = deserializeRouteData(serializedRoute.routeData);
  }
  const assets = new Set(serializedManifest.assets);
  const componentMetadata = new Map(serializedManifest.componentMetadata);
  const inlinedScripts = new Map(serializedManifest.inlinedScripts);
  const clientDirectives = new Map(serializedManifest.clientDirectives);
  const serverIslandNameMap = new Map(serializedManifest.serverIslandNameMap);
  const key = decodeKey(serializedManifest.key);
  return {
    // in case user middleware exists, this no-op middleware will be reassigned (see plugin-ssr.ts)
    middleware() {
      return { onRequest: NOOP_MIDDLEWARE_FN };
    },
    ...serializedManifest,
    assets,
    componentMetadata,
    inlinedScripts,
    clientDirectives,
    routes,
    serverIslandNameMap,
    key
  };
}

const manifest = deserializeManifest({"hrefRoot":"file:///C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/","cacheDir":"file:///C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/.astro/","outDir":"file:///C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/dist/","srcDir":"file:///C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/","publicDir":"file:///C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/public/","buildClientDir":"file:///C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/dist/client/","buildServerDir":"file:///C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/dist/server/","adapterName":"@astrojs/vercel","routes":[{"file":"","links":[],"scripts":[],"styles":[],"routeData":{"type":"page","component":"_server-islands.astro","params":["name"],"segments":[[{"content":"_server-islands","dynamic":false,"spread":false}],[{"content":"name","dynamic":true,"spread":false}]],"pattern":"^\\/_server-islands\\/([^/]+?)\\/?$","prerender":false,"isIndex":false,"fallbackRoutes":[],"route":"/_server-islands/[name]","origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"type":"endpoint","isIndex":false,"route":"/_image","pattern":"^\\/_image\\/?$","segments":[[{"content":"_image","dynamic":false,"spread":false}]],"params":[],"component":"node_modules/astro/dist/assets/endpoint/generic.js","pathname":"/_image","prerender":false,"fallbackRoutes":[],"origin":"internal","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/404","isIndex":false,"type":"page","pattern":"^\\/404\\/?$","segments":[[{"content":"404","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/404.astro","pathname":"/404","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/about","isIndex":false,"type":"page","pattern":"^\\/about\\/?$","segments":[[{"content":"about","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/about.astro","pathname":"/about","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/affiliate-disclosure","isIndex":false,"type":"page","pattern":"^\\/affiliate-disclosure\\/?$","segments":[[{"content":"affiliate-disclosure","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/affiliate-disclosure.astro","pathname":"/affiliate-disclosure","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/alerts/admin-notification","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/alerts\\/admin-notification\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"alerts","dynamic":false,"spread":false}],[{"content":"admin-notification","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/alerts/admin-notification.ts","pathname":"/api/alerts/admin-notification","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/alerts/manage-subscriptions","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/alerts\\/manage-subscriptions\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"alerts","dynamic":false,"spread":false}],[{"content":"manage-subscriptions","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/alerts/manage-subscriptions.ts","pathname":"/api/alerts/manage-subscriptions","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/alerts/send-brand-alert","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/alerts\\/send-brand-alert\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"alerts","dynamic":false,"spread":false}],[{"content":"send-brand-alert","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/alerts/send-brand-alert.ts","pathname":"/api/alerts/send-brand-alert","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/alerts/send-merchant-alert","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/alerts\\/send-merchant-alert\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"alerts","dynamic":false,"spread":false}],[{"content":"send-merchant-alert","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/alerts/send-merchant-alert.ts","pathname":"/api/alerts/send-merchant-alert","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/alerts/subscribe-multi","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/alerts\\/subscribe-multi\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"alerts","dynamic":false,"spread":false}],[{"content":"subscribe-multi","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/alerts/subscribe-multi.ts","pathname":"/api/alerts/subscribe-multi","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/alerts/trigger-new-coupon","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/alerts\\/trigger-new-coupon\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"alerts","dynamic":false,"spread":false}],[{"content":"trigger-new-coupon","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/alerts/trigger-new-coupon.ts","pathname":"/api/alerts/trigger-new-coupon","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/brands","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/brands\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"brands","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/brands.ts","pathname":"/api/brands","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/categories","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/categories\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"categories","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/categories.ts","pathname":"/api/categories","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/contact","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/contact\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"contact","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/contact.ts","pathname":"/api/contact","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/create-tables","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/create-tables\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"create-tables","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/create-tables.ts","pathname":"/api/create-tables","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/database/create-tables-direct","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/database\\/create-tables-direct\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"database","dynamic":false,"spread":false}],[{"content":"create-tables-direct","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/database/create-tables-direct.ts","pathname":"/api/database/create-tables-direct","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/database/setup-multi-subscription-direct","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/database\\/setup-multi-subscription-direct\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"database","dynamic":false,"spread":false}],[{"content":"setup-multi-subscription-direct","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/database/setup-multi-subscription-direct.ts","pathname":"/api/database/setup-multi-subscription-direct","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/database/setup-multi-subscription-system","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/database\\/setup-multi-subscription-system\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"database","dynamic":false,"spread":false}],[{"content":"setup-multi-subscription-system","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/database/setup-multi-subscription-system.ts","pathname":"/api/database/setup-multi-subscription-system","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/deals","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/deals\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"deals","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/deals.ts","pathname":"/api/deals","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/debug/direct-supabase-test","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/debug\\/direct-supabase-test\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"debug","dynamic":false,"spread":false}],[{"content":"direct-supabase-test","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/debug/direct-supabase-test.ts","pathname":"/api/debug/direct-supabase-test","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/debug/env-check","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/debug\\/env-check\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"debug","dynamic":false,"spread":false}],[{"content":"env-check","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/debug/env-check.ts","pathname":"/api/debug/env-check","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/debug/supabase-test","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/debug\\/supabase-test\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"debug","dynamic":false,"spread":false}],[{"content":"supabase-test","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/debug/supabase-test.ts","pathname":"/api/debug/supabase-test","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/direct-setup","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/direct-setup\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"direct-setup","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/direct-setup.ts","pathname":"/api/direct-setup","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/fix-rls","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/fix-rls\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"fix-rls","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/fix-rls.ts","pathname":"/api/fix-rls","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/health","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/health\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"health","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/health.ts","pathname":"/api/health","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/merchants","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/merchants\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"merchants","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/merchants.ts","pathname":"/api/merchants","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/add-enhanced-columns","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/add-enhanced-columns\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"add-enhanced-columns","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/add-enhanced-columns.ts","pathname":"/api/newsletter/add-enhanced-columns","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/confirm","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/confirm\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"confirm","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/confirm.ts","pathname":"/api/newsletter/confirm","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/consolidate-tables","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/consolidate-tables\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"consolidate-tables","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/consolidate-tables.ts","pathname":"/api/newsletter/consolidate-tables","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/create-preferences-table","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/create-preferences-table\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"create-preferences-table","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/create-preferences-table.ts","pathname":"/api/newsletter/create-preferences-table","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/get-preferences","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/get-preferences\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"get-preferences","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/get-preferences.ts","pathname":"/api/newsletter/get-preferences","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/migrate-preferences","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/migrate-preferences\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"migrate-preferences","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/migrate-preferences.ts","pathname":"/api/newsletter/migrate-preferences","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/subscribe","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/subscribe\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"subscribe","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/subscribe.ts","pathname":"/api/newsletter/subscribe","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/unsubscribe","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/unsubscribe\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"unsubscribe","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/unsubscribe.ts","pathname":"/api/newsletter/unsubscribe","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/update-preferences","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/update-preferences\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"update-preferences","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/update-preferences.ts","pathname":"/api/newsletter/update-preferences","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/newsletter/user-subscribe","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/newsletter\\/user-subscribe\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"user-subscribe","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/newsletter/user-subscribe.ts","pathname":"/api/newsletter/user-subscribe","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/performance/vitals","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/performance\\/vitals\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"performance","dynamic":false,"spread":false}],[{"content":"vitals","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/performance/vitals.ts","pathname":"/api/performance/vitals","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/setup-db","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/setup-db\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"setup-db","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/setup-db.ts","pathname":"/api/setup-db","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/track-click","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/track-click\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"track-click","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/track-click.ts","pathname":"/api/track-click","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/track-impression","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/track-impression\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"track-impression","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/track-impression.ts","pathname":"/api/track-impression","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/vote","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/vote\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"vote","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/vote.ts","pathname":"/api/vote","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/api/vote-counts","isIndex":false,"type":"endpoint","pattern":"^\\/api\\/vote-counts\\/?$","segments":[[{"content":"api","dynamic":false,"spread":false}],[{"content":"vote-counts","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/api/vote-counts.ts","pathname":"/api/vote-counts","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/blog","isIndex":false,"type":"page","pattern":"^\\/blog\\/?$","segments":[[{"content":"blog","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/blog.astro","pathname":"/blog","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/bookmarks","isIndex":false,"type":"page","pattern":"^\\/bookmarks\\/?$","segments":[[{"content":"bookmarks","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/bookmarks.astro","pathname":"/bookmarks","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/brands/[slug]","isIndex":false,"type":"page","pattern":"^\\/brands\\/([^/]+?)\\/?$","segments":[[{"content":"brands","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/brands/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/brands","isIndex":false,"type":"page","pattern":"^\\/brands\\/?$","segments":[[{"content":"brands","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/brands.astro","pathname":"/brands","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/card-comparison","isIndex":false,"type":"page","pattern":"^\\/card-comparison\\/?$","segments":[[{"content":"card-comparison","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/card-comparison.astro","pathname":"/card-comparison","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/categories/[slug]","isIndex":false,"type":"page","pattern":"^\\/categories\\/([^/]+?)\\/?$","segments":[[{"content":"categories","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/categories/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/categories","isIndex":false,"type":"page","pattern":"^\\/categories\\/?$","segments":[[{"content":"categories","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/categories.astro","pathname":"/categories","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/comprehensive-review-analysis","isIndex":false,"type":"page","pattern":"^\\/comprehensive-review-analysis\\/?$","segments":[[{"content":"comprehensive-review-analysis","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/comprehensive-review-analysis.astro","pathname":"/comprehensive-review-analysis","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/contact","isIndex":false,"type":"page","pattern":"^\\/contact\\/?$","segments":[[{"content":"contact","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/contact.astro","pathname":"/contact","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/cookie-policy","isIndex":false,"type":"page","pattern":"^\\/cookie-policy\\/?$","segments":[[{"content":"cookie-policy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/cookie-policy.astro","pathname":"/cookie-policy","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/coupon/[slug]","isIndex":false,"type":"page","pattern":"^\\/coupon\\/([^/]+?)\\/?$","segments":[[{"content":"coupon","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/coupon/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\nhtml{scroll-behavior:smooth}.brand-page[data-astro-cid-mzab7qh6] .merchant-deal-list-card[data-astro-cid-mzab7qh6]+.merchant-deal-list-card[data-astro-cid-mzab7qh6]{margin-top:1.5rem}\n"}],"routeData":{"route":"/coupons/brands/[slug]","isIndex":false,"type":"page","pattern":"^\\/coupons\\/brands\\/([^/]+?)\\/?$","segments":[[{"content":"coupons","dynamic":false,"spread":false}],[{"content":"brands","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/coupons/brands/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/coupons/brands","isIndex":true,"type":"page","pattern":"^\\/coupons\\/brands\\/?$","segments":[[{"content":"coupons","dynamic":false,"spread":false}],[{"content":"brands","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/coupons/brands/index.astro","pathname":"/coupons/brands","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/coupons/categories/[slug]","isIndex":false,"type":"page","pattern":"^\\/coupons\\/categories\\/([^/]+?)\\/?$","segments":[[{"content":"coupons","dynamic":false,"spread":false}],[{"content":"categories","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/coupons/categories/[slug].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/coupons/categories","isIndex":true,"type":"page","pattern":"^\\/coupons\\/categories\\/?$","segments":[[{"content":"coupons","dynamic":false,"spread":false}],[{"content":"categories","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/coupons/categories/index.astro","pathname":"/coupons/categories","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n.merchant-page[data-astro-cid-thj6pv23] .merchant-deal-list-card[data-astro-cid-thj6pv23]{min-height:175px!important;max-height:175px!important}.merchant-right-column[data-astro-cid-thj6pv23]{min-width:730px}html{scroll-behavior:smooth}.merchant-page[data-astro-cid-thj6pv23] .merchant-deal-list-card[data-astro-cid-thj6pv23]+.merchant-deal-list-card[data-astro-cid-thj6pv23]{margin-top:1.5rem}\n"}],"routeData":{"route":"/coupons/merchants/[id]","isIndex":false,"type":"page","pattern":"^\\/coupons\\/merchants\\/([^/]+?)\\/?$","segments":[[{"content":"coupons","dynamic":false,"spread":false}],[{"content":"merchants","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}]],"params":["id"],"component":"src/pages/coupons/merchants/[id].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/coupons/merchants","isIndex":true,"type":"page","pattern":"^\\/coupons\\/merchants\\/?$","segments":[[{"content":"coupons","dynamic":false,"spread":false}],[{"content":"merchants","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/coupons/merchants/index.astro","pathname":"/coupons/merchants","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/coupons","isIndex":true,"type":"page","pattern":"^\\/coupons\\/?$","segments":[[{"content":"coupons","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/coupons/index.astro","pathname":"/coupons","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/deal/[id]","isIndex":false,"type":"page","pattern":"^\\/deal\\/([^/]+?)\\/?$","segments":[[{"content":"deal","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}]],"params":["id"],"component":"src/pages/deal/[id].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/deals","isIndex":false,"type":"page","pattern":"^\\/deals\\/?$","segments":[[{"content":"deals","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/deals.astro","pathname":"/deals","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/disclaimer","isIndex":false,"type":"page","pattern":"^\\/disclaimer\\/?$","segments":[[{"content":"disclaimer","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/disclaimer.astro","pathname":"/disclaimer","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/faq","isIndex":false,"type":"page","pattern":"^\\/faq\\/?$","segments":[[{"content":"faq","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/faq.astro","pathname":"/faq","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/go/deal/[id]","isIndex":false,"type":"endpoint","pattern":"^\\/go\\/deal\\/([^/]+?)\\/?$","segments":[[{"content":"go","dynamic":false,"spread":false}],[{"content":"deal","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}]],"params":["id"],"component":"src/pages/go/deal/[id].ts","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/go/merchant/[slug]","isIndex":false,"type":"endpoint","pattern":"^\\/go\\/merchant\\/([^/]+?)\\/?$","segments":[[{"content":"go","dynamic":false,"spread":false}],[{"content":"merchant","dynamic":false,"spread":false}],[{"content":"slug","dynamic":true,"spread":false}]],"params":["slug"],"component":"src/pages/go/merchant/[slug].ts","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/go/[id]","isIndex":false,"type":"page","pattern":"^\\/go\\/([^/]+?)\\/?$","segments":[[{"content":"go","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}]],"params":["id"],"component":"src/pages/go/[id].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/how-it-works","isIndex":false,"type":"page","pattern":"^\\/how-it-works\\/?$","segments":[[{"content":"how-it-works","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/how-it-works.astro","pathname":"/how-it-works","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"},{"type":"external","src":"/_astro/index.AjOd9ujj.css"}],"routeData":{"route":"/legal","isIndex":false,"type":"page","pattern":"^\\/legal\\/?$","segments":[[{"content":"legal","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/legal.astro","pathname":"/legal","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/merchants/[id]","isIndex":false,"type":"page","pattern":"^\\/merchants\\/([^/]+?)\\/?$","segments":[[{"content":"merchants","dynamic":false,"spread":false}],[{"content":"id","dynamic":true,"spread":false}]],"params":["id"],"component":"src/pages/merchants/[id].astro","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/merchants","isIndex":false,"type":"page","pattern":"^\\/merchants\\/?$","segments":[[{"content":"merchants","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/merchants.astro","pathname":"/merchants","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/newsletter/confirm","isIndex":false,"type":"page","pattern":"^\\/newsletter\\/confirm\\/?$","segments":[[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"confirm","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/newsletter/confirm.astro","pathname":"/newsletter/confirm","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/newsletter/error","isIndex":false,"type":"page","pattern":"^\\/newsletter\\/error\\/?$","segments":[[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"error","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/newsletter/error.astro","pathname":"/newsletter/error","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/newsletter/preferences","isIndex":false,"type":"page","pattern":"^\\/newsletter\\/preferences\\/?$","segments":[[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"preferences","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/newsletter/preferences.astro","pathname":"/newsletter/preferences","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/newsletter/success","isIndex":false,"type":"page","pattern":"^\\/newsletter\\/success\\/?$","segments":[[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"success","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/newsletter/success.astro","pathname":"/newsletter/success","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/newsletter/unsubscribed","isIndex":false,"type":"page","pattern":"^\\/newsletter\\/unsubscribed\\/?$","segments":[[{"content":"newsletter","dynamic":false,"spread":false}],[{"content":"unsubscribed","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/newsletter/unsubscribed.astro","pathname":"/newsletter/unsubscribed","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/privacy","isIndex":false,"type":"page","pattern":"^\\/privacy\\/?$","segments":[[{"content":"privacy","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/privacy.astro","pathname":"/privacy","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"},{"type":"external","src":"/_astro/index.AjOd9ujj.css"}],"routeData":{"route":"/search","isIndex":false,"type":"page","pattern":"^\\/search\\/?$","segments":[[{"content":"search","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/search.astro","pathname":"/search","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[],"routeData":{"route":"/sitemap.xml","isIndex":false,"type":"endpoint","pattern":"^\\/sitemap\\.xml\\/?$","segments":[[{"content":"sitemap.xml","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/sitemap.xml.ts","pathname":"/sitemap.xml","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/terms","isIndex":false,"type":"page","pattern":"^\\/terms\\/?$","segments":[[{"content":"terms","dynamic":false,"spread":false}]],"params":[],"component":"src/pages/terms.astro","pathname":"/terms","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}},{"file":"","links":[],"scripts":[{"type":"external","value":"/assets/js/page-DH0Fop0q.js"},{"stage":"head-inline","children":"window.va = window.va || function () { (window.vaq = window.vaq || []).push(arguments); };\n\t\tvar script = document.createElement('script');\n\t\tscript.defer = true;\n\t\tscript.src = '/_vercel/insights/script.js';\n\t\tvar head = document.querySelector('head');\n\t\thead.appendChild(script);\n\t"}],"styles":[{"type":"external","src":"/_astro/index.AjOd9ujj.css"},{"type":"inline","content":"picture[data-astro-cid-hdpspurq]{width:100%;height:100%;display:inline-block}img[data-astro-cid-hdpspurq]{max-width:100%;height:auto;display:block}\n"}],"routeData":{"route":"/","isIndex":true,"type":"page","pattern":"^\\/$","segments":[],"params":[],"component":"src/pages/index.astro","pathname":"/","prerender":false,"fallbackRoutes":[],"distURL":[],"origin":"project","_meta":{"trailingSlash":"ignore"}}}],"site":"http://localhost:4321","base":"/","trailingSlash":"ignore","compressHTML":true,"componentMetadata":[["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/index.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/about.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/contact.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/faq.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/how-it-works.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/affiliate-disclosure.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/cookie-policy.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/disclaimer.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/legal.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/privacy.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/terms.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/search.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/404.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/blog.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/bookmarks.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/card-comparison.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/comprehensive-review-analysis.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupon/[slug].astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/brands/[slug].astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/brands/index.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/categories/[slug].astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/categories/index.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/index.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/merchants/[id].astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/coupons/merchants/index.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/merchants.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/error.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/preferences.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/success.astro",{"propagation":"none","containsHead":true}],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/newsletter/unsubscribed.astro",{"propagation":"none","containsHead":true}]],"renderers":[],"clientDirectives":[["idle","(()=>{var l=(n,t)=>{let i=async()=>{await(await n())()},e=typeof t.value==\"object\"?t.value:void 0,s={timeout:e==null?void 0:e.timeout};\"requestIdleCallback\"in window?window.requestIdleCallback(i,s):setTimeout(i,s.timeout||200)};(self.Astro||(self.Astro={})).idle=l;window.dispatchEvent(new Event(\"astro:idle\"));})();"],["load","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).load=e;window.dispatchEvent(new Event(\"astro:load\"));})();"],["media","(()=>{var n=(a,t)=>{let i=async()=>{await(await a())()};if(t.value){let e=matchMedia(t.value);e.matches?i():e.addEventListener(\"change\",i,{once:!0})}};(self.Astro||(self.Astro={})).media=n;window.dispatchEvent(new Event(\"astro:media\"));})();"],["only","(()=>{var e=async t=>{await(await t())()};(self.Astro||(self.Astro={})).only=e;window.dispatchEvent(new Event(\"astro:only\"));})();"],["visible","(()=>{var a=(s,i,o)=>{let r=async()=>{await(await s())()},t=typeof i.value==\"object\"?i.value:void 0,c={rootMargin:t==null?void 0:t.rootMargin},n=new IntersectionObserver(e=>{for(let l of e)if(l.isIntersecting){n.disconnect(),r();break}},c);for(let e of o.children)n.observe(e)};(self.Astro||(self.Astro={})).visible=a;window.dispatchEvent(new Event(\"astro:visible\"));})();"]],"entryModules":{"\u0000@astrojs-ssr-adapter":"<EMAIL>","\u0000noop-actions":"_noop-actions.mjs","\u0000astro-internal:middleware":"_astro-internal_middleware.mjs","\u0000@astro-page:src/pages/404@_@astro":"pages/404.astro.mjs","\u0000@astro-page:src/pages/about@_@astro":"pages/about.astro.mjs","\u0000@astro-page:src/pages/affiliate-disclosure@_@astro":"pages/affiliate-disclosure.astro.mjs","\u0000@astro-page:src/pages/api/alerts/manage-subscriptions@_@ts":"pages/api/alerts/manage-subscriptions.astro.mjs","\u0000@astro-page:src/pages/api/alerts/send-brand-alert@_@ts":"pages/api/alerts/send-brand-alert.astro.mjs","\u0000@astro-page:src/pages/api/alerts/send-merchant-alert@_@ts":"pages/api/alerts/send-merchant-alert.astro.mjs","\u0000@astro-page:src/pages/api/alerts/subscribe-multi@_@ts":"pages/api/alerts/subscribe-multi.astro.mjs","\u0000@astro-page:src/pages/api/alerts/trigger-new-coupon@_@ts":"pages/api/alerts/trigger-new-coupon.astro.mjs","\u0000@astro-page:src/pages/api/brands@_@ts":"pages/api/brands.astro.mjs","\u0000@astro-page:src/pages/api/categories@_@ts":"pages/api/categories.astro.mjs","\u0000@astro-page:src/pages/api/contact@_@ts":"pages/api/contact.astro.mjs","\u0000@astro-page:src/pages/api/create-tables@_@ts":"pages/api/create-tables.astro.mjs","\u0000@astro-page:src/pages/api/database/create-tables-direct@_@ts":"pages/api/database/create-tables-direct.astro.mjs","\u0000@astro-page:src/pages/api/database/setup-multi-subscription-direct@_@ts":"pages/api/database/setup-multi-subscription-direct.astro.mjs","\u0000@astro-page:src/pages/api/database/setup-multi-subscription-system@_@ts":"pages/api/database/setup-multi-subscription-system.astro.mjs","\u0000@astro-page:src/pages/api/deals@_@ts":"pages/api/deals.astro.mjs","\u0000@astro-page:src/pages/api/debug/direct-supabase-test@_@ts":"pages/api/debug/direct-supabase-test.astro.mjs","\u0000@astro-page:src/pages/api/debug/env-check@_@ts":"pages/api/debug/env-check.astro.mjs","\u0000@astro-page:src/pages/api/debug/supabase-test@_@ts":"pages/api/debug/supabase-test.astro.mjs","\u0000@astro-page:src/pages/api/direct-setup@_@ts":"pages/api/direct-setup.astro.mjs","\u0000@astro-page:src/pages/api/fix-rls@_@ts":"pages/api/fix-rls.astro.mjs","\u0000@astro-page:src/pages/api/health@_@ts":"pages/api/health.astro.mjs","\u0000@astro-page:src/pages/api/merchants@_@ts":"pages/api/merchants.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/add-enhanced-columns@_@ts":"pages/api/newsletter/add-enhanced-columns.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/confirm@_@ts":"pages/api/newsletter/confirm.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/consolidate-tables@_@ts":"pages/api/newsletter/consolidate-tables.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/create-preferences-table@_@ts":"pages/api/newsletter/create-preferences-table.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/get-preferences@_@ts":"pages/api/newsletter/get-preferences.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/migrate-preferences@_@ts":"pages/api/newsletter/migrate-preferences.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/subscribe@_@ts":"pages/api/newsletter/subscribe.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/unsubscribe@_@ts":"pages/api/newsletter/unsubscribe.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/update-preferences@_@ts":"pages/api/newsletter/update-preferences.astro.mjs","\u0000@astro-page:src/pages/api/newsletter/user-subscribe@_@ts":"pages/api/newsletter/user-subscribe.astro.mjs","\u0000@astro-page:src/pages/api/performance/vitals@_@ts":"pages/api/performance/vitals.astro.mjs","\u0000@astro-page:src/pages/api/setup-db@_@ts":"pages/api/setup-db.astro.mjs","\u0000@astro-page:src/pages/api/track-click@_@ts":"pages/api/track-click.astro.mjs","\u0000@astro-page:src/pages/api/track-impression@_@ts":"pages/api/track-impression.astro.mjs","\u0000@astro-page:src/pages/api/vote@_@ts":"pages/api/vote.astro.mjs","\u0000@astro-page:src/pages/api/vote-counts@_@ts":"pages/api/vote-counts.astro.mjs","\u0000@astro-page:src/pages/blog@_@astro":"pages/blog.astro.mjs","\u0000@astro-page:src/pages/brands/[slug]@_@astro":"pages/brands/_slug_.astro.mjs","\u0000@astro-page:src/pages/brands@_@astro":"pages/brands.astro.mjs","\u0000@astro-page:src/pages/card-comparison@_@astro":"pages/card-comparison.astro.mjs","\u0000@astro-page:src/pages/categories/[slug]@_@astro":"pages/categories/_slug_.astro.mjs","\u0000@astro-page:src/pages/categories@_@astro":"pages/categories.astro.mjs","\u0000@astro-page:src/pages/comprehensive-review-analysis@_@astro":"pages/comprehensive-review-analysis.astro.mjs","\u0000@astro-page:src/pages/cookie-policy@_@astro":"pages/cookie-policy.astro.mjs","\u0000@astro-page:src/pages/coupons/brands/index@_@astro":"pages/coupons/brands.astro.mjs","\u0000@astro-page:src/pages/coupons/categories/index@_@astro":"pages/coupons/categories.astro.mjs","\u0000@astro-page:src/pages/coupons/merchants/index@_@astro":"pages/coupons/merchants.astro.mjs","\u0000@astro-page:src/pages/coupons/index@_@astro":"pages/coupons.astro.mjs","\u0000@astro-page:src/pages/deal/[id]@_@astro":"pages/deal/_id_.astro.mjs","\u0000@astro-page:src/pages/deals@_@astro":"pages/deals.astro.mjs","\u0000@astro-page:src/pages/disclaimer@_@astro":"pages/disclaimer.astro.mjs","\u0000@astro-page:src/pages/faq@_@astro":"pages/faq.astro.mjs","\u0000@astro-page:src/pages/go/deal/[id]@_@ts":"pages/go/deal/_id_.astro.mjs","\u0000@astro-page:src/pages/go/merchant/[slug]@_@ts":"pages/go/merchant/_slug_.astro.mjs","\u0000@astro-page:src/pages/go/[id]@_@astro":"pages/go/_id_.astro.mjs","\u0000@astro-page:src/pages/how-it-works@_@astro":"pages/how-it-works.astro.mjs","\u0000@astro-page:src/pages/legal@_@astro":"pages/legal.astro.mjs","\u0000@astro-page:src/pages/merchants/[id]@_@astro":"pages/merchants/_id_.astro.mjs","\u0000@astro-page:src/pages/merchants@_@astro":"pages/merchants.astro.mjs","\u0000@astro-page:src/pages/newsletter/confirm@_@astro":"pages/newsletter/confirm.astro.mjs","\u0000@astro-page:src/pages/newsletter/error@_@astro":"pages/newsletter/error.astro.mjs","\u0000@astro-page:src/pages/newsletter/preferences@_@astro":"pages/newsletter/preferences.astro.mjs","\u0000@astro-page:src/pages/newsletter/success@_@astro":"pages/newsletter/success.astro.mjs","\u0000@astro-page:src/pages/newsletter/unsubscribed@_@astro":"pages/newsletter/unsubscribed.astro.mjs","\u0000@astro-page:src/pages/privacy@_@astro":"pages/privacy.astro.mjs","\u0000@astro-page:src/pages/terms@_@astro":"pages/terms.astro.mjs","\u0000@astrojs-ssr-virtual-entry":"entry.mjs","\u0000@astro-page:src/pages/api/alerts/admin-notification@_@ts":"pages/api/alerts/admin-notification.astro.mjs","\u0000@astro-page:src/pages/contact@_@astro":"pages/contact.astro.mjs","\u0000@astro-page:src/pages/search@_@astro":"pages/search.astro.mjs","\u0000@astro-renderers":"renderers.mjs","\u0000@astro-page:src/pages/bookmarks@_@astro":"pages/bookmarks.astro.mjs","\u0000@astro-page:src/pages/sitemap.xml@_@ts":"pages/sitemap.xml.astro.mjs","\u0000@astro-page:src/pages/coupon/[slug]@_@astro":"pages/coupon/_slug_.astro.mjs","\u0000@astro-page:src/pages/coupons/merchants/[id]@_@astro":"pages/coupons/merchants/_id_.astro.mjs","\u0000@astro-page:src/pages/coupons/categories/[slug]@_@astro":"pages/coupons/categories/_slug_.astro.mjs","\u0000@astro-page:src/pages/coupons/brands/[slug]@_@astro":"pages/coupons/brands/_slug_.astro.mjs","\u0000@astro-page:src/pages/index@_@astro":"pages/index.astro.mjs","\u0000@astro-page:node_modules/astro/dist/assets/endpoint/generic@_@js":"pages/_image.astro.mjs","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/featured-deals.tsx":"assets/js/featured-deals-ERmVkM9j.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/faq-section.tsx":"assets/js/faq-section-DnK0tX_o.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/node_modules/astro/dist/assets/services/sharp.js":"assets/js/sharp-DHh20IMn.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/flash-deals.tsx":"assets/js/flash-deals-CLVvCCUD.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/newsletter-section.tsx":"assets/js/newsletter-section-Bv7hy9NP.js","\u0000@astrojs-manifest":"manifest_DB_lc-ZN.mjs","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem":"assets/js/DesignSystem-BwQ2pBe0.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponActionButton":"assets/js/CouponActionButton-lL_ALfZo.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponVotingButton":"assets/js/CouponVotingButton-DwtngNvq.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponBookmarkButton":"assets/js/CouponBookmarkButton-B6e5Le4r.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/CouponPage/CouponPagePopup":"assets/js/CouponPagePopup-Dj_0a-HV.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/shared/UniversalEmailCaptureForm":"assets/js/UniversalEmailCaptureForm-BrQcmfx3.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/ExpandableBrandInfo":"assets/js/ExpandableBrandInfo-DK-egmNS.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandActivity":"assets/js/BrandActivity-BuIpHBGf.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandAbout":"assets/js/BrandAbout-BBbeaMtN.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandFAQ":"assets/js/BrandFAQ-CabUBd1t.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandRelatedSections":"assets/js/BrandRelatedSections--JTezToK.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/ExpandableCategoryInfo":"assets/js/ExpandableCategoryInfo-Dq3BtYu2.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/CategoryActivity":"assets/js/CategoryActivity-BUOAXVvO.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/CategoryAbout":"assets/js/CategoryAbout-BxSwpeMp.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/CategoryFAQ":"assets/js/CategoryFAQ-Bpi_qcdr.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Category/CategoryRelatedSections":"assets/js/CategoryRelatedSections-DNj8WXiz.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Merchant/EmailCaptureForm":"assets/js/EmailCaptureForm-BPKg9YAh.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Merchant/ExpandableMerchantInfo":"assets/js/ExpandableMerchantInfo-B-mjn4Kv.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Merchant/MerchantDealListCard":"assets/js/MerchantDealListCard-DRUXgr6O.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/enhanced-hero":"assets/js/enhanced-hero-BmM948g9.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/category-nav":"assets/js/category-nav-CbpwE2E4.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/testimonials-section":"assets/js/testimonials-section-jOYZAScp.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/real-time-savings":"assets/js/real-time-savings-D72Vzi9y.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/SimplifiedBrandGrid":"assets/js/SimplifiedBrandGrid-3XgxHcG0.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/react-providers":"assets/js/react-providers-IL5KYsWU.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/back-to-top":"assets/js/back-to-top-Czj9AJh1.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ExitIntentPopup":"assets/js/ExitIntentPopup-DnwXezpn.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/FooterNewsletter":"assets/js/FooterNewsletter-VtESi6He.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/404.astro?astro&type=script&index=0&lang.ts":"assets/js/404.astro_astro_type_script_index_0_lang-CvgeJTCe.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/faq.astro?astro&type=script&index=0&lang.ts":"assets/js/faq.astro_astro_type_script_index_0_lang-BLb3hRLq.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/BookmarksPage":"assets/js/BookmarksPage-CeojvRtc.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ContactFormFixed":"assets/js/ContactFormFixed-Cipm2tps.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Brand/BrandDealsWrapper":"assets/js/BrandDealsWrapper-D_LrRgEU.js","@astrojs/react/client.js":"assets/js/client-Cv_bynNb.js","astro:scripts/page.js":"assets/js/page-DH0Fop0q.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/featured-deals":"assets/js/featured-deals-C6oOi6BK.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/newsletter-section":"assets/js/newsletter-section-C56z7yNB.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/ui/homepage/faq-section":"assets/js/faq-section-DWQ-310V.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/OptimizedDealsPageWrapper":"assets/js/OptimizedDealsPageWrapper-BSkcPkRt.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DealsFAQ.tsx":"assets/js/DealsFAQ-oi0vS1Zt.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DealsPageWrapper":"assets/js/DealsPageWrapper-BTd695s1.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton":"assets/js/GlowingButton-BJz91Mbc.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Card/OptimizedDealCard":"assets/js/OptimizedDealCard-CtDMHSdj.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/FilterDrawer":"assets/js/FilterDrawer-CciBBUGI.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/SortSelect":"assets/js/SortSelect-BRCNFHYj.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/ViewToggle":"assets/js/ViewToggle--Squ5axs.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/NoticeBar.tsx":"assets/js/NoticeBar-eij8PQyC.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/SearchBox.tsx":"assets/js/SearchBox-rk90tynV.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/FilterDrawer.tsx":"assets/js/FilterDrawer-EKUMYTVO.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/SortSelect.tsx":"assets/js/SortSelect-u5iuRJLV.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/ViewToggle.tsx":"assets/js/ViewToggle-BJzqMmDg.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Navbar":"assets/js/Navbar-Bsdo0Cif.js","C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/OptimizedDealsPageWrapper.tsx":"assets/js/OptimizedDealsPageWrapper-CapKi-HY.js","astro:scripts/before-hydration.js":""},"inlinedScripts":[["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/404.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",(()=>{const e=window.location.href,t=document.referrer;if(window.location.pathname.startsWith(\"/admin\"))try{fetch(\"/admin/api/monitor/404\",{method:\"POST\",headers:{\"Content-Type\":\"application/json\"},body:JSON.stringify({url:e,referrer:t,timestamp:(new Date).toISOString(),userAgent:navigator.userAgent})})}catch(r){}else void 0!==window.gtag&&window.gtag(\"event\",\"404_error\",{event_category:\"Error\",event_label:e,referrer:t})}));"],["C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/faq.astro?astro&type=script&index=0&lang.ts","document.addEventListener(\"DOMContentLoaded\",(()=>{if(document.querySelectorAll('a[href^=\"#\"]').forEach((t=>{t.addEventListener(\"click\",(function(t){t.preventDefault();const e=this.getAttribute(\"href\")?.substring(1);if(!e)return;const o=document.getElementById(e);o&&(window.scrollTo({top:o.offsetTop-100,behavior:\"smooth\"}),history.pushState(null,\"\",`#${e}`),o.classList.add(\"bg-primary/10\"),setTimeout((()=>{o.classList.remove(\"bg-primary/10\")}),1500))}))})),window.location.hash){const t=window.location.hash.substring(1),e=document.getElementById(t);e&&setTimeout((()=>{window.scrollTo({top:e.offsetTop-100,behavior:\"smooth\"}),e.classList.add(\"bg-primary/10\"),setTimeout((()=>{e.classList.remove(\"bg-primary/10\")}),1500)}),300)}}));"]],"assets":["/_astro/index.AjOd9ujj.css","/apple-touch-icon.png","/apple-touch-icon.webp","/bg-pattern.svg","/dots-dark.svg","/dots-light.svg","/favicon.ico","/favicon.svg","/footer-logo-dark.svg","/footer-logo-light.svg","/logo-dark.svg","/logo-dark.webp","/logo-light.svg","/logo-light.webp","/manifest.json","/og-image.svg","/placeholder-image.svg","/placeholder-image.webp","/robots.txt","/Vapehybrid dark icon.svg","/Vapehybrid light icon.svg","/how/Finding-vape-deals.jpeg","/how/Finding-vape-deals.webp","/how/Saving-money-on-vape-deals.jpeg","/how/Saving-money-on-vape-deals.webp","/how/Verifying-vape-deals.jpeg","/how/Verifying-vape-deals.webp","/patterns/circle-dark.svg","/patterns/circle-light.svg","/patterns/dot-light-black.svg","/patterns/dot-pattern-dark.svg","/patterns/dot-pattern-light.svg","/patterns/dots-dark.svg","/patterns/dots-light.svg","/patterns/grid-dark.svg","/patterns/grid-light.svg","/patterns/hexagon-dark.svg","/patterns/hexagon-light.svg","/patterns/wave-dark.svg","/patterns/wave-light.svg","/images/hero-vape-deals.svg","/images/vape-deals-illustration.svg","/images/vape-device-1.svg","/images/vape-device-2.svg","/images/vape-juice.svg","/images/vape-pod.svg","/images/vapehyrid-deals.png","/images/vapehyrid-deals.webp","/icons/001-grape.png","/icons/001-grape.webp","/icons/002-vape.png","/icons/002-vape.webp","/icons/003-disposable.png","/icons/003-disposable.webp","/icons/004-vape-1.png","/icons/004-vape-1.webp","/icons/005-strawberry-juice.png","/icons/005-strawberry-juice.webp","/icons/006-coil.png","/icons/006-coil.webp","/icons/007-vape-2.png","/icons/007-vape-2.webp","/icons/008-coil-1.png","/icons/008-coil-1.webp","/icons/009-atomizer.png","/icons/009-atomizer.webp","/icons/010-electronic-cigarette.png","/icons/010-electronic-cigarette.webp","/js/error-handler.js","/products/Banana-Raspberry-Ice-waka-duo-27000.jpeg","/products/Banana-Raspberry-Ice-waka-duo-27000.webp","/products/Crystal-Cave-Centaurus-N200-Mod-Kit.jpeg","/products/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/products/ego-nexo-blue.jpeg","/products/ego-nexo-blue.webp","/products/geekvape-digi.jpeg","/products/geekvape-digi.webp","/products/grape-mint-drops-by-blis.jpeg","/products/grape-mint-drops-by-blis.webp","/products/Sour-Apple-Upends-CD30000.jpeg","/products/Sour-Apple-Upends-CD30000.webp","/staff/Chyou_Shen-removebg.png","/staff/Chyou_Shen-removebg.webp","/staff/Cynthia_S._Garcia-removebg.png","/staff/Cynthia_S._Garcia-removebg.webp","/staff/Dannielle_E._Benn-removebg.png","/staff/Dannielle_E._Benn-removebg.webp","/staff/Geoffrey_J._Koehler-removebg.png","/staff/Geoffrey_J._Koehler-removebg.webp","/staff/Harvey_B._Green-removebg-preview.png","/staff/Harvey_B._Green-removebg-preview.webp","/staff/Victoria_F._Ingram-removebg.png","/staff/Victoria_F._Ingram-removebg.webp","/scripts/modal-handler.js","/SVG/FaviconLogo.svg","/assets/excluded/index.css","/assets/excluded/README.css","/assets/js/back-to-top-Czj9AJh1.js","/assets/js/BookmarksPage-CeojvRtc.js","/assets/js/BrandAbout-BBbeaMtN.js","/assets/js/BrandActivity-BuIpHBGf.js","/assets/js/BrandDealsWrapper-D_LrRgEU.js","/assets/js/BrandFAQ-CabUBd1t.js","/assets/js/BrandRelatedSections--JTezToK.js","/assets/js/category-nav-CbpwE2E4.js","/assets/js/CategoryAbout-BxSwpeMp.js","/assets/js/CategoryActivity-BUOAXVvO.js","/assets/js/CategoryFAQ-Bpi_qcdr.js","/assets/js/CategoryRelatedSections-DNj8WXiz.js","/assets/js/client-Cv_bynNb.js","/assets/js/ContactFormFixed-Cipm2tps.js","/assets/js/countdown-timer-on56VBNl.js","/assets/js/CouponActionButton-lL_ALfZo.js","/assets/js/CouponBookmarkButton-B6e5Le4r.js","/assets/js/CouponPagePopup-Dj_0a-HV.js","/assets/js/CouponVotingButton-DwtngNvq.js","/assets/js/DealsFAQ-oi0vS1Zt.js","/assets/js/DealsPageWrapper-BTd695s1.js","/assets/js/DesignSystem-BwQ2pBe0.js","/assets/js/EmailCaptureForm-BPKg9YAh.js","/assets/js/enhanced-hero-BmM948g9.js","/assets/js/ExitIntentPopup-DnwXezpn.js","/assets/js/ExpandableBrandInfo-DK-egmNS.js","/assets/js/ExpandableCategoryInfo-Dq3BtYu2.js","/assets/js/ExpandableMerchantInfo-B-mjn4Kv.js","/assets/js/faq-section-4Kz2v8GC.js","/assets/js/faq-section-DWQ-310V.js","/assets/js/featured-deals-C6oOi6BK.js","/assets/js/featured-deals-Nf14Q-mO.js","/assets/js/FilterDrawer-CciBBUGI.js","/assets/js/FilterDrawer-EKUMYTVO.js","/assets/js/flash-deals-CLVvCCUD.js","/assets/js/FooterNewsletter-VtESi6He.js","/assets/js/GlowingButton-BJz91Mbc.js","/assets/js/index-tFD8qxOu.js","/assets/js/MerchantDealListCard-DRUXgr6O.js","/assets/js/Navbar-Bsdo0Cif.js","/assets/js/newsletter-section-C56z7yNB.js","/assets/js/newsletter-section-FtJve_5R.js","/assets/js/NoticeBar-eij8PQyC.js","/assets/js/OptimizedDealCard-CtDMHSdj.js","/assets/js/OptimizedDealsPageWrapper-BSkcPkRt.js","/assets/js/OptimizedDealsPageWrapper-CapKi-HY.js","/assets/js/page-DH0Fop0q.js","/assets/js/proxy-BAs1uqPX.js","/assets/js/react-providers-IL5KYsWU.js","/assets/js/real-time-savings-D72Vzi9y.js","/assets/js/SearchBox-rk90tynV.js","/assets/js/SimplifiedBrandGrid-3XgxHcG0.js","/assets/js/SortSelect-BRCNFHYj.js","/assets/js/SortSelect-u5iuRJLV.js","/assets/js/testimonials-section-jOYZAScp.js","/assets/js/UniversalEmailCaptureForm-BrQcmfx3.js","/assets/js/ViewToggle--Squ5axs.js","/assets/js/ViewToggle-BJzqMmDg.js","/images/testimonials/amanda-p.jpg","/images/testimonials/amanda-p.webp","/images/testimonials/david-m.jpeg","/images/testimonials/david-m.webp","/images/testimonials/jessica-r.jpeg","/images/testimonials/jessica-r.webp","/images/testimonials/michael-t.jpeg","/images/testimonials/michael-t.webp","/images/testimonials/robert-j.jpeg","/images/testimonials/robert-j.webp","/images/testimonials/sarah-k.jpeg","/images/testimonials/sarah-k.webp","/images/testimonials/user-1.svg","/images/testimonials/user-2.svg","/images/testimonials/user-3.svg","/images/testimonials/user-4.svg","/images/testimonials/user-5.svg","/images/testimonials/user-6.svg","/images/original/001-grape.png","/images/original/001-grape.webp","/images/original/002-vape.png","/images/original/002-vape.webp","/images/original/003-disposable.png","/images/original/003-disposable.webp","/images/original/004-vape-1.png","/images/original/004-vape-1.webp","/images/original/005-strawberry-juice.png","/images/original/005-strawberry-juice.webp","/images/original/006-coil.png","/images/original/006-coil.webp","/images/original/007-vape-2.png","/images/original/007-vape-2.webp","/images/original/008-coil-1.png","/images/original/008-coil-1.webp","/images/original/009-atomizer.png","/images/original/009-atomizer.webp","/images/original/010-electronic-cigarette.png","/images/original/010-electronic-cigarette.webp","/images/original/Amanda P.jpg","/images/original/Amanda P.webp","/images/original/amanda-p.jpg","/images/original/amanda-p.webp","/images/original/apple-touch-icon.png","/images/original/apple-touch-icon.webp","/images/original/Banana-Raspberry-Ice-waka-duo-27000.jpeg","/images/original/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/original/Chyou_Shen-removebg.png","/images/original/Chyou_Shen-removebg.webp","/images/original/Crystal-Cave-Centaurus-N200-Mod-Kit.jpeg","/images/original/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/original/Cynthia_S._Garcia-removebg.png","/images/original/Cynthia_S._Garcia-removebg.webp","/images/original/Dannielle_E._Benn-removebg.png","/images/original/Dannielle_E._Benn-removebg.webp","/images/original/David M.jpeg","/images/original/David M.webp","/images/original/david-m.jpeg","/images/original/david-m.webp","/images/original/disposable-vapes.png","/images/original/disposable-vapes.webp","/images/original/ego-nexo-blue.jpeg","/images/original/ego-nexo-blue.webp","/images/original/Finding-vape-deals.jpeg","/images/original/Finding-vape-deals.webp","/images/original/geekvape-digi.jpeg","/images/original/geekvape-digi.webp","/images/original/Geoffrey_J._Koehler-removebg.png","/images/original/Geoffrey_J._Koehler-removebg.webp","/images/original/grape-mint-drops-by-blis.jpeg","/images/original/grape-mint-drops-by-blis.webp","/images/original/Harvey_B._Green-removebg-preview.png","/images/original/Harvey_B._Green-removebg-preview.webp","/images/original/Jessica R.jpeg","/images/original/Jessica R.webp","/images/original/jessica-r.jpeg","/images/original/jessica-r.webp","/images/original/Michael T.jpeg","/images/original/Michael T.webp","/images/original/michael-t.jpeg","/images/original/michael-t.webp","/images/original/placeholder-image.png","/images/original/placeholder-image.webp","/images/original/Robert J.jpeg","/images/original/Robert J.webp","/images/original/robert-j.jpeg","/images/original/robert-j.webp","/images/original/Sarah K.jpeg","/images/original/Sarah K.webp","/images/original/sarah-k.jpeg","/images/original/sarah-k.webp","/images/original/Saving-money-on-vape-deals.jpeg","/images/original/Saving-money-on-vape-deals.webp","/images/original/Sour-Apple-Upends-CD30000.jpeg","/images/original/Sour-Apple-Upends-CD30000.webp","/images/original/vape-1.png","/images/original/vape-1.webp","/images/original/vapehyrid-deals.png","/images/original/vapehyrid-deals.webp","/images/original/Verifying-vape-deals.jpeg","/images/original/Verifying-vape-deals.webp","/images/original/Victoria_F._Ingram-removebg.png","/images/original/Victoria_F._Ingram-removebg.webp","/assets/js/app/deals-3jyzwLVb.js","/assets/js/app/design-system-lJ5ci6hi.js","/assets/js/vendor/react-bq1VYqFi.js","/assets/js/vendor/ui-components-_FzKGR3X.js","/assets/js/vendor/utils-hwhCH1Yb.js","/images/sizes/1280/disposable-vapes.png","/images/sizes/1280/disposable-vapes.webp","/images/sizes/1920/disposable-vapes.png","/images/sizes/1920/disposable-vapes.webp","/images/sizes/640/Banana-Raspberry-Ice-waka-duo-27000.jpeg","/images/sizes/640/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/sizes/640/Crystal-Cave-Centaurus-N200-Mod-Kit.jpeg","/images/sizes/640/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/sizes/640/David M.jpeg","/images/sizes/640/David M.webp","/images/sizes/640/david-m.jpeg","/images/sizes/640/david-m.webp","/images/sizes/640/disposable-vapes.png","/images/sizes/640/disposable-vapes.webp","/images/sizes/640/ego-nexo-blue.jpeg","/images/sizes/640/ego-nexo-blue.webp","/images/sizes/640/Finding-vape-deals.jpeg","/images/sizes/640/Finding-vape-deals.webp","/images/sizes/640/geekvape-digi.jpeg","/images/sizes/640/geekvape-digi.webp","/images/sizes/640/grape-mint-drops-by-blis.jpeg","/images/sizes/640/grape-mint-drops-by-blis.webp","/images/sizes/640/Jessica R.jpeg","/images/sizes/640/Jessica R.webp","/images/sizes/640/jessica-r.jpeg","/images/sizes/640/jessica-r.webp","/images/sizes/640/Michael T.jpeg","/images/sizes/640/Michael T.webp","/images/sizes/640/michael-t.jpeg","/images/sizes/640/michael-t.webp","/images/sizes/640/Robert J.jpeg","/images/sizes/640/Robert J.webp","/images/sizes/640/robert-j.jpeg","/images/sizes/640/robert-j.webp","/images/sizes/640/Sarah K.jpeg","/images/sizes/640/Sarah K.webp","/images/sizes/640/sarah-k.jpeg","/images/sizes/640/sarah-k.webp","/images/sizes/640/Saving-money-on-vape-deals.jpeg","/images/sizes/640/Saving-money-on-vape-deals.webp","/images/sizes/640/Sour-Apple-Upends-CD30000.jpeg","/images/sizes/640/Sour-Apple-Upends-CD30000.webp","/images/sizes/640/vape-1.png","/images/sizes/640/vape-1.webp","/images/sizes/640/vapehyrid-deals.png","/images/sizes/640/vapehyrid-deals.webp","/images/sizes/640/Verifying-vape-deals.jpeg","/images/sizes/640/Verifying-vape-deals.webp","/images/sizes/480/001-grape.png","/images/sizes/480/001-grape.webp","/images/sizes/480/002-vape.png","/images/sizes/480/002-vape.webp","/images/sizes/480/003-disposable.png","/images/sizes/480/003-disposable.webp","/images/sizes/480/004-vape-1.png","/images/sizes/480/004-vape-1.webp","/images/sizes/480/005-strawberry-juice.png","/images/sizes/480/005-strawberry-juice.webp","/images/sizes/480/006-coil.png","/images/sizes/480/006-coil.webp","/images/sizes/480/007-vape-2.png","/images/sizes/480/007-vape-2.webp","/images/sizes/480/008-coil-1.png","/images/sizes/480/008-coil-1.webp","/images/sizes/480/009-atomizer.png","/images/sizes/480/009-atomizer.webp","/images/sizes/480/010-electronic-cigarette.png","/images/sizes/480/010-electronic-cigarette.webp","/images/sizes/480/Amanda P.jpg","/images/sizes/480/Amanda P.webp","/images/sizes/480/amanda-p.jpg","/images/sizes/480/amanda-p.webp","/images/sizes/480/Banana-Raspberry-Ice-waka-duo-27000.jpeg","/images/sizes/480/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/sizes/480/Crystal-Cave-Centaurus-N200-Mod-Kit.jpeg","/images/sizes/480/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/sizes/480/David M.jpeg","/images/sizes/480/David M.webp","/images/sizes/480/david-m.jpeg","/images/sizes/480/david-m.webp","/images/sizes/480/disposable-vapes.png","/images/sizes/480/disposable-vapes.webp","/images/sizes/480/ego-nexo-blue.jpeg","/images/sizes/480/ego-nexo-blue.webp","/images/sizes/480/Finding-vape-deals.jpeg","/images/sizes/480/Finding-vape-deals.webp","/images/sizes/480/geekvape-digi.jpeg","/images/sizes/480/geekvape-digi.webp","/images/sizes/480/grape-mint-drops-by-blis.jpeg","/images/sizes/480/grape-mint-drops-by-blis.webp","/images/sizes/480/Jessica R.jpeg","/images/sizes/480/Jessica R.webp","/images/sizes/480/jessica-r.jpeg","/images/sizes/480/jessica-r.webp","/images/sizes/480/Michael T.jpeg","/images/sizes/480/Michael T.webp","/images/sizes/480/michael-t.jpeg","/images/sizes/480/michael-t.webp","/images/sizes/480/Robert J.jpeg","/images/sizes/480/Robert J.webp","/images/sizes/480/robert-j.jpeg","/images/sizes/480/robert-j.webp","/images/sizes/480/Sarah K.jpeg","/images/sizes/480/Sarah K.webp","/images/sizes/480/sarah-k.jpeg","/images/sizes/480/sarah-k.webp","/images/sizes/480/Saving-money-on-vape-deals.jpeg","/images/sizes/480/Saving-money-on-vape-deals.webp","/images/sizes/480/Sour-Apple-Upends-CD30000.jpeg","/images/sizes/480/Sour-Apple-Upends-CD30000.webp","/images/sizes/480/vape-1.png","/images/sizes/480/vape-1.webp","/images/sizes/480/vapehyrid-deals.png","/images/sizes/480/vapehyrid-deals.webp","/images/sizes/480/Verifying-vape-deals.jpeg","/images/sizes/480/Verifying-vape-deals.webp","/images/sizes/800/Banana-Raspberry-Ice-waka-duo-27000.jpeg","/images/sizes/800/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/sizes/800/Crystal-Cave-Centaurus-N200-Mod-Kit.jpeg","/images/sizes/800/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/sizes/800/David M.jpeg","/images/sizes/800/David M.webp","/images/sizes/800/david-m.jpeg","/images/sizes/800/david-m.webp","/images/sizes/800/disposable-vapes.png","/images/sizes/800/disposable-vapes.webp","/images/sizes/800/ego-nexo-blue.jpeg","/images/sizes/800/ego-nexo-blue.webp","/images/sizes/800/Finding-vape-deals.jpeg","/images/sizes/800/Finding-vape-deals.webp","/images/sizes/800/geekvape-digi.jpeg","/images/sizes/800/geekvape-digi.webp","/images/sizes/800/grape-mint-drops-by-blis.jpeg","/images/sizes/800/grape-mint-drops-by-blis.webp","/images/sizes/800/Jessica R.jpeg","/images/sizes/800/Jessica R.webp","/images/sizes/800/jessica-r.jpeg","/images/sizes/800/jessica-r.webp","/images/sizes/800/Michael T.jpeg","/images/sizes/800/Michael T.webp","/images/sizes/800/michael-t.jpeg","/images/sizes/800/michael-t.webp","/images/sizes/800/Robert J.jpeg","/images/sizes/800/Robert J.webp","/images/sizes/800/robert-j.jpeg","/images/sizes/800/robert-j.webp","/images/sizes/800/Sarah K.jpeg","/images/sizes/800/Sarah K.webp","/images/sizes/800/sarah-k.jpeg","/images/sizes/800/sarah-k.webp","/images/sizes/800/Saving-money-on-vape-deals.jpeg","/images/sizes/800/Saving-money-on-vape-deals.webp","/images/sizes/800/Sour-Apple-Upends-CD30000.jpeg","/images/sizes/800/Sour-Apple-Upends-CD30000.webp","/images/sizes/800/vape-1.png","/images/sizes/800/vape-1.webp","/images/sizes/800/vapehyrid-deals.png","/images/sizes/800/vapehyrid-deals.webp","/images/sizes/800/Verifying-vape-deals.jpeg","/images/sizes/800/Verifying-vape-deals.webp","/images/png/1280/disposable-vapes.png","/images/png/1280/disposable-vapes.webp","/images/png/1024/disposable-vapes.png","/images/png/1024/disposable-vapes.webp","/images/png/1024/vape-1.png","/images/png/1024/vape-1.webp","/images/png/1024/vapehyrid-deals.png","/images/png/1024/vapehyrid-deals.webp","/images/png/1920/disposable-vapes.png","/images/png/1920/disposable-vapes.webp","/images/png/320/001-grape.png","/images/png/320/001-grape.webp","/images/png/320/002-vape.png","/images/png/320/002-vape.webp","/images/png/320/003-disposable.png","/images/png/320/003-disposable.webp","/images/png/320/004-vape-1.png","/images/png/320/004-vape-1.webp","/images/png/320/005-strawberry-juice.png","/images/png/320/005-strawberry-juice.webp","/images/png/320/006-coil.png","/images/png/320/006-coil.webp","/images/png/320/007-vape-2.png","/images/png/320/007-vape-2.webp","/images/png/320/008-coil-1.png","/images/png/320/008-coil-1.webp","/images/png/320/009-atomizer.png","/images/png/320/009-atomizer.webp","/images/png/320/010-electronic-cigarette.png","/images/png/320/010-electronic-cigarette.webp","/images/png/320/disposable-vapes.png","/images/png/320/disposable-vapes.webp","/images/png/320/placeholder-image.png","/images/png/320/placeholder-image.webp","/images/png/320/vape-1.png","/images/png/320/vape-1.webp","/images/png/320/vapehyrid-deals.png","/images/png/320/vapehyrid-deals.webp","/images/png/480/001-grape.png","/images/png/480/001-grape.webp","/images/png/480/002-vape.png","/images/png/480/002-vape.webp","/images/png/480/003-disposable.png","/images/png/480/003-disposable.webp","/images/png/480/004-vape-1.png","/images/png/480/004-vape-1.webp","/images/png/480/005-strawberry-juice.png","/images/png/480/005-strawberry-juice.webp","/images/png/480/006-coil.png","/images/png/480/006-coil.webp","/images/png/480/007-vape-2.png","/images/png/480/007-vape-2.webp","/images/png/480/008-coil-1.png","/images/png/480/008-coil-1.webp","/images/png/480/009-atomizer.png","/images/png/480/009-atomizer.webp","/images/png/480/010-electronic-cigarette.png","/images/png/480/010-electronic-cigarette.webp","/images/png/480/disposable-vapes.png","/images/png/480/disposable-vapes.webp","/images/png/480/vape-1.png","/images/png/480/vape-1.webp","/images/png/480/vapehyrid-deals.png","/images/png/480/vapehyrid-deals.webp","/images/png/640/disposable-vapes.png","/images/png/640/disposable-vapes.webp","/images/png/640/vape-1.png","/images/png/640/vape-1.webp","/images/png/640/vapehyrid-deals.png","/images/png/640/vapehyrid-deals.webp","/images/png/800/disposable-vapes.png","/images/png/800/disposable-vapes.webp","/images/png/800/vape-1.png","/images/png/800/vape-1.webp","/images/png/800/vapehyrid-deals.png","/images/png/800/vapehyrid-deals.webp","/images/webp/1024/disposable-vapes.webp","/images/webp/1024/Finding-vape-deals.webp","/images/webp/1024/hero.webp","/images/webp/1024/Saving-money-on-vape-deals.webp","/images/webp/1024/vape-1.webp","/images/webp/1024/vapehyrid-deals.webp","/images/webp/1024/Verifying-vape-deals.webp","/images/webp/1920/disposable-vapes.webp","/images/webp/1280/disposable-vapes.webp","/images/webp/1280/hero.webp","/images/webp/320/001-grape.webp","/images/webp/320/002-vape.webp","/images/webp/320/003-disposable.webp","/images/webp/320/004-vape-1.webp","/images/webp/320/005-strawberry-juice.webp","/images/webp/320/006-coil.webp","/images/webp/320/007-vape-2.webp","/images/webp/320/008-coil-1.webp","/images/webp/320/009-atomizer.webp","/images/webp/320/010-electronic-cigarette.webp","/images/webp/320/Amanda P.webp","/images/webp/320/amanda-p.webp","/images/webp/320/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/webp/320/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/webp/320/David M.webp","/images/webp/320/david-m.webp","/images/webp/320/disposable-vapes.webp","/images/webp/320/ego-nexo-blue.webp","/images/webp/320/Finding-vape-deals.webp","/images/webp/320/geekvape-digi.webp","/images/webp/320/grape-mint-drops-by-blis.webp","/images/webp/320/Jessica R.webp","/images/webp/320/jessica-r.webp","/images/webp/320/Michael T.webp","/images/webp/320/michael-t.webp","/images/webp/320/placeholder-image.webp","/images/webp/320/Robert J.webp","/images/webp/320/robert-j.webp","/images/webp/320/Sarah K.webp","/images/webp/320/sarah-k.webp","/images/webp/320/Saving-money-on-vape-deals.webp","/images/webp/320/Sour-Apple-Upends-CD30000.webp","/images/webp/320/vape-1.webp","/images/webp/320/vapehyrid-deals.webp","/images/webp/320/Verifying-vape-deals.webp","/images/webp/640/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/webp/640/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/webp/640/David M.webp","/images/webp/640/david-m.webp","/images/webp/640/disposable-vapes.webp","/images/webp/640/ego-nexo-blue.webp","/images/webp/640/Finding-vape-deals.webp","/images/webp/640/geekvape-digi.webp","/images/webp/640/grape-mint-drops-by-blis.webp","/images/webp/640/Jessica R.webp","/images/webp/640/jessica-r.webp","/images/webp/640/Michael T.webp","/images/webp/640/michael-t.webp","/images/webp/640/Robert J.webp","/images/webp/640/robert-j.webp","/images/webp/640/Sarah K.webp","/images/webp/640/sarah-k.webp","/images/webp/640/Saving-money-on-vape-deals.webp","/images/webp/640/Sour-Apple-Upends-CD30000.webp","/images/webp/640/vape-1.webp","/images/webp/640/vapehyrid-deals.webp","/images/webp/640/Verifying-vape-deals.webp","/images/webp/480/001-grape.webp","/images/webp/480/002-vape.webp","/images/webp/480/003-disposable.webp","/images/webp/480/004-vape-1.webp","/images/webp/480/005-strawberry-juice.webp","/images/webp/480/006-coil.webp","/images/webp/480/007-vape-2.webp","/images/webp/480/008-coil-1.webp","/images/webp/480/009-atomizer.webp","/images/webp/480/010-electronic-cigarette.webp","/images/webp/480/Amanda P.webp","/images/webp/480/amanda-p.webp","/images/webp/480/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/webp/480/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/webp/480/David M.webp","/images/webp/480/david-m.webp","/images/webp/480/disposable-vapes.webp","/images/webp/480/ego-nexo-blue.webp","/images/webp/480/Finding-vape-deals.webp","/images/webp/480/geekvape-digi.webp","/images/webp/480/grape-mint-drops-by-blis.webp","/images/webp/480/Jessica R.webp","/images/webp/480/jessica-r.webp","/images/webp/480/Michael T.webp","/images/webp/480/michael-t.webp","/images/webp/480/placeholder-image.webp","/images/webp/480/Robert J.webp","/images/webp/480/robert-j.webp","/images/webp/480/Sarah K.webp","/images/webp/480/sarah-k.webp","/images/webp/480/Saving-money-on-vape-deals.webp","/images/webp/480/Sour-Apple-Upends-CD30000.webp","/images/webp/480/vape-1.webp","/images/webp/480/vapehyrid-deals.webp","/images/webp/480/Verifying-vape-deals.webp","/images/webp/800/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/webp/800/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/webp/800/David M.webp","/images/webp/800/david-m.webp","/images/webp/800/disposable-vapes.webp","/images/webp/800/ego-nexo-blue.webp","/images/webp/800/Finding-vape-deals.webp","/images/webp/800/geekvape-digi.webp","/images/webp/800/grape-mint-drops-by-blis.webp","/images/webp/800/hero.webp","/images/webp/800/Jessica R.webp","/images/webp/800/jessica-r.webp","/images/webp/800/Michael T.webp","/images/webp/800/michael-t.webp","/images/webp/800/Robert J.webp","/images/webp/800/robert-j.webp","/images/webp/800/Sarah K.webp","/images/webp/800/sarah-k.webp","/images/webp/800/Saving-money-on-vape-deals.webp","/images/webp/800/Sour-Apple-Upends-CD30000.webp","/images/webp/800/vape-1.webp","/images/webp/800/vapehyrid-deals.webp","/images/webp/800/Verifying-vape-deals.webp","/images/sizes/320/001-grape.png","/images/sizes/320/001-grape.webp","/images/sizes/320/002-vape.png","/images/sizes/320/002-vape.webp","/images/sizes/320/003-disposable.png","/images/sizes/320/003-disposable.webp","/images/sizes/320/004-vape-1.png","/images/sizes/320/004-vape-1.webp","/images/sizes/320/005-strawberry-juice.png","/images/sizes/320/005-strawberry-juice.webp","/images/sizes/320/006-coil.png","/images/sizes/320/006-coil.webp","/images/sizes/320/007-vape-2.png","/images/sizes/320/007-vape-2.webp","/images/sizes/320/008-coil-1.png","/images/sizes/320/008-coil-1.webp","/images/sizes/320/009-atomizer.png","/images/sizes/320/009-atomizer.webp","/images/sizes/320/010-electronic-cigarette.png","/images/sizes/320/010-electronic-cigarette.webp","/images/sizes/320/Amanda P.jpg","/images/sizes/320/Amanda P.webp","/images/sizes/320/amanda-p.jpg","/images/sizes/320/amanda-p.webp","/images/sizes/320/Banana-Raspberry-Ice-waka-duo-27000.jpeg","/images/sizes/320/Banana-Raspberry-Ice-waka-duo-27000.webp","/images/sizes/320/Crystal-Cave-Centaurus-N200-Mod-Kit.jpeg","/images/sizes/320/Crystal-Cave-Centaurus-N200-Mod-Kit.webp","/images/sizes/320/David M.jpeg","/images/sizes/320/David M.webp","/images/sizes/320/david-m.jpeg","/images/sizes/320/david-m.webp","/images/sizes/320/disposable-vapes.png","/images/sizes/320/disposable-vapes.webp","/images/sizes/320/ego-nexo-blue.jpeg","/images/sizes/320/ego-nexo-blue.webp","/images/sizes/320/Finding-vape-deals.jpeg","/images/sizes/320/Finding-vape-deals.webp","/images/sizes/320/geekvape-digi.jpeg","/images/sizes/320/geekvape-digi.webp","/images/sizes/320/grape-mint-drops-by-blis.jpeg","/images/sizes/320/grape-mint-drops-by-blis.webp","/images/sizes/320/Jessica R.jpeg","/images/sizes/320/Jessica R.webp","/images/sizes/320/jessica-r.jpeg","/images/sizes/320/jessica-r.webp","/images/sizes/320/Michael T.jpeg","/images/sizes/320/Michael T.webp","/images/sizes/320/michael-t.jpeg","/images/sizes/320/michael-t.webp","/images/sizes/320/placeholder-image.png","/images/sizes/320/placeholder-image.webp","/images/sizes/320/Robert J.jpeg","/images/sizes/320/Robert J.webp","/images/sizes/320/robert-j.jpeg","/images/sizes/320/robert-j.webp","/images/sizes/320/Sarah K.jpeg","/images/sizes/320/Sarah K.webp","/images/sizes/320/sarah-k.jpeg","/images/sizes/320/sarah-k.webp","/images/sizes/320/Saving-money-on-vape-deals.jpeg","/images/sizes/320/Saving-money-on-vape-deals.webp","/images/sizes/320/Sour-Apple-Upends-CD30000.jpeg","/images/sizes/320/Sour-Apple-Upends-CD30000.webp","/images/sizes/320/vape-1.png","/images/sizes/320/vape-1.webp","/images/sizes/320/vapehyrid-deals.png","/images/sizes/320/vapehyrid-deals.webp","/images/sizes/320/Verifying-vape-deals.jpeg","/images/sizes/320/Verifying-vape-deals.webp","/assets/js/page-DH0Fop0q.js"],"buildFormat":"directory","checkOrigin":true,"serverIslandNameMap":[],"key":"LRWFclTuK+6Tkt5+xtJvlP3urUOLuK3xPnZ2AUAmmW0="});
if (manifest.sessionConfig) manifest.sessionConfig.driverModule = null;

export { manifest };
