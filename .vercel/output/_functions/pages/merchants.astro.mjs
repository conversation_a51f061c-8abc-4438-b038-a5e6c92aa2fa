import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeR<PERSON>Head, b as addAttribute } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$MainLayout } from '../assets/js/MainLayout-BVJnMCp3.js';
import { c as createServerSupabaseClient } from '../assets/js/server-e_5TR1Eu.js';
import { $ as $$PageBreadcrumbs } from '../assets/js/PageBreadcrumbs-DYZTpX-J.js';
import { G as GlowingButton } from '../assets/js/app/design-system-CUssmfny.js';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$Merchants = createComponent(async ($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$Merchants;
  const supabase = createServerSupabaseClient();
  const { data, error } = await supabase.from("merchants").select("*").eq("status", "active").order("name");
  if (error) {
    console.error("Error fetching merchants:", error);
  }
  const merchants = data || [];
  const title = "All Merchants | VapeHybrid Coupons";
  const description = "Browse all vape merchants and find exclusive coupons and deals for your favorite vape stores.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "CollectionPage",
    name: "Vape Merchants Directory",
    description,
    url: Astro2.url.href,
    mainEntity: {
      "@type": "ItemList",
      itemListElement: data?.map((merchant, index) => ({
        "@type": "ListItem",
        position: index + 1,
        item: {
          "@type": "Organization",
          name: merchant.name,
          url: `${Astro2.url.origin}/merchants/${merchant.slug || merchant.id}`,
          logo: merchant.logo_url || void 0
        }
      }))
    }
  };
  return renderTemplate`${renderComponent($$result, "MainLayout", $$MainLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": async ($$result2) => renderTemplate`${maybeRenderHead()}<div class="relative py-16 md:py-20 overflow-hidden border-b border-design-border">  <div class="absolute inset-0 z-0 pointer-events-none overflow-hidden">  <div class="absolute inset-0 bg-[url('/patterns/dot-pattern-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/dot-pattern-dark.svg')] dark:opacity-10"></div>  <div class="absolute inset-0 bg-[url('/patterns/hexagon-light.svg')] bg-repeat opacity-10 dark:bg-[url('/patterns/hexagon-dark.svg')] dark:opacity-5"></div>  <div class="absolute top-0 left-0 w-full h-full bg-gradient-to-b from-design-background/80 via-design-background/60 to-design-background/40 backdrop-blur-[3px]"></div>  <div class="absolute top-[-5%] right-[-10%] w-[40%] h-[40%] bg-primary/10 dark:bg-primary/5 rounded-full filter blur-[80px]"></div> <div class="absolute bottom-[-5%] left-[-10%] w-[40%] h-[40%] bg-primary/10 dark:bg-primary/5 rounded-full filter blur-[80px]"></div> </div>  <div class="container mx-auto px-4 relative z-10"> <div class="max-w-3xl mx-auto text-center"> ${renderComponent($$result2, "PageBreadcrumbs", $$PageBreadcrumbs, { "currentPage": "Merchants" })} <h1 class="text-4xl md:text-5xl font-bold text-design-foreground mb-6 leading-tight">
Browse <span class="text-design-primary">Merchants</span> </h1> <p class="text-lg text-design-muted-foreground mb-8 max-w-2xl mx-auto">
Explore our curated collection of vape merchants and find exclusive deals and discounts from your favorite stores.
</p> </div> </div> </div> <div class="max-w-[960px] mx-auto px-4 pt-4 sm:pt-2 lg:pt-4 pb-12"> ${error && renderTemplate`<div class="bg-design-destructive/10 text-design-destructive p-4 rounded-md mb-8"> <p>There was an error loading merchants. Please try again later.</p> </div>`}  <div class="mb-16"> <h2 class="text-2xl font-bold text-design-foreground mb-10 text-center">Our Trusted Merchants</h2> <div class="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-10"> ${merchants.map((merchant) => renderTemplate`<a${addAttribute(`/merchants/${merchant.slug || merchant.id}`, "href")} class="group bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 hover:border-transparent rounded-xl p-8 transition-all duration-300 hover:shadow-md flex flex-col items-center text-center relative overflow-hidden card-glow">  <div class="w-32 h-32 mb-6 rounded-full bg-primary/5 dark:bg-primary/10 flex items-center justify-center overflow-hidden relative"> <div class="absolute inset-0 bg-gradient-to-br from-primary/5 to-transparent dark:from-primary/10 dark:to-transparent rounded-full transform scale-0 group-hover:scale-100 transition-transform duration-500 ease-out"></div> ${merchant.logo_url ? renderTemplate`<img${addAttribute(merchant.logo_url, "src")}${addAttribute(merchant.name, "alt")}${addAttribute(128, "width")}${addAttribute(128, "height")} class="object-contain w-full h-full transform group-hover:scale-105 transition-transform duration-300" loading="lazy">` : renderTemplate`<div class="text-4xl font-bold text-design-primary transform group-hover:scale-110 transition-transform duration-300"> ${merchant.name.charAt(0)} </div>`} </div>  <h3 class="text-xl font-semibold text-design-foreground group-hover:text-design-primary transition-colors duration-300 mb-3"> ${merchant.name} </h3>  <div class="mt-auto pt-4"> ${renderComponent($$result2, "GlowingButton", GlowingButton, { "client:load": true, "forceSpan": true, "className": "text-sm px-6 py-2.5", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result3) => renderTemplate`
View Deals
<svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 ml-1 transform group-hover:translate-x-0.5 transition-transform duration-300" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"></path> </svg> ` })} </div> </a>`)} </div> </div>  ${data?.length === 0 && !error && renderTemplate`<div class="text-center py-16 bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/10 rounded-xl shadow-sm max-w-2xl mx-auto"> <div class="bg-primary/10 dark:bg-primary/20 p-4 rounded-full w-20 h-20 mx-auto mb-6 flex items-center justify-center"> <svg xmlns="http://www.w3.org/2000/svg" class="h-10 w-10 text-primary dark:text-design-secondary" fill="none" viewBox="0 0 24 24" stroke="currentColor"> <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9.75 9.75l4.5 4.5m0-4.5l-4.5 4.5M21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path> </svg> </div> <h3 class="text-2xl font-bold text-design-foreground mb-3">No Merchants Found</h3> <p class="text-design-muted-foreground mb-8 max-w-md mx-auto">
We couldn't find any merchants at the moment. Please check back later as we're constantly adding new partners.
</p> ${renderComponent($$result2, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/coupons", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result3) => renderTemplate`
Browse All Coupons
` })} </div>`}  <div class="mt-20 mb-16 max-w-4xl mx-auto"> <div class="bg-gradient-to-br from-primary/5 to-transparent dark:from-primary/10 dark:to-transparent p-10 rounded-xl relative overflow-hidden"> <div class="absolute top-0 left-0 w-full h-1 bg-primary/30 dark:bg-primary/40 rounded-t-lg"></div> <h2 class="text-2xl font-bold text-design-foreground mb-6 text-center">How to Use Merchants</h2> <p class="text-design-muted-foreground mb-10 text-center max-w-2xl mx-auto">
Browse our collection of merchants to find deals and coupons from your favorite vape stores. Click on any merchant to see all their available deals.
</p> <div class="grid grid-cols-1 md:grid-cols-3 gap-8"> <div class="bg-white/50 dark:bg-black/50 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/10 relative overflow-hidden"> <div class="absolute top-0 right-0 w-24 h-24 bg-primary/5 rounded-bl-full"></div> <div class="relative z-10"> <div class="flex items-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-3 rounded-full mr-4"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary dark:text-design-secondary"><circle cx="11" cy="11" r="8"></circle><path d="m21 21-4.3-4.3"></path></svg> </div> <h3 class="text-xl font-bold text-design-foreground">Browse Merchants</h3> </div> <p class="text-design-muted-foreground pl-14">
Explore our selection of merchants to find your favorite stores
</p> </div> </div> <div class="bg-white/50 dark:bg-black/50 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/10 relative overflow-hidden"> <div class="absolute top-0 right-0 w-24 h-24 bg-primary/5 rounded-bl-full"></div> <div class="relative z-10"> <div class="flex items-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-3 rounded-full mr-4"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary dark:text-design-secondary"><path d="M4 22h16a2 2 0 0 0 2-2V4a2 2 0 0 0-2-2H8a2 2 0 0 0-2 2v16a2 2 0 0 1-2 2Zm0 0a2 2 0 0 1-2-2v-9c0-1.1.9-2 2-2h2"></path><path d="M18 14h-8"></path><path d="M15 18h-5"></path><path d="M10 6h8v4h-8V6Z"></path></svg> </div> <h3 class="text-xl font-bold text-design-foreground">Select a Merchant</h3> </div> <p class="text-design-muted-foreground pl-14">
Click on any merchant card to see all deals from that store
</p> </div> </div> <div class="bg-white/50 dark:bg-black/50 backdrop-blur-sm p-6 rounded-lg shadow-sm border border-design-border/10 relative overflow-hidden"> <div class="absolute top-0 right-0 w-24 h-24 bg-primary/5 rounded-bl-full"></div> <div class="relative z-10"> <div class="flex items-center mb-4"> <div class="bg-primary/10 dark:bg-primary/20 p-3 rounded-full mr-4"> <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="text-primary dark:text-design-secondary"><circle cx="12" cy="12" r="10"></circle><path d="M16 8h-6a2 2 0 1 0 0 4h4a2 2 0 1 1 0 4H8"></path><path d="M12 18V6"></path></svg> </div> <h3 class="text-xl font-bold text-design-foreground">Save Money</h3> </div> <p class="text-design-muted-foreground pl-14">
Find the best deals and discounts from your favorite vape stores
</p> </div> </div> </div> </div> </div>  <div class="mt-12 mb-16 flex flex-wrap justify-center gap-4"> ${renderComponent($$result2, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/coupons/categories", "className": "text-sm px-6 py-2.5 bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/20 text-design-foreground dark:text-design-foreground hover:text-design-foreground dark:hover:text-design-foreground", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result3) => renderTemplate`
Browse Categories
` })} ${renderComponent($$result2, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/coupons/brands", "className": "text-sm px-6 py-2.5 bg-white/50 dark:bg-black/50 backdrop-blur-sm border border-design-border/20 text-design-foreground dark:text-design-foreground hover:text-design-foreground dark:hover:text-design-foreground", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result3) => renderTemplate`
Browse Brands
` })} ${renderComponent($$result2, "GlowingButton", GlowingButton, { "client:load": true, "asLink": true, "href": "/coupons", "className": "text-sm px-6 py-2.5", "client:component-hydration": "load", "client:component-path": "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/DesignSystem/Button/GlowingButton", "client:component-export": "GlowingButton" }, { "default": async ($$result3) => renderTemplate`
View All Coupons
<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="ml-2"><path d="M5 12h14"></path><path d="m12 5 7 7-7 7"></path></svg> ` })} </div> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/merchants.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/merchants.astro";
const $$url = "/merchants";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$Merchants,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
