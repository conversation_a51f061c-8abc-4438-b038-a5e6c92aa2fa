import { c as createAstro, a as createComponent, d as renderComponent, r as renderTemplate, m as maybeRenderHead } from '../assets/js/astro/server-CU4H3-CM.js';
import 'kleur/colors';
import { $ as $$LegalPageLayout } from '../assets/js/LegalPageLayout-DSHTXlhZ.js';
export { renderers } from '../renderers.mjs';

const $$Astro = createAstro("http://localhost:4321");
const $$AffiliateDisclosure = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$AffiliateDisclosure;
  const title = "Affiliate Disclosure | VapeHybrid";
  const description = "Transparency about our affiliate relationships and how they influence our content.";
  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebPage",
    "name": title,
    "description": description,
    "publisher": {
      "@type": "Organization",
      "name": "VapeHybrid",
      "logo": {
        "@type": "ImageObject",
        "url": `${Astro2.url.origin}/Vapehybrid light icon.svg`
      }
    }
  };
  (/* @__PURE__ */ new Date()).getFullYear();
  return renderTemplate`${renderComponent($$result, "LegalPageLayout", $$LegalPageLayout, { "title": title, "description": description, "structuredData": structuredData }, { "default": ($$result2) => renderTemplate` ${maybeRenderHead()}<div class="notice"> <p class="text-design-foreground font-medium text-lg">
We independently review everything we recommend. When you buy through our links, we may earn a commission—at no extra cost to you.
</p> </div> <h1 class="text-3xl font-bold text-design-foreground mb-6">Affiliate Disclosure</h1> <p class="text-design-muted-foreground mb-8">Last updated: ${(/* @__PURE__ */ new Date()).toLocaleDateString("en-US", { year: "numeric", month: "long", day: "numeric" })}</p> <div class="prose prose-lg max-w-none text-design-foreground prose-headings:text-design-foreground prose-strong:text-design-foreground prose-a:text-design-primary"> <h2>Our Affiliate Relationships</h2> <p>VapeHybrid is a participant in various affiliate marketing programs. This means that when you click on links on our site and make a purchase, we may earn a commission from the retailer or brand at no additional cost to you.</p> <p>We currently have affiliate relationships with:</p> <ul> <li>Direct partnerships with vape brands and retailers</li> <li>ShareASale</li> <li>Commission Junction (CJ)</li> <li>Other affiliate networks and programs</li> </ul> <h2>How Commissions Work</h2> <p>When you click on an affiliate link on our site and make a purchase, we earn a small percentage of the sale as a commission. This commission:</p> <ul> <li>Does not affect the price you pay for products</li> <li>Is paid by the merchant, not by you</li> <li>Helps support our work in finding and verifying the best deals</li> </ul> <p>The commission rates vary depending on the merchant and the specific program, but typically range from 5% to 15% of the purchase price.</p> <h2>Our Commitment to You</h2> <p>Despite our affiliate relationships, we are committed to providing honest, unbiased information. Our editorial decisions are made independently of our affiliate partnerships, and we prioritize your interests above commission potential.</p> <p>We promise that:</p> <ul> <li>We only recommend products and deals we believe offer genuine value</li> <li>We verify deals and coupon codes before publishing them</li> <li>We clearly disclose our affiliate relationships throughout our site</li> <li>We will never recommend a product solely because it offers a commission</li> </ul> <h2>Identifying Affiliate Links</h2> <p>All affiliate links on our site are implemented with the appropriate disclosure. We use the <code>rel="sponsored"</code> attribute on our affiliate links as recommended by search engines and regulatory guidelines.</p> <p>Additionally, we provide this disclosure notice:</p> <ul> <li>At the top of this page</li> <li>In the footer of our website</li> <li>On our deals pages</li> </ul> <h2>Questions About Our Affiliate Relationships</h2> <p>If you have any questions about our affiliate relationships or how we earn commissions, please contact us at <a href="mailto:<EMAIL>"><EMAIL></a>.</p> </div> ` })}`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/affiliate-disclosure.astro", void 0);

const $$file = "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/pages/affiliate-disclosure.astro";
const $$url = "/affiliate-disclosure";

const _page = /*#__PURE__*/Object.freeze(/*#__PURE__*/Object.defineProperty({
  __proto__: null,
  default: $$AffiliateDisclosure,
  file: $$file,
  url: $$url
}, Symbol.toStringTag, { value: 'Module' }));

const page = () => _page;

export { page };
