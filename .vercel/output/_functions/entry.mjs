import { renderers } from './renderers.mjs';
import { c as createExports } from './assets/js/entrypoint-D2nSN21M.js';
import { manifest } from './manifest_DB_lc-ZN.mjs';

const serverIslandMap = new Map();;

const _page0 = () => import('./pages/_image.astro.mjs');
const _page1 = () => import('./pages/404.astro.mjs');
const _page2 = () => import('./pages/about.astro.mjs');
const _page3 = () => import('./pages/affiliate-disclosure.astro.mjs');
const _page4 = () => import('./pages/api/alerts/admin-notification.astro.mjs');
const _page5 = () => import('./pages/api/alerts/manage-subscriptions.astro.mjs');
const _page6 = () => import('./pages/api/alerts/send-brand-alert.astro.mjs');
const _page7 = () => import('./pages/api/alerts/send-merchant-alert.astro.mjs');
const _page8 = () => import('./pages/api/alerts/subscribe-multi.astro.mjs');
const _page9 = () => import('./pages/api/alerts/trigger-new-coupon.astro.mjs');
const _page10 = () => import('./pages/api/brands.astro.mjs');
const _page11 = () => import('./pages/api/categories.astro.mjs');
const _page12 = () => import('./pages/api/contact.astro.mjs');
const _page13 = () => import('./pages/api/create-tables.astro.mjs');
const _page14 = () => import('./pages/api/database/create-tables-direct.astro.mjs');
const _page15 = () => import('./pages/api/database/setup-multi-subscription-direct.astro.mjs');
const _page16 = () => import('./pages/api/database/setup-multi-subscription-system.astro.mjs');
const _page17 = () => import('./pages/api/deals.astro.mjs');
const _page18 = () => import('./pages/api/debug/direct-supabase-test.astro.mjs');
const _page19 = () => import('./pages/api/debug/env-check.astro.mjs');
const _page20 = () => import('./pages/api/debug/supabase-test.astro.mjs');
const _page21 = () => import('./pages/api/direct-setup.astro.mjs');
const _page22 = () => import('./pages/api/fix-rls.astro.mjs');
const _page23 = () => import('./pages/api/health.astro.mjs');
const _page24 = () => import('./pages/api/merchants.astro.mjs');
const _page25 = () => import('./pages/api/newsletter/add-enhanced-columns.astro.mjs');
const _page26 = () => import('./pages/api/newsletter/confirm.astro.mjs');
const _page27 = () => import('./pages/api/newsletter/consolidate-tables.astro.mjs');
const _page28 = () => import('./pages/api/newsletter/create-preferences-table.astro.mjs');
const _page29 = () => import('./pages/api/newsletter/get-preferences.astro.mjs');
const _page30 = () => import('./pages/api/newsletter/migrate-preferences.astro.mjs');
const _page31 = () => import('./pages/api/newsletter/subscribe.astro.mjs');
const _page32 = () => import('./pages/api/newsletter/unsubscribe.astro.mjs');
const _page33 = () => import('./pages/api/newsletter/update-preferences.astro.mjs');
const _page34 = () => import('./pages/api/newsletter/user-subscribe.astro.mjs');
const _page35 = () => import('./pages/api/performance/vitals.astro.mjs');
const _page36 = () => import('./pages/api/setup-db.astro.mjs');
const _page37 = () => import('./pages/api/track-click.astro.mjs');
const _page38 = () => import('./pages/api/track-impression.astro.mjs');
const _page39 = () => import('./pages/api/vote.astro.mjs');
const _page40 = () => import('./pages/api/vote-counts.astro.mjs');
const _page41 = () => import('./pages/blog.astro.mjs');
const _page42 = () => import('./pages/bookmarks.astro.mjs');
const _page43 = () => import('./pages/brands/_slug_.astro.mjs');
const _page44 = () => import('./pages/brands.astro.mjs');
const _page45 = () => import('./pages/card-comparison.astro.mjs');
const _page46 = () => import('./pages/categories/_slug_.astro.mjs');
const _page47 = () => import('./pages/categories.astro.mjs');
const _page48 = () => import('./pages/comprehensive-review-analysis.astro.mjs');
const _page49 = () => import('./pages/contact.astro.mjs');
const _page50 = () => import('./pages/cookie-policy.astro.mjs');
const _page51 = () => import('./pages/coupon/_slug_.astro.mjs');
const _page52 = () => import('./pages/coupons/brands/_slug_.astro.mjs');
const _page53 = () => import('./pages/coupons/brands.astro.mjs');
const _page54 = () => import('./pages/coupons/categories/_slug_.astro.mjs');
const _page55 = () => import('./pages/coupons/categories.astro.mjs');
const _page56 = () => import('./pages/coupons/merchants/_id_.astro.mjs');
const _page57 = () => import('./pages/coupons/merchants.astro.mjs');
const _page58 = () => import('./pages/coupons.astro.mjs');
const _page59 = () => import('./pages/deal/_id_.astro.mjs');
const _page60 = () => import('./pages/deals.astro.mjs');
const _page61 = () => import('./pages/disclaimer.astro.mjs');
const _page62 = () => import('./pages/faq.astro.mjs');
const _page63 = () => import('./pages/go/deal/_id_.astro.mjs');
const _page64 = () => import('./pages/go/merchant/_slug_.astro.mjs');
const _page65 = () => import('./pages/go/_id_.astro.mjs');
const _page66 = () => import('./pages/how-it-works.astro.mjs');
const _page67 = () => import('./pages/legal.astro.mjs');
const _page68 = () => import('./pages/merchants/_id_.astro.mjs');
const _page69 = () => import('./pages/merchants.astro.mjs');
const _page70 = () => import('./pages/newsletter/confirm.astro.mjs');
const _page71 = () => import('./pages/newsletter/error.astro.mjs');
const _page72 = () => import('./pages/newsletter/preferences.astro.mjs');
const _page73 = () => import('./pages/newsletter/success.astro.mjs');
const _page74 = () => import('./pages/newsletter/unsubscribed.astro.mjs');
const _page75 = () => import('./pages/privacy.astro.mjs');
const _page76 = () => import('./pages/search.astro.mjs');
const _page77 = () => import('./pages/sitemap.xml.astro.mjs');
const _page78 = () => import('./pages/terms.astro.mjs');
const _page79 = () => import('./pages/index.astro.mjs');
const pageMap = new Map([
    ["node_modules/astro/dist/assets/endpoint/generic.js", _page0],
    ["src/pages/404.astro", _page1],
    ["src/pages/about.astro", _page2],
    ["src/pages/affiliate-disclosure.astro", _page3],
    ["src/pages/api/alerts/admin-notification.ts", _page4],
    ["src/pages/api/alerts/manage-subscriptions.ts", _page5],
    ["src/pages/api/alerts/send-brand-alert.ts", _page6],
    ["src/pages/api/alerts/send-merchant-alert.ts", _page7],
    ["src/pages/api/alerts/subscribe-multi.ts", _page8],
    ["src/pages/api/alerts/trigger-new-coupon.ts", _page9],
    ["src/pages/api/brands.ts", _page10],
    ["src/pages/api/categories.ts", _page11],
    ["src/pages/api/contact.ts", _page12],
    ["src/pages/api/create-tables.ts", _page13],
    ["src/pages/api/database/create-tables-direct.ts", _page14],
    ["src/pages/api/database/setup-multi-subscription-direct.ts", _page15],
    ["src/pages/api/database/setup-multi-subscription-system.ts", _page16],
    ["src/pages/api/deals.ts", _page17],
    ["src/pages/api/debug/direct-supabase-test.ts", _page18],
    ["src/pages/api/debug/env-check.ts", _page19],
    ["src/pages/api/debug/supabase-test.ts", _page20],
    ["src/pages/api/direct-setup.ts", _page21],
    ["src/pages/api/fix-rls.ts", _page22],
    ["src/pages/api/health.ts", _page23],
    ["src/pages/api/merchants.ts", _page24],
    ["src/pages/api/newsletter/add-enhanced-columns.ts", _page25],
    ["src/pages/api/newsletter/confirm.ts", _page26],
    ["src/pages/api/newsletter/consolidate-tables.ts", _page27],
    ["src/pages/api/newsletter/create-preferences-table.ts", _page28],
    ["src/pages/api/newsletter/get-preferences.ts", _page29],
    ["src/pages/api/newsletter/migrate-preferences.ts", _page30],
    ["src/pages/api/newsletter/subscribe.ts", _page31],
    ["src/pages/api/newsletter/unsubscribe.ts", _page32],
    ["src/pages/api/newsletter/update-preferences.ts", _page33],
    ["src/pages/api/newsletter/user-subscribe.ts", _page34],
    ["src/pages/api/performance/vitals.ts", _page35],
    ["src/pages/api/setup-db.ts", _page36],
    ["src/pages/api/track-click.ts", _page37],
    ["src/pages/api/track-impression.ts", _page38],
    ["src/pages/api/vote.ts", _page39],
    ["src/pages/api/vote-counts.ts", _page40],
    ["src/pages/blog.astro", _page41],
    ["src/pages/bookmarks.astro", _page42],
    ["src/pages/brands/[slug].astro", _page43],
    ["src/pages/brands.astro", _page44],
    ["src/pages/card-comparison.astro", _page45],
    ["src/pages/categories/[slug].astro", _page46],
    ["src/pages/categories.astro", _page47],
    ["src/pages/comprehensive-review-analysis.astro", _page48],
    ["src/pages/contact.astro", _page49],
    ["src/pages/cookie-policy.astro", _page50],
    ["src/pages/coupon/[slug].astro", _page51],
    ["src/pages/coupons/brands/[slug].astro", _page52],
    ["src/pages/coupons/brands/index.astro", _page53],
    ["src/pages/coupons/categories/[slug].astro", _page54],
    ["src/pages/coupons/categories/index.astro", _page55],
    ["src/pages/coupons/merchants/[id].astro", _page56],
    ["src/pages/coupons/merchants/index.astro", _page57],
    ["src/pages/coupons/index.astro", _page58],
    ["src/pages/deal/[id].astro", _page59],
    ["src/pages/deals.astro", _page60],
    ["src/pages/disclaimer.astro", _page61],
    ["src/pages/faq.astro", _page62],
    ["src/pages/go/deal/[id].ts", _page63],
    ["src/pages/go/merchant/[slug].ts", _page64],
    ["src/pages/go/[id].astro", _page65],
    ["src/pages/how-it-works.astro", _page66],
    ["src/pages/legal.astro", _page67],
    ["src/pages/merchants/[id].astro", _page68],
    ["src/pages/merchants.astro", _page69],
    ["src/pages/newsletter/confirm.astro", _page70],
    ["src/pages/newsletter/error.astro", _page71],
    ["src/pages/newsletter/preferences.astro", _page72],
    ["src/pages/newsletter/success.astro", _page73],
    ["src/pages/newsletter/unsubscribed.astro", _page74],
    ["src/pages/privacy.astro", _page75],
    ["src/pages/search.astro", _page76],
    ["src/pages/sitemap.xml.ts", _page77],
    ["src/pages/terms.astro", _page78],
    ["src/pages/index.astro", _page79]
]);

const _manifest = Object.assign(manifest, {
    pageMap,
    serverIslandMap,
    renderers,
    actions: () => import('./_noop-actions.mjs'),
    middleware: () => import('./_astro-internal_middleware.mjs')
});
const _args = {
    "middlewareSecret": "0eeb5754-b000-4729-a88f-00949fc8fac3",
    "skewProtection": false
};
const _exports = createExports(_manifest, _args);
const __astrojsSsrVirtualEntry = _exports.default;

export { __astrojsSsrVirtualEntry as default, pageMap };
