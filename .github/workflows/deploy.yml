name: Deploy to <PERSON>flare Pages

on:
  push:
    branches: [workers-production]
  pull_request:
    branches: [workers-production]

jobs:
  deploy:
    runs-on: ubuntu-latest
    name: Deploy
    steps:
      - uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '20'
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build
        run: npm run build

      - name: Publish to Cloudflare Pages
        uses: cloudflare/pages-action@v1
        with:
          apiToken: ${{ secrets.CLOUDFLARE_API_TOKEN }}
          accountId: ${{ secrets.CLOUDFLARE_ACCOUNT_ID }}
          projectName: vapehybrid
          directory: dist
          gitHubToken: ${{ secrets.GITHUB_TOKEN }}
          workingDirectory: ./
          wranglerVersion: '3'
          environmentVariables: >
            NODE_VERSION=20
            NODE_ENV=production
            PUBLIC_SITE_URL=https://vapehybrid.com
            SUPABASE_URL=${{ secrets.SUPABASE_URL }}
            SUPABASE_ANON_KEY=${{ secrets.SUPABASE_ANON_KEY }}
            PUBLIC_SUPABASE_URL=${{ secrets.PUBLIC_SUPABASE_URL }}
            PUBLIC_SUPABASE_ANON_KEY=${{ secrets.PUBLIC_SUPABASE_ANON_KEY }}
            ASTRO_SITE_URL=https://vapehybrid.com
