# VapeHybrid Coupon Site - Production Deployment Guide

This comprehensive guide outlines the branching strategy, deployment process, and maintenance procedures for the VapeHybrid Coupon Site. Use this document as your reference for all production-related operations.

## Branching Strategy

We follow a structured branching strategy to maintain code quality and ensure smooth deployments:

| Branch | Purpose |
|--------|---------|
| master | Development work, preview deployments |
| production | Stable live releases |
| feature/* | Isolated features (e.g., feature/saved-deals) |
| hotfix/* | Emergency fixes for production |
| Tags (PRV001, PRV002, etc.) | Versioned production releases |

### Branch Protection Rules

To maintain code quality, we recommend setting up these branch protection rules in GitHub:

1. **For master branch:**
   - Require pull request reviews before merging
   - Require status checks to pass before merging
   - Require branches to be up to date before merging

2. **For production branch:**
   - Require pull request reviews before merging
   - Require status checks to pass before merging
   - Restrict who can push to the branch (only administrators)

## Git Aliases

For faster workflow, add these aliases to your `.gitconfig`:

```ini
[alias]
  co = checkout
  br = branch
  cm = commit -m
  st = status
  lg = log --oneline --graph --all
```

## Development Workflow

### Daily Development

1. Work on the `master` branch for ongoing development:
   ```bash
   git co master
   git pull  # Always pull latest changes before starting work
   ```

2. For new features, create a feature branch:
   ```bash
   git co -b feature/new-feature
   ```

3. Make regular commits with descriptive messages:
   ```bash
   git cm "Add user profile avatar selection"
   ```

4. Push your feature branch to remote to back up your work:
   ```bash
   git push -u origin feature/new-feature
   ```

5. When the feature is complete, create a pull request to master in GitHub

6. After the PR is approved and merged, delete the feature branch:
   ```bash
   git co master
   git pull  # Get the latest changes including your merged feature
   git br -d feature/new-feature  # Delete local branch
   git push origin --delete feature/new-feature  # Delete remote branch
   ```

### Production Release Process

1. Ensure all tests pass on the master branch

2. Update version numbers in package.json and README.md:
   ```bash
   git co master
   # Edit files to update version numbers
   git cm "Bump version to 1.x.0"
   git push origin master
   ```

3. When ready for a production release, merge master into production:
   ```bash
   git co production
   git pull  # Ensure production is up-to-date
   git merge master
   ```

4. Tag the production release with the next PRV number:
   ```bash
   git tag -a PRV002 -m "Production release v1.x.0"
   ```

5. Push the production branch and tags:
   ```bash
   git push origin production
   git push origin --tags
   ```

6. Monitor the deployment in Cloudflare Pages dashboard

### Hotfix Process

For critical bugs in production that need immediate fixing:

1. Create a hotfix branch from production:
   ```bash
   git co production
   git co -b hotfix/critical-bug-fix
   ```

2. Fix the issue and commit:
   ```bash
   # Make your changes
   git cm "Fix critical authentication issue"
   ```

3. Push the hotfix branch and create a PR to production:
   ```bash
   git push -u origin hotfix/critical-bug-fix
   # Create PR in GitHub targeting production branch
   ```

4. After the PR is merged to production, tag a new patch release:
   ```bash
   git co production
   git pull
   git tag -a PRV003 -m "Hotfix: Critical authentication issue"
   git push origin --tags
   ```

5. Don't forget to merge the hotfix back to master:
   ```bash
   git co master
   git merge hotfix/critical-bug-fix
   git push origin master
   ```

## Deployment Configuration

### Cloudflare Pages Setup

1. Log in to the Cloudflare Dashboard
2. Navigate to Pages > Create a project
3. Connect to your GitHub repository
4. Configure build settings:
   - Production branch: `production`
   - Preview branch: `master`
   - Build command: `npm run build`
   - Build output directory: `dist`
   - Node.js version: 18 (or latest LTS)

5. Configure environment variables for both production and preview environments:

| Variable | Production Example | Preview Example |
|----------|-------------------|----------------|
| `ASTRO_SITE_URL` | https://coupons.vapehybrid.com | https://preview-coupons.vapehybrid.com |
| `SUPABASE_URL` | https://zlnvivfgzgcjuspktadj.supabase.co | https://zlnvivfgzgcjuspktadj.supabase.co |
| `SUPABASE_ANON_KEY` | [PRODUCTION KEY] | [PREVIEW KEY] |
| `SUPABASE_SERVICE_ROLE_KEY` | [PRODUCTION KEY] | [PREVIEW KEY] |
| `AFFILIATE_CLOAK_BASE_URL` | /go | /go |
| `REVEAL_COOKIE_PREFIX` | revealed_deal_ | revealed_deal_ |
| `PUBLIC_RECAPTCHA_SITE_KEY` | [SITE KEY] | [SITE KEY] |
| `RECAPTCHA_SECRET_KEY` | [SECRET KEY] | [SECRET KEY] |
| `RESEND_API_KEY` | [PRODUCTION KEY] | [PREVIEW KEY] |

### Custom Domains

1. In Cloudflare Pages, go to your project settings
2. Navigate to Custom Domains
3. Add your domains:
   - Production: coupons.vapehybrid.com
   - Preview: preview-coupons.vapehybrid.com

### Deployment Hooks

You can set up deployment hooks to trigger builds from external systems:

1. In Cloudflare Pages, go to your project settings
2. Navigate to Deployment hooks
3. Create hooks for production and preview environments
4. Use these webhook URLs in your CI/CD pipeline if needed

## Maintenance Procedures

### Rollback Procedure

If critical issues are found in a production release:

1. Identify the last stable tag:
   ```bash
   git tag -l
   ```

2. Checkout the production branch and reset to the stable tag:
   ```bash
   git co production
   git reset --hard PRV001  # Replace with the last stable tag
   ```

3. Force push the rollback:
   ```bash
   git push -f origin production
   ```

4. Cloudflare Pages will automatically deploy the rolled-back version

5. Create a new tag to mark the rollback:
   ```bash
   git tag -a PRV004-rollback -m "Rollback to PRV001 due to critical issue"
   git push origin --tags
   ```

### Monitoring Production

1. **Error Monitoring:**
   - Set up error tracking with a service like Sentry
   - Configure alerts for critical errors

2. **Performance Monitoring:**
   - Use Cloudflare Analytics to monitor site performance
   - Set up custom performance metrics in your application

3. **Security Monitoring:**
   - Regularly check Supabase logs for unusual activity
   - Monitor authentication attempts and API usage

### Database Backups

1. **Automated Backups:**
   - Supabase provides daily backups by default
   - Verify backup settings in Supabase dashboard

2. **Manual Backups Before Major Changes:**
   ```bash
   # Using Supabase CLI
   supabase db dump -f backup-YYYY-MM-DD.sql
   ```

### Regular Maintenance Tasks

1. **Dependency Updates:**
   - Regularly update dependencies to patch security vulnerabilities
   - Schedule monthly dependency updates

2. **Security Audits:**
   - Quarterly review of RLS policies
   - Check for exposed API keys or credentials

3. **Performance Optimization:**
   - Review and optimize database queries
   - Analyze and improve page load times

## Version History

| Version | Tag | Date | Description |
|---------|-----|------|-------------|
| 1.0.0 | PRV001 | 2024-05-15 | Initial production release |
| 2.9.0 | PRV002 | 2024-08-03 | Security and performance improvements: CSP implementation, responsive images, SEO optimization |
| 3.0.0 | PRV003 | 2024-08-10 | Cloudflare deployment configuration: Switched from Node.js to Cloudflare adapter |

## Troubleshooting Common Issues

### Deployment Failures

1. **Build Errors:**
   - Check build logs in Cloudflare Pages
   - Verify all dependencies are correctly installed
   - Ensure environment variables are properly set

2. **Runtime Errors:**
   - Check browser console for JavaScript errors
   - Verify API endpoints are responding correctly
   - Check Supabase connection and authentication

### Git Issues

1. **Merge Conflicts:**
   ```bash
   # When encountering merge conflicts
   git status  # Identify conflicted files
   # Resolve conflicts in your editor
   git add .  # Add resolved files
   git merge --continue  # Complete the merge
   ```

2. **Accidental Commits to Wrong Branch:**
   ```bash
   # If you committed to production instead of a feature branch
   git log  # Identify the commit hash
   git reset --soft HEAD~1  # Undo the last commit but keep changes
   git stash  # Stash the changes
   git co feature/branch  # Switch to correct branch
   git stash pop  # Apply the changes
   git cm "Your commit message"  # Commit to correct branch
   ```

## Contact Information

- **DevOps Team:** <EMAIL>
- **Lead Developer:** <EMAIL>
- **Emergency Support:** <EMAIL> or +1-555-123-4567

## Additional Resources

- [Cloudflare Pages Documentation](https://developers.cloudflare.com/pages/)
- [Supabase Documentation](https://supabase.com/docs)
- [Astro Documentation](https://docs.astro.build/)
- [Git Branching Strategies](https://www.atlassian.com/git/tutorials/comparing-workflows)


