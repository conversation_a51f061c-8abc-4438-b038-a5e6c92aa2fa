import{j as e}from"./vendor/react-bq1VYqFi.js";import{W as s}from"./app/design-system-lJ5ci6hi.js";import"./vendor/ui-components-_FzKGR3X.js";import"./vendor/utils-hwhCH1Yb.js";const i=({categories:i})=>e.jsx("div",{className:"bg-design-background border-b border-design-border py-4 z-10",children:e.jsx("div",{className:"container mx-auto px-4",children:e.jsx("div",{className:"flex items-center justify-between",children:e.jsx("div",{className:"flex-1 overflow-x-auto hide-scrollbar",children:e.jsxs("div",{className:"flex space-x-8 md:space-x-10 px-2 justify-center",children:[i.slice(0,8).map((i=>{return e.jsxs("a",{href:`/coupons/categories/${i.slug}`,className:"flex flex-col items-center justify-center min-w-[80px] group",children:[e.jsx("div",{className:"w-16 h-16 md:w-14 md:h-14 rounded-full bg-design-primary/10 dark:bg-design-primary/20 flex items-center justify-center mb-3 group-hover:bg-design-primary/20 dark:group-hover:bg-design-primary/30 transition-colors shadow-sm",children:e.jsx(s,{src:(r=i.title,{"Box Mods":"/icons/002-vape.png","Disposable Pod Kits":"/icons/003-disposable.png","Disposable Vapes":"/icons/004-vape-1.png","E-Juice":"/icons/005-strawberry-juice.png",Festival:"/icons/001-grape.png",Giveaway:"/icons/010-electronic-cigarette.png","Pod System Kits":"/icons/007-vape-2.png",Sale:"/icons/009-atomizer.png",Coils:"/icons/006-coil.png",Tanks:"/icons/008-coil-1.png"}[r]||"/icons/002-vape.png"),alt:`${i.title} category icon`,className:"w-8 h-8 md:w-7 md:h-7 object-contain",fallbackSrc:"/icons/002-vape.png"})}),e.jsx("span",{className:"text-sm font-medium text-design-foreground whitespace-nowrap",children:i.title})]},i.slug);var r})),e.jsxs("a",{href:"/coupons/categories",className:"flex flex-col items-center justify-center min-w-[80px] group",children:[e.jsx("div",{className:"w-16 h-16 md:w-14 md:h-14 rounded-full bg-design-muted flex items-center justify-center mb-3 group-hover:bg-design-muted/80 transition-colors shadow-sm",children:e.jsx("svg",{xmlns:"http://www.w3.org/2000/svg",className:"h-8 w-8 md:h-6 md:w-6 text-design-foreground",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor",children:e.jsx("path",{strokeLinecap:"round",strokeLinejoin:"round",strokeWidth:2,d:"M5 12h.01M12 12h.01M19 12h.01M6 12a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0zm7 0a1 1 0 11-2 0 1 1 0 012 0z"})})}),e.jsx("span",{className:"text-sm font-medium text-design-foreground whitespace-nowrap",children:"More Categories"})]})]})})})})});export{i as CategoryNav};
