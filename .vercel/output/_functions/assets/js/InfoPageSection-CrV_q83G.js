import { c as createAstro, a as createComponent, m as maybeRenderHead, r as renderTemplate, b as addAttribute, d as renderComponent, F as Fragment, e as renderSlot, u as unescapeHTML } from './astro/server-CU4H3-CM.js';
import 'kleur/colors';
import 'clsx';

const $$Astro$2 = createAstro("http://localhost:4321");
const $$Breadcrumbs = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$2, $$props, $$slots);
  Astro2.self = $$Breadcrumbs;
  const { currentPage } = Astro2.props;
  return renderTemplate`${maybeRenderHead()}<div class="text-center mb-2"> <p class="text-xs text-black/60 dark:text-white/60 font-medium"> <a href="/" class="hover:text-primary transition-colors">Home</a> <span class="mx-1.5">/</span> <span>${currentPage}</span> </p> </div>`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/Breadcrumbs.astro", void 0);

const $$Astro$1 = createAstro("http://localhost:4321");
const $$InfoPageHeader = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro$1, $$props, $$slots);
  Astro2.self = $$InfoPageHeader;
  const {
    title,
    subtitle,
    background = "default",
    pattern = "none",
    glass = false,
    className = ""
  } = Astro2.props;
  const backgroundStyles = {
    default: "",
    primary: "bg-design-primary text-white dark:text-black",
    secondary: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    accent: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    muted: "bg-design-muted",
    transparent: "bg-transparent"
  };
  const patternStyles = {
    none: "",
    dots: "bg-[url('/patterns/dots-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/dots-dark.svg')] dark:opacity-10",
    grid: "bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-10",
    circles: "bg-[url('/patterns/circle-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/circle-dark.svg')] dark:opacity-10",
    hexagon: "bg-[url('/patterns/hexagon-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/hexagon-dark.svg')] dark:opacity-10",
    wave: "bg-[url('/patterns/wave-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/wave-dark.svg')] dark:opacity-10"
  };
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(`relative mb-6 text-center px-4 pt-2 pb-6 md:pt-3 md:pb-8 ${backgroundStyles[background]} ${className} ${glass ? "backdrop-blur-sm" : ""} ${background !== "transparent" ? "rounded-lg" : ""}`, "class")}>  ${pattern !== "none" && renderTemplate`<div${addAttribute(`absolute inset-0 ${patternStyles[pattern]} z-0 pointer-events-none`, "class")}></div>`}  ${glass && renderTemplate`${renderComponent($$result, "Fragment", Fragment, {}, { "default": ($$result2) => renderTemplate` <div class="absolute top-[-10%] right-[-5%] w-[30%] h-[30%] bg-design-primary/10 dark:bg-design-primary/5 rounded-full filter blur-[60px] pointer-events-none"></div> <div class="absolute bottom-[-10%] left-[-5%] w-[30%] h-[30%] bg-design-primary/10 dark:bg-design-primary/5 rounded-full filter blur-[60px] pointer-events-none"></div> ` })}`}  <div class="relative z-10 -mt-1"> ${renderComponent($$result, "Breadcrumbs", $$Breadcrumbs, { "currentPage": title })} <h1${addAttribute(`text-4xl md:text-5xl font-normal mb-3 ${background === "default" || background === "transparent" ? "text-design-foreground" : ""}`, "class")}> ${title} </h1> ${subtitle && renderTemplate`<p${addAttribute(`text-xl ${background === "default" || background === "transparent" ? "text-design-muted-foreground" : "text-white/80 dark:text-black/80"} max-w-2xl mx-auto`, "class")}> ${subtitle} </p>`} </div> </div>`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/InfoPageHeader.astro", void 0);

const $$Astro = createAstro("http://localhost:4321");
const $$InfoPageSection = createComponent(($$result, $$props, $$slots) => {
  const Astro2 = $$result.createAstro($$Astro, $$props, $$slots);
  Astro2.self = $$InfoPageSection;
  const {
    title,
    iconName,
    iconSize = "w-12 h-12",
    iconBg = "bg-primary/10 dark:bg-primary/20",
    iconColor = "text-primary dark:text-design-secondary",
    withMargin = true,
    centerTitle = false,
    centerContent = false,
    background = "default",
    pattern = "none",
    glass = false,
    className = ""
  } = Astro2.props;
  const iconMap = {
    building: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="${iconColor}"><rect width="16" height="20" x="4" y="2" rx="2" ry="2"></rect><path d="M9 22v-4h6v4"></path><path d="M8 6h.01"></path><path d="M16 6h.01"></path><path d="M12 6h.01"></path><path d="M12 10h.01"></path><path d="M12 14h.01"></path><path d="M16 10h.01"></path><path d="M16 14h.01"></path><path d="M8 10h.01"></path><path d="M8 14h.01"></path></svg>`,
    messageSquare: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="${iconColor}"><path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path></svg>`,
    globe: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="${iconColor}"><circle cx="12" cy="12" r="10"></circle><line x1="2" x2="22" y1="12" y2="12"></line><path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path></svg>`,
    helpCircle: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="${iconColor}"><circle cx="12" cy="12" r="10"></circle><path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"></path><path d="M12 17h.01"></path></svg>`,
    bookOpen: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="${iconColor}"><path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path><path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path></svg>`,
    users: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="${iconColor}"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>`,
    search: `<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="${iconColor}"><circle cx="11" cy="11" r="8"></circle><line x1="21" y1="21" x2="16.65" y2="16.65"></line></svg>`
  };
  const backgroundStyles = {
    default: "",
    primary: "bg-design-primary text-white dark:text-black",
    secondary: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    accent: "bg-design-primary text-white dark:bg-design-primary dark:text-black",
    muted: "bg-design-muted",
    transparent: "bg-transparent"
  };
  const patternStyles = {
    none: "",
    dots: "bg-[url('/patterns/dots-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/dots-dark.svg')] dark:opacity-10",
    grid: "bg-[url('/patterns/grid-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/grid-dark.svg')] dark:opacity-10",
    circles: "bg-[url('/patterns/circle-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/circle-dark.svg')] dark:opacity-10",
    hexagon: "bg-[url('/patterns/hexagon-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/hexagon-dark.svg')] dark:opacity-10",
    wave: "bg-[url('/patterns/wave-light.svg')] bg-repeat opacity-15 dark:bg-[url('/patterns/wave-dark.svg')] dark:opacity-10"
  };
  return renderTemplate`${maybeRenderHead()}<div${addAttribute(`relative ${withMargin ? "mb-20" : ""} ${backgroundStyles[background]} ${className} ${glass ? "backdrop-blur-sm" : ""} ${background !== "transparent" ? "rounded-lg" : ""} p-4`, "class")}>  ${pattern !== "none" && renderTemplate`<div${addAttribute(`absolute inset-0 ${patternStyles[pattern]} z-0 pointer-events-none`, "class")}></div>`}  ${glass && renderTemplate`${renderComponent($$result, "Fragment", Fragment, {}, { "default": ($$result2) => renderTemplate` <div class="absolute top-[-10%] right-[-5%] w-[40%] h-[40%] bg-design-primary/10 dark:bg-design-primary/5 rounded-full filter blur-[80px] pointer-events-none"></div> <div class="absolute bottom-[-10%] left-[-5%] w-[40%] h-[40%] bg-design-primary/10 dark:bg-design-primary/5 rounded-full filter blur-[80px] pointer-events-none"></div> <div class="absolute top-[30%] left-[20%] w-[25%] h-[25%] bg-design-primary/8 dark:bg-design-primary/3 rounded-full filter blur-[60px] animate-float pointer-events-none"></div> <div class="absolute bottom-[20%] right-[15%] w-[20%] h-[20%] bg-design-primary/8 dark:bg-design-primary/3 rounded-full filter blur-[50px] animate-float animation-delay-200 pointer-events-none"></div> ` })}`}  <div${addAttribute(`relative z-10 ${background !== "transparent" ? "p-6" : ""} overflow-visible`, "class")}> ${title && renderTemplate`<div${addAttribute(`${centerTitle ? "text-center" : ""}`, "class")}> ${iconName && iconMap[iconName] && renderTemplate`<div class="flex justify-center mb-4"> <div${addAttribute(`${iconBg} p-6 rounded-full`, "class")}> <span${addAttribute(iconSize, "class")}>${unescapeHTML(iconMap[iconName])}</span> </div> </div>`} <h2${addAttribute(`text-2xl md:text-3xl font-bold mb-10 ${background === "default" || background === "transparent" ? "text-design-foreground" : ""}`, "class")}> ${title} </h2> </div>`} <div${addAttribute(`${background === "default" || background === "transparent" ? "text-design-muted-foreground" : ""} ${centerContent ? "text-center" : ""}`, "class")}> ${renderSlot($$result, $$slots["default"])} </div> </div> </div>`;
}, "C:/Users/<USER>/WebstormProjects/Vape-Hybrid-Coupons-workers-production/src/components/InfoPageSection.astro", void 0);

export { $$InfoPageHeader as $, $$InfoPageSection as a };
