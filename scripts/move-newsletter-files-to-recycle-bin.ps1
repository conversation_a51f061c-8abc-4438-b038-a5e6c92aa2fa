# PowerShell script to move redundant newsletter files to the recycle bin
# Run this script from the project root directory

# Create recycle bin directories if they don't exist
Write-Host "Creating recycle bin directories..." -ForegroundColor Green
New-Item -ItemType Directory -Force -Path "src\recycle-bin\pages\api\newsletter" | Out-Null
New-Item -ItemType Directory -Force -Path "src\recycle-bin\docs" | Out-Null

# Files to move from src/pages to recycle bin
$pagesToMove = @(
    "src\pages\test-newsletter.astro",
    "src\pages\test-newsletter-popup.astro",
    "src\pages\test-exit-intent.astro",
    "src\pages\test-newsletter-section.astro",
    "src\pages\test-newsletter-api.astro",
    "src\pages\test-newsletter-db.astro"
)

# Files to move from src/pages/api to recycle bin
$apiToMove = @(
    "src\pages\api\test-newsletter-db.ts",
    "src\pages\api\newsletter\drop-redundant-tables.ts"
)

# Files to move from docs to recycle bin
$docsToMove = @(
    "docs\newsletter-db-cleanup.sql",
    "docs\delete-redundant-tables.sql"
)

# Function to move a file to the recycle bin if it exists
function Move-FileToRecycleBin {
    param (
        [string]$sourcePath,
        [string]$destinationPath
    )
    
    if (Test-Path $sourcePath) {
        Write-Host "Moving $sourcePath to $destinationPath" -ForegroundColor Yellow
        
        # Create destination directory if it doesn't exist
        $destinationDir = Split-Path -Parent $destinationPath
        if (-not (Test-Path $destinationDir)) {
            New-Item -ItemType Directory -Force -Path $destinationDir | Out-Null
        }
        
        # Move the file
        Move-Item -Path $sourcePath -Destination $destinationPath -Force
        Write-Host "  ✓ Moved successfully" -ForegroundColor Green
    } else {
        Write-Host "File not found: $sourcePath" -ForegroundColor Gray
    }
}

# Move page files
Write-Host "`nMoving page files..." -ForegroundColor Cyan
foreach ($file in $pagesToMove) {
    $fileName = Split-Path -Leaf $file
    $destination = "src\recycle-bin\pages\$fileName"
    Move-FileToRecycleBin -sourcePath $file -destinationPath $destination
}

# Move API files
Write-Host "`nMoving API files..." -ForegroundColor Cyan
foreach ($file in $apiToMove) {
    if ($file -like "*\newsletter\*") {
        $fileName = Split-Path -Leaf $file
        $destination = "src\recycle-bin\pages\api\newsletter\$fileName"
    } else {
        $fileName = Split-Path -Leaf $file
        $destination = "src\recycle-bin\pages\api\$fileName"
    }
    Move-FileToRecycleBin -sourcePath $file -destinationPath $destination
}

# Move docs files
Write-Host "`nMoving documentation files..." -ForegroundColor Cyan
foreach ($file in $docsToMove) {
    $fileName = Split-Path -Leaf $file
    $destination = "src\recycle-bin\docs\$fileName"
    Move-FileToRecycleBin -sourcePath $file -destinationPath $destination
}

Write-Host "`nAll redundant newsletter files have been moved to the recycle bin." -ForegroundColor Green
Write-Host "Please restart the development server to apply changes." -ForegroundColor Yellow
