fix(cloudflare): v3.12.0 - Fix Cloudflare Workers environment variables and RLS policies

- Updated Supabase anon key in wrangler.toml (expired 1999 → 2059)
- Fixed environment variable access for Cloudflare Workers runtime
- Updated all Supabase client functions to use locals.runtime.env
- Fixed RLS policies for public read access on main tables (deals, categories, brands, merchants)
- Created missing sitemap.xml.ts with comprehensive error handling
- Added /coupons/brands to sitemap generator as requested
- Updated all API routes and Astro pages to pass locals parameter
- Resolved "supabaseUrl is required" errors causing blank pages
- All 306 deals now loading correctly on frontend
- Sitemap now accessible at /sitemap.xml with proper SEO structure

Fixes: Empty page loads, API authentication errors, missing sitemap
Result: Full site functionality restored on Cloudflare Workers deployment
